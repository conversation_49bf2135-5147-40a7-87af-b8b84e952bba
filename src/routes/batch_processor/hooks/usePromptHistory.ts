/**
 * usePromptHistory.ts
 * 提示词历史记录 React Hook
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import {
  promptHistoryService,
  PromptHistoryItem,
} from '../services/PromptHistoryService';

export interface UsePromptHistoryReturn {
  // 数据
  historyItems: PromptHistoryItem[];

  // 状态
  isLoading: boolean;
  error: Error | null;

  // 操作方法
  addPrompt: (content: string, title?: string) => Promise<PromptHistoryItem>;
  usePrompt: (id: string) => Promise<PromptHistoryItem | null>;
  renamePrompt: (id: string, newTitle: string) => Promise<boolean>;
  deletePrompt: (id: string) => Promise<boolean>;
  clearHistory: () => Promise<void>;
  refreshHistory: () => Promise<void>;

  // 工具方法
  getPromptById: (id: string) => PromptHistoryItem | null;
  isHistoryLoaded: boolean;
}

/**
 * 提示词历史记录 Hook
 * 提供历史记录的状态管理和操作方法
 */
export function usePromptHistory(): UsePromptHistoryReturn {
  const [historyItems, setHistoryItems] = useState<PromptHistoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [isHistoryLoaded, setIsHistoryLoaded] = useState(false);

  // 使用 ref 来避免重复加载
  const loadingRef = useRef(false);
  const mountedRef = useRef(true);

  /**
   * 安全的状态更新函数
   */
  const safeSetState = useCallback(
    <T>(setter: React.Dispatch<React.SetStateAction<T>>, value: T) => {
      if (mountedRef.current) {
        setter(value);
      }
    },
    [],
  );

  /**
   * 错误处理函数
   */
  const handleError = useCallback(
    (err: unknown, operation: string) => {
      const error = err instanceof Error ? err : new Error(String(err));
      console.error(`Prompt history ${operation} failed:`, error);
      safeSetState(setError, error);
      return error;
    },
    [safeSetState],
  );

  /**
   * 刷新历史记录
   */
  const refreshHistory = useCallback(async (): Promise<void> => {
    if (loadingRef.current) {
      return;
    }

    loadingRef.current = true;
    safeSetState(setIsLoading, true);
    safeSetState(setError, null);

    try {
      const items = await promptHistoryService.getHistory();
      safeSetState(setHistoryItems, items);
      safeSetState(setIsHistoryLoaded, true);
    } catch (err) {
      handleError(err, 'refresh');
    } finally {
      loadingRef.current = false;
      safeSetState(setIsLoading, false);
    }
  }, [safeSetState, handleError]);

  /**
   * 闲时初始化历史记录加载
   */
  const initializeHistoryLoading = useCallback(() => {
    // 启动闲时加载
    promptHistoryService
      .loadHistoryWhenIdle()
      .then(() => {
        // 加载完成后刷新状态
        if (mountedRef.current) {
          refreshHistory();
        }
      })
      .catch(err => {
        handleError(err, 'idle loading');
      });
  }, [refreshHistory, handleError]);

  /**
   * 添加提示词
   */
  const addPrompt = useCallback(
    async (content: string, title?: string): Promise<PromptHistoryItem> => {
      safeSetState(setError, null);

      try {
        const item = await promptHistoryService.addOrUpdatePrompt(
          content,
          title,
        );

        // 更新本地状态
        setHistoryItems(prevItems => {
          const existingIndex = prevItems.findIndex(p => p.id === item.id);
          if (existingIndex >= 0) {
            // 更新现有项目并移到首位
            const newItems = [...prevItems];
            newItems.splice(existingIndex, 1);
            return [item, ...newItems];
          } else {
            // 添加新项目到首位
            const newItems = [item, ...prevItems];
            return newItems.slice(0, 10); // 保持最大10个
          }
        });

        return item;
      } catch (err) {
        throw handleError(err, 'add');
      }
    },
    [safeSetState, handleError],
  );

  /**
   * 使用提示词
   */
  const usePrompt = useCallback(
    async (id: string): Promise<PromptHistoryItem | null> => {
      safeSetState(setError, null);

      try {
        const item = await promptHistoryService.usePrompt(id);

        if (item) {
          // 更新本地状态，将使用的项目移到首位
          setHistoryItems(prevItems => {
            const newItems = prevItems.filter(p => p.id !== id);
            return [item, ...newItems];
          });
        }

        return item;
      } catch (err) {
        handleError(err, 'use');
        return null;
      }
    },
    [safeSetState, handleError],
  );

  /**
   * 重命名提示词
   */
  const renamePrompt = useCallback(
    async (id: string, newTitle: string): Promise<boolean> => {
      safeSetState(setError, null);

      try {
        const success = await promptHistoryService.renamePrompt(id, newTitle);

        if (success) {
          // 更新本地状态
          setHistoryItems(prevItems =>
            prevItems.map(item =>
              item.id === id
                ? { ...item, title: newTitle, updatedAt: Date.now() }
                : item,
            ),
          );
        }

        return success;
      } catch (err) {
        handleError(err, 'rename');
        return false;
      }
    },
    [safeSetState, handleError],
  );

  /**
   * 删除提示词
   */
  const deletePrompt = useCallback(
    async (id: string): Promise<boolean> => {
      safeSetState(setError, null);

      try {
        const success = await promptHistoryService.deletePrompt(id);

        if (success) {
          // 更新本地状态
          setHistoryItems(prevItems =>
            prevItems.filter(item => item.id !== id),
          );
        }

        return success;
      } catch (err) {
        handleError(err, 'delete');
        return false;
      }
    },
    [safeSetState, handleError],
  );

  /**
   * 清空历史记录
   */
  const clearHistory = useCallback(async (): Promise<void> => {
    safeSetState(setError, null);

    try {
      await promptHistoryService.clearHistory();
      safeSetState(setHistoryItems, []);
    } catch (err) {
      throw handleError(err, 'clear');
    }
  }, [safeSetState, handleError]);

  /**
   * 根据 ID 获取提示词
   */
  const getPromptById = useCallback(
    (id: string): PromptHistoryItem | null =>
      historyItems.find(item => item.id === id) || null,
    [historyItems],
  );

  // 组件挂载时初始化闲时加载
  useEffect(() => {
    mountedRef.current = true;
    initializeHistoryLoading();

    return () => {
      mountedRef.current = false;
    };
  }, [initializeHistoryLoading]);

  return {
    // 数据
    historyItems,

    // 状态
    isLoading,
    error,
    isHistoryLoaded,

    // 操作方法
    addPrompt,
    usePrompt,
    renamePrompt,
    deletePrompt,
    clearHistory,
    refreshHistory,

    // 工具方法
    getPromptById,
  };
}
