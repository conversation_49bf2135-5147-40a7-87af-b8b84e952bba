import { useState, useEffect, useCallback, createContext, useContext } from 'react';

/**
 * Responsive Layout Management Hook
 * Handles responsive design breakpoints, layout states, and adaptive behavior
 */

interface Breakpoints {
  mobile: number;
  tablet: number;
  desktop: number;
  largeDesktop: number;
}

interface LayoutDimensions {
  width: number;
  height: number;
  aspectRatio: number;
  orientation: 'portrait' | 'landscape';
}

interface ResponsiveState {
  breakpoint: keyof Breakpoints;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
  dimensions: LayoutDimensions;
  columns: number;
  gridGap: number;
  containerMaxWidth: number;
  sidebarWidth: number;
  isCompact: boolean;
}

interface UseResponsiveLayoutOptions {
  breakpoints?: Partial<Breakpoints>;
  debounceMs?: number;
  enableTouch?: boolean;
  customColumns?: { [key in keyof Breakpoints]?: number };
}

const DEFAULT_BREAKPOINTS: Breakpoints = {
  mobile: 768,
  tablet: 1024,
  desktop: 1440,
  largeDesktop: 1920,
};

const DEFAULT_OPTIONS: UseResponsiveLayoutOptions = {
  breakpoints: DEFAULT_BREAKPOINTS,
  debounceMs: 100,
  enableTouch: true,
  customColumns: {
    mobile: 1,
    tablet: 2,
    desktop: 3,
    largeDesktop: 4,
  },
};

export const useResponsiveLayout = (
  options: UseResponsiveLayoutOptions = {}
): ResponsiveState => {
  const config = { ...DEFAULT_OPTIONS, ...options };
  const breakpoints = { ...DEFAULT_BREAKPOINTS, ...config.breakpoints };

  const [state, setState] = useState<ResponsiveState>(() => {
    if (typeof window === 'undefined') {
      return {
        breakpoint: 'desktop',
        isMobile: false,
        isTablet: false,
        isDesktop: true,
        isLargeDesktop: false,
        dimensions: {
          width: 1440,
          height: 900,
          aspectRatio: 1.6,
          orientation: 'landscape',
        },
        columns: 3,
        gridGap: 16,
        containerMaxWidth: 1200,
        sidebarWidth: 280,
        isCompact: false,
      };
    }

    return calculateResponsiveState(window.innerWidth, window.innerHeight, breakpoints, config);
  });

  const updateLayout = useCallback(() => {
    if (typeof window === 'undefined') return;

    const newState = calculateResponsiveState(
      window.innerWidth,
      window.innerHeight,
      breakpoints,
      config
    );

    setState(prevState => {
      // Only update if state actually changed
      if (JSON.stringify(prevState) !== JSON.stringify(newState)) {
        return newState;
      }
      return prevState;
    });
  }, [breakpoints, config]);

  useEffect(() => {
    if (typeof window === 'undefined') return;

    let timeoutId: NodeJS.Timeout;
    
    const debouncedUpdate = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(updateLayout, config.debounceMs || 100);
    };

    // Initial update
    updateLayout();

    // Listen for resize events
    window.addEventListener('resize', debouncedUpdate);
    
    // Listen for orientation changes on mobile
    if (config.enableTouch) {
      window.addEventListener('orientationchange', debouncedUpdate);
    }

    return () => {
      clearTimeout(timeoutId);
      window.removeEventListener('resize', debouncedUpdate);
      if (config.enableTouch) {
        window.removeEventListener('orientationchange', debouncedUpdate);
      }
    };
  }, [updateLayout, config.debounceMs, config.enableTouch]);

  return state;
};

// Helper function to calculate responsive state
function calculateResponsiveState(
  width: number,
  height: number,
  breakpoints: Breakpoints,
  config: UseResponsiveLayoutOptions
): ResponsiveState {
  const dimensions: LayoutDimensions = {
    width,
    height,
    aspectRatio: width / height,
    orientation: width > height ? 'landscape' : 'portrait',
  };

  let breakpoint: keyof Breakpoints = 'mobile';
  let columns = 1;
  let gridGap = 8;
  let containerMaxWidth = width - 32;
  let sidebarWidth = 240;

  if (width >= breakpoints.largeDesktop) {
    breakpoint = 'largeDesktop';
    columns = config.customColumns?.largeDesktop || 4;
    gridGap = 24;
    containerMaxWidth = 1400;
    sidebarWidth = 320;
  } else if (width >= breakpoints.desktop) {
    breakpoint = 'desktop';
    columns = config.customColumns?.desktop || 3;
    gridGap = 20;
    containerMaxWidth = 1200;
    sidebarWidth = 280;
  } else if (width >= breakpoints.tablet) {
    breakpoint = 'tablet';
    columns = config.customColumns?.tablet || 2;
    gridGap = 16;
    containerMaxWidth = 960;
    sidebarWidth = 260;
  } else {
    breakpoint = 'mobile';
    columns = config.customColumns?.mobile || 1;
    gridGap = 12;
    containerMaxWidth = width - 32;
    sidebarWidth = width - 40;
  }

  const isCompact = width < breakpoints.tablet || height < 600;

  return {
    breakpoint,
    isMobile: breakpoint === 'mobile',
    isTablet: breakpoint === 'tablet',
    isDesktop: breakpoint === 'desktop',
    isLargeDesktop: breakpoint === 'largeDesktop',
    dimensions,
    columns,
    gridGap,
    containerMaxWidth,
    sidebarWidth,
    isCompact,
  };
}

// React Context for responsive layout
export const ResponsiveLayoutContext = createContext<ResponsiveState | null>(null);

export const useResponsiveLayoutContext = (): ResponsiveState => {
  const context = useContext(ResponsiveLayoutContext);
  if (!context) {
    throw new Error('useResponsiveLayoutContext must be used within ResponsiveLayoutProvider');
  }
  return context;
};

// Provider component
interface ResponsiveLayoutProviderProps {
  children: React.ReactNode;
  options?: UseResponsiveLayoutOptions;
}

export const ResponsiveLayoutProvider: React.FC<ResponsiveLayoutProviderProps> = ({
  children,
  options,
}) => {
  const layoutState = useResponsiveLayout(options);

  return (
    <ResponsiveLayoutContext.Provider value={layoutState}>
      {children}
    </ResponsiveLayoutContext.Provider>
  );
};

// Utility hooks for specific breakpoints
export const useIsMobile = (): boolean => {
  const { isMobile } = useResponsiveLayout();
  return isMobile;
};

export const useIsTablet = (): boolean => {
  const { isTablet } = useResponsiveLayout();
  return isTablet;
};

export const useIsDesktop = (): boolean => {
  const { isDesktop } = useResponsiveLayout();
  return isDesktop;
};

export const useColumns = (): number => {
  const { columns } = useResponsiveLayout();
  return columns;
};

export const useDimensions = (): LayoutDimensions => {
  const { dimensions } = useResponsiveLayout();
  return dimensions;
};

// CSS-in-JS helper for responsive styles
export const createResponsiveStyles = (
  styles: { [key in keyof Breakpoints]?: React.CSSProperties }
) => {
  const { breakpoint } = useResponsiveLayout();
  return styles[breakpoint] || {};
};

// Higher-order component for responsive behavior
interface WithResponsiveProps {
  children: (layoutState: ResponsiveState) => React.ReactNode;
  fallback?: React.ReactNode;
}

export const WithResponsive: React.FC<WithResponsiveProps> = ({
  children,
  fallback = null,
}) => {
  const layoutState = useResponsiveLayout();

  if (typeof window === 'undefined') {
    return <>{fallback}</>;
  }

  return <>{children(layoutState)}</>;
};

export default useResponsiveLayout;