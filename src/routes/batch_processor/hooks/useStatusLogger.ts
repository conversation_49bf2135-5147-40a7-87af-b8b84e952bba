import { useState, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { LogEntry, LogLevel } from '../components/StatusLogger';

// -----------------------------------------------------------------------------
// useStatusLogger
// -----------------------------------------------------------------------------
// 该 Hook 提供一个易用的日志管理系统，允许组件：
//  - 添加不同级别的日志（info/success/warning/error）
//  - 清除日志
//  - 限制日志数量
// -----------------------------------------------------------------------------

export function useStatusLogger(maxLogs = 100) {
  const [logs, setLogs] = useState<LogEntry[]>([]);

  /**
   * 添加日志
   * @param message 日志消息
   * @param level 日志级别
   * @returns 日志ID
   */
  const addLog = useCallback(
    (message: string, level: LogLevel = 'info'): string => {
      const id = uuidv4();
      const newLog: LogEntry = {
        id,
        timestamp: Date.now(),
        message,
        level,
      };

      // 限制日志数量，删除最早的记录
      setLogs(prevLogs => {
        if (prevLogs.length >= maxLogs) {
          return [...prevLogs.slice(1), newLog];
        } else {
          return [...prevLogs, newLog];
        }
      });

      return id;
    },
    [maxLogs],
  );

  /**
   * 快捷添加信息日志
   * @param message 日志消息
   */
  const info = useCallback(
    (message: string) => addLog(message, 'info'),
    [addLog],
  );

  /**
   * 快捷添加成功日志
   * @param message 日志消息
   */
  const success = useCallback(
    (message: string) => addLog(message, 'success'),
    [addLog],
  );

  /**
   * 快捷添加警告日志
   * @param message 日志消息
   */
  const warning = useCallback(
    (message: string) => addLog(message, 'warning'),
    [addLog],
  );

  /**
   * 快捷添加错误日志
   * @param message 日志消息
   */
  const error = useCallback(
    (message: string) => addLog(message, 'error'),
    [addLog],
  );

  /**
   * 清除所有日志
   */
  const clearLogs = useCallback(() => {
    setLogs([]);
  }, []);

  /**
   * 删除指定ID的日志
   * @param id 日志ID
   */
  const removeLog = useCallback((id: string) => {
    setLogs(prevLogs => prevLogs.filter(log => log.id !== id));
  }, []);

  return {
    logs,
    addLog,
    info,
    success,
    warning,
    error,
    clearLogs,
    removeLog,
  };
}

export default useStatusLogger;
