import { useState, useCallback, useRef, useEffect } from 'react';

/**
 * Interactive Feedback Management Hook
 * Handles UI interaction feedback including haptic feedback, visual effects, and sound feedback
 */

interface RippleItem {
  id: string;
  x: number;
  y: number;
  size: number;
  timestamp: number;
}

interface HapticOptions {
  type: 'light' | 'medium' | 'heavy' | 'selection';
  enabled: boolean;
}

interface AudioOptions {
  enabled: boolean;
  volume: number;
  clickSound?: string;
  errorSound?: string;
  successSound?: string;
}

interface VisualFeedbackOptions {
  rippleEnabled: boolean;
  highlightEnabled: boolean;
  rippleColor: string;
  highlightColor: string;
  animationDuration: number;
}

interface InteractionFeedbackState {
  ripples: RippleItem[];
  isHighlighted: boolean;
  lastInteraction: number;
}

interface UseInteractionFeedbackOptions {
  haptic?: HapticOptions;
  audio?: AudioOptions;
  visual?: VisualFeedbackOptions;
  throttleMs?: number;
}

interface UseInteractionFeedbackReturn {
  feedbackState: InteractionFeedbackState;
  triggerFeedback: (event: React.MouseEvent | React.TouchEvent, type?: 'click' | 'error' | 'success') => void;
  clearFeedback: () => void;
  updateOptions: (options: Partial<UseInteractionFeedbackOptions>) => void;
  Ripple: React.FC<RippleProps>;
}

const DEFAULT_OPTIONS: UseInteractionFeedbackOptions = {
  haptic: {
    type: 'light',
    enabled: true,
  },
  audio: {
    enabled: false,
    volume: 0.5,
  },
  visual: {
    rippleEnabled: true,
    highlightEnabled: true,
    rippleColor: 'rgba(255, 255, 255, 0.6)',
    highlightColor: 'rgba(0, 123, 255, 0.1)',
    animationDuration: 600,
  },
  throttleMs: 100,
};

export const useInteractionFeedback = (
  initialOptions: UseInteractionFeedbackOptions = {}
): UseInteractionFeedbackReturn => {
  const [options, setOptions] = useState<UseInteractionFeedbackOptions>({
    ...DEFAULT_OPTIONS,
    ...initialOptions,
  });

  const [feedbackState, setFeedbackState] = useState<InteractionFeedbackState>({
    ripples: [],
    isHighlighted: false,
    lastInteraction: 0,
  });

  const audioContextRef = useRef<AudioContext | null>(null);
  const soundBuffersRef = useRef<Map<string, AudioBuffer>>(new Map());
  const lastTriggerTimeRef = useRef<number>(0);

  // Initialize audio context
  useEffect(() => {
    if (options.audio?.enabled && typeof window !== 'undefined') {
      try {
        audioContextRef.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      } catch (error) {
        console.warn('Audio context initialization failed:', error);
      }
    }

    return () => {
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [options.audio?.enabled]);

  // Haptic feedback handler
  const triggerHaptic = useCallback((type: string) => {
    if (!options.haptic?.enabled || typeof navigator === 'undefined') return;

    if ('vibrate' in navigator) {
      switch (type) {
        case 'light':
          navigator.vibrate(10);
          break;
        case 'medium':
          navigator.vibrate(20);
          break;
        case 'heavy':
          navigator.vibrate(40);
          break;
        case 'selection':
          navigator.vibrate([5, 5]);
          break;
        default:
          navigator.vibrate(10);
      }
    }
  }, [options.haptic]);

  // Audio feedback handler
  const triggerAudio = useCallback(async (soundType: 'click' | 'error' | 'success') => {
    if (!options.audio?.enabled || !audioContextRef.current) return;

    try {
      const audioContext = audioContextRef.current;
      
      // Create a simple beep sound if no sound file is provided
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      // Set frequency based on sound type
      switch (soundType) {
        case 'click':
          oscillator.frequency.value = 800;
          break;
        case 'error':
          oscillator.frequency.value = 400;
          break;
        case 'success':
          oscillator.frequency.value = 1200;
          break;
      }

      gainNode.gain.setValueAtTime(options.audio?.volume || 0.5, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
      console.warn('Audio feedback failed:', error);
    }
  }, [options.audio]);

  // Visual feedback handler
  const triggerVisualFeedback = useCallback((event: React.MouseEvent | React.TouchEvent) => {
    if (!options.visual?.rippleEnabled) return;

    const target = event.currentTarget as HTMLElement;
    const rect = target.getBoundingClientRect();
    
    let clientX: number, clientY: number;
    
    if ('touches' in event && event.touches.length > 0) {
      clientX = event.touches[0].clientX;
      clientY = event.touches[0].clientY;
    } else if ('clientX' in event) {
      clientX = event.clientX;
      clientY = event.clientY;
    } else {
      // Fallback to center of element
      clientX = rect.left + rect.width / 2;
      clientY = rect.top + rect.height / 2;
    }

    const x = clientX - rect.left;
    const y = clientY - rect.top;
    const size = Math.max(rect.width, rect.height) * 2;

    const newRipple: RippleItem = {
      id: `ripple-${Date.now()}-${Math.random()}`,
      x: x - size / 2,
      y: y - size / 2,
      size,
      timestamp: Date.now(),
    };

    setFeedbackState(prev => ({
      ...prev,
      ripples: [...prev.ripples, newRipple],
      isHighlighted: options.visual?.highlightEnabled || false,
      lastInteraction: Date.now(),
    }));

    // Remove ripple after animation
    setTimeout(() => {
      setFeedbackState(prev => ({
        ...prev,
        ripples: prev.ripples.filter(r => r.id !== newRipple.id),
        isHighlighted: false,
      }));
    }, options.visual?.animationDuration || 600);
  }, [options.visual]);

  // Main trigger function
  const triggerFeedback = useCallback((
    event: React.MouseEvent | React.TouchEvent,
    type: 'click' | 'error' | 'success' = 'click'
  ) => {
    const now = Date.now();
    
    // Throttle rapid triggers
    if (now - lastTriggerTimeRef.current < (options.throttleMs || 100)) {
      return;
    }
    
    lastTriggerTimeRef.current = now;

    // Trigger all feedback types
    triggerHaptic(options.haptic?.type || 'light');
    triggerAudio(type);
    triggerVisualFeedback(event);
  }, [options, triggerHaptic, triggerAudio, triggerVisualFeedback]);

  // Clear all feedback
  const clearFeedback = useCallback(() => {
    setFeedbackState({
      ripples: [],
      isHighlighted: false,
      lastInteraction: 0,
    });
  }, []);

  // Update options
  const updateOptions = useCallback((newOptions: Partial<UseInteractionFeedbackOptions>) => {
    setOptions(prev => ({
      ...prev,
      ...newOptions,
    }));
  }, []);

  return {
    feedbackState,
    triggerFeedback,
    clearFeedback,
    updateOptions,
    Ripple,
  };
};

// Ripple component props
interface RippleProps {
  ripples: RippleItem[];
}

export const Ripple: React.FC<RippleProps> = ({ ripples }) => {
  return (
    <>
      {ripples.map(ripple => (
        <span
          key={ripple.id}
          className="ripple"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
          }}
        />
      ))}
    </>
  );
};

// Higher-order component for automatic feedback
interface WithInteractionFeedbackProps {
  feedbackOptions?: UseInteractionFeedbackOptions;
  feedbackType?: 'click' | 'error' | 'success';
  children: React.ReactNode;
  className?: string;
  onClick?: (event: React.MouseEvent) => void;
}

export const WithInteractionFeedback: React.FC<WithInteractionFeedbackProps> = ({
  feedbackOptions,
  feedbackType = 'click',
  children,
  className = '',
  onClick,
  ...props
}) => {
  const { feedbackState, triggerFeedback, Ripple } = useInteractionFeedback(feedbackOptions);

  const handleClick = useCallback((event: React.MouseEvent) => {
    triggerFeedback(event, feedbackType);
    onClick?.(event);
  }, [triggerFeedback, feedbackType, onClick]);

  return (
    <div
      className={`interaction-feedback-container ${className}`}
      onClick={handleClick}
      style={{ position: 'relative', overflow: 'hidden' }}
      {...props}
    >
      {children}
      <Ripple ripples={feedbackState.ripples} />
    </div>
  );
};

export default useInteractionFeedback;