# Lynx Prompts Architecture

 Overview

TypeScript-based modular prompt system for Lynx code generation.

 Structure

```
prompts/
├── core/                    # Core framework
├── components/              # UI components  
├── styles/                  # TTSS styles
├── events/                  # Event handling
├── api/                     # Lynx APIs
├── advanced/                # Advanced features
├── examples/                # Best practices
├── utils/                   # Utilities
├── ModularPromptLoader.ts   # Main loader
└── README.md               # This file
```

 Usage

```typescript
import { getMasterLevelLynxPromptContent } from './ModularPromptLoader';

const prompt = getMasterLevelLynxPromptContent();
```

 Rules

- Only functional content allowed
- No version info, benchmarks, or meta-commentary
- Compact structure, minimal formatting
- Focus on Lynx syntax and UI generation