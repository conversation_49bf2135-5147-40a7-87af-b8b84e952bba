# LightChart 使用核心指南

`@byted/lynx-lightcharts` 是专为 Lynx 环境设计的高性能图表库，它体积小、性能高，并支持丰富的图表类型。

## 🚨 关键规则 (必须遵守，否则图表无法渲染)

### 规则1: 数据模型 - `option.data` + `series.encode` 是强制要求

**LightChart 方式**: 必须在 `option.data` 中提供扁平数组数据对象，然后使用 `series.encode` 将字段映射到视觉属性。**禁止使用** `series.data` 模式，这会导致空白图表。

```javascript
// ❌ 错误 - ECharts 风格 (会导致空白图表)
const option = {
  xAxis: { data: ['游戏化', '故事化', '可视化'] },
  series: [{
    type: 'bar',
    data: [85, 78, 82] // 禁止使用！
  }]
};

// ✅ 正确 - LightChart 风格
const option = {
  data: [
    { method: '游戏化', effectiveness: 85 },
    { method: '故事化', effectiveness: 78 },
    { method: '可视化', effectiveness: 82 }
  ],
  xAxis: { type: 'category' },
  yAxis: { type: 'value' },
  series: [{
    type: 'bar',
    encode: { x: 'method', y: 'effectiveness' } // 必须使用 encode 映射
  }]
};
```

### 规则3: 生命周期管理 - 与 Lynx 组件事件绑定

**LightChart 方式**: 图表的生命周期由 `<lightcharts-canvas>` 组件管理。必须在 `bindinitchart` 中创建图表，在 `onUnload` 中销毁图表以防止内存泄漏。

```javascript
// ❌ 错误 - 在错误的生命周期创建图表
Card({
  onLoad() {
    // 错误！此时 canvas 还未准备好
    this.chart = new LynxChart({ canvasName: 'test', width: 400, height: 300 });
  }
});

// ✅ 正确 - 在正确的生命周期管理图表
Card({
  chart: null,

  // 必须在 bindinitchart 回调中创建
  initChart(e) {
    const { canvasName, width, height } = e.detail;
    this.chart = new LynxChart({ canvasName, width, height });
    this.chart.setOption(option);
  },

  // 必须在 onUnload 中销毁
  onUnload() {
    if (this.chart) {
      this.chart.destroy();
      this.chart = null;
    }
  }
});
```

**常见错误诊断**:

- **空白图表**: 99% 是违反了规则1 (使用了 `series.data` 而不是 `option.data` + `series.encode`)
- **图表消失**: 违反了规则3 (忘记在 `onUnload` 中调用 `chart.destroy()`)

---

 一、三步快速集成

**1. 注册组件 (`index.json`)**

```json
{
  "usingComponents": {
    "lightcharts-canvas": "@byted/lynx-lightcharts/lightcharts-canvas/lightcharts-canvas"
  }
}
```

**2. 放置画布 (`index.ttml`)**

```html
<lightcharts-canvas 
  bindinitchart="initChart"
  style="width: 100%; height: 300px;"
  canvasName="myUniqueChart"
  useKrypton="{{SystemInfo.enableKrypton}}"
/>
```

- `bindinitchart`: **必需**，初始化回调函数。
- `canvasName`: **必需**，画布唯一标识。
- `useKrypton`: 推荐开启以获得更优性能。
- `style`: **必需**，必须指定明确的宽高。

**3. 初始化图表 (`index.js`)**

```javascript
import LynxChart from '@byted/lynx-lightcharts/src/chart';
import { setImageLoader } from '@byted/lynx-lightcharts/lightcharts';

if (SystemInfo.enableKrypton) {
  const lynxImageLoader = (src, callback) => {
    const image = lynx.krypton.createImage(src);
    image.onload = () => callback(image);
  };
  setImageLoader(lynxImageLoader);
}

Card({
  chart: null, // 图表实例存储

  // ✅ 正确：在 bindinitchart 回调中创建图表
  initChart(event) {
    const { canvasName, width, height } = event.detail;
    this.chart = new LynxChart({ canvasName, width, height });

    // ✅ 正确：使用 option.data + series.encode 模式
    const option = {
      data: [
        { year: '1991', value: 3 },
        { year: '1992', value: 4 },
        { year: '1993', value: 3.5 },
        { year: '1994', value: 5 },
        { year: '1995', value: 4.9 }
      ],
      tooltip: {
        trigger: 'axis',
        borderWidth: 1,
        borderColor: '#aaa'
      },
      xAxis: { type: 'category' },
      yAxis: { type: 'value' },
      series: [
        {
          type: 'line',
          name: "数值",
          smooth: true,
          encode: {
            x: 'year',  // 映射到 data 中的 year 字段
            y: 'value'  // 映射到 data 中的 value 字段
          }
        }
      ]
    };

    // 延迟设置选项，确保 canvas 准备就绪
    setTimeout(() => {
      this.chart.setOption(option);
    }, 100);
  },

  // ✅ 正确：在 onUnload 中销毁图表
  onUnload() {
    if (this.chart) {
      this.chart.destroy();
      this.chart = null;
    }
  }
});
```

---

# 深度参考与高级主题

为了方便您进行更复杂的开发和问题排查，以下是来自原始文档的完整参考信息。

 一、支持的图表类型 (25+种)

**基础图表**: bar, line, area, pie, scatter, funnel, gauge
**高级图表**: heatmap, treemap, wordCloud, sankey, sunburst, tree, graph
**专业图表**: map, liquid, waterfall, candlestick, boxplot, radar, venn

 二、完整API与内部机制参考

 1. LynxChart构造配置与内部规则

```typescript
interface LynxChartConfig {
  canvasName: string;
  width: number;
  height: number;
}

bindinitchart(e) {
  const { canvasName, useKrypton, width, height } = e.detail;
  this.chart = new LynxChart({ canvasName, width, height });
}
```

 2. 核心实例方法

```javascript
chart.setOption(option, notMerge?, lazyUpdate?)
const currentOption = chart.getOption();

chart.resize(newWidth?, newHeight?)
const width = chart.getWidth();
const height = chart.getHeight();

chart.on(eventName, handler)
chart.off(eventName, handler?)
chart.dispatchAction(payload)

chart.clear()
chart.destroy()
```

 3. 全局实例管理API

```javascript
import { getChartByCanvasName, destroyChartByCanvasName } from '@byted/lynx-lightcharts/src/chart';

const chart = getChartByCanvasName('myChart');
destroyChartByCanvasName('myChart');
```

 三、性能优化、事件与资源管理

 1. 自动性能优化
高分屏适配**: 自动使用 `SystemInfo.pixelRatio`
脏矩形优化**: 移动端关闭 `enableDirtyRect`
Canvas引擎**: 使用 `lynx.krypton.createCanvas`
离屏Canvas**: 支持 `lynx.createOffscreenCanvas`

 2. 自动触摸事件处理
```javascript
["touchstart", "touchmove", "touchend"].forEach(type => {
   canvas.addEventListener(type, this._handleEvent);
});

private _getTouchEvent(e: TouchEvent) {
   return {
     ...e,
     touches: this.getTouchesFromTouchList(e.touches),
     changedTouches: this.getTouchesFromTouchList(e.changedTouches),
   };
}
```

 3. 自动资源管理与内存泄漏防护
组件detached时**: 自动调用 `destroyChartByCanvasName`
图表destroy时**: 自动清理事件、上下文、全局引用
页面onUnload时**: **必须**手动调用 `this.chart.destroy()`

 四、数据更新与交互模式

**实时数据更新**:
```javascript
// ✅ 正确：更新整个 option.data，而不是 series.data
updateChart(newData) {
  if (this.chart) {
    const newOption = {
      data: newData, // 使用 option.data
      xAxis: { type: 'category' },
      yAxis: { type: 'value' },
      series: [{
        type: 'line',
        encode: { x: 'year', y: 'value' } // 保持 encode 映射
      }]
    };
    this.chart.setOption(newOption, true); // notMerge=true 完全替换
  }
}
```

**事件交互处理**:
```javascript
chart.on('click', function(params) {
  console.log(params.name, params.value, params.seriesIndex);
});

chart.dispatchAction({ type: 'showTip', seriesIndex: 0, dataIndex: 2 });
chart.dispatchAction({ type: 'highlight', seriesIndex: 0, dataIndex: 1 });
```

 五、LightChart独有API与关键约束 (重要)

 1. 关键实现约束
JS实现**: 导入路径固定, 构造参数必需, `onUnload` 必须调用 `destroy()`。
禁止混用**: 严禁使用其他库的初始化、DOM查询、Web事件、Canvas API。

 2. API差异对比 (正误示例)

**构造函数差异**:
```javascript
// 错误
const chart = echarts.init(document.getElementById('chart'));
// 正确
const chart = new LynxChart({ canvasName: 'myChart', width: 400, height: 300 });
```

**事件处理差异**:
```javascript
// 错误
chart.addEventListener('click', handler);
// 正确
chart.on('click', handler);
```

**实例管理差异**:
```javascript
// 错误
chart.getInstanceById('myChart');
// 正确
import { getChartByCanvasName } from '@byted/lynx-lightcharts/src/chart';
const chart = getChartByCanvasName('myChart');
```

 六、常见问题与解决方案

**问题1: 坐标系显示但数据不显示**

```javascript
// ❌ 错误：使用 series.data (会导致空白图表)
series: [{ type: 'line', data: [['0°', 0], ['10°', 0.174]] }]

// ✅ 正确：使用 option.data + series.encode
const option = {
  data: [
    { angle: '0°', value: 0 },
    { angle: '10°', value: 0.174 },
    { angle: '20°', value: 0.364 }
  ],
  xAxis: { type: 'category' },
  yAxis: { type: 'value' },
  series: [{
    type: 'line',
    encode: { x: 'angle', y: 'value' }
  }]
};
```

**问题2: 数据超出渲染范围导致图表中断**
```javascript
let tanValue = Math.tan(radian);
if (!isFinite(tanValue) || Math.abs(tanValue) > 10) {
  tanValue = null;
}
tanData.push(tanValue);
```

**问题3: 数据密度过高导致性能问题**
```javascript
// 优化建议: 增加采样间隔
for (let i = 0; i <= 720; i += 5) {
  // ...
}

 七、核心渲染规则 (AI必读)

为了确保图表在Lynx环境中稳定渲染，请严格遵守以下通用方法：

 1. 数据格式必须使用 option.data + series.encode

**规则**: 必须使用 `option.data` 提供扁平数组数据，并通过 `series.encode` 映射字段到视觉属性。禁止使用 `series.data`。
**原因**: LightChart 是原生组件，不支持 ECharts 的 `series.data` 模式，会导致空白图表。
**示例**:
  ```javascript
  // ❌ 错误: 使用 series.data
  series: [{ data: [10, 20, 30] }]
  series: [{ data: [['A', 10], ['B', 20], ['C', 30]] }]

  // ✅ 正确: 使用 option.data + series.encode
  const option = {
    data: [
      { category: 'A', value: 10 },
      { category: 'B', value: 20 },
      { category: 'C', value: 30 }
    ],
    xAxis: { type: 'category' },
    yAxis: { type: 'value' },
    series: [{
      type: 'bar',
      encode: { x: 'category', y: 'value' }
    }]
  };
  ```

 2. 延迟初始化图表配置

规则**: 在 `bindinitchart` 回调中，使用 `setTimeout` 延迟至少 `100ms` 再调用 `chart.setOption()`。
原因**: `bindinitchart` 事件触发时，Canvas 可能尚未完全准备就绪，立即调用 `setOption` 可能导致渲染失败。
**示例**:

  ```javascript
  initChart(event) {
    const chart = new LynxChart(event.detail);
    setTimeout(() => {
      chart.setOption(option);
    }, 100);
    this.chart = chart;
  }
  ```

 3. 禁用 Tooltip 的 HTML 渲染

**规则**: 在 `tooltip` 配置中，务必设置 `useHTML: false`。
**原因**: Lynx 是一个非标准的浏览器环境，不支持复杂的 HTML 渲染。开启 `useHTML` 会导致提示框无法正常显示。
**示例**:

  ```javascript
  tooltip: {
    trigger: 'axis',
    useHTML: false, // 必须设置为 false
    formatter: '{b}: {c}'
  }
  ```