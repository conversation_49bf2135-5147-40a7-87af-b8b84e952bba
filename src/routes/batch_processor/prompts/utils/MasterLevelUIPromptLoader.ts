/**
 * Lynx Prompt Loader - Modular Version
 *
 * Automatically composes all prompt modules for Lynx code generation
 */

// 导入模块化提示加载器
import { ModularPromptLoader } from '../ModularPromptLoader';
// 导入LightChart图表库规则
import { LIGHTCHART_PROMPT_CONTENT } from './LightChartPromptLoader';

/**
 * Get Lynx prompt content using modular composition
 *
 * @returns {string} Complete prompt content
 */
export function getMasterLevelLynxPromptContent(): string {
  try {
    const loader = ModularPromptLoader.getInstance();
    const modularContent = loader.buildFullPrompt();

    const lightChartIntegration = `LightChart Library Module

${LIGHTCHART_PROMPT_CONTENT}


`;

    return modularContent + lightChartIntegration;
  } catch (error) {
    console.error('[LynxPromptLoader] Loading failed:', error);
    return getFallbackContent();
  }
}

/**
 * Fallback content for error handling
 */
function getFallbackContent(): string {
  return `
Lynx Framework Basic Development

# Basic Components
- view, text, image - 基础容器和显示组件
- scroll-view, swiper - 基础布局组件

# Style System
- TTSS样式系统，支持响应式设计
- 使用rpx单位进行设备适配
- 支持CSS3动画和过渡效果

# Event System
- 触摸事件：bindtap, bindlongtap, bindtouchmove
- 表单事件：bindinput, bindchange, bindsubmit
- 生命周期：onLoad, onShow, onHide, onUnload

# Data Binding
- 模板语法：{{expression}}
- 列表渲染：tt:for="{{array}}"
- 条件渲染：tt:if="{{condition}}"
- 事件绑定：bindevent="handler"

# Development Guidelines
- 遵循Lynx Framework最佳实践
- 使用TypeScript进行类型安全开发
- 优化性能，减少内存占用
- 符合小程序开发规范
`;
}

/**
 * Get prompt loading information
 */
export function getPromptLoadingInfo(): {
  totalModules: number;
  loadedModules: string[];
  hasLightChart: boolean;
} {
  return {
    totalModules: 2,
    loadedModules: ['ModularPromptLoader', 'LightChartPromptLoader'],
    hasLightChart: true,
  };
}

/**
 * Validate content integrity
 */
export function validateContentIntegrity(content: string): boolean {
  const requiredKeywords = [
    'Lynx Framework',
    'LightChart',
    'Components',
    'TTSS',
    'API',
  ];

  return requiredKeywords.every(keyword => content.includes(keyword));
}

// 导出兼容性接口
export default {
  getMasterLevelLynxPromptContent,
  getPromptLoadingInfo,
  validateContentIntegrity,
};
