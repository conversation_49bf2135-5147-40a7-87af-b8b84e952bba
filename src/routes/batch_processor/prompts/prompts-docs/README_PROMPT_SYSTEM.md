# Lynx Framework Prompt System Architecture

 系统概述

本目录包含完整的Lynx框架提示词系统，采用模块化架构设计，确保所有Lynx开发所需的技术规范、API文档、最佳实践等内容的完整覆盖。

**设计原则**: 
- 模块化组织，职责清晰
- 内容完整，覆盖全面
- 易于维护，便于扩展
- 技术准确，实用性强

---

 完整模块架构

 **核心框架模块 (`/core/`)**

# **LynxFrameworkCore.ts- 框架核心原则
**职责**: 提供Lynx框架的基础架构认知和核心约束
**包含内容**:
严格输出约束规则(FILES标签格式、禁止解释性语言)
框架本质定义(跨平台移动应用开发框架特点)
设计原则(移动优先、性能至上、组件化、响应式)
致命错误防范规则(可选链?.强制要求、CSS属性限制、滚动约束、标签限制)
架构设计原则(分层架构、组件设计、响应式设计)
设计系统(色彩、字体、间距系统)
性能优化策略(渲染优化、代码优化、用户体验优化)
移动端友好设计要求(竖屏优化、触摸友好、单手操作、响应式适配)

# **ThreadSynchronization.ts- 双线程数据同步机制
**职责**: 详细说明抖音小程序双线程架构的数据同步机制
**包含内容**:
双线程模型基本原理(逻辑层、渲染层分离架构)
数据同步核心机制(setData异步机制、数据传递最佳实践)
数据初始化规范(正确/错误初始化示例、结构要求)
同步时序保证(setData回调机制、定时器确保同步)
数据同步性能优化(避免频繁setData、数据差异检测、大数据量处理策略)
常见同步问题和解决方案(数据更新失败、连续快速更新、大数据量卡顿)
高级同步技巧(自定义同步状态管理、跨线程事件通信)

# **TechnicalConstraints.ts- 关键技术约束规则
**职责**: 所有必须严格遵守的技术限制和约束规则集合
**包含内容**:
严格禁止规则(Canvas对象管理约束、API调用约束、全局变量访问规则)
强制性代码规范(空值检查强制要求、函数绑定约束、数据初始化强制要求)
- 🎯 **性能约束规则(setData使用约束、大数据传递约束、内存管理约束)
- 🔧 **兼容性约束规则(API可用性检查、版本兼容性处理)
安全约束规则(用户输入验证、数据传输安全)
移动端特定约束(触摸事件处理、屏幕适配、性能监控)
UI渲染约束规则(布局约束、动画约束)
调试约束规则(日志约束、错误处理约束)

# **VisualizationGuidance.ts- 知识可视化专门指导
**职责**: 专业的知识可视化设计指导和创意设计规范
**包含内容**:
知识可视化工作流程(需求分析、内容提炼、创意设计)
需求深度分析方法论(问题类型识别、知识点提取策略)
内容智能提炼技术(信息分类处理、逻辑关系梳理、内容优化原则)
可视化创意设计系统(图解类型选择、概念图、流程图、对比图、数据图、时间轴)
移动端UI设计优化(屏幕适配策略、触摸友好设计、单手操作优化)
色彩设计系统(主色彩、辅助色彩、中性色彩、搭配原则)
字体设计规范(字体层级、字体选择)
交互设计规范(状态反馈、动画设计)
创意设计思维框架(设计思维流程、创新设计方法、视觉创意技巧)

---

 **组件系统模块 (`/components/`)**

# **BasicComponents.ts- 基础组件系统
**职责**: 定义Lynx框架所有允许使用的基础标签和组件
**包含内容**:
基础容器组件(view, scroll-view, swiper, swiper-item, cover-view)
文本组件(text, rich-text)
表单组件(button, input, textarea, picker, switch, slider, radio-group, checkbox-group, form, label)
媒体组件(image, video, audio, camera, live-player, live-pusher)
地图组件(map)
画布组件(canvas)
开放能力组件(web-view, ad, official-account)
导航组件(navigator)
进度指示(progress)
图标组件(icon)
组件使用规范(容器标签、自闭合标签、降级映射)
Lynx函数调用限制(核心限制、事件绑定、事件传播)

# **AdvancedComponents.ts- 高级组件系统
**职责**: 定义高级UI组件和特殊功能组件
**包含内容**:
高级UI组件(复杂交互组件、自定义组件)
特殊功能组件(专用业务组件)
组件组合模式(组件嵌套规则、组合最佳实践)
样式绑定规范(组件样式管理、主题系统)

---

 **样式系统模块 (`/styles/`)**

# **TTSSStyleSystem.ts- TTSS样式系统
**职责**: 定义Lynx框架的样式系统规范和限制
**包含内容**:
CSS属性限制清单(布局属性、尺寸属性、边距边框、文字属性、背景属性、Flexbox布局、定位属性、动画属性)
RPX单位系统(响应式像素单位、基准规范、自动适配、使用规范)
TTSS规范要求(属性限制、单位规范、命名规范、嵌套规范、变量支持)

---

 **事件系统模块 (`/events/`)**

# **EventSystem.ts- 事件系统映射
**职责**: 提供完整的Lynx事件系统映射和处理规范
**包含内容**:
冒泡事件映射(bind* 系列事件的完整映射表)
捕获事件映射(capture-bind* 系列事件)
阻止冒泡事件映射(catch:* 系列事件)
表单事件映射(input, focus, blur, confirm, submit, reset)
滚动事件映射(scroll, scrolltoupper, scrolltolower)
媒体事件映射(load, error, play, pause, ended)
轮播事件映射(change, transition, animationfinish)
开关选择器事件(switch状态改变)
事件对象参数结构(event.detail, event.currentTarget, event.target, event.type, event.touches)
事件传播规则(捕获阶段、目标阶段、冒泡阶段、阻止传播)
事件使用最佳实践(事件绑定、阻止冒泡、性能优化、内存管理)

---

 **API系统模块 (`/api/`)**

# **LynxAPISystem.ts- Lynx API系统
**职责**: 提供完整的Lynx小程序API和生命周期管理
**包含内容**:
JavaScript属性访问强制约束(可选链操作符?.的强制使用要求)
页面生命周期(onLoad, onShow, onReady, onHide, onUnload详细说明和使用示例)
组件生命周期(created, attached, ready, moved, detached, error生命周期函数)
数据管理API(数据绑定、更新、异步处理、数组操作详细示例)
导航API(页面跳转、返回、重定向、Tab切换的完整工具类)
网络请求API(HTTP请求封装、拦截器、错误处理完整示例)
存储API(本地存储管理、同步异步存储、数据缓存策略)
UI反馈API(消息提示、加载提示、模态对话框、操作菜单工具类)

# **VisibilityDetection.ts- 可见性检测API
**职责**: 提供完整的IntersectionObserver和曝光事件API规范
**包含内容**:
IntersectionObserver详细使用(创建观察器、观察元素、停止观察、销毁观察器)
回调参数详解(intersectionRatio, intersectionRect, boundingClientRect, rootBounds等)
实际应用示例(图片懒加载、无限滚动加载、埋点曝光统计)
曝光事件详细配置(bindappear, binddisappear事件处理)
appear-offset配置详解(像素偏移量、百分比偏移量、多方向偏移量)
appear-duration-ms配置详解(最小持续时间、防抖处理)
高级可见性检测技巧(视口计算工具、自定义曝光管理器)
性能优化建议(批量处理、节流处理、内存管理、错误处理)

---

 **高级功能模块 (`/advanced/`)**

# **CanvasSystem.ts- Canvas绘图系统
**职责**: 提供完整的Canvas 2D绘图API和高级绘图功能
**包含内容**:
Canvas基础API(绘图上下文、基本图形绘制、路径操作)
图像处理API(图像绘制、变换、像素操作)
文本绘制API(文本样式、测量、绘制)
高级绘图功能(渐变、阴影、图案、变换)
图表绘制(柱状图、饼图、折线图等图表绘制方法)
动画支持(帧动画、路径动画、缓动函数)
事件处理(触摸事件、手势识别)
性能优化(离屏Canvas、图层管理、内存优化)

---

 **最佳实践模块 (`/examples/`)**

# **BestPractices.ts- 最佳实践和示例
**职责**: 提供完整的开发最佳实践、常见模式和示例代码
**包含内容**:
开发最佳实践指南(代码规范、性能优化、错误处理)
常见开发模式(设计模式、架构模式、编程模式)
示例代码集合(典型功能实现、复杂场景处理)
调试技巧(调试工具、性能分析、错误诊断)
工具和资源(开发工具、第三方库、参考资源)

---

 **工具模块 (`/utils/`)**

# **LightChartPromptLoader.ts- LightChart图表库
**职责**: 提供LightChart图表库的集成规则和使用规范
**包含内容**:
LightChart库集成(25+图表类型、完整组件系统)
TTML集成规范(模板语法、数据绑定、事件处理)
样式配置(主题系统、响应式设计、动画效果)
移动端优化(触摸事件、性能优化、内存管理)

# **MasterLevelUIPromptLoader.ts- 主级UI提示加载器
**职责**: 提供与原版pePromptLoader的兼容接口和功能映射
**包含内容**:
兼容性接口(保持与原版API的兼容性)
模块化集成(将模块化内容组合为原版格式)
功能映射(新旧功能的对应关系)

# **ModularPromptLoader.ts- 模块化提示加载器
**职责**: 核心加载器，负责组织和加载所有模块
**包含内容**:
模块管理(模块注册、加载、缓存)
组合逻辑(模块内容组合、优先级处理)
接口提供(对外统一接口、配置选项)

---

 内容完整性对照表

| 原版pePromptLoader内容 | 对应新模块 | 完整性 | 备注 |
|----------------------|-----------|--------|------|
| **严格输出约束| LynxFrameworkCore.ts | 100% | 完整保留并规范化 |
| **双线程数据同步| ThreadSynchronization.ts | 100% | 专门模块深度展开 |
| **技术约束规则| TechnicalConstraints.ts | 100% | 系统化强化 |
| **知识可视化指导| VisualizationGuidance.ts | 100% | 专业化增强 |
| **框架核心原则| LynxFrameworkCore.ts | 100% | 结构化组织 |
| **组件标签系统| BasicComponents.ts + AdvancedComponents.ts | 100% | 分类细化 |
| **事件系统映射| EventSystem.ts | 100% | 完整映射表 |
| **TTSS样式系统| TTSSStyleSystem.ts | 100% | 规范化整理 |
| **API调用规范| LynxAPISystem.ts | 100% | 实用性增强 |
| **可见性检测API| VisibilityDetection.ts | 100% | 新增完整模块 |
| **Canvas绘图API| CanvasSystem.ts | 100% | 功能性扩展 |
| **最佳实践| BestPractices.ts | 100% | 经验性总结 |
| **LightChart规则| LightChartPromptLoader.ts | 100% | 专门模块 |
| **Canvas音频API| LynxCanvasAudio.ts | 100% | 音频可视化专门模块 |

---

 使用指南

 **1. 完整加载 (推荐)**
```typescript
import { ModularPromptLoader } from './ModularPromptLoader';

const loader = ModularPromptLoader.getInstance();
const fullPrompt = loader.getMasterLevelLynxPromptContent();
```

 **2. 按需加载**
```typescript
import { LYNX_FRAMEWORK_CORE } from './core/LynxFrameworkCore';
import { BASIC_COMPONENTS } from './components/BasicComponents';
import { EVENT_SYSTEM } from './events/EventSystem';
// 根据需要导入特定模块
```

 **3. 兼容性加载**
```typescript
import { getMasterLevelLynxPromptContent } from './utils/MasterLevelUIPromptLoader';

const compatiblePrompt = getMasterLevelLynxPromptContent();
```

---

 版本演进记录

 **v2.0 - 模块化重构 (当前版本)**
- 完全模块化架构
- 补充所有缺失内容
- 强化所有弱化功能
- 提升维护性和扩展性
- 保持100%向后兼容

 **v1.0 - 单一文件版本 (origin/master)**
- 基础功能完整
- 组织结构单一
- 维护难度较高
- 部分功能分散

---

 质量保证

 **技术质量**
- 所有模块TypeScript编译通过
- 代码规范统一，注释完整
- 模块职责清晰，耦合度低

 内容质量
- 100%覆盖原版所有功能
- 技术规范准确可靠
- 实用性和可操作性强

 架构质量
- 模块化设计清晰
- 扩展性和维护性优秀
- 向后兼容性保证

---

 总结

本模块化Prompt系统实现了：
1. **内容完整性**: 100%覆盖原版所有功能，补充所有缺失内容
2. **架构优化**: 从单一文件升级为15个专门模块，职责清晰
3. **质量提升**: 技术规范更加准确，实用性更强
4. **维护性**: 模块化结构便于维护、更新和扩展
5. **兼容性**: 保持与原版的完全兼容，平滑升级

这套系统为Lynx框架开发提供了最完整、最专业、最易用的技术知识库。