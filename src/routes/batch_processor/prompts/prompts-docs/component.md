


一个 TTML 自定义组件在使用前需要“注册“，这样才能在编译阶段确定组件之间的依赖关系，并在渲染时找到其组件对应的渲染函数。 组件注册有两种方式：全局注册和局部注册。

局部注册#
通常，当开发者需要使用一个组件时，需要在 json 配置文件中进行局部注册，如下所示：

./src/page/index.json
{
  "usingComponents": {
    "local-header": "../components/header",
    "local-footer": "../components/footer"
  }
}


在上述配置中，将 local-header 和 local-footer 两个组件被注册到当前页面，只能在当前的 TTML 文件中使用。

./src/page/index.ttml
<view>
  <local-header />
  <local-footer />
</view>


全局注册#
开发者可以在项目配置中通过 ttmlOptions.usingComponents 字段进行全局组件注册，如下所示：

lynx.config.js
module.exports = {
  ttmlOptions: {
    usingComponents: {
      "global-herader": "./src/components/header",
      "global-footer": "./src/components/footer",
    },
  },
};


编译阶段将 global-herader 和 global-footer 两个组件配置注入到所有组件的 json 配置文件中， 将局部注册的组件和全局注册的组件合并，然后再进行编译。 这样，在任意的 TTML 文件中就可以直接使用这两个全局组件。

TIP
对于配置中的组件路径，如果是相对路径，则应该是相对于项目根目录的。开发者也可以通过 nodejs 的 path 模块处理为绝对路径。

如何选择注册方式#
大部分场景下，建议使用局部注册，因为局部注册的组件只会影响到当前的 TTML 文件。
全局注册主要适用于使用了定制化组件的场景。 比如，在一个通用组件内部，通过 properties 中接受的 componentName 属性，动态决定要渲染的组件。
common-component.ttml
<component is={{componentName}} />


此时，在该组件内无法进行局部注册，只能通过全局注册的方式进行，使用方式如下：

index.ttml
<common-component componentName="global-header">


lynx.config.js
module.exports = {
  ttmlOptions: {
    usingComponents: {
      "global-herader": "./src/components/header",
    },
  },
};


局部注册的组件优先级是高于全局注册的组件，所以如果一个组件同时被局部注册和全局注册，则会优先使用局部注册的组件。

properties#
INFO
此章节假设你已经看过了 组件基础。若你还不了解组件是什么，请先阅读该章节。

声明组件 properties#
在使用 Component 定义组件时，可以通过 properties 属性来声明它能接收的属性。

通常由 type、value 和 observer 三部分组成
type 是必需的，value 和 observer 是可选的
支持的 type 有：String、Number、Boolean、Array 和 Object，用于数据提取和类型推导，运行时不做校验
observer 用于监听属性值的变化，在属性值变化时触发对应的回调函数
主要有以下几种写法：

Component({
  properties: {
    myProperty1: Number,
    myProperty2: {
      type: Number,
    },
    myProperty3: {
      type: Number,
      value: 1
    },
    myProperty4: {
      type: Number,
      observer(newVal, oldVal) {}
    }
  }
})


传递 properties#
静态属性值
<my-com numberProperty={{1}} />
<my-com stringProperty="test" />
<my-com booleanProperty />
<my-com booleanProperty={{true}} />
<my-com arrayProperty={{ [1, 2, 3] }} />
<my-com objectProperty={{ {a: 1, b: 2} }} />


WARNING
不使用 {{}} 包裹的属性值会被当作字符串处理。 如 <my-com myProperty1=1 /> 等价于 <my-com myProperty1="1" />，而不是 <my-com myProperty1={{1}} />。

动态属性值
<my-com myProperty={{dynamicValue}} />


未传递属性值：使用编译期生成的默认值

特殊属性值：null 可以正常传递，传递 undefined 等价于不传递该属性值

<my-com myProperty={{null}} />
<my-com myProperty={{undefined}} />


消费 properties#
在 ttml 文件中，可以直接使用顶层变量 {{myProperty}}
在后台线程 js 文件中，可以使用 this.properties.myProperty 访问
编译提取#
Lynx 的主要优势是在主线程执行模版逻辑，做到“首屏直出”。 组件的 properties 通常是在 JS 代码中定义的，运行在后台线程中。 为了首屏就能获取 properties 的初始值， 工具链会在编译阶段提取 properties 的初始值，并将提取结果注入主线程执行的 lepus.js 代码中。

针对不同的类型和写法，提取到的初始值都有所不同。

设置了静态 value，则提取 value 作为初始值
设置了动态 value，且为动态属性读取或者函数调用，则在提取编译阶段执行的结果作为初始值
未设置 value，但设置了 type，则使用 type 构造默认值作为初始值
若 type 为 Number，则提取 new Number()，即 0 作为初始值
若 type 为 String，则提取 new String()，即 '' 作为初始值
若 type 为 Boolean，则提取 new String()，即 false 作为初始值
若 type 为 Array，则提取 new Array()，即 [] 作为初始值
若 type 为 Object，则提取 new Object()，即 作为初始值
WARNING
为了保证首屏能够拿到符合预期的初始值，建议在 properties 中设置静态 value，动态 value 可能存在编译提取错误的风险。

事件#
INFO
此章节假设你已经看过了 组件基础。若你还不了解组件是什么，请先阅读该章节。

原生事件#
组件也是一个普通的渲染节点，拥有和 <view>、<text> 等节点相同的原生事件，详见 元件事件。

WARNING
若将组件节点的 removeComponentElement 属性设置为 true，则该组件节点不再能够响应事件，请谨慎使用。

自定义事件#
在 TTML DSL 中，properties 属性只能是字符串、数字、布尔值、对象、数组，不能是函数。 当子组件需要向父组件传递数据时，无法利用 properties 属性来传递回调函数。 针对这种场景，开发者可以使用自定义事件来实现子组件向父组件传递数据。

触发自定义事件#
在子组件的 JS 代码中，通过 this.triggerEvent 方法来触发自定义事件
第一个参数为事件名称，第二个参数为事件的参数。
Component({
  handleTap() {
    this.triggerEvent('myevent', {
      text: 'hello'
    });
  }
});


监听自定义事件#
在父组件的 ttml 代码中，通过 bind 或 catch 来监听子组件的自定义事件
在父组件的 JS 代码中，定义事件的回调函数
<comp bindmyevent="handleMyEvent" />


Component({
  handleMyEvent(e) {
    console.log(e);
  }
});


全局事件#
在组件内部也可以注册全局事件，详见 全局事件发射器。 利用全局事件，可以实现页面内多个组件之间的通信。

触发全局事件#
通过 toggle 方法来触发全局事件
组件A
Component({
  handleTap() {
    this.getJSModule('GlobalEventEmitter').toggle('fromComA', 1, {
      content: 'hello'
    });
  }
})


监听全局事件#
通过 addListener 方法来监听全局事件
组件B
Component({
  ready() {
    this.getJSModule('GlobalEventEmitter').addListener('fromComA', (res) => {
      console.log(res);
    });
  }
})

插槽#
INFO
此章节假设你已经看过了 组件基础。若你还不了解组件是什么，请先阅读该章节。

插槽内容与出口#
在之前的章节中，我们已经了解到组件能够接基础类型的 JavaScript 值作为 properties。 在某些场景中，我们可能想要为子组件传递一些模板片段，让子组件在它们的组件中渲染这些片段。

举例来说，这里有一个 <Test> 组件，可以像这样使用：

card/index.ttml
<Test>
  <text>Hello Lynx</text> <!-- 插槽内容 -->
</Test>


而 <Test> 的模板是这样的：

test/index.ttml
<view>
  <slot></slot> <!-- 插槽出口 -->
</view>


在这个例子中，<slot> 元素是一个插槽出口 (slot outlet)，标示了父组件提供的插槽内容 (slot content) 将在哪里被渲染。

具体的例子是这样的：


理解插槽的另一种方式是和下面的 JavaScript 函数作类比，其概念是类似的：

// 父组件传入插槽内容
Test({
  slots: {
    default: function() {
      return <text>Hello Lynx</text>
    },
    header: function() {
      return <text>Hello Header</text>
    },
  }
})
// 子组件在自己的模板中渲染插槽内容
function Test({slots}) {
  return (
    <view>
      {slots.default()}
      {slots.header()}
    </view>
  )
}


通过使用插槽，自定义组件更加灵活和具有可复用性。

渲染作用域#
插槽内容本身是在父组件模板中定义的，能够访问到父组件中的数据。举例来说：

card/index.ttml
<text>{{content}}</text>
<Test>
  <text>{{content}}</text>
</Test>


这里的两个 {{ content }} 插值表达式渲染的内容都是一样的。

插槽内容的事件回调也是在父组件中定义，并且能够访问到父组件中的数据。具体代码如下：

card/index.ttml
<Test>
  <text bindtap="handleTap">{{content}}</text>
</Test>


card/index.js
Card({
  data: {
    content: 'Hello Lynx',
  },
  handleTap() {
    console.log(this.data.content)
  }
})


对于 TTML 中，CSS 样式默认是组件作用域的，因此插槽内容的样式也是在父组件的样式表内进行匹配的。例如：

card/index.ttml
<Test>
  <text class="content">Hello Lynx</text>
</Test>


text 节点的样式只受到 card/index.ttss 中定义的样式的影响，而不会受到 test/index.ttss 中定义的样式的影响。

插槽类型#
匿名插槽#
插槽内容和插槽出口的名字是可以省略的，底层框架会主动给其一个 default 的名字。

上述的例子等价于：

card/index.ttml
<Test>
  <text slot="default">Hello Lynx</text><!-- 插槽内容 -->
</Test>


test/index.ttml
<view>
  <slot name='default'></slot> <!-- 插槽出口 -->
</view>


INFO
当业务方定义了多个未命名插槽内容时，会出现多个 default 插槽内容和出口，此时会出现多个插槽出口渲染同一个插槽内容的情况。 为了避免出现这样的情况，我们建议业务方尽量使用具名插槽。

具名插槽#
插槽内容和插槽出口的名字是可以自定义的，插槽出口在渲染插槽内容时，会根据 <slot> 节点设置的 name 属性去匹配传入的插槽内容。

例如，在下面的例子中，父组件传入了两个插槽内容，一个是 header 插槽内容，一个是 footer 插槽内容。

card/index.ttml
<Test>
  <text slot="header">Hello Header</text>
  <text slot="footer">Hello Footer</text>
</Test>


子组件内部即可通过 <slot> 和指定的 name 来渲染对应的插槽内容。

test/index.ttml
<view>
  <slot name='header'></slot>
  <text>Hello Body</text>
  <slot name='footer'></slot>
</view>


动态插槽#
在一些高动态场景下，可能需要在运行时才能确定插槽内容和插槽出口的名字。 此时，可以设置 <slot> 节点的 name 属性为一个动态的值。

test/index.ttml
<view>
  <slot name='{{slotName}}'></slot>
</view>


这样，父组件就可以传入多个插槽内容，子组件根据运行时的 slotName 来渲染对应的插槽内容。

同样地，也可以设置插槽内容的 slot 属性为一个动态的值。

card/index.ttml
<Test>
  <text slot="{{slotName}}">Hello Header</text>
</Test>


这种方式可以让子组件定义多个插槽出口，父组件动态地传入插槽内容。

条件插槽#
在某些场景下，需要结合条件来动态地传入插槽内容，我们可以结合 tt:if 来实现。

card/index.ttml
<Test>
  <text tt:if={{needHeader}} slot="header">Hello Header</text>
  <text tt:if={{needFooter}} slot="footer">Hello Footer</text>
</Test>


循环插槽#
对一些复杂场景，需要通过运行时数据循环渲染插槽内容，我们可以结合 tt:for 来实现。

card/index.ttml
<Test>
  <text tt:for={{slotList}} slot="{{item.slotName}}">{{slotContent}}</text>
</Test>


test/index.ttml
<view>
  <slot tt:for={{slotList}} name='{{item.slotName}}'></slot>
</view>


插槽传递#
插槽内容可以传递多层，传递到子孙组件内部进行渲染。

首先，在父组件中定义插槽内容。

card/index.ttml
<Test>
  <text>Hello Lynx</text>
</Test>


接着，在子组件的插槽出口中，继续传递插槽内容。

test/index.ttml
<SubTest>
  <slot></slot>
</SubTest>


最后，在孙子组件的插槽出口中渲染插槽内容。

sub-test/index.ttml
<view>
  <slot></slot>
</view>


在这里查看最终结果：


需要注意的是，插槽传递的层数越多，运行时的性能开销越大。 因此，在实际使用时，需要合理地设计和使用插槽，避免过度依赖，详见 合理使用组件插槽。


动态组件（实验性）#
在大型项目中，开发者会将一些组件拆分成动态组件，然后通过动态加载的方式来按需使用。 在前端工程化方面，动态组件也可用于模块化开发，不同的业务方可以独立动态开发，然后在主工程中按需引入。

Lynx 的动态组件产物是一种特殊形态的 template.js，整体结构与主模版的 template.js 一致，包含了 lepus.js、 app-service.js、cssMap 等部分，且可以被主模版动态加载，实现按需加载或者延迟加载。

声明一个动态组件#
动态组件的实现和一般的组件没有区别。

打包动态组件#
独立打包：通过 isDynamicComponent 配置进入动态组件编译流程，可同时配置多个组件入口。
lynx.config.js
module.exports = {
  dsl: 'miniAppNodiff',
  input: {
    dynamic: './index',
  },
  dslPlugin: miniAppNodiffPlugin({
    isDynamicComponent: true
  }),
  pageConfig: {
    enableJSRender: true,
  },
  encode: {
    useLepusNG: true,
    targetSdkVersion: '2.13',
    enableParallelElement: true,
  },
};


WARNING
动态组件的 enableJSRender 和 enableParallelElement 配置需要和主模版保持一致，否则会导致动态组件无法正常运行。

整体打包：编译阶段会自动识别动态组件，无需额外配置。 动态组件的产物会被打包到 lynx_dynamic_components 目录下。
TIP
如无特殊的工程化需求，建议使用整体打包的方式，以避免配置不一致带来的运行时问题。

使用动态组件#
动态组件的使用方式和普通组件一致，可以通过 properties 传递属性，也可以通过事件监听组件事件。

声明式#
通过 usingDynamicComponents 配置动态组件路径，可以为独立打包后的静态资源地址， 也可以是本地文件地址。如果使用本地文件地址，则会在主编译流程的过程中自动编译动态组件。
index.json
{
  "usingDynamicComponents": {
    "dynamic-component-url": "https://examples/dynamic-component/template.js",
    "dynamic-component": "./dynamic-component.js"
  }
}


通过 <suspense> 包裹动态组件，并使用名为 fallback 的 slot 属性指定加载中的占位内容。
index.html
<suspense>
  <text slot="fallback">Loading</text>
  <dynamic-component />
</suspense>



非声明式#
可以通过 <component is> 使用静态资源地址或表达式的方式使用动态组件。
index.ttml
<suspense>
  <text slot="fallback">Loading</text>
  <component is={{ xxx ? "dynamic-component" : "https://examples/dynamic-component/template.js"}} />
  <component is="https://examples/dynamic-component/template.js" />
</suspense>

逻辑复用#
INFO
此章节假设你已经看过了 组件基础。若你还不了解组件是什么，请先阅读该章节。

Lynx TTML 官方提供了 Card 与 Component 两个全局方法来定义卡片入口和自定义组件。

多卡片复用逻辑的场景，可以对默认方法进行封装，来拓展通用的自定义能力。 例如，下面的代码拓展了 Card 的实现，默认添加了 commonData 和 onLoad 相关的逻辑。

myCard.js
const MyCard = (options) => Card({
  data: {
    commonData: 'Hello Common Data',
    ...(options.data || {}),
  },
  onLoad() {
    console.log('Do something before onLoad!');
    options.onLoad?.call(this);
  },
});


在实际业务中，可以通过 MyCard 来定义卡片逻辑。

card1/index.js
MyCard({
  data: {
    time: '2024.10.01'
  }
})


card2/index.js
MyCard({
  data: {
    age: 75
  }
})


这样，两个卡片不仅可以定义特有的 time 和 age，而且复用了 commonData 的定义逻辑和 onLoad 生命周期的拓展逻辑。

同样地，开发者也可以用上述方式实现组件逻辑的复用。代码如下：

myComponent.js
const MyComponent = (options) => Component({
  data: {
    commonData: 'Hello Common Data',
    ...(options.data || {}),
  },
  properties: {
    commonProperties: {
      type: String,
      value: 'Hello Common Properties'
    },
    ...(options.properties || {}),
  },
  created() {
    console.log('Do something before created!');
    options.created?.call(this);
  },
});


component/com1.js
MyComponent({
  properties:{
    time: String
  }
})


component/com2.js
MyComponent({
  properties:{
    age: Number
  }
})


上述代码实现了组件 data、properties 及 created 生命周期的逻辑复用。

TIP
Lynx TTML 未提供类似小程序的 behavior 或者 Vue 的 mixin 能力， 开发者可以通过上述方式进行灵活封装达到类似的效果， 具体可参考已有业务方的实现 Lina 开放 - 运行时 Mixin 技术方案​
