index.js:
Card({
    data:{
        testCase:"",
        title: "",
        engineType: "default",
        playbackRate:"",
    },
    frameFunction: null,
    canvas: null,
    audioContext: null,
    audioNode:null,
    audioBufferNode:null,
    oldAudio:null,
    newAudio:null,
    analyser: null,
    processCount:0,
    splitMergeMode:0,
    onViewDisappeared() {
        console.log('视图消失');
        var pause = lynx.aurum().pause;
        if (pause) {
            pause();
            console.log('停止音乐');
        }
    },
    onViewAppeared() {
        console.log('视图显示');
        var resume = lynx.aurum().resume;
        if (resume) {
            resume();
            console.log('继续音乐');
        }
    },
    onTapSin(){ 
        this.setData({testCase:"sin", title:"正弦波"});
        var {node, bufferNode} = this.playSin(this.audioContext);
        this.switchAudioNode(node, bufferNode);
    },
    onTapMic(){ 
        this.setData({testCase:"mic", title:"麦克风，应当能听到本地麦克风，并看到波形频谱"});
        lynx.getUserMedia({audio:true}, (obj)=> {
            const node = this.audioContext.createMediaStreamSource(obj);
            this.switchAudioNode(node);
        }, err => {
            this.switchAudioNode(null);
            console.error('请求麦克风失败' + err);
        });
    },
    onTapWav(){ 
        this.setData({testCase:"wav", title:"播放 WAV"});
        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/PinkPanther30.wav";
        var node = this.playAudioSrc(src);
        this.switchAudioNode(node);
    },
    onTapMp4(){ 
        this.setData({testCase:"mp4", title:"播放 MP4"});
        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/audio-13.mp4";
        var node = this.playAudioSrc(src);
        this.switchAudioNode(node);
    },
    onTapM4a(){ 
        this.setData({testCase:"m4a", title:"播放 M4A"});
        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/ff-16b-2c-44100hz.m4a";
        var node = this.playAudioSrc(src);
        this.switchAudioNode(node);
    },
    onTapMp3(){ 
        this.setData({testCase:"mp3", title:"播放 MP3"});
        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/home_bgm.mp3";
        var node = this.playAudioSrc(src);
        this.switchAudioNode(node);
    },
    onTapAac(){ 
        this.setData({testCase:"aac", title:"播放 AAC"});
        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/14235-AAC-20K-FTD.aac";
        var node = this.playAudioSrc(src);
        this.switchAudioNode(node);
    },
    onTapOgg(){ 
        this.setData({testCase:"ogg", title:"播放 OGG（Lynx不测）"});
        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/Demo_chorus.ogg"
        var node = this.playAudioSrc(src);
        this.switchAudioNode(node);
    },
    onTapDecodeWav() {
        this.setData({testCase:"wav-decode", title:"解码后播放 WAV"});
        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/PinkPanther30.wav";
        this.switchAudioNode(null);
        this.decodeAudioData(src);
    },
    onTapDecodeMp4() {
        this.setData({testCase:"mp4-decode", title:"解码后播放 MP4"});
        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/audio-13.mp4";
        this.switchAudioNode(null);
        this.decodeAudioData(src);
    },
    onTapOscillator() {
        let typeArray = ['square', 'sine', 'sawtooth', 'triangle'];
        let type = typeArray[Math.floor(Math.random() * Math.floor(typeArray.length))];
        this.setData({testCase:"oscillator", title:"信号发生器 " + type + "（点击按钮或波形切换不同信号类型）"});
        var node = this.playOscillator(type);
        this.switchAudioNode(node);
    },
    onTapScript() {
        this.setData({testCase:"script", title:"脚本修改Buffer（>=3.1版本有声音）"});
        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/audio-13.mp4";
        this.switchAudioNode(null);
        this.scriptProcess(src);
    },
    onChannelSplitAndMerge() {
        const title = "声道拆分合并（>=3.2版本，点击按钮或波形切换不同模式）";
        this.setData({testCase:"channel", title:title});
        var node = this.channelSplitAndMerge(title);
        this.switchAudioNode(node);
    },
    onTouchCanvas(valX, valY) {
        if (this.data.testCase === 'sin' || this.data.testCase === 'wav-decode' || this.data.testCase === 'mp4-decode') {
            this.updatePlaybackRate(valX ? 0.05 : -0.05);
        } else if (this.data.testCase === 'oscillator') {
            this.onTapOscillator();
        } else if (this.data.testCase === 'channel') {
            this.onChannelSplitAndMerge();
        }
    },
    switchAudioNode(node, bufferNode = null) {
        var oldNode = this.audioNode;
        if (oldNode) {
            oldNode.disconnect(this.audioContext.destination)
            oldNode.disconnect(this.analyser)
        }
        if (this.oldAudio) {
            this.oldAudio.stop();
            this.oldAudio = null;
        }
        this.oldAudio = this.newAudio;
        this.newAudio = null;

        this.audioBufferNode = bufferNode;
        this.audioNode = node;
        if (node) {
            node.connect(this.audioContext.destination)
            node.connect(this.analyser)
        }
        this.updatePlaybackRate(0);
        this.frameFunction = this.startAnalyse(this.canvas, this.analyser);
    },
    onReady() {
        this.getJSModule('GlobalEventEmitter').addListener('viewAppeared', this.onViewAppeared, this);
        this.getJSModule('GlobalEventEmitter').addListener('viewDisappeared', this.onViewDisappeared, this);
        this.setupCanvas();
        this.canvas.addEventListener("touchstart", (event)=>{
            if (event.changedTouches) {
                const valX = event.changedTouches[0].clientX * 2 * SystemInfo.pixelRatio > this.canvas.width;
                const valY = event.changedTouches[0].clientY * 2 * SystemInfo.pixelRatio > this.canvas.height;
                this.onTouchCanvas(valX, valY);
            }
        }) 

        this.audioContext = lynx.getAudioContext();
        var engineType = lynx.krypton.aurum().engineType || "default";
        this.setData({"engineType" : engineType});
        console.log('音频引擎类型: ' + engineType);

        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = 512; // 更快一些

        this.onTapSin();

        var last_uptime = 0, frames = 0, time = 0, fps_str = 'FPS:';
        var drawFrame = (timestamp)=> {
            lynx.requestAnimationFrame(drawFrame);
            if (this.frameFunction) {
                this.frameFunction();
            }
            frames++;
            time = Math.floor(timestamp / 3000) | 0;
            if (time !== last_uptime) {
            fps_str = "FPS:" + Math.floor(frames / 3);
            // console.log(fps_str);
            last_uptime = time;
            frames = 0;
            }
        }
        lynx.requestAnimationFrame(drawFrame);
  },
  createSinBuffer(ctx, freq) {
    // 根据频率 freq 计算确保波形周期完整展示在 3 秒内的缓冲区长度
    const bufferLength = Math.ceil(ctx.sampleRate * 3 / freq) * freq;
    const buf = ctx.createBuffer(2, bufferLength, ctx.sampleRate);
    for (let channel = 0; channel < buf.numberOfChannels; channel++) {
        // This gives us the actual array that contains the data
        const arr = buf.getChannelData(channel);
        for (let i = 0; i < buf.length; i++) {
            // 限制峰值，防止混声溢出
            arr[i] = Math.sin((i / ctx.sampleRate) * freq * Math.PI * 2) * 0.6;
        }
    }
    return buf;
  }, 
  playSin() {
        var ctx = this.audioContext;
        const buf = this.createSinBuffer(ctx, 400);
        const source = ctx.createBufferSource();
        source.buffer = buf;
        source.loop = true;
        // 创建增益中间节点，并且初始值为0
        const gainNode = ctx.createGain();
        gainNode.gain.value = 0;
        source.connect(gainNode);
        gainNode.connect(ctx.destination);

        source.start();
        source.onended = function () {
            console.log('事件 ended', this);
        };
        // 淡入效果
        let x = 0;
        const step = 1 / (2000 / 100); // 2s fadeIn
        const fadeIn = lynx.setInterval(() => {
            if (x > 1) {
                gainNode.gain.value = 1;
                clearInterval(fadeIn);
            } else {
                // 增益按照时间指数级增长
                gainNode.gain.value = Math.pow(2, x * 10) / 1024;
                x += step;
            }
        }, 100);
        return {"node": gainNode, "bufferNode": source};
   },
    startAnalyse(canvas, analyser) {

        var bufferLength = analyser.frequencyBinCount;
        var curTimeArray = new Uint8Array(bufferLength);
        var curFrequencyArray = new Uint8Array(bufferLength);

        return (time)=>{
            analyser.getByteTimeDomainData(curTimeArray);
            analyser.getByteFrequencyData(curFrequencyArray);

            const context = canvas.getContext('2d');
            context.clearRect(0, 0, canvas.width, canvas.height);

            context.textBaseline = 'middle';
            context.font = '35px monospace';
            context.fillStyle = 'blue';
            context.fillText('波 形', canvas.width - 150, canvas.height - 50);
            context.fillStyle = 'green';
            context.fillText('频 谱', canvas.width - 150, canvas.height - 90);

            context.strokeStyle = "blue";
            context.lineWidth = 3;
            context.beginPath();
            context.moveTo(0, canvas.height / 2);
            context.lineTo(canvas.width, canvas.height / 2)
            context.stroke();

            context.beginPath();
            context.lineWidth = 1;
            var sliceWidth = canvas.width * 1.0 / bufferLength;
            var x = 0;
            for (var i = 0; i < bufferLength; i++) {
                var v = curTimeArray[i] / 128.0;
                var y = v * canvas.height / 2;
                if (i === 0) {
                    context.moveTo(x, y);
                } else {
                    context.lineTo(x, y);
                }
                x += sliceWidth;
            }
            context.lineTo(canvas.width, canvas.height / 2);
            context.stroke();

            context.beginPath();
            context.strokeStyle = "green";
            context.lineWidth = 3;
            x = 0;
            for (var i = 0; i < bufferLength; i++) {
                var v = 2 - curFrequencyArray[i] / 128.0;
                var y = v * canvas.height / 2;
                if (i === 0) {
                    context.moveTo(x, y);
                } else {
                    context.lineTo(x, y);
                }
                x += sliceWidth;
            }
            context.lineTo(canvas.width, canvas.height / 2);
            context.stroke();
        }
    },
    playAudioSrc(src) {
        var ctx = this.audioContext;
        const audio = lynx.createAudio(src);
        this.newAudio = audio;
        const source = ctx.createMediaElementSource(audio);
        source.connect(ctx.destination);
        console.log('new createAudio');

        audio.loop = true;
        audio.currentTime = 0; // 可调整起始位置
        audio.autoplay = true;

        audio.oncanplay = () => {
            console.log('事件 canplay');
        };
        audio.onseeking = () => {
            console.log('事件 seeking');
        };
        audio.onwaiting = () => {
            console.log('事件 waiting');
        };
        audio.onseeked = () => {
            console.log('事件 seeked');
        };
        audio.onplaying = () => {
            console.log('事件 playing');
        };
        audio.onpause = () => {
            console.log('事件 pause');
        };
        audio.onended = () => {
            console.log('事件 ended');
        };
        audio.onstop = () => {
            // 跟 web 不同的事件
            console.log('事件 stop');
        };

        audio.onerror = (err) => {
            console.log('事件 error:' + err["errMsg"]);
        };

        return source;
    },
    decodeAudioData(src) {
        const audioCtx = lynx.getAudioContext();
        lynx.krypton.readFile(src, (audioData)=> {
            if (!audioData) {
                console.error('读取音频文件失败');
                return;
            }

            console.log('音频文件读取成功，总长度:' + audioData.byteLength);
            audioCtx.decodeAudioData(audioData).then(audioBuffer => {
                console.log('音频文件解码成功');
                const source = audioCtx.createBufferSource();
                source.buffer = audioBuffer;
                source.loop = false;
                source.connect(audioCtx.destination);
                this.switchAudioNode(source, source);
                source.start();
                source.onended = () => {
                    console.log('音频文件解码数据播放结束');
                };
            }).catch(err => {
                console.error("音频文件解码失败 " + err);
            });
        })        

    },
    scriptProcess(src) {
        this.processCount = 0;
        var decoded = null;
        const audioCtx = lynx.getAudioContext();
        lynx.krypton.readFile(src, (audioData)=> {
            if (!audioData) {
                console.error('音频文件读取失败');
                return;
            }

            console.log('音频文件读取成功，总长度: ' + audioData.byteLength);
            audioCtx.decodeAudioData(audioData).then(audioBuffer => {
                console.log('音频文件解码成功');
                decoded = audioBuffer.getChannelData(0);
            }).catch(err => {
                console.error("音频文件解码失败 " + err);
            });
        }) 

        const source = audioCtx.createScriptProcessor(16384, 0, 2);
        let pos = 0;
        source.onaudioprocess = audioProcessingEvent => {
            if (!decoded) return;
            const buf = audioProcessingEvent.outputBuffer;
            for (let channel = 0; channel < buf.numberOfChannels; channel++) {
                // This gives us the actual array that contains the data
                const arr = buf.getChannelData(channel);
                arr.set(decoded.slice(pos, pos + arr.length));
            }
            pos += buf.length;
            if (pos > decoded.length) {
                pos = 0;
            }
            this.processCount++;
            console.log("Script处理中 " + this.processCount);
            this.setData({title:"Script处理中 " + this.processCount});
        };
        this.switchAudioNode(source);
        lynx.setTimeout(() => {
            this.switchAudioNode(null);
            console.log('处理节点断开连接');
            this.setData({title:"Script暂停 " + this.processCount});
        }, 4000);
        lynx.setTimeout(() => {
            this.switchAudioNode(source);
            console.log('处理节点重新连接');
        }, 6000);
        lynx.setTimeout(() => {
            this.switchAudioNode(null);
            console.log('处理节点断开连接');
            this.setData({title:"Script测试结束"});
        }, 9000);
    },
    playOscillator(type) {
        var ctx = this.audioContext;
        const oscillator = ctx.createOscillator();
        oscillator.type = type;
        oscillator.start();
        var started = true;
        setInterval(()=>{
            if (started) {
                oscillator.stop();
            } else {
                oscillator.start();
            }
            started = !started;
        },1000);
        return oscillator;
    },
    updatePlaybackRate(value) {
        const playbackRate = this.audioBufferNode?.playbackRate;
        if (!playbackRate) {
            this.setData({"playbackRate": ""});
            return;
        }
        playbackRate.value = playbackRate.value + value;
        this.setData({"playbackRate": Math.abs(playbackRate.value - 1.0) > 0.001 ? "调速 "+ playbackRate.value.toFixed(2) : "点击波形左右两边可调整播放速度"});
    },
    channelSplitAndMerge(titleStr) {
        var ctx = this.audioContext;
        const buf = this.createSinBuffer(ctx, 400);
        const bufferNode = ctx.createBufferSource();
        bufferNode.buffer = buf;
        bufferNode.loop = true;
        bufferNode.start();

        var src = "https://voffline.byted.org/obj/lynx/canvas/test-case/resources/PinkPanther30.wav";
        const audio = lynx.createAudio(src);
        audio.loop = true;
        audio.startTime = 0;
        audio.autoplay = true;
        const audioNode = ctx.createMediaElementSource(audio);

        var resultNode = null;
        if (this.splitMergeMode == 0) {
            // 使用 ChannelMerger 合并， 左：正弦波  右：音乐
            var merger = ctx.createChannelMerger(2);
            bufferNode.connect(merger, 0, 0);
            audioNode.connect(merger, 0, 1);
            // 直接播放合成后的立体声
            this.setData({title: titleStr + "0：左正弦波 右音乐"});
            resultNode = merger;
        } else if (this.splitMergeMode == 1) {
            // 使用 ChannelMerger 仅添加右边右：音乐，左边不添加
            var merger = ctx.createChannelMerger(2);
            audioNode.connect(merger, 0, 1);
            this.setData({title: titleStr + "1：左静音 右音乐"});
            resultNode = merger;
        } else if (this.splitMergeMode == 2) {
            // 使用 ChannelMerger 合并， 左：正弦波  右：音乐
            var merger = ctx.createChannelMerger(2);
            bufferNode.connect(merger, 0, 0);
            audioNode.connect(merger, 0, 1);
            // 使用 createChannelSplitter 单独拆分左声道（正弦波）
            this.setData({title: titleStr + "2：单声道正弦波"});
            const splitter = ctx.createChannelSplitter(2);
            merger.connect(splitter);
            // 会当做单声道播放，两边都是正弦波
            resultNode = splitter;
        } else if (this.splitMergeMode == 3) {
            // 使用 ChannelMerger 合并， 左：正弦波  右：音乐
            var merger = ctx.createChannelMerger(2);
            bufferNode.connect(merger, 0, 0);
            audioNode.connect(merger, 0, 1);
            this.setData({title: titleStr + "3：单声道音乐"});
            // 使用 createChannelSplitter 单独拆分右声道（音乐）
            const splitter = ctx.createChannelSplitter(2);
            merger.connect(splitter);
            var gainNode = ctx.createGain();
            gainNode.gain.value = 1.5;  // 放大 1.5 倍
            // 单拆出merger的右声道，播放时按单声道对待，两边都是音乐
            splitter.connect(gainNode, 1.5);
            resultNode = gainNode;
        } else if (this.splitMergeMode == 4) {
            this.setData({title: titleStr + "4：左音乐 右正弦波"});
            // 使用 ChannelMerger 合并， 左：正弦波  右：音乐
            var merger = ctx.createChannelMerger(2);
            bufferNode.connect(merger, 0, 0);
            audioNode.connect(merger, 0, 1);
            const splitter = ctx.createChannelSplitter(2);
            merger.connect(splitter);
            // 拆分出的左、右声道分别接新的ChannelMerger的右、左声道，即左右对换
            var merger2 = ctx.createChannelMerger(2);
            splitter.connect(merger2, 0, 1);
            splitter.connect(merger2, 1, 0);
            resultNode = merger2;
        }
        this.splitMergeMode = (this.splitMergeMode + 1) % 5;
        return resultNode;
    },

    // 正确的Canvas初始化方法
    setupCanvas() {
        console.log('Setting up canvas...');
        try {
            const canvas = lynx.createCanvasNG();

            // 重要：resize事件监听必须在绑定前设置
            canvas.addEventListener("resize", ({ width, height }) => {
                console.log('Canvas resize event:', width, height);
                canvas.width = width * SystemInfo.pixelRatio;
                canvas.height = height * SystemInfo.pixelRatio;
                const ctx = canvas.getContext('2d');
                ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
                this.canvas = canvas;
                this.ctx = ctx;
                this.canvasWidth = width;
                this.canvasHeight = height;
                console.log('Canvas setup complete');
            });

            // 绑定到Canvas视图
            canvas.attachToCanvasView("firstCanvas");
        } catch (error) {
            console.error('Canvas setup failed:', error);
        }
    }

});

ttml:<canvas  style="width: 100%; height: 50%;" name="firstCanvas"></canvas>

