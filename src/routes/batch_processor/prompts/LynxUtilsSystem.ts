export const LYNX_UTILS_SYSTEM = `
Lynx工具与实用模块系统 - 完整开发辅助指南

UTIL SYSTEM ARCHITECTURE - 工具系统架构

Modular Prompt Loading System:
ModularPromptLoader类负责动态组装所有prompt模块
- getInstance(): 获取单例实例
- buildFullPrompt(): 构建完整prompt内容
- 支持模块化组装和错误回退机制

Master Level UI Prompt Integration:
- 自动组合所有核心模块内容
- 专用工具指南集成
- 完整性验证和错误处理
- 兼容性接口设计

DEVELOPMENT WORKFLOW OPTIMIZATION - 开发工作流优化

1. 自动化提示加载
- 无需手动拼接多个prompt文件
- 智能检测模块依赖关系
- 动态内容组装和验证

2. 错误恢复机制
- 模块加载失败时自动降级
- 基础功能保障fallback内容
- 完整性验证确保prompt质量

3. 扩展性设计
- 新增模块时自动集成
- 模块化架构便于维护
- 版本控制和兼容性管理

CRITICAL ERROR PREVENTION - 关键错误预防

1. 系统级工具错误:
- 模块加载失败 → 自动降级机制
- 内容完整性 → 关键词验证
- 版本兼容性 → 接口标准化

2. 开发效率优化:
- 一键获取完整prompt内容
- 自动集成所有必要模块
- 智能错误检测和修复建议

SYSTEMINFO GLOBAL VARIABLE - SystemInfo全局变量

SystemInfo是Lynx框架提供的全局变量，可直接使用无需导入：

基础平台信息：
- SystemInfo.platform: 平台类型（"ios"、"android"、"web"等）
- SystemInfo.version: 系统版本信息
- SystemInfo.model: 设备型号信息

屏幕和显示信息：
- SystemInfo.screenWidth: 屏幕宽度（像素）
- SystemInfo.screenHeight: 屏幕高度（像素）
- SystemInfo.pixelRatio: 设备像素比
- SystemInfo.statusBarHeight: 状态栏高度

网络和性能信息：
- SystemInfo.networkType: 网络类型
- SystemInfo.enableKrypton: 是否支持Krypton渲染引擎

使用示例：
const screenWidth = SystemInfo.screenWidth;
const isIOS = SystemInfo.platform === 'ios';
const pixelRatio = SystemInfo.pixelRatio;

THIRD-PARTY LIBRARY INTEGRATION - 第三方库集成

图表库集成：
详细的图表库集成指南请参考 LightChartPromptLoader.ts 文件
包含完整的使用说明、API差异、错误处理等内容

开发工具集成：
- 自动化构建工具配置
- 调试工具配置
- 性能监控工具集成
- 代码质量检查工具

BEST PRACTICES - 最佳实践

1. 工具使用规范：
- 优先使用官方推荐的工具和库
- 保持工具版本的一致性
- 定期更新和维护工具链

2. 性能优化策略：
- 合理使用缓存机制
- 优化资源加载顺序
- 减少不必要的工具依赖

3. 错误处理标准：
- 实现完善的错误捕获机制
- 提供友好的错误提示
- 建立错误恢复流程
`;

export default {
  LYNX_UTILS_SYSTEM,
};