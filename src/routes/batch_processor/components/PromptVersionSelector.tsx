import React from 'react';
import Icon from './Icon';
import { getPEPromptContent } from '../utils/pePromptLoader';

/**
 * Props interface for PromptVersionSelector component
 */
interface PromptVersionSelectorProps {
  /** Callback when a prompt version is selected */
  onPromptSelect: (prompt: string, version: string) => void;
  /** Logger instance for status messages */
  logger: {
    success: (message: string) => void;
    error: (message: string) => void;
    info: (message: string) => void;
  };
}

/**
 * Prompt version information
 */
interface PromptVersion {
  id: string;
  name: string;
  description: string;
  size: string;
  features: string[];
  color: string;
  bgColor: string;
  getContent: () => string;
}

/**
 * Prompt version selector component
 * 提供快速选择不同版本prompt的测试按钮
 */
const PromptVersionSelector: React.FC<PromptVersionSelectorProps> = ({
  onPromptSelect,
  logger,
}) => {
  // 定义所有可用的prompt版本
  const promptVersions: PromptVersion[] = [
    {
      id: 'original',
      name: '🔧 原版',
      description: '完整详细的原始版本，包含所有功能和规则',
      size: '79KB',
      features: ['完整语法规则', '详细示例', '开发参考'],
      color: '#6b46c1',
      bgColor: 'linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 50%, #ddd6fe 100%)',
      getContent: getPEPromptContent,
    },
  ];

  /**
   * Handle version selection
   */
  const handleVersionSelect = (version: PromptVersion) => {
    try {
      const content = version.getContent();
      onPromptSelect(content, version.name);
      logger.success(
        `✅ 已加载${version.name}！特色: ${version.features.join('、')}`,
      );
    } catch (error) {
      logger.error(
        `加载${version.name}失败: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  };

  return (
    <div className="glass-card-gold">
      <h4 className="typography-body-large flex items-center mb-4">
        <Icon type="layers" color="primary" size="sm" className="mr-2" />
        Prompt版本快速切换
      </h4>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {promptVersions.map(version => (
          <div
            key={version.id}
            className="relative group cursor-pointer"
            onClick={() => handleVersionSelect(version)}
            title={`点击加载${version.name} - ${version.description}`}
          >
            <div
              className="p-4 rounded-xl border transition-all duration-300 hover:shadow-lg hover:scale-[1.02] hover:-translate-y-1"
              style={{
                background: version.bgColor,
                border: `1px solid ${version.color}40`,
              }}
            >
              {/* 版本标题和大小 */}
              <div className="flex items-center justify-between mb-2">
                <h5
                  className="font-semibold text-sm"
                  style={{ color: version.color }}
                >
                  {version.name}
                </h5>
                <span
                  className="text-xs px-2 py-1 rounded-md font-medium"
                  style={{
                    backgroundColor: `${version.color}20`,
                    color: version.color,
                  }}
                >
                  {version.size}
                </span>
              </div>

              {/* 描述 */}
              <p className="text-xs text-gray-600 mb-3 leading-relaxed">
                {version.description}
              </p>

              {/* 特性标签 */}
              <div className="flex flex-wrap gap-1">
                {version.features.map((feature, idx) => (
                  <span
                    key={idx}
                    className="text-xs px-2 py-1 rounded-md"
                    style={{
                      backgroundColor: `${version.color}15`,
                      color: version.color,
                      border: `1px solid ${version.color}30`,
                    }}
                  >
                    {feature}
                  </span>
                ))}
              </div>

              {/* 鼠标悬停效果指示器 */}
              <div
                className="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"
                style={{
                  background: `linear-gradient(135deg, ${version.color}10 0%, ${version.color}05 100%)`,
                  boxShadow: `0 8px 25px ${version.color}25`,
                }}
              />

              {/* 点击动画指示器 */}
              <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <Icon type="arrow_right" color={version.color} size="xs" />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 使用说明 */}
      <div className="mt-4 p-3 rounded-lg bg-blue-50 border border-blue-200">
        <div className="flex items-start gap-2">
          <Icon type="info" color="blue" size="sm" className="mt-0.5" />
          <div className="text-xs text-blue-700">
            <p className="font-medium mb-1">使用建议:</p>
            <ul className="space-y-1 text-blue-600">
              <li>
                • <strong>原版</strong>: 开发调试时使用，包含完整文档
              </li>
              <li>
                • <strong>精简版</strong>: 基础功能测试，快速响应
              </li>
              <li>
                • <strong>平衡版</strong>: 生产环境推荐，最佳平衡
              </li>
              <li>
                • <strong>大师版</strong>: 顶级UI需求，专业视觉品质
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptVersionSelector;
