import React, { useEffect, useRef, useState } from 'react';
import Icon, { IconType } from './Icon';
import drawerAnimationHelper from '../utils/drawerAnimationHelper';


/**
 * 抽屉操作按钮配置
 */
interface HeaderAction {
  /** 按钮点击事件处理 */
  onClick: () => void;
  /** 按钮图标类型 */
  icon?: IconType;
  /** 按钮标签文本 */
  label?: string;
  /** 按钮CSS类名 */
  className?: string;
  /** 是否禁用 */
  disabled?: boolean;
  /** 按钮提示文本 */
  tooltip?: string;
}

interface RightDrawerProps {
  /** 是否显示抽屉 */
  isOpen: boolean;
  /** 关闭抽屉回调 */
  onClose: () => void;
  /** 抽屉标题 */
  title: string | React.ReactNode;
  /** 抽屉内容 */
  children: React.ReactNode;
  /** 抽屉宽度 */
  width?: string;
  /** 是否显示关闭按钮 */
  showCloseButton?: boolean;
  /** 标题栏额外内容 */
  headerExtra?: React.ReactNode;
  /** 头部操作按钮列表 */
  headerActions?: HeaderAction[];
  /** 抽屉主题 */
  theme?: 'settings' | 'history' | 'prompt' | 'default' | 'gold' | 'blue';
  /** 是否阻止点击遮罩层关闭 */
  preventBackdropClose?: boolean;
  /** 是否自动聚焦第一个可聚焦元素 */
  autoFocus?: boolean;
  /** 底部内容 */
  footer?: React.ReactNode;
  /** 打开时回调 */
  onOpen?: () => void;
  /** 自定义类名 */
  className?: string;
  /** 是否显示帮助按钮 */
  showHelpButton?: boolean;
  /** 帮助按钮点击回调 */
  onHelpClick?: () => void;
}

/**
 * 右滑抽屉组件
 * 从右侧滑入的抽屉，带有遮罩层和动画效果
 * 支持自定义主题、头部操作按钮和灵活的内容布局
 *
 * 2025-06-20: 修复重复动画问题
 */
const RightDrawer: React.FC<RightDrawerProps> = ({
  isOpen,
  onClose,
  title,
  children,
  width = '400px',
  showCloseButton = true,
  headerExtra,
  headerActions = [],
  theme = 'default',
  preventBackdropClose = false,
  autoFocus = true,
  footer,
  onOpen,
  className = '',
  showHelpButton = false,
  onHelpClick,
}) => {
  const drawerRef = useRef<HTMLDivElement>(null);
  const [isClosing, setIsClosing] = useState(false);
  const [shouldRender, setShouldRender] = useState(false);
  const initialFocusRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      setShouldRender(true);
      // 简化：只需要一个 requestAnimationFrame 确保DOM渲染完成
      requestAnimationFrame(() => {
        setIsClosing(false);
      });

      // 延迟执行焦点和回调
      const timer = setTimeout(() => {
        if (onOpen) {
          onOpen();
        }

        if (autoFocus && drawerRef.current) {
          const focusableElements = drawerRef.current.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
          );
          if (focusableElements.length > 0) {
            (focusableElements[0] as HTMLElement).focus();
            initialFocusRef.current = focusableElements[0] as HTMLElement;
          }
        }
      }, 400);

      return () => clearTimeout(timer);
    } else if (shouldRender) {
      setIsClosing(true);
      const timer = setTimeout(() => {
        setShouldRender(false);
        setIsClosing(false);
      }, 300);

      if (initialFocusRef.current) {
        initialFocusRef.current.focus();
        initialFocusRef.current = null;
      }

      return () => clearTimeout(timer);
    }
  }, [isOpen, onOpen, autoFocus]);

  // 处理ESC键关闭
  useEffect(() => {
    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen && !preventBackdropClose) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // 只在抽屉打开时防止背景滚动
      document.body.style.overflow = 'hidden';
    } else {
      // 抽屉关闭时恢复滚动
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      // 清理时确保恢复滚动
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose, preventBackdropClose]);

  // 点击外部关闭
  const handleOverlayClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget && !preventBackdropClose) {
      onClose();
    }
  };

  if (!shouldRender) {
    return null;
  }

  // 获取抽屉相关图标类型
  const getDrawerIconType = (): IconType => {
    switch (theme) {
      case 'history':
        return 'history';
      case 'settings':
        return 'settings';
      case 'prompt':
        return 'edit';
      case 'gold':
        return 'grid';
      case 'blue':
        return 'info';
      default:
        return 'grid';
    }
  };

  // 计算drawer container类名 - 使用左侧滑入动画（从左边滑入到右侧定位）
  const getAnimationClass = () => {
    if (isClosing) {
      return 'slide-out'; // 向左滑出（滑出到屏幕左侧）
    }
    if (isOpen && !isClosing) {
      return 'slide-in'; // 从左侧滑入（从屏幕左侧滑入到右侧定位）
    }
    return '';
  };

  const drawerContainerClassName = `enhanced-drawer-container right-positioned ${theme}-drawer ${className} ${getAnimationClass()} ${isOpen && !isClosing ? 'open' : ''}`;

  const drawerOverlayClass = [
    'drawer-overlay',
    'smooth-drawer-overlay',
    isClosing ? 'closing' : '',
  ].filter(Boolean).join(' ');

  return shouldRender ? (
    <div className="fixed inset-0 z-50 overflow-hidden">
      {/* 半透明遮罩层 */}
      <div
        className="absolute inset-0 left-drawer-overlay"
        onClick={handleOverlayClick}
        aria-hidden="true"
        style={drawerAnimationHelper.getLeftDrawerOverlayStyle(
          isClosing,
          false,
        )}
      />

      {/* 抽屉容器 */}
      <div
        ref={drawerRef}
        className={drawerContainerClassName}
        style={{ width }}
      >
        {/* 抽屉标题栏 */}
        <div
          className="enhanced-drawer-header"
          style={drawerAnimationHelper.getDrawerHeaderStyle()}
        >
          {/* 左滑指示器 */}
          <div
            className="slide-indicator"
            style={drawerAnimationHelper.getSlideIndicatorStyle(false)}
          />

          <div className="title-container">
            {typeof title === 'string' ? (
              <h2 className="enhanced-drawer-title">
                <Icon
                  type={getDrawerIconType()}
                  color="primary"
                  size="sm"
                  className="mr-2"
                />
                {title}
              </h2>
            ) : (
              title
            )}
          </div>
          <div className="action-buttons">
            {/* 自定义头部操作按钮 */}
            {headerActions.map((action, index) => (
              <button
                key={`header-action-${index}`}
                onClick={action.onClick}
                className={
                  action.className || 'btn-authority enhanced-secondary-button'
                }
                aria-label={action.label || `Action ${index + 1}`}
                disabled={action.disabled}
                title={action.tooltip}
              >
                {action.icon && (
                  <Icon type={action.icon} color="primary" size="sm" />
                )}
                {action.label}
              </button>
            ))}

            {/* 帮助按钮 */}
            {showHelpButton && (
              <button
                onClick={onHelpClick}
                className="drawer-help-button btn-authority enhanced-secondary-button"
                aria-label="帮助"
              >
                <Icon type="help" color="primary" size="sm" />
              </button>
            )}

            {/* 自定义头部额外内容 */}
            {headerExtra}

            {/* 关闭按钮 */}
            {showCloseButton && (
              <button
                onClick={onClose}
                className="drawer-close-button"
                aria-label="关闭抽屉"
                style={
                  drawerAnimationHelper.getCloseButtonStyle() as React.CSSProperties
                }
                onMouseOver={e => {
                  const target = e.currentTarget;
                  const styles =
                    drawerAnimationHelper.getCloseButtonHoverStyle() as Record<
                      string,
                      string
                    >;
                  Object.keys(styles).forEach(key => {
                    target.style[key as any] = styles[key];
                  });
                }}
                onMouseOut={e => {
                  const target = e.currentTarget;
                  const styles =
                    drawerAnimationHelper.getCloseButtonNormalStyle() as Record<
                      string,
                      string
                    >;
                  Object.keys(styles).forEach(key => {
                    target.style[key as any] = styles[key];
                  });
                }}
              >
                <Icon type="close" color="primary" size="sm" />
              </button>
            )}
          </div>
        </div>

        {/* 抽屉内容区域 */}
        <div
          className="enhanced-drawer-content flex-1 overflow-y-auto overflow-x-hidden"
          style={
            drawerAnimationHelper.getDrawerContentStyle() as React.CSSProperties
          }
        >
          {children}
        </div>

        {/* 抽屉底部区域 */}
        {footer && <div className="enhanced-drawer-footer">{footer}</div>}
      </div>
    </div>
  ) : null;
};

export default RightDrawer;
