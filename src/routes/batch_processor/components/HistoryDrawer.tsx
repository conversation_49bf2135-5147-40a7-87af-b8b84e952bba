import React from 'react';
import RightDrawer from './RightDrawer';
import './../styles/components/LeftDrawer.css';
import HistoryPanel from './HistoryPanel';

import useStatusLogger from '../hooks/useStatusLogger';

interface HistoryDrawerProps {
  /** 是否显示抽屉 */
  isOpen: boolean;
  /** 关闭抽屉回调 */
  onClose: () => void;
  /** 重用查询回调 */
  onReuseQuery: (query: string) => void;
  /** 日志记录器 */
  logger: ReturnType<typeof useStatusLogger>;
}

/**
 * 历史记录抽屉组件
 * 显示历史记录列表和详细信息
 */
const HistoryDrawer: React.FC<HistoryDrawerProps> = ({
  isOpen,
  onClose,
  onReuseQuery,
  logger,
}) => {
  const handleClearHistory = () => {
    if (confirm('确定要清空历史记录吗？此操作不可撤销。')) {
      try {
        localStorage.removeItem('batch_processor_history');
        logger.success('历史记录已清空');
        // 通过关闭再打开抽屉来刷新历史记录
        onClose();
        setTimeout(() => {
          // 可以添加刷新历史记录的回调，如果有的话
        }, 100);
      } catch (error) {
        logger.error(
          `清空历史记录失败: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }
  };

  return (
    <RightDrawer
      isOpen={isOpen}
      onClose={onClose}
      title="历史记录管理"
      width="620px"
      theme="history"
      headerActions={[
        {
          icon: 'trash',
          label: '清空',
          onClick: handleClearHistory,
          className: 'btn-authority btn-secondary-glass text-xs',
        },
      ]}
    >
      <div className="h-full">
        <HistoryPanel onReuseQuery={onReuseQuery} logger={logger} />
      </div>
    </RightDrawer>
  );
};

export default HistoryDrawer;
