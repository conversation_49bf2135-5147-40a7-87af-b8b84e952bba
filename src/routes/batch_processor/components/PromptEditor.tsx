'use client';

import React, { useState, useEffect } from 'react';

// -----------------------------------------------------------------------------
// PromptEditor 组件
// -----------------------------------------------------------------------------
// 提供系统提示词(System Prompt)的编辑、预览和保存功能。
// 系统提示词与用户查询一起构成消息数组，发送给AI模型。
// 目前仅实现基础功能，后续可扩展为多模板管理。
// -----------------------------------------------------------------------------

interface PromptEditorProps {
  /** 系统提示词 */
  systemPrompt: string;
  /** 系统提示词变更回调 */
  onSystemPromptChange: (prompt: string) => void;
  /** 关闭编辑器回调 */
  onClose: () => void;
}

// 预览标签类型
type PreviewTab = 'formatted' | 'api' | 'example';

const PromptEditor: React.FC<PromptEditorProps> = ({
  systemPrompt,
  onSystemPromptChange,
  onClose,
}) => {
  // 本地编辑状态
  const [prompt, setPrompt] = useState(systemPrompt);
  const [sampleQuery, setSampleQuery] = useState('简单计算器');
  const [activeTab, setActiveTab] = useState<PreviewTab>('formatted');
  const [showTemplateHint, setShowTemplateHint] = useState(false);

  // 同步外部systemPrompt变化
  useEffect(() => {
    setPrompt(systemPrompt);
  }, [systemPrompt]);

  // 处理保存事件
  const handleSave = () => {
    console.log('[PromptEditor] 保存提示词:', prompt);
    onSystemPromptChange(prompt);
    onClose();
  };

  // 构建API消息结构
  const apiMessages = [
    {
      role: 'system',
      content: prompt,
    },
    {
      role: 'user',
      content: sampleQuery,
    },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        {/* 头部 */}
        <div className="bg-gray-100 px-4 py-3 flex justify-between items-center">
          <h3 className="text-lg font-medium">编辑系统提示词</h3>
          <button
            className="text-gray-500 hover:text-gray-700"
            onClick={onClose}
          >
            <span className="text-xl">×</span>
          </button>
        </div>

        {/* 内容区 */}
        <div className="flex-1 overflow-auto p-6">
          {/* 主编辑区 */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-2">
              <label
                htmlFor="prompt-editor"
                className="block text-sm font-medium text-gray-700"
              >
                System Prompt
              </label>
              <button
                className="text-xs text-blue-600 hover:text-blue-800 flex items-center"
                onClick={() => setShowTemplateHint(!showTemplateHint)}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-1"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
                提示词模板说明
              </button>
            </div>

            {showTemplateHint && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3 mb-3 text-sm text-blue-800">
                <p className="font-medium mb-1">提示词模板说明:</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>系统提示词将与每个查询组合，发送给AI模型</li>
                  <li>保持提示词清晰、具体，避免过于复杂的指令</li>
                  <li>确保提示词包含对Lynx代码生成的明确要求</li>
                  <li>建议包含代码格式要求，如使用```lynx包裹代码</li>
                </ul>
              </div>
            )}

            <textarea
              id="prompt-editor"
              className="w-full h-64 p-3 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500 font-mono text-sm"
              value={prompt}
              onChange={e => setPrompt(e.target.value)}
              placeholder="输入系统提示词，例如: 请生成一个简单的 Lynx 应用..."
            />
          </div>

          {/* 预览区 */}
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h4 className="text-sm font-medium text-gray-700">预览</h4>

              <div className="flex border border-gray-300 rounded-md overflow-hidden">
                <button
                  className={`px-3 py-1 text-xs ${activeTab === 'formatted' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                  onClick={() => setActiveTab('formatted')}
                >
                  格式化预览
                </button>
                <button
                  className={`px-3 py-1 text-xs ${activeTab === 'api' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                  onClick={() => setActiveTab('api')}
                >
                  API结构
                </button>
                <button
                  className={`px-3 py-1 text-xs ${activeTab === 'example' ? 'bg-blue-600 text-white' : 'bg-white text-gray-700 hover:bg-gray-50'}`}
                  onClick={() => setActiveTab('example')}
                >
                  实际效果
                </button>
              </div>
            </div>

            <div className="mb-4">
              <label
                htmlFor="sample-query"
                className="block text-sm font-medium text-gray-600 mb-1"
              >
                示例查询:
              </label>
              <input
                id="sample-query"
                type="text"
                className="w-full p-2 border border-gray-300 rounded"
                value={sampleQuery}
                onChange={e => setSampleQuery(e.target.value)}
              />
            </div>

            {/* 预览内容 - 根据选中的标签显示不同内容 */}
            {activeTab === 'formatted' && (
              <div className="bg-white p-3 rounded border border-gray-200 max-h-64 overflow-auto">
                <div className="mb-3 pb-3 border-b border-gray-100">
                  <div className="text-xs text-gray-500 mb-1">系统消息:</div>
                  <div className="whitespace-pre-wrap text-sm font-mono bg-gray-50 p-2 rounded">
                    {prompt}
                  </div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 mb-1">用户消息:</div>
                  <div className="whitespace-pre-wrap text-sm font-mono bg-gray-50 p-2 rounded">
                    {sampleQuery}
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'api' && (
              <div className="bg-white p-3 rounded border border-gray-200 max-h-64 overflow-auto">
                <pre className="text-xs font-mono whitespace-pre-wrap">
                  {JSON.stringify(apiMessages, null, 2)}
                </pre>
                <p className="text-xs text-gray-500 mt-2">
                  这是发送给AI接口的实际JSON消息结构
                </p>
              </div>
            )}

            {activeTab === 'example' && (
              <div className="bg-white p-3 rounded border border-gray-200 max-h-64 overflow-auto">
                <div className="bg-blue-50 border-l-4 border-blue-400 p-3 mb-3">
                  <p className="text-sm text-blue-800">
                    以下是批处理时，每个查询将如何与系统提示词组合
                  </p>
                </div>

                <div className="mb-3 pb-2 border-b border-gray-100">
                  <div className="flex items-center mb-1">
                    <div className="w-16 text-xs font-medium text-gray-500">
                      系统:
                    </div>
                    <div className="text-sm text-gray-800 line-clamp-2">
                      {prompt.length > 100
                        ? `${prompt.substring(0, 100)}...`
                        : prompt}
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-16 text-xs font-medium text-gray-500">
                      用户:
                    </div>
                    <div className="text-sm text-gray-800">{sampleQuery}</div>
                  </div>
                </div>

                <div className="text-xs text-gray-600">
                  <p className="mb-2">
                    批处理时，系统将对查询列表中的
                    <span className="font-medium">每一项</span>执行以下操作:
                  </p>
                  <ol className="list-decimal pl-5 space-y-1">
                    <li>将系统提示词作为system消息</li>
                    <li>将当前查询作为user消息</li>
                    <li>发送到AI接口生成Lynx代码</li>
                    <li>解析返回的流式数据，提取代码</li>
                    <li>上传到CDN并生成Playground URL</li>
                  </ol>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 底部按钮 */}
        <div className="bg-gray-50 px-4 py-3 flex justify-between">
          <div>
            <span className="text-xs text-gray-500">
              提示词长度: {prompt.length} 字符
            </span>
          </div>
          <div className="flex space-x-2">
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
            >
              取消
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
            >
              保存
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PromptEditor;
