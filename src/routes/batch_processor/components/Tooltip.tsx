import React, { useState, ReactNode } from 'react';

interface TooltipProps {
  /** 提示文本内容 */
  content: string;
  /** 要包裹的子元素 */
  children: ReactNode;
  /** 提示框位置，默认为底部 */
  position?: 'top' | 'right' | 'bottom' | 'left';
  /** 额外的CSS类 */
  className?: string;
  /** 延迟显示时间(毫秒) */
  delay?: number;
}

/**
 * 增强的浮动提示组件
 * 鼠标悬停在子元素上时显示提示内容
 * 使用更精细的动画和视觉效果
 */
const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'bottom',
  className = '',
  delay = 300,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showTimer, setShowTimer] = useState<NodeJS.Timeout | null>(null);

  // 处理鼠标进入
  const handleMouseEnter = () => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, delay);
    setShowTimer(timer);
  };

  // 处理鼠标离开
  const handleMouseLeave = () => {
    if (showTimer) {
      clearTimeout(showTimer);
      setShowTimer(null);
    }
    setIsVisible(false);
  };

  // 根据位置设置样式类
  const getPositionClass = () => {
    switch (position) {
      case 'top':
        return 'tooltip-top bottom-full mb-3';
      case 'right':
        return 'tooltip-right left-full ml-3';
      case 'left':
        return 'tooltip-left right-full mr-3';
      case 'bottom':
      default:
        return 'tooltip-bottom top-full mt-3';
    }
  };

  // 为提示框添加入场和退场动画
  const getAnimationClass = () => {
    if (!isVisible) {
      return 'opacity-0 scale-95';
    }

    switch (position) {
      case 'top':
        return 'opacity-100 transform-gpu';
      case 'right':
        return 'opacity-100 transform-gpu';
      case 'left':
        return 'opacity-100 transform-gpu';
      case 'bottom':
      default:
        return 'opacity-100 transform-gpu';
    }
  };

  return (
    <div
      className={`relative inline-flex ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {children}
      <div
        className={`absolute px-3 py-2 text-xs z-50 rounded-lg shadow-lg tooltip-content tooltip-dark ${getPositionClass()} ${getAnimationClass()}`}
        style={{
          opacity: isVisible ? 1 : 0,
          visibility: isVisible ? 'visible' : 'hidden',
          pointerEvents: isVisible ? 'auto' : 'none',
          backgroundColor: 'var(--color-gray-800)',
          color: 'var(--color-white)',
          border: '1px solid var(--color-gray-700)',
        }}
        role="tooltip"
      >
        {content}
        {/* 箭头 */}
        <div
          className="absolute w-2 h-2 transform rotate-45 tooltip-arrow tooltip-arrow-dark"
          style={{
            backgroundColor: 'var(--color-gray-800)',
            border: '1px solid var(--color-gray-700)',
            ...(position === 'top' && {
              bottom: '-5px',
              left: '50%',
              marginLeft: '-4px',
              borderTop: 'none',
              borderLeft: 'none',
            }),
            ...(position === 'bottom' && {
              top: '-5px',
              left: '50%',
              marginLeft: '-4px',
              borderBottom: 'none',
              borderRight: 'none',
            }),
            ...(position === 'left' && {
              right: '-5px',
              top: '50%',
              marginTop: '-4px',
              borderLeft: 'none',
              borderBottom: 'none',
            }),
            ...(position === 'right' && {
              left: '-5px',
              top: '50%',
              marginTop: '-4px',
              borderRight: 'none',
              borderTop: 'none',
            }),
          }}
        />
      </div>
    </div>
  );
};

export default Tooltip;
