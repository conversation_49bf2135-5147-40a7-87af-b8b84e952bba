import React, { useState, useEffect } from 'react';
import RightDrawer from './RightDrawer';
import Icon, { IconType } from './Icon';
import Tooltip from './Tooltip';
import { BatchConfig } from '../types/index';

interface SettingsDrawerProps {
  /** 是否显示抽屉 */
  isOpen: boolean;
  /** 关闭抽屉回调 */
  onClose: () => void;
  /** 当前配置 */
  config: BatchConfig;
  /** 配置变更回调 */
  onConfigChange: (config: BatchConfig) => void;
  /** 日志记录器 */
  logger: any;
}

/**
 * 系统设置抽屉组件
 * 提供系统配置、性能监控、数据管理等功能
 */
const SettingsDrawer: React.FC<SettingsDrawerProps> = ({
  isOpen,
  onClose,
  config,
  onConfigChange,
  logger,
}) => {
  const [localConfig, setLocalConfig] = useState<BatchConfig>(config);

  // 检测配置是否有变化
  const hasChanges = JSON.stringify(localConfig) !== JSON.stringify(config);

  // 同步外部配置变化到本地状态
  useEffect(() => {
    setLocalConfig(config);
  }, [config]);

  // 处理配置变更 - 增强验证
  const handleConfigChange = (
    section: keyof BatchConfig,
    key: string,
    value: any,
  ) => {
    try {
      // 对数值类型进行验证
      if (
        typeof value === 'number' ||
        (typeof value === 'string' && !isNaN(Number(value)))
      ) {
        const numValue = Number(value);

        // 根据不同字段设置合理的范围
        if (key === 'concurrent' && (numValue < 1 || numValue > 50)) {
          logger.warning('并发数应在 1-50 之间');
          return;
        }
        if (key === 'batchSize' && (numValue < 1 || numValue > 1000)) {
          logger.warning('批次大小应在 1-1000 之间');
          return;
        }
        if (key === 'timeout' && (numValue < 1000 || numValue > 300000)) {
          logger.warning('超时时间应在 1000-300000 毫秒之间');
          return;
        }
        if (key === 'maxRetries' && (numValue < 0 || numValue > 10)) {
          logger.warning('最大重试次数应在 0-10 之间');
          return;
        }
        if (key === 'rateLimit' && (numValue < 1 || numValue > 1000)) {
          logger.warning('速率限制应在 1-1000 之间');
          return;
        }
        if (
          key === 'delayBetweenRequests' &&
          (numValue < 0 || numValue > 60000)
        ) {
          logger.warning('请求间延迟应在 0-60000 毫秒之间');
          return;
        }

        value = numValue;
      }

      const newConfig = {
        ...localConfig,
        [section]: {
          ...localConfig[section],
          [key]: value,
        },
      };
      setLocalConfig(newConfig);
      console.log(`[SettingsDrawer] 配置更新: ${section}.${key} = ${value}`);
    } catch (error) {
      logger.error(
        `配置更新失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      console.error('[SettingsDrawer] 配置更新失败:', error);
    }
  };

  // 保存配置
  const handleSave = () => {
    try {
      onConfigChange(localConfig);
      logger.success('系统配置已保存');
      console.log('[SettingsDrawer] 配置保存成功:', localConfig);
    } catch (error) {
      logger.error(
        `保存配置失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      console.error('[SettingsDrawer] 配置保存失败:', error);
    }
  };

  // 重置配置到发布时的默认值
  const handleReset = () => {
    try {
      // 发布时的默认配置
      const DEFAULT_CONFIG = {
        api: {
          endpoint: 'https://gen-ui.bytedance.net/agent/api/apaas/v2/chat',
          workflowId: 'fc02f6eb-26db-4c63-be62-483ab8abce34',
          timeout: 30000,
          maxRetries: 3,
          rateLimit: 10,
        },
        processing: {
          concurrent: 5,
          batchSize: 10,
          delayBetweenRequests: 2000,
          enableCache: true,
          skipExistingFiles: true,
        },
      };

      setLocalConfig(DEFAULT_CONFIG);
      logger.success('配置已重置为发布时的默认值');
      console.log('[SettingsDrawer] 配置重置成功:', DEFAULT_CONFIG);
    } catch (error) {
      logger.error(
        `重置配置失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      console.error('[SettingsDrawer] 配置重置失败:', error);
    }
  };

  // 导出配置
  const handleExport = () => {
    try {
      const configJson = JSON.stringify(localConfig, null, 2);
      const blob = new Blob([configJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `batch-processor-config-${new Date().toISOString().slice(0, 10)}.json`;
      a.click();
      URL.revokeObjectURL(url);
      logger.success('配置已导出到下载文件夹');
      console.log('[SettingsDrawer] 配置导出成功:', localConfig);
    } catch (error) {
      logger.error(
        `导出配置失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      console.error('[SettingsDrawer] 配置导出失败:', error);
    }
  };

  // 导入配置
  const handleImport = () => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = '.json';
      input.onchange = e => {
        const file = (e.target as HTMLInputElement).files?.[0];
        if (file) {
          const reader = new FileReader();
          reader.onload = event => {
            try {
              const importedConfig = JSON.parse(event.target?.result as string);
              // 验证配置格式
              if (importedConfig.api && importedConfig.processing) {
                setLocalConfig(importedConfig);
                logger.success('配置导入成功');
                console.log('[SettingsDrawer] 配置导入成功:', importedConfig);
              } else {
                throw new Error('配置文件格式不正确');
              }
            } catch (parseError) {
              logger.error(
                `解析配置文件失败: ${parseError instanceof Error ? parseError.message : String(parseError)}`,
              );
              console.error('[SettingsDrawer] 配置解析失败:', parseError);
            }
          };
          reader.readAsText(file);
        }
      };
      input.click();
    } catch (error) {
      logger.error(
        `导入配置失败: ${error instanceof Error ? error.message : String(error)}`,
      );
      console.error('[SettingsDrawer] 配置导入失败:', error);
    }
  };

  return (
    <RightDrawer
      isOpen={isOpen}
      onClose={onClose}
      title="系统设置"
      width="620px" // 与其他抽屉保持一致的宽度
      theme="settings"
    >
      <div className="settings-drawer-content-compact">
        {/* API 配置 */}
        <div className="enhanced-settings-card group">
          <div className="card-header">
            <h3 className="card-title">
              <Icon type="external-link" color="primary" size="md" />
              <span>API 配置</span>
            </h3>
            <div className="card-status-indicator api-status" />
          </div>

          <div className="card-content">
            {/* API 端点和工作流 ID - 使用更宽的布局 */}
            <div className="form-section">
              <div className="form-group full-width">
                <Tooltip
                  content="API 服务的完整端点地址，用于发送处理请求。确保地址正确且服务可访问。"
                  position="top"
                >
                  <label className="enhanced-form-label">
                    <Icon type="external-link" size="xs" />
                    API 端点
                  </label>
                </Tooltip>
                <div className="input-wrapper">
                  <input
                    type="text"
                    value={localConfig.api.endpoint}
                    onChange={e =>
                      handleConfigChange('api', 'endpoint', e.target.value)
                    }
                    className="enhanced-form-input"
                    placeholder="请输入 API 端点地址"
                  />
                  <div className="input-status-indicator" />
                </div>
              </div>

              <div className="form-group full-width">
                <Tooltip
                  content="工作流的唯一标识符，用于指定要执行的具体处理流程。请确保ID正确。"
                  position="top"
                >
                  <label className="enhanced-form-label">
                    <Icon type="grid" size="xs" />
                    工作流 ID
                  </label>
                </Tooltip>
                <div className="input-wrapper">
                  <input
                    type="text"
                    value={localConfig.api.workflowId}
                    onChange={e =>
                      handleConfigChange('api', 'workflowId', e.target.value)
                    }
                    className="enhanced-form-input"
                    placeholder="请输入工作流 ID"
                  />
                  <div className="input-status-indicator" />
                </div>
              </div>
            </div>

            {/* 数值配置 - 使用双列布局 */}
            <div className="form-section">
              <h4 className="section-title">
                <Icon type="sliders" size="sm" />
                性能参数
              </h4>

              {/* 第一行：超时时间 + 最大重试次数 */}
              <div className="form-row">
                <div className="form-group numeric-card">
                  <Tooltip
                    content="单个请求的最大等待时间。超过此时间将被视为失败。建议设置为150秒。"
                    position="top"
                  >
                    <label className="enhanced-form-label">
                      <Icon type="clock" size="xs" />
                      超时时间
                    </label>
                  </Tooltip>
                  <div className="input-wrapper numeric enhanced">
                    <input
                      type="number"
                      value={localConfig.api.timeout}
                      onChange={e =>
                        handleConfigChange('api', 'timeout', e.target.value)
                      }
                      min="30000"
                      max="300000"
                      step="1000"
                      className="enhanced-form-input numeric"
                      placeholder="150000"
                    />
                    <span className="input-unit">毫秒</span>
                  </div>
                  <div className="field-hint">
                    <Icon type="info" size="xs" />
                    推荐: 150000ms
                  </div>
                </div>

                <div className="form-group numeric-card">
                  <Tooltip
                    content="请求失败时的自动重试次数。设置为0表示不重试，最多可设置10次。"
                    position="top"
                  >
                    <label className="enhanced-form-label">
                      <Icon type="refresh" size="xs" />
                      最大重试次数
                    </label>
                  </Tooltip>
                  <div className="input-wrapper numeric enhanced">
                    <input
                      type="number"
                      value={localConfig.api.maxRetries}
                      onChange={e =>
                        handleConfigChange('api', 'maxRetries', e.target.value)
                      }
                      min="0"
                      max="10"
                      className="enhanced-form-input numeric"
                      placeholder="3"
                    />
                    <span className="input-unit">次</span>
                  </div>
                  <div className="field-hint">
                    <Icon type="info" size="xs" />
                    推荐: 3次
                  </div>
                </div>
              </div>

              {/* 第二行：速率限制（单独一行，因为只有一个） */}
              <div className="form-row">
                <div className="form-group numeric-card">
                  <Tooltip
                    content="每分钟允许发送的最大请求数量，用于控制API调用频率，避免触发限流。"
                    position="top"
                  >
                    <label className="enhanced-form-label">
                      <Icon type="gauge" size="xs" />
                      速率限制
                    </label>
                  </Tooltip>
                  <div className="input-wrapper numeric enhanced">
                    <input
                      type="number"
                      value={localConfig.api.rateLimit}
                      onChange={e =>
                        handleConfigChange('api', 'rateLimit', e.target.value)
                      }
                      min="1"
                      max="1000"
                      className="enhanced-form-input numeric"
                      placeholder="10"
                    />
                    <span className="input-unit">次/分钟</span>
                  </div>
                  <div className="field-hint">
                    <Icon type="info" size="xs" />
                    推荐: 10次/分钟
                  </div>
                </div>
                {/* 空的第二列 */}
                <div className="form-group"></div>
              </div>
            </div>
          </div>
        </div>

        {/* 处理配置 */}
        <div className="enhanced-settings-card group">
          <div className="card-header">
            <h3 className="card-title">
              <Icon type="processing" color="primary" size="md" />
              <span>处理配置</span>
            </h3>
            <div className="card-status-indicator processing-status" />
          </div>

          <div className="card-content">
            {/* 性能配置 */}
            <div className="form-section">
              <h4 className="section-title">
                <Icon type="zap" size="sm" />
                性能参数
              </h4>

              {/* 第一行：最大并发数 + 批次大小 */}
              <div className="form-row">
                <div className="form-group numeric-card">
                  <Tooltip
                    content="同时处理的最大任务数量。数值越大处理越快，但会增加系统负载。建议根据设备性能调整。"
                    position="top"
                  >
                    <label className="enhanced-form-label">
                      <Icon type="layers" size="xs" />
                      最大并发数
                    </label>
                  </Tooltip>
                  <div className="input-wrapper numeric enhanced">
                    <input
                      type="number"
                      value={localConfig.processing.concurrent}
                      onChange={e =>
                        handleConfigChange(
                          'processing',
                          'concurrent',
                          e.target.value,
                        )
                      }
                      min="1"
                      max="50"
                      className="enhanced-form-input numeric"
                      placeholder="5"
                    />
                    <span className="input-unit">个</span>
                  </div>
                  <div className="field-hint">
                    <Icon type="info" size="xs" />
                    推荐: 5个
                  </div>
                </div>

                <div className="form-group numeric-card">
                  <Tooltip
                    content="每个批次处理的数据条数。较大的批次可以提高效率，但会增加内存使用。"
                    position="top"
                  >
                    <label className="enhanced-form-label">
                      <Icon type="package" size="xs" />
                      批次大小
                    </label>
                  </Tooltip>
                  <div className="input-wrapper numeric enhanced">
                    <input
                      type="number"
                      value={localConfig.processing.batchSize}
                      onChange={e =>
                        handleConfigChange(
                          'processing',
                          'batchSize',
                          e.target.value,
                        )
                      }
                      min="1"
                      max="1000"
                      className="enhanced-form-input numeric"
                      placeholder="10"
                    />
                    <span className="input-unit">条</span>
                  </div>
                  <div className="field-hint">
                    <Icon type="info" size="xs" />
                    推荐: 10条
                  </div>
                </div>
              </div>

              {/* 第二行：请求间延迟（单独一行） */}
              <div className="form-row">
                <div className="form-group numeric-card">
                  <Tooltip
                    content="每个请求之间的等待时间。增加延迟可以减少服务器压力，避免被限流。"
                    position="top"
                  >
                    <label className="enhanced-form-label">
                      <Icon type="timer" size="xs" />
                      请求间延迟
                    </label>
                  </Tooltip>
                  <div className="input-wrapper numeric enhanced">
                    <input
                      type="number"
                      value={localConfig.processing.delayBetweenRequests}
                      onChange={e =>
                        handleConfigChange(
                          'processing',
                          'delayBetweenRequests',
                          e.target.value,
                        )
                      }
                      min="0"
                      max="60000"
                      step="100"
                      className="enhanced-form-input numeric"
                      placeholder="2000"
                    />
                    <span className="input-unit">毫秒</span>
                  </div>
                  <div className="field-hint">
                    <Icon type="info" size="xs" />
                    推荐: 2000ms
                  </div>
                </div>
                {/* 空的第二列 */}
                <div className="form-group"></div>
              </div>
            </div>

            {/* 功能开关 */}
            <div className="form-section">
              <div className="toggle-grid">
                <div className="form-group toggle">
                  <Tooltip
                    content="启用后会缓存请求结果，相同查询将直接返回缓存结果，提高处理速度并减少API调用。"
                    position="top"
                  >
                    <div className="enhanced-toggle-wrapper">
                      <input
                        type="checkbox"
                        id="enableCache"
                        checked={localConfig.processing.enableCache}
                        onChange={e =>
                          handleConfigChange(
                            'processing',
                            'enableCache',
                            e.target.checked,
                          )
                        }
                        className="enhanced-toggle-input"
                      />
                      <label
                        htmlFor="enableCache"
                        className="enhanced-toggle-label"
                      >
                        <div className="toggle-switch">
                          <div className="toggle-slider" />
                        </div>
                        <div className="toggle-content">
                          <div className="toggle-title">
                            <Icon type="database" size="sm" />
                            启用缓存
                          </div>
                          <div className="toggle-description">
                            优化性能，减少重复请求
                          </div>
                        </div>
                      </label>
                    </div>
                  </Tooltip>
                </div>

                <div className="form-group toggle">
                  <Tooltip
                    content="启用后会检查文件是否已存在，如果存在则跳过处理，避免重复工作和意外覆盖。"
                    position="top"
                  >
                    <div className="enhanced-toggle-wrapper">
                      <input
                        type="checkbox"
                        id="skipExistingFiles"
                        checked={localConfig.processing.skipExistingFiles}
                        onChange={e =>
                          handleConfigChange(
                            'processing',
                            'skipExistingFiles',
                            e.target.checked,
                          )
                        }
                        className="enhanced-toggle-input"
                      />
                      <label
                        htmlFor="skipExistingFiles"
                        className="enhanced-toggle-label"
                      >
                        <div className="toggle-switch">
                          <div className="toggle-slider" />
                        </div>
                        <div className="toggle-content">
                          <div className="toggle-title">
                            <Icon type="shield" size="sm" />
                            跳过已存在文件
                          </div>
                          <div className="toggle-description">
                            避免覆盖已处理文件
                          </div>
                        </div>
                      </label>
                    </div>
                  </Tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 底部操作按钮 - 优化版 */}
        <div className="settings-drawer-footer">
          <div className="footer-actions">
            <button
              type="button"
              onClick={handleReset}
              disabled={!hasChanges}
              className="action-button btn-authority btn-secondary-glass"
              title={hasChanges ? "重置所有设置到默认值" : "没有可重置的更改"}
              aria-label="重置设置"
            >
              <Icon type="refresh" size="xs" />
              <span>重置</span>
            </button>
            <button
              type="button"
              onClick={handleExport}
              className="action-button btn-authority btn-secondary-glass"
              title="导出当前配置到文件"
              aria-label="导出配置"
            >
              <Icon type="download" size="xs" />
              <span>导出</span>
            </button>
            <button
              type="button"
              onClick={handleImport}
              className="action-button btn-authority btn-secondary-glass"
              title="从文件导入配置"
              aria-label="导入配置"
            >
              <Icon type="upload" size="xs" />
              <span>导入</span>
            </button>
            <button
              type="button"
              onClick={handleSave}
              disabled={!hasChanges}
              className="action-button primary btn-authority btn-secondary-glass"
              title={hasChanges ? "保存当前设置" : "没有需要保存的更改"}
              aria-label="保存设置"
            >
              <Icon type="check" size="xs" />
              <span>保存</span>
            </button>
          </div>
        </div>
      </div>
    </RightDrawer>
  );
};

export default SettingsDrawer;
