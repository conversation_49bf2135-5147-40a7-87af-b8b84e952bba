/**
 * Font Awesome Lynx Framework Integration - Claude 4 Guide
 * Font Awesome Lynx 框架集成 - Claude 4 专用指南
 *
 * @version 6.8.0
 * @updated 2025-01-09
 * @framework Lynx Only
 */

export const FONT_AWESOME_KNOWLEDGE = `Font Awesome Lynx 框架专用指南

## 🎯 1. 字体配置 (CRITICAL)

必须在 TTSS 中通过 \`@font-face\` 引入字体文件。

\`\`\`ttss
@font-face {
  font-family: 'font-awesome-icon';
  src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
}
\`\`\`

## 📱 2. 核心使用方法

在 Lynx 中，有两种主要方式使用 Font Awesome 图标：

### 方法一：在 TTML 中使用字符实体 (推荐)

直接在 \`<text>\` 标签内使用 HTML/XML 字符实体。这是最直接、最语义化的方式。

- **格式**: \`&#x{Unicode十六进制码};\`
- **示例**:
  \`\`\`ttml
  <!-- 直接在 text 标签中使用字符实体 -->
  <text style="font-family: 'font-awesome-icon'; font-size: 32rpx;">&#xf015;</text> <!-- home 图标 -->
  <text style="font-family: 'font-awesome-icon'; font-size: 32rpx;">&#xf002;</text> <!-- search 图标 -->
  \`\`\`

### 方法二：在 TTSS 中使用 content 属性

通过定义 TTSS 类，使用 CSS 转义序列设置 \`content\` 属性。

- **格式**: \`content: '\\{Unicode十六进制码}';\`
- **示例**:
  \`\`\`ttss
  /* 1. 定义基础图标类 */
  .fa-icon {
    font-family: 'font-awesome-icon';
  }

  /* 2. 定义具体图标 */
  .fa-home::before { content: '\\f015'; }
  .fa-search::before { content: '\\f002'; }
  \`\`\`
  \`\`\`ttml
  <!-- 在 ttml 中应用 class -->
  <text class="fa-icon fa-home" style="font-size: 32rpx;"></text>
  <text class="fa-icon fa-search" style="font-size: 32rpx;"></text>
  \`\`\`

### 图标尺寸与动态渲染

- **尺寸控制**: 始终使用 \`rpx\` 单位，通过 \`font-size\` 样式控制图标大小。
  \`\`\`ttss
  .icon-sm { font-size: 24rpx; }
  .icon-md { font-size: 32rpx; }
  .icon-lg { font-size: 40rpx; }
  \`\`\`
- **动态渲染**: 结合 \`tt:for\` 和 \`tt:if\` 进行动态渲染。
  \`\`\`ttml
  <view tt:for="{{iconList}}">
    <!-- 推荐动态渲染字符实体 -->
    <text style="font-family: 'font-awesome-icon';">{{'&#x' + item.unicode + ';' }}</text>
  </view>
  <text tt:if="{{showHome}}" style="font-family: 'font-awesome-icon';">&#xf015;</text>
  \`\`\`

## 📜 3. 规则与最佳实践 (Claude 4 必须遵守)

### 绝对禁止 (Strictly Forbidden)
- **严禁使用 Emoji**: 任何 Emoji 表情符号都不允许出现在代码中。
- **严禁使用 HTML 标签**: 禁止使用 \`<i>\`, \`<span>\` 等任何非 Lynx 标准的 HTML 标签。
- **严禁使用外部 CDN**: 禁止引用除指定 TTF 文件外的任何 Web 字体 (如 woff, woff2)。
- **严禁使用复杂 CSS**: 禁止使用 CSS 变量等高级特性。

### 推荐做法 (Recommended)
- **组件**: 始终使用 \`<text>\` 组件作为图标的唯一容器。
- **单位**: 尺寸和间距等必须使用 \`rpx\` 单位。
- **字体**: 必须将 \`font-family\` 设置为 \`'font-awesome-icon'\`。
- **方法**: 优先使用字符实体 (\`&#xf015;\`)，其次是 TTSS 类。
- **动态化**: 使用 \`tt:if\` 和 \`tt:for\` 进行条件和列表渲染。

### 总结：Claude 4 行为指导
1.  **只用 \`<text>\`**: 图标只能放在 \`<text>\` 标签里。
2.  **指定字体**: 必须应用 \`font-family: 'font-awesome-icon'\`。
3.  **使用字符实体**: 主要通过 \`&#xf015;\` 的方式来指定图标。
4.  **RPX 单位**: 所有尺寸都用 \`rpx\`。
5.  **语义化**: 根据上下文语义选择最合适的图标。
`;

export default FONT_AWESOME_KNOWLEDGE;