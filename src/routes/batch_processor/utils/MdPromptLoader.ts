/**
 * Markdown Prompt Loader - 严格版本
 * 禁止任何回退机制，所有错误必须抛出
 * 根据用户要求："禁止存在回退机制！！！！！任何error都需要抛出！！1"
 */

// MD模块映射表 - 只映射实际存在的文件
const MD_MODULES_MAP = {
  componentMapping: () => import('./lynx-prompts-md/ComponentMapping.md?raw'),
  coreArchitecture: () => import('./lynx-prompts-md/CoreArchitecture.md?raw'),
  designSystem: () => import('./lynx-prompts-md/DesignSystem.md?raw'),
  eventSystem: () => import('./lynx-prompts-md/EventSystem.md?raw'),
  styleSystem: () => import('./lynx-prompts-md/StyleSystem.md?raw')
} as const;

// 内容缓存
const contentCache = new Map<string, string>();

/**
 * 加载单个MD文件内容 - 严格版本
 * 任何错误都直接抛出，不提供任何回退机制
 */
async function loadMdContent(moduleKey: keyof typeof MD_MODULES_MAP): Promise<string> {
  // 检查缓存
  if (contentCache.has(moduleKey)) {
    return contentCache.get(moduleKey)!;
  }

  // 动态导入 MD 文件
  const moduleImporter = MD_MODULES_MAP[moduleKey];
  if (!moduleImporter) {
    throw new Error(`Unknown module: ${moduleKey}`);
  }

  // 使用构建工具的 ?raw 后缀直接获取文件内容
  const module = await moduleImporter(); // 不捕获错误，直接抛出
  const content = (module as any).default || '';

  if (!content) {
    throw new Error(`Empty content for module: ${moduleKey}`);
  }

  // 缓存内容
  contentCache.set(moduleKey, content);
  
  return content;
}

/**
 * 加载多个MD文件内容 - 严格版本
 */
async function loadMultipleMdContents(moduleKeys: (keyof typeof MD_MODULES_MAP)[]): Promise<string> {
  const contents = await Promise.all(
    moduleKeys.map(key => loadMdContent(key))
  );
  
  return contents.join('\n\n');
}

/**
 * 获取完整的 Lynx Prompt 内容 (Markdown 版本) - 严格版本
 * 禁止任何回退机制，任何错误都直接抛出
 */
export async function getMasterLevelLynxPromptFromMd(): Promise<string> {
  console.log('[MdPromptLoader] 开始加载 Markdown 版本 - 严格模式');
  
  // 直接执行，不捕获错误
  const availableFiles = await getAvailableModules();
  
  // 核心功能模块
  const coreFiles = availableFiles.filter(key => 
    ['coreArchitecture', 'componentMapping', 'eventSystem'].includes(key)
  );
  
  // 设计和样式模块  
  const designFiles = availableFiles.filter(key => 
    ['designSystem', 'styleSystem'].includes(key)
  );
  
  // 并行加载所有内容 - 任何失败都会直接抛出
  const [coreContent, designContent] = await Promise.all([
    loadMultipleMdContents(coreFiles),
    loadMultipleMdContents(designFiles)
  ]);
  
  // 加载 LightChart 集成内容
  const { LYNX_UTILS_SYSTEM } = await import('../prompts/LynxUtilsSystem');
  
  // 组装最终内容
  const finalContent = `# Lynx框架移动端UI设计专家

基于Template-Assembler v3.0.8完整规则和@byted-lynx/web-speedy-plugin映射系统，生成企业级TTML、TTSS和JavaScript代码。

<!-- === 核心功能模块 === -->

${coreContent}

<!-- === 设计和样式模块 === -->

${designContent}

<!-- === LIGHTCHART集成 === -->

${LYNX_UTILS_SYSTEM}`;
  
  console.log('[MdPromptLoader] Markdown版本加载完成');
  return finalContent;
}

/**
 * 获取特定模块的 Prompt 内容 - 严格版本
 */
export async function getModulePromptFromMd(moduleKey: string): Promise<string> {
  if (!(moduleKey in MD_MODULES_MAP)) {
    throw new Error(`Unknown module key: ${moduleKey}`);
  }
  
  return await loadMdContent(moduleKey as keyof typeof MD_MODULES_MAP);
}

/**
 * 获取自定义组合的 Prompt 内容 - 严格版本
 */
export async function getCustomPromptFromMd(moduleKeys: string[]): Promise<string> {
  // 验证所有模块键
  const invalidKeys = moduleKeys.filter(key => !(key in MD_MODULES_MAP));
  if (invalidKeys.length > 0) {
    throw new Error(`Invalid module keys: ${invalidKeys.join(', ')}`);
  }
  
  const validKeys = moduleKeys as (keyof typeof MD_MODULES_MAP)[];
  return await loadMultipleMdContents(validKeys);
}

/**
 * 检查可用的模块 - 严格版本
 */
export async function getAvailableModules(): Promise<(keyof typeof MD_MODULES_MAP)[]> {
  const availableModules: (keyof typeof MD_MODULES_MAP)[] = [];
  
  for (const moduleKey of Object.keys(MD_MODULES_MAP) as (keyof typeof MD_MODULES_MAP)[]) {
    // 直接调用，不捕获错误
    const content = await loadMdContent(moduleKey);
    if (content) {
      availableModules.push(moduleKey);
    }
  }
  
  return availableModules;
}

/**
 * 检查 MD 文件的可用性 - 严格版本
 */
export async function checkMdFilesAvailability(): Promise<{
  available: string[];
  unavailable: string[];
}> {
  const available: string[] = [];
  const unavailable: string[] = [];
  
  for (const moduleKey of Object.keys(MD_MODULES_MAP)) {
    const content = await loadMdContent(moduleKey as keyof typeof MD_MODULES_MAP);
    if (content && content.trim()) {
      available.push(moduleKey);
    } else {
      unavailable.push(moduleKey);
    }
  }
  
  return { available, unavailable };
}

/**
 * 预加载所有可用的 MD 模块 - 严格版本
 */
export async function preloadAllMdModules(): Promise<void> {
  console.log('[MdPromptLoader] 开始预加载所有MD模块 - 严格模式');
  
  const moduleKeys = Object.keys(MD_MODULES_MAP) as (keyof typeof MD_MODULES_MAP)[];
  
  // 并行预加载所有模块 - 任何失败都会直接抛出
  await Promise.all(
    moduleKeys.map(key => loadMdContent(key))
  );
  
  console.log('[MdPromptLoader] 所有MD模块预加载完成');
}

/**
 * 清理缓存 - 严格版本
 */
export function clearCache(): void {
  contentCache.clear();
  console.log('[MdPromptLoader] 缓存已清理');
}