#!/usr/bin/env node

/**
 * 反引号转义修复脚本
 * Fix Backtick Escaping Script
 * 
 * 批量修复TypeScript文件中的反引号转义问题
 * - 将 \\`\\`\\` 修复为 \`\`\`
 * - 将 \\` 修复为 \`
 * - 保持文件的其他内容不变
 */

const fs = require('fs');
const path = require('path');

// 配置
const CONFIG = {
  // 要处理的目录
  targetDirs: [
    '../prompts/components',
    '../prompts/styles', 
    '../prompts/events',
    '../prompts/api',
    '../prompts/advanced',
    '../prompts/examples',
    '../prompts/core'
  ],
  
  // 文件扩展名过滤
  extensions: ['.ts', '.js'],
  
  // 备份选项
  createBackup: true,
  backupSuffix: '.backup',
  
  // 输出选项
  verbose: true,
  dryRun: false // 设为 true 时只显示会修改什么，不实际修改
};

// 修复规则
const FIXES = [
  {
    name: '修复代码块反引号（三个）',
    pattern: /\\`\\`\\`/g,
    replacement: '```',
    description: '将 \\`\\`\\` 替换为 ```'
  },
  {
    name: '修复单个反引号',
    pattern: /\\`([^`\\]*)`/g,
    replacement: '`$1`',
    description: '将 \\`content` 替换为 `content`'
  },
  {
    name: '修复不匹配的反引号转义',
    pattern: /\\`([^`\\]*?)\\`/g,
    replacement: '`$1`',
    description: '将 \\`content\\` 替换为 `content`'
  }
];

// 统计信息
const stats = {
  filesProcessed: 0,
  filesModified: 0,
  totalFixes: 0,
  fixesByType: {},
  errors: []
};

/**
 * 主函数
 */
async function main() {
  console.log('🔧 反引号转义修复脚本启动');
  console.log('=====================================');
  
  if (CONFIG.dryRun) {
    console.log('🔍 运行模式: DRY RUN (仅预览，不实际修改)');
  }
  
  console.log('📂 目标目录:', CONFIG.targetDirs);
  console.log('📄 文件类型:', CONFIG.extensions);
  console.log('');
  
  // 初始化统计
  FIXES.forEach(fix => {
    stats.fixesByType[fix.name] = 0;
  });
  
  // 处理每个目录
  for (const dir of CONFIG.targetDirs) {
    await processDirectory(dir);
  }
  
  // 输出统计结果
  printStats();
}

/**
 * 处理目录
 */
async function processDirectory(dirPath) {
  const fullPath = path.resolve(dirPath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️ 目录不存在: ${dirPath}`);
    return;
  }
  
  console.log(`📁 处理目录: ${dirPath}`);
  
  try {
    const files = fs.readdirSync(fullPath);
    
    for (const file of files) {
      const filePath = path.join(fullPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isFile() && shouldProcessFile(file)) {
        await processFile(filePath);
      }
    }
  } catch (error) {
    console.error(`❌ 处理目录失败: ${dirPath}`, error.message);
    stats.errors.push(`目录处理失败: ${dirPath} - ${error.message}`);
  }
}

/**
 * 判断是否应该处理该文件
 */
function shouldProcessFile(filename) {
  return CONFIG.extensions.some(ext => filename.endsWith(ext));
}

/**
 * 处理单个文件
 */
async function processFile(filePath) {
  stats.filesProcessed++;
  
  try {
    // 读取文件内容
    const content = fs.readFileSync(filePath, 'utf-8');
    let modifiedContent = content;
    let fileModified = false;
    let fileFixes = 0;
    
    // 应用所有修复规则
    for (const fix of FIXES) {
      const beforeLength = modifiedContent.length;
      const matchCount = (modifiedContent.match(fix.pattern) || []).length;
      
      if (matchCount > 0) {
        modifiedContent = modifiedContent.replace(fix.pattern, fix.replacement);
        const fixesApplied = matchCount;
        
        fileFixes += fixesApplied;
        stats.totalFixes += fixesApplied;
        stats.fixesByType[fix.name] += fixesApplied;
        fileModified = true;
        
        if (CONFIG.verbose) {
          console.log(`  ✓ ${fix.name}: ${fixesApplied} 个修复`);
        }
      }
    }
    
    // 如果文件被修改了
    if (fileModified) {
      stats.filesModified++;
      
      const relativePath = path.relative(process.cwd(), filePath);
      console.log(`📝 ${relativePath}: ${fileFixes} 个修复`);
      
      if (!CONFIG.dryRun) {
        // 创建备份
        if (CONFIG.createBackup) {
          const backupPath = filePath + CONFIG.backupSuffix;
          fs.writeFileSync(backupPath, content, 'utf-8');
          if (CONFIG.verbose) {
            console.log(`  💾 备份创建: ${path.basename(backupPath)}`);
          }
        }
        
        // 写入修复后的内容
        fs.writeFileSync(filePath, modifiedContent, 'utf-8');
      }
    } else if (CONFIG.verbose) {
      const relativePath = path.relative(process.cwd(), filePath);
      console.log(`✅ ${relativePath}: 无需修复`);
    }
    
  } catch (error) {
    console.error(`❌ 处理文件失败: ${filePath}`, error.message);
    stats.errors.push(`文件处理失败: ${filePath} - ${error.message}`);
  }
}

/**
 * 输出统计信息
 */
function printStats() {
  console.log('');
  console.log('📊 处理统计');
  console.log('=====================================');
  console.log(`📄 处理文件数: ${stats.filesProcessed}`);
  console.log(`📝 修改文件数: ${stats.filesModified}`);
  console.log(`🔧 总修复数: ${stats.totalFixes}`);
  console.log('');
  
  if (stats.totalFixes > 0) {
    console.log('🔍 修复详情:');
    Object.entries(stats.fixesByType).forEach(([fixName, count]) => {
      if (count > 0) {
        console.log(`  • ${fixName}: ${count} 个`);
      }
    });
    console.log('');
  }
  
  if (stats.errors.length > 0) {
    console.log('❌ 错误列表:');
    stats.errors.forEach(error => console.log(`  • ${error}`));
    console.log('');
  }
  
  if (CONFIG.dryRun) {
    console.log('🔍 这是 DRY RUN 模式，没有实际修改文件');
    console.log('💡 要实际执行修复，请设置 CONFIG.dryRun = false');
  } else if (stats.filesModified > 0) {
    console.log('✅ 反引号转义修复完成！');
    if (CONFIG.createBackup) {
      console.log(`💾 备份文件已创建（后缀: ${CONFIG.backupSuffix}）`);
    }
  } else {
    console.log('✅ 所有文件都没有反引号转义问题');
  }
}

/**
 * 错误处理
 */
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 运行脚本
if (require.main === module) {
  main().catch(error => {
    console.error('💥 脚本执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  main,
  processFile,
  processDirectory,
  CONFIG,
  FIXES,
  stats
};