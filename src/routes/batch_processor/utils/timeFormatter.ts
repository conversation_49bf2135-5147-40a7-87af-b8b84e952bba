/**
 * timeFormatter.ts
 * 用于格式化时间和计算时间差的工具函数
 */

/**
 * 格式化时间为可读字符串
 * @param timestamp 时间戳(毫秒)
 * @param format 格式选项: 'full'(完整日期时间), 'date'(仅日期), 'time'(仅时间), 'relative'(相对时间)
 */
export function formatTime(
  timestamp: number,
  format: 'full' | 'date' | 'time' | 'relative' = 'full',
): string {
  if (!timestamp) {
    return '未知时间';
  }

  const date = new Date(timestamp);

  switch (format) {
    case 'date':
      return date.toLocaleDateString();
    case 'time':
      return date.toLocaleTimeString();
    case 'relative':
      return getRelativeTimeString(timestamp);
    case 'full':
    default:
      return date.toLocaleString();
  }
}

/**
 * 计算相对时间(如"5分钟前", "昨天"等)
 */
export function getRelativeTimeString(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;

  // 将毫秒转换为秒
  const seconds = Math.floor(diff / 1000);

  if (seconds < 60) {
    return `${seconds}秒前`;
  }

  // 分钟
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    return `${minutes}分钟前`;
  }

  // 小时
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    return `${hours}小时前`;
  }

  // 天
  const days = Math.floor(hours / 24);
  if (days < 7) {
    if (days === 1) {
      return '昨天';
    }
    return `${days}天前`;
  }

  // 周
  const weeks = Math.floor(days / 7);
  if (weeks < 5) {
    return `${weeks}周前`;
  }

  // 月
  const months = Math.floor(days / 30);
  if (months < 12) {
    return `${months}个月前`;
  }

  // 年
  const years = Math.floor(days / 365);
  return `${years}年前`;
}

/**
 * 格式化处理时间(毫秒)为可读格式
 */
export function formatDuration(ms: number): string {
  if (ms < 0) {
    return '0秒';
  }

  if (ms < 1000) {
    return `${ms}毫秒`;
  }

  const seconds = Math.floor(ms / 1000);
  if (seconds < 60) {
    return `${seconds}秒`;
  }

  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  if (minutes < 60) {
    if (remainingSeconds === 0) {
      return `${minutes}分钟`;
    }
    return `${minutes}分${remainingSeconds}秒`;
  }

  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  if (remainingMinutes === 0) {
    return `${hours}小时`;
  }
  return `${hours}小时${remainingMinutes}分钟`;
}

/**
 * 计算剩余时间估计
 * @param completed 已完成数量
 * @param total 总数量
 * @param elapsedMs 已经过时间(毫秒)
 * @returns 估计剩余时间(毫秒)
 */
export function estimateRemainingTime(
  completed: number,
  total: number,
  elapsedMs: number,
): number {
  if (completed <= 0 || total <= 0 || completed > total) {
    return 0;
  }

  // 计算每项任务的平均时间
  const avgTimePerItem = elapsedMs / completed;

  // 计算剩余项数
  const remaining = total - completed;

  // 估计剩余时间
  return avgTimePerItem * remaining;
}

export default {
  formatTime,
  getRelativeTimeString,
  formatDuration,
  estimateRemainingTime,
};
