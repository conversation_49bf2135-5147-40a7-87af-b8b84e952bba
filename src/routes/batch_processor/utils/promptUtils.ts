/**
 * promptUtils.ts
 * 提示词处理工具函数
 */

/**
 * 格式化相对时间
 */
export function formatRelativeTime(timestamp: number): string {
  const now = Date.now();
  const diff = now - timestamp;

  const minute = 60 * 1000;
  const hour = 60 * minute;
  const day = 24 * hour;
  const week = 7 * day;
  const month = 30 * day;

  if (diff < minute) {
    return '刚刚';
  } else if (diff < hour) {
    const minutes = Math.floor(diff / minute);
    return `${minutes}分钟前`;
  } else if (diff < day) {
    const hours = Math.floor(diff / hour);
    return `${hours}小时前`;
  } else if (diff < week) {
    const days = Math.floor(diff / day);
    return `${days}天前`;
  } else if (diff < month) {
    const weeks = Math.floor(diff / week);
    return `${weeks}周前`;
  } else {
    const months = Math.floor(diff / month);
    return `${months}个月前`;
  }
}

/**
 * 截取文本预览
 */
export function truncateText(text: string, maxLength = 100): string {
  if (text.length <= maxLength) {
    return text;
  }

  // 在单词边界截断
  const truncated = text.substring(0, maxLength);
  const lastSpaceIndex = truncated.lastIndexOf(' ');

  if (lastSpaceIndex > maxLength * 0.8) {
    return `${truncated.substring(0, lastSpaceIndex)}...`;
  }

  return `${truncated}...`;
}

/**
 * 清理文本内容（移除多余空白字符）
 */
export function cleanText(text: string): string {
  return text
    .replace(/\r\n/g, '\n') // 统一换行符
    .replace(/\n{3,}/g, '\n\n') // 移除多余的空行
    .trim();
}

/**
 * 比较两个提示词内容是否相同
 */
export function comparePromptContent(
  content1: string,
  content2: string,
): boolean {
  return cleanText(content1) === cleanText(content2);
}

/**
 * 从提示词内容提取标题
 */
export function extractTitleFromContent(content: string): string {
  const lines = content.trim().split('\n');

  for (const line of lines) {
    const cleanLine = line
      .replace(/^#+\s*/, '') // 移除 markdown 标题标记
      .replace(/[*_`]/g, '') // 移除 markdown 格式标记
      .replace(/^[-*+]\s*/, '') // 移除列表标记
      .trim();

    if (cleanLine.length > 0) {
      // 截取前30个字符作为标题
      return cleanLine.length > 30
        ? `${cleanLine.substring(0, 30)}...`
        : cleanLine;
    }
  }

  return '未命名提示词';
}

/**
 * 验证提示词内容
 */
export function validatePromptContent(content: string): {
  isValid: boolean;
  error?: string;
} {
  if (!content || typeof content !== 'string') {
    return {
      isValid: false,
      error: '提示词内容不能为空',
    };
  }

  const cleanedContent = cleanText(content);

  if (cleanedContent.length === 0) {
    return {
      isValid: false,
      error: '提示词内容不能为空',
    };
  }

  if (cleanedContent.length > 50000) {
    return {
      isValid: false,
      error: '提示词内容过长，请控制在50000字符以内',
    };
  }

  return { isValid: true };
}

/**
 * 验证提示词标题
 */
export function validatePromptTitle(title: string): {
  isValid: boolean;
  error?: string;
} {
  if (!title || typeof title !== 'string') {
    return {
      isValid: false,
      error: '标题不能为空',
    };
  }

  const cleanedTitle = title.trim();

  if (cleanedTitle.length === 0) {
    return {
      isValid: false,
      error: '标题不能为空',
    };
  }

  if (cleanedTitle.length > 100) {
    return {
      isValid: false,
      error: '标题过长，请控制在100字符以内',
    };
  }

  return { isValid: true };
}

/**
 * 高亮搜索关键词
 */
export function highlightSearchTerm(text: string, searchTerm: string): string {
  if (!searchTerm || searchTerm.trim().length === 0) {
    return text;
  }

  const regex = new RegExp(
    `(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`,
    'gi',
  );
  return text.replace(regex, '<mark>$1</mark>');
}

/**
 * 计算文本相似度（简单的字符串相似度算法）
 */
export function calculateSimilarity(str1: string, str2: string): number {
  const clean1 = cleanText(str1).toLowerCase();
  const clean2 = cleanText(str2).toLowerCase();

  if (clean1 === clean2) {
    return 1;
  }
  if (clean1.length === 0 || clean2.length === 0) {
    return 0;
  }

  // 使用简单的编辑距离算法
  const matrix = Array(clean2.length + 1)
    .fill(null)
    .map(() => Array(clean1.length + 1).fill(null));

  for (let i = 0; i <= clean1.length; i++) {
    matrix[0][i] = i;
  }

  for (let j = 0; j <= clean2.length; j++) {
    matrix[j][0] = j;
  }

  for (let j = 1; j <= clean2.length; j++) {
    for (let i = 1; i <= clean1.length; i++) {
      const indicator = clean1[i - 1] === clean2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator, // substitution
      );
    }
  }

  const maxLength = Math.max(clean1.length, clean2.length);
  return 1 - matrix[clean2.length][clean1.length] / maxLength;
}

/**
 * 生成提示词摘要
 */
export function generatePromptSummary(
  content: string,
  maxLength = 200,
): string {
  const cleaned = cleanText(content);
  const lines = cleaned.split('\n').filter(line => line.trim().length > 0);

  let summary = '';
  let currentLength = 0;

  for (const line of lines) {
    const trimmedLine = line.trim();
    if (currentLength + trimmedLine.length > maxLength) {
      if (summary.length > 0) {
        summary += '...';
      } else {
        summary = `${trimmedLine.substring(0, maxLength - 3)}...`;
      }
      break;
    }

    if (summary.length > 0) {
      summary += ' ';
    }
    summary += trimmedLine;
    currentLength += trimmedLine.length + 1;
  }

  return summary || '空提示词';
}

/**
 * 检查提示词是否包含敏感内容（简单检查）
 */
export function checkSensitiveContent(content: string): {
  hasSensitive: boolean;
  warnings: string[];
} {
  const warnings: string[] = [];
  const lowerContent = content.toLowerCase();

  // 简单的敏感词检查
  const sensitivePatterns = [
    /password|密码/i,
    /api[_\s]*key|密钥/i,
    /token|令牌/i,
    /secret|机密/i,
  ];

  for (const pattern of sensitivePatterns) {
    if (pattern.test(lowerContent)) {
      warnings.push('检测到可能包含敏感信息，请确认是否需要保存');
      break;
    }
  }

  return {
    hasSensitive: warnings.length > 0,
    warnings,
  };
}
