/**
 * Template-Assembler 完整配置映射规则补充文件
 * 基于真实 template-assembler 配置文件分析的详细映射规则
 * 
 * 版本: 3.0.0
 * 更新: 2024-01-15
 * 来源: /Users/<USER>/repos/template-assembler/example/showcases/
 */

// Template-Assembler完整映射规则总结 (基于真实配置文件)
export const TEMPLATE_ASSEMBLER_MAPPING_RULES = {
  // 配置文件映射规则
  configMapping: {
    lynx: {
      projectType: 'lynx',
      abilities: {
        lynx: { 
          reactDSL: true, 
          targetSdkVersion: '1.4' 
        },
        image: { 
          limit: '2KB', 
          name: 'resource/[name].[hash:8].[ext]' 
        }
      },
      output: 'output/',
      features: ['ReactDSL编译', '原生组件指令', 'SDK版本控制'],
      description: 'Lynx原生端编译配置，支持React DSL到原生组件的完整转换'
    },
    web: {
      projectType: 'static',
      abilities: {
        lynx: true,
        html: { 
          template: 'src/index.html' 
        },
        sass: { 
          postCssLoader: { 
            plugins: ['pxtransform'] 
          } 
        },
        vendor: ['/node_modules/']
      },
      output: 'build/',
      features: ['H5平台转换', 'PostCSS处理', 'Webpack开发服务器'],
      description: 'Web H5端编译配置，支持原生组件到Web标准的完整转换'
    }
  },
  
  // 编译入口映射规则 (基于真实getCompileInput函数)
  entryMapping: {
    lynx: [
      // 功能示例模块
      'feature-example/event',
      'feature-example/event_module', 
      'feature-example/lepusfc',
      'feature-example/hooks',
      'feature-example/props',
      'feature-example/props_child',
      'feature-example/setstate',
      
      // 测试用例模块
      'test/lifecycle',
      
      // 内置组件模块
      'internal-component/main',
      'internal-component/swiper',
      'internal-component/texts',
      'internal-component/form',
      'internal-component/scrollview',
      'internal-component/picker',
      'internal-component/images',
      'internal-component/list',
      
      // 引用测试用例
      'ref-test-cases/no-loop/card-component',
      'ref-test-cases/no-loop/custom-component',
      'ref-test-cases/single-loop/card-component',
      'ref-test-cases/single-loop/custom-component',
      'ref-test-cases/multiple-loop/card-component',
      'ref-test-cases/multiple-loop/custom-component'
    ],
    web: [
      // 页面模板映射 (path.resolve形式)
      'pages/main/template',
      'pages/swiper/template',
      'pages/texts/template',
      'pages/form/template',
      'pages/scrollview/template',
      'pages/picker/template',
      'pages/images/template',
      'pages/list/template'
    ],
    mappingPattern: {
      lynx: 'compileEntry[]',
      web: '{ "pages/*/template": path.resolve(process.cwd(), "./src/pages/*/index") }'
    }
  },
  
  // PostCSS转换规则 (pxtransform完整配置)
  transformRules: {
    pxtransform: {
      platform: 'h5',
      purpose: '像素单位转换',
      target: 'responsive design',
      config: {
        designWidth: 750,
        deviceRatio: {
          '640': 2.34 / 2,
          '750': 1,
          '828': 1.81 / 2
        },
        unitPrecision: 5,
        propBlackList: [],
        propWhiteList: [],
        exclude: /node_modules/,
        selectorBlackList: [],
        minPixelValue: 0,
        mediaQuery: false
      }
    },
    imageOptimization: {
      limit: 2048, // 2KB
      encoding: 'base64 inline or file reference',
      naming: '[name].[hash:8].[ext]',
      supportedFormats: ['png', 'jpg', 'jpeg', 'gif', 'svg', 'webp']
    },
    sassProcessing: {
      postCssLoader: true,
      plugins: ['pxtransform'],
      scssVariables: true,
      autoprefixer: false // Lynx不使用浏览器前缀
    }
  },

  // 开发环境配置映射
  devEnvironment: {
    lynx: {
      intermediates: true, // 产出中间产物
      hotReload: false,
      debugging: true
    },
    web: {
      openBrowser: {
        enable: true,
        url: 'http://localhost:4000/webpack-dev-server'
      },
      hotReload: true,
      devServer: {
        port: 4000,
        host: 'localhost'
      }
    }
  },

  // 构建能力映射 (abilities详细说明)
  buildCapabilities: {
    shared: {
      image: {
        limit: '2 * 1024', // 2KB
        name: 'resource/[name].[hash:8].[ext]',
        formats: ['png', 'jpg', 'gif', 'svg', 'webp']
      },
      typescript: {
        compilerMode: 'babel',
        target: 'es5',
        jsx: 'react'
      }
    },
    lynxSpecific: {
      reactDSL: true,
      targetSdkVersion: '1.4',
      nativeComponents: true,
      templateAssembler: true
    },
    webSpecific: {
      html: {
        template: 'src/index.html',
        inject: true,
        minify: true
      },
      sass: {
        implementation: 'sass',
        sourceMap: true
      },
      vendor: ['/node_modules/'],
      splitChunks: true
    }
  }
};

// 双轨配置对比表格数据
export const DUAL_TRACK_CONFIG_COMPARISON = {
  configFiles: {
    lynx: 'lynx.config.js',
    web: 'web.config.js'
  },
  
  differences: [
    {
      aspect: 'Project Type',
      lynx: 'lynx',
      web: 'static',
      description: '项目类型决定编译目标平台'
    },
    {
      aspect: 'Input Source',
      lynx: 'getCompileInput({ compileEntry })',
      web: 'Object mapping with path.resolve()',
      description: '输入源的获取方式不同'
    },
    {
      aspect: 'Output Directory',
      lynx: 'output/',
      web: 'build/',
      description: '输出目录命名规范'
    },
    {
      aspect: 'React DSL Support',
      lynx: 'abilities.lynx.reactDSL: true',
      web: 'abilities.lynx: true',
      description: 'React DSL支持级别不同'
    },
    {
      aspect: 'HTML Template',
      lynx: 'Not applicable',
      web: 'abilities.html.template',
      description: 'Web端需要HTML模板'
    },
    {
      aspect: 'Sass Processing',
      lynx: 'Not applicable', 
      web: 'abilities.sass.postCssLoader',
      description: 'Web端需要PostCSS处理'
    },
    {
      aspect: 'Vendor Dependencies',
      lynx: 'Not applicable',
      web: 'abilities.vendor: [/node_modules/]',
      description: 'Web端需要处理第三方依赖'
    },
    {
      aspect: 'Development Features',
      lynx: 'intermediates: true',
      web: 'openBrowser + devServer',
      description: '开发环境功能差异'
    }
  ],

  similarities: [
    {
      aspect: 'Image Processing',
      config: 'abilities.image.limit + abilities.image.name',
      description: '图片处理配置完全一致'
    },
    {
      aspect: 'TypeScript Support',
      config: 'abilities.ts.compilerMode: "babel"',
      description: 'TypeScript编译配置一致'
    }
  ]
};

// 编译流程映射
export const COMPILATION_PROCESS_MAPPING = {
  lynxFlow: [
    'React DSL源码',
    'Template-Assembler解析',
    'AST语法树构建',
    'Lynx组件指令生成',
    '原生平台渲染指令',
    'iOS/Android原生渲染'
  ],
  
  webFlow: [
    'React DSL源码',
    'Template-Assembler解析', 
    'HTML/CSS/JS转换',
    'PostCSS pxtransform处理',
    'Webpack打包优化',
    '浏览器标准渲染'
  ],

  sharedSteps: [
    'React DSL解析',
    'Template-Assembler核心处理',
    '组件映射转换',
    '样式计算优化'
  ],

  optimizations: {
    lynx: [
      '字符串池化',
      'JS字节码编译',
      '原生组件复用',
      'SDK版本控制'
    ],
    web: [
      'pxtransform响应式转换',
      'Webpack代码分割',
      '浏览器缓存优化',
      'Tree Shaking'
    ]
  }
};

// Platform服务映射 (基于LynxService+Mapping.m)
export const PLATFORM_SERVICE_MAPPING = {
  serviceTypes: {
    http: {
      lynx: 'LynxHTTPService',
      web: 'fetch API / XMLHttpRequest',
      mapping: 'Network service abstraction'
    },
    image: {
      lynx: 'LynxImageService', 
      web: 'Image loading + Canvas API',
      mapping: 'Image processing service'
    },
    video: {
      lynx: 'LynxVideoService',
      web: 'HTML5 Video API',
      mapping: 'Video playback service'
    },
    canvas: {
      lynx: 'LynxCanvasService',
      web: 'Canvas 2D/WebGL Context',
      mapping: 'Graphics rendering service'
    },
    storage: {
      lynx: 'LynxStorageService',
      web: 'localStorage/sessionStorage',
      mapping: 'Data persistence service'
    },
    timer: {
      lynx: 'LynxTimerService',
      web: 'setTimeout/setInterval',
      mapping: 'Timer and scheduler service'
    }
  },

  protocolImplementations: {
    description: '服务类型到协议实现的映射',
    patterns: {
      registration: 'service->protocol mapping registration',
      invocation: 'unified service call interface',
      abstraction: 'platform-specific implementation hiding'
    }
  }
};

// 实际项目结构映射 (基于真实文件结构)
export const PROJECT_STRUCTURE_MAPPING = {
  templateAssemblerStructure: {
    root: '/Users/<USER>/repos/template-assembler/',
    core: {
      'lynx/core/renderer/template_assembler.h': 'C++核心渲染引擎头文件',
      'lynx/core/renderer/template_assembler.cc': 'C++核心渲染引擎实现',
      'platform/darwin/common/lynx/service/': 'macOS平台服务实现'
    },
    examples: {
      'example/showcases/react-example/': 'React示例项目',
      'example/showcases/dynamic-component/': '动态组件示例',
      'example/showcases/playground/': '开发测试场地'
    },
    configs: {
      'react-example/web.config.js': 'Web端配置示例',
      'react-example/lynx.config.js': 'Lynx端配置示例',
      'playground/lynx.config.js': '开发环境配置'
    }
  },

  supportedPlatforms: [
    'Android',
    'iOS', 
    'macOS',
    'Windows',
    'Harmony OS',
    'Web/H5'
  ],

  architectureFeatures: [
    'Multi-platform template compilation',
    'Cross-platform service abstraction', 
    'Unified component API',
    'Performance optimization pipeline',
    'Development debugging tools'
  ]
};

// 获取完整的Template-Assembler映射规则
export function getTemplateAssemblerMappingRules() {
  return TEMPLATE_ASSEMBLER_MAPPING_RULES;
}

// 获取双轨配置对比
export function getDualTrackConfigComparison() {
  return DUAL_TRACK_CONFIG_COMPARISON;
}

// 获取编译流程映射
export function getCompilationProcessMapping() {
  return COMPILATION_PROCESS_MAPPING;
}

// 获取平台服务映射
export function getPlatformServiceMapping() {
  return PLATFORM_SERVICE_MAPPING;
}

// 获取项目结构映射
export function getProjectStructureMapping() {
  return PROJECT_STRUCTURE_MAPPING;
}

// 验证配置映射的完整性
export function validateTemplateAssemblerMapping(): {
  isValid: boolean;
  missingFeatures: string[];
  recommendations: string[];
} {
  const requiredFeatures = [
    'lynx.config.js配置',
    'web.config.js配置',
    'pxtransform转换',
    'reactDSL支持',
    '双轨编译流程',
    '平台服务映射'
  ];

  const availableFeatures = [
    'lynx.config.js配置',
    'web.config.js配置', 
    'pxtransform转换',
    'reactDSL支持',
    '双轨编译流程',
    '平台服务映射'
  ];

  const missingFeatures = requiredFeatures.filter(
    feature => !availableFeatures.includes(feature)
  );

  const recommendations = [
    '定期同步template-assembler仓库的最新配置',
    '验证编译入口映射的准确性',
    '测试双轨编译流程的完整性',
    '确保平台服务映射的时效性'
  ];

  return {
    isValid: missingFeatures.length === 0,
    missingFeatures,
    recommendations
  };
}

// 导出所有映射规则的统一接口
export const COMPLETE_TEMPLATE_ASSEMBLER_RULES = {
  mapping: TEMPLATE_ASSEMBLER_MAPPING_RULES,
  comparison: DUAL_TRACK_CONFIG_COMPARISON,
  compilation: COMPILATION_PROCESS_MAPPING,
  services: PLATFORM_SERVICE_MAPPING,
  structure: PROJECT_STRUCTURE_MAPPING,
  
  // 获取指定配置的详细信息
  getConfigDetails: (configType: 'lynx' | 'web') => {
    return TEMPLATE_ASSEMBLER_MAPPING_RULES.configMapping[configType];
  },
  
  // 获取指定平台的编译入口
  getCompileEntries: (platform: 'lynx' | 'web') => {
    return TEMPLATE_ASSEMBLER_MAPPING_RULES.entryMapping[platform];
  },
  
  // 获取PostCSS转换配置
  getTransformConfig: () => {
    return TEMPLATE_ASSEMBLER_MAPPING_RULES.transformRules.pxtransform;
  }
};