/**
 * Lynx CSS属性映射和样式系统部分
 */

export const STYLE_SYSTEM_PROMPT = `Lynx CSS属性映射规范 (ComputedCSSStyle 核心)

长度单位完整支持
- \`px\`: 物理像素，直接映射
- \`rpx\`: 响应式像素，四种转换模式
  - VW模式: 30rpx → 4.000000vw (推荐，适配性最好)
  - REM模式: 30rpx → 0.800000rem (需要根元素字体大小设置)
  - PX模式: 30rpx → 15.00px (固定转换，兼容性好)
  - CALC模式: 30rpx → calc(30 * 100vw / 750) (动态计算)
- \`rem/em\`: 相对字体大小，直接支持
- \`vh/vw\`: 视口单位，直接支持
- \`%\`: 百分比，直接支持
- \`auto\`: 自动计算，直接支持
- \`calc()\`: 计算函数，完整支持

布局属性映射 (完整Flexbox + Grid)
- \`width/height\` → ComputedCSSStyle::SetWidth()/SetHeight()
  - 支持所有长度单位、auto、fit-content、min-content、max-content
  - 响应式计算：min/max-width/height自动处理

- \`margin/padding\` → ComputedCSSStyle::SetMargin()/SetPadding()
  - 支持四方向设置: margin/padding-top/right/bottom/left
  - 简写支持: margin: 10px 20px (二值) | 10px 20px 15px (三值) | 10px 20px 15px 5px (四值)
  - 负值支持: margin: -10px (用于布局微调)

- \`position\` → ComputedCSSStyle::SetPosition()
  - 支持static|relative|absolute|fixed|sticky定位
  - 配合top/left/right/bottom使用
  - z-index层级控制（范围：-999到9999）

- \`display\` → ComputedCSSStyle::SetDisplay()
  - 完整支持: block|inline|inline-block|flex|grid|none
  - Flexbox布局: flex-direction、justify-content、align-items、flex-wrap、gap
  - Grid布局: grid-template-columns/rows、grid-gap、grid-area

- \`flex\` → ComputedCSSStyle::SetFlex()
  - flex-grow、flex-shrink、flex-basis精确控制
  - 简写语法: flex: 1 (等于flex: 1 1 0%)
  - gap属性: 项目间距，支持row-gap、column-gap

视觉属性映射 (完整颜色系统)
- \`background-color\` → ComputedCSSStyle::SetBackgroundColor()
  - 颜色格式: hex(#fff)、rgb()、rgba()、hsl()、hsla()
  - 渐变支持: linear-gradient()、radial-gradient()
  - 多重背景: 逗号分隔多个背景值

- \`color\` → ComputedCSSStyle::SetColor()
  - 文本颜色设置，支持currentColor继承
  - 透明度: rgba(255, 255, 255, 0.8)

- \`font-size\` → ComputedCSSStyle::SetFontSize()
  - 支持px、rem、em、%单位
  - 动态字体: clamp(16px, 4vw, 24px)响应式字体

- \`border\` → ComputedCSSStyle::SetBorder()
  - border-width: 1px|thin|medium|thick
  - border-style: solid|dashed|dotted|double|groove|ridge|inset|outset
  - border-color: 任何有效颜色值
  - border-radius: 圆角，支持elliptical语法

- \`box-shadow\` → ComputedCSSStyle::SetBoxShadow()
  - 语法: offset-x offset-y blur-radius spread-radius color inset
  - 多重阴影: 逗号分隔
  - 内阴影: inset关键字

Lynx专有样式属性 (Template-Assembler特有)
- \`linear-orientation\` → flex-direction
  - horizontal → row
  - vertical → column
- \`linear-weight\` → flex权重分配
- \`linear-gravity\` → align-items|justify-content
- \`relative-align-*\` → position: absolute + top/left/right/bottom计算
- \`line-spacing\` → letter-spacing (Lynx特有属性)
- \`text-stroke\` → text-stroke (文字描边效果, 禁用-webkit-前缀)
- \`text-stroke-color\` → text-stroke-color (禁用-webkit-前缀)
- \`text-stroke-width\` → text-stroke-width (禁用-webkit-前缀)`;