/**
 * Lynx JavaScript API映射和生命周期管理部分
 */

export const JAVASCRIPT_API_PROMPT = `Lynx JavaScript API映射 (原生功能调用)

Lynx类核心接口 (DOM操作映射)
\`\`\`javascript
// DOM操作 (映射到原生组件)
const element = lynx.createElement('root', 'container');
const title = lynx.getElementById('title');
const buttons = lynx.getElementsByClassName('btn');
const inputs = lynx.getElementsByTagName('input');

// 属性设置 (Element::SetAttribute调用)
element.setProperty('color', 'red');
element.setProperty('font-size', '20px');
element.setProperty('text', 'Hello World');
element.setAttribute('data-id', '123');

// 事件监听 (Element::SetEventHandler调用)
element.addEventListener('tap', (e) => {
  console.log('Element tapped!', e.detail);
  console.log('Dataset:', e.currentTarget.dataset);
});

// 动画控制 (Element::Animate调用)
element.animate([
  { opacity: 0, transform: 'scale(0.8) translateY(20px)' },
  { opacity: 1, transform: 'scale(1) translateY(0)' }
], {
  duration: 300,
  easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
  fill: 'forwards'
});

// 样式操作
element.style.setProperty('--custom-color', '#ff0000');
element.classList.add('active', 'highlighted');
element.classList.remove('disabled');
element.classList.toggle('selected');
\`\`\`

**系统API集成 (完整小程序API支持)**
\`\`\`javascript
// 定时器 (映射到原生定时器)
const timer = lynx.setTimeout(() => {
  this.updateData();
}, 1000);

lynx.clearTimeout(timer);

const interval = lynx.setInterval(() => {
  this.refreshStats();
}, 5000);

// 网络请求 (映射到原生网络层)
lynx.fetch('/api/data', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({ page: 1, size: 20 })
}).then(response => response.json())
  .then(data => this.handleData(data))
  .catch(error => this.handleError(error));

// 模块加载 (Template-Assembler机制)
const module = lynx.requireModule('/components/header', 'default', {
  timeout: 5000,
  cache: true
});

// 存储操作
lynx.setStorage('userSettings', JSON.stringify(settings));
const savedSettings = JSON.parse(lynx.getStorage('userSettings') || '{}');
lynx.removeStorage('tempData');

// 系统信息
const systemInfo = lynx.getSystemInfo();
console.log('屏幕尺寸:', systemInfo.screenWidth, systemInfo.screenHeight);
console.log('设备像素比:', systemInfo.pixelRatio);
console.log('平台:', systemInfo.platform);
\`\`\`

📊 **Card生命周期管理 (完整Lynx应用架构)**

\`\`\`javascript
Card({
  // 数据初始化 (状态管理完整架构)
  data: {
    // 业务数据层
    user: { name: '', avatar: '', level: 0, settings: {} },
    items: [],
    categories: [],
    stats: { total: 0, active: 0, pending: 0 },
    
    // 界面状态层
    loading: false,
    error: null,
    refreshing: false,
    
    // 分页状态层
    pagination: { page: 1, size: 20, total: 0, hasMore: true },
    
    // UI交互状态层
    uiState: {
      currentTab: 0,
      showModal: false,
      selectedIds: [],
      searchKeyword: '',
      sortBy: 'createTime',
      sortOrder: 'desc',
      viewMode: 'list', // list|grid|card
      animating: false,
      fullscreen: false
    },
    
    // 表单状态层
    formData: {},
    formErrors: {},
    formTouched: {}
  },

  // 计算属性 (依赖追踪)
  computed: {
    filteredItems() {
      const { items, uiState } = this.data;
      let result = items;
      
      // 关键词过滤
      if (uiState.searchKeyword) {
        const keyword = uiState.searchKeyword.toLowerCase();
        result = result.filter(item => 
          item.title.toLowerCase().includes(keyword) ||
          item.description.toLowerCase().includes(keyword)
        );
      }
      
      // 排序
      result.sort((a, b) => {
        const order = uiState.sortOrder === 'asc' ? 1 : -1;
        return (a[uiState.sortBy] > b[uiState.sortBy] ? 1 : -1) * order;
      });
      
      return result;
    },
    
    selectedItems() {
      return this.data.items.filter(item => 
        this.data.uiState.selectedIds.includes(item.id)
      );
    },
    
    isAllSelected() {
      return this.data.items.length > 0 && 
        this.data.items.every(item => 
          this.data.uiState.selectedIds.includes(item.id)
        );
    }
  },

  // 生命周期钩子 (Template-Assembler流程)
  onLoad(options) {
    console.log('页面加载，参数:', options);
    
    // 解析路由参数
    this.parseRouteParams(options);
    
    // 初始化配置
    this.initializeConfig();
    
    // 注册全局事件监听
    this.registerEventListeners();
    
    // 设置页面标题
    this.setPageTitle();
  },

  onReady() {
    console.log('页面首次渲染完成');
    
    // DOM操作初始化
    this.initializeComponents();
    
    // 开始数据加载
    this.loadInitialData();
    
    // 设置无障碍支持
    this.setupAccessibility();
  },

  onShow() {
    console.log('页面显示');
    
    // 刷新数据（如果需要）
    this.refreshDataIfNeeded();
    
    // 恢复动画和定时器
    this.resumeAnimations();
    
    // 页面曝光统计
    this.trackPageView();
  },

  onHide() {
    console.log('页面隐藏');
    
    // 暂停动画和定时器
    this.pauseAnimations();
    
    // 保存用户输入状态
    this.saveCurrentState();
    
    // 清理临时资源
    this.cleanupTempResources();
  },

  onUnload() {
    console.log('页面卸载');
    
    // 移除事件监听
    this.removeEventListeners();
    
    // 清理所有资源
    this.cleanupResources();
    
    // 页面退出统计
    this.trackPageExit();
  },

  // 事件处理最佳实践 (防重复、错误处理、数据传递)
  onTap(e) {
    const {id, action, type, index} = e.currentTarget.dataset;
    console.log('点击事件:', {id, action, type, index});
    
    // 防重复点击
    if (this.data.uiState.animating) {
      console.log('操作进行中，忽略重复点击');
      return;
    }
    
    // 设置操作状态
    this.setData({'uiState.animating': true});
    
    try {
      // 根据操作类型分发处理
      switch(action) {
        case 'select':
          this.handleItemSelect(id, e);
          break;
        case 'delete':
          this.handleItemDelete(id, e);
          break;
        case 'edit':
          this.handleItemEdit(id, e);
          break;
        case 'toggle':
          this.handleItemToggle(id, e);
          break;
        case 'preview':
          this.handleItemPreview(id, e);
          break;
        case 'share':
          this.handleItemShare(id, e);
          break;
        case 'sort':
          this.handleSort(type, e);
          break;
        case 'filter':
          this.handleFilter(type, e);
          break;
        case 'navigate':
          this.handleNavigate(type, id, e);
          break;
        default:
          console.warn('未知操作:', action);
          this.showToast('未知操作');
      }
    } catch (error) {
      console.error('操作失败:', error);
      this.handleError(error, '操作失败，请重试');
    } finally {
      // 清除操作状态
      setTimeout(() => {
        this.setData({'uiState.animating': false});
      }, 300); // 给动画时间完成
    }
  },

  // 高效数据更新策略 (批量更新、路径更新)
  updateData(updates, callback) {
    // 数据更新前的验证
    const validatedUpdates = this.validateUpdates(updates);
    
    // 批量路径更新，避免整对象覆盖
    this.setData(validatedUpdates, () => {
      console.log('数据更新完成:', Object.keys(validatedUpdates));
      
      // 触发计算属性重新计算
      this.recomputeProperties();
      
      // 调用回调
      if (callback) callback();
      
      // 数据更新统计
      this.trackDataUpdate(validatedUpdates);
    });
  },

  // 表单处理 (验证、提交、重置)
  onFormInput(e) {
    const {field} = e.currentTarget.dataset;
    const value = e.detail.value;
    
    // 更新表单数据
    this.setData({
      [\`formData.\${field}\`]: value,
      [\`formTouched.\${field}\`]: true
    });
    
    // 实时验证
    this.validateField(field, value);
  },

  onFormSubmit(e) {
    const {formData} = this.data;
    
    // 全量验证
    const errors = this.validateForm(formData);
    
    if (Object.keys(errors).length > 0) {
      this.setData({formErrors: errors});
      this.showToast('请修正表单错误');
      return;
    }
    
    // 提交表单
    this.submitForm(formData);
  },

  // 错误处理和状态恢复 (统一错误处理)
  handleError(error, context = '操作') {
    console.error(\`\${context}错误:\`, error);
    
    // 更新错误状态
    this.setData({
      error: error.message || '操作失败',
      loading: false,
      refreshing: false
    });
    
    // 显示用户友好的错误信息
    let userMessage = '操作失败，请重试';
    if (error.code === 'NETWORK_ERROR') {
      userMessage = '网络连接失败，请检查网络';
    } else if (error.code === 'AUTH_ERROR') {
      userMessage = '登录已过期，请重新登录';
    } else if (error.code === 'PERMISSION_ERROR') {
      userMessage = '权限不足，无法执行此操作';
    }
    
    this.showToast(userMessage);
    
    // 错误上报
    this.reportError(error, context);
  },

  // 性能优化方法
  optimizePerformance() {
    // 图片懒加载
    this.setupImageLazyLoad();
    
    // 列表虚拟滚动
    this.setupVirtualScroll();
    
    // 防抖搜索
    this.setupDebouncedSearch();
    
    // 预加载关键资源
    this.preloadCriticalResources();
  }
});
\`\`\``;