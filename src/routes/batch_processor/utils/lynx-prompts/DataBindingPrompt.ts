/**
 * Lynx数据绑定系统部分
 */

export const DATA_BINDING_PROMPT = `Lynx数据绑定系统 (JavaScript接口映射)

基础数据绑定语法 (模板表达式完整支持)
\`\`\`html
<!-- 简单属性绑定 -->
<text>{{message}}</text>
<text>{{user.name}}</text>
<text>{{items[0].title}}</text>
<text>{{config['api-key']}}</text>

<!-- 复杂表达式绑定 (支持完整JavaScript表达式) -->
<text>{{price * quantity}}</text>
<text>{{isVip ? 'VIP用户' : '普通用户'}}</text>
<text>{{(score >= 90) ? '优秀' : (score >= 60) ? '合格' : '不及格'}}</text>
<text>{{status === 'loading' ? '加载中...' : status === 'error' ? '加载失败' : '加载完成'}}</text>

<!-- 字符串拼接和模板字面量 -->
<text>{{'用户名: ' + user.name}}</text>
<text>{{\`\${user.name} (\${user.age}岁)\`}}</text>
<text>{{\`总价: ¥\${(price * quantity).toFixed(2)}\`}}</text>

<!-- 方法调用和计算属性 -->
<text>{{formatDate(createTime)}}</text>
<text>{{getFullName(user.firstName, user.lastName)}}</text>
<text>{{computedPrice(item.price, item.discount)}}</text>

<!-- 高级表达式 (数组方法、对象方法) -->
<text>{{items.join(', ')}}</text>
<text>{{users.filter(u => u.age > 18).length}}</text>
<text>{{user.profile?.avatar || '/default-avatar.png'}}</text>
<text>{{Object.keys(stats).length}}</text>
<text>{{Math.max(...scores)}}</text>

<!-- 内联样式绑定 -->
<view style="color: {{textColor}}; background: {{bgColor}}; font-size: {{fontSize}}px;">
<view style="transform: rotate({{angle}}deg) scale({{scale}});">

<!-- 类名动态绑定 -->
<view class="base-class {{isActive ? 'active' : ''}} {{type}} {{disabled ? 'disabled' : ''}}">
<button class="btn {{size}} {{variant}}" tt:class="{{customClass}}">
\`\`\`

**双向数据绑定规范 (model指令)**
\`\`\`html
<!-- 表单控件双向绑定 -->
<input model:value="{{inputValue}}" placeholder="请输入内容" />
<textarea model:value="{{textContent}}" placeholder="多行文本" auto-height="{{true}}" />
<switch model:checked="{{isEnabled}}" />
<slider model:value="{{sliderValue}}" min="0" max="100" />
<picker model:value="{{selectedIndex}}" range="{{options}}">

<!-- 自定义组件双向绑定 -->
<custom-component model:data="{{customData}}" />
<date-picker model:date="{{selectedDate}}" />
<rich-text model:content="{{richContent}}" />

<!-- 数组项绑定 -->
<input model:value="{{items[index].name}}" />
<switch model:checked="{{config.features[featureName]}}" />
\`\`\`

**条件渲染指令 (Template-Assembler 解析)**
\`\`\`html
<!-- 基础条件渲染 -->
<view tt:if="{{isLoggedIn}}">欢迎回来！</view>
<view tt:if="{{user && user.role === 'admin'}}">管理员专区</view>
<view tt:if="{{score >= 90}}">优秀</view>
<view tt:elif="{{score >= 80}}">良好</view>  
<view tt:elif="{{score >= 60}}">合格</view>
<view tt:else>不及格</view>

<!-- 复杂条件判断 -->
<view tt:if="{{isLoggedIn && user.status === 'active' && !user.banned}}">
  正常用户界面
</view>

<!-- 多元素条件渲染 -->
<block tt:if="{{hasData}}">
  <view>数据展示区域</view>
  <text>共{{items.length}}条记录</text>
  <button bindtap="exportData">导出数据</button>
</block>

<!-- 显隐控制区别 -->
<view hidden="{{!isVisible}}">CSS显隐控制 - DOM存在，visibility: hidden</view>
<view tt:if="{{isVisible}}">DOM条件渲染 - DOM添加/移除</view>

<!-- 嵌套条件 -->
<view tt:if="{{user}}">
  <text tt:if="{{user.avatar}}">用户头像: {{user.avatar}}</text>
  <text tt:else>无头像</text>
  <view tt:if="{{user.vip}}">
    <text>VIP用户</text>
    <text>到期时间: {{formatDate(user.vipExpire)}}</text>
  </view>
</view>
\`\`\`

**列表渲染指令 (虚拟DOM优化)**
\`\`\`html
<!-- 基础列表渲染 -->
<view tt:for="{{items}}" tt:key="{{item.id}}">
  {{index}}: {{item.name}}
</view>

<!-- 自定义变量名 -->
<view tt:for="{{items}}" tt:for-index="i" tt:for-item="product" tt:key="{{product.id}}">
  第{{i + 1}}个产品: {{product.name}} - ¥{{product.price}}
</view>

<!-- 嵌套循环 -->
<view tt:for="{{categories}}" tt:key="{{category.id}}" tt:for-item="category" tt:for-index="catIndex">
  <text>{{category.name}}</text>
  <view tt:for="{{category.items}}" tt:key="{{item.id}}" tt:for-item="item" tt:for-index="itemIndex">
    <text>{{catIndex}}-{{itemIndex}}: {{item.name}}</text>
  </view>
</view>

<!-- 数组方法结合 -->
<view tt:for="{{items.filter(item => item.visible)}}" tt:key="{{item.id}}">
  可见项目: {{item.name}}
</view>

<view tt:for="{{Object.keys(config)}}" tt:key="{{item}}" tt:for-item="key">
  配置项: {{key}} = {{config[key]}}
</view>

<!-- 性能优化：大列表虚拟渲染 -->
<list class="long-list" item-count="{{items.length}}" buffer-size="20">
  <list-item tt:for="{{visibleItems}}" tt:key="{{item.id}}" type="{{item.type}}">
    <view>{{item.title}}</view>
    <text>{{item.description}}</text>
  </list-item>
</list>
\`\`\``;