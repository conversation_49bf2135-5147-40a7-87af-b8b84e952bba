/**
 * 详细组件映射规则部分 - 从MasterLevelUIPromptLoader迁移
 */

export const COMPONENT_MAPPING_DETAIL_PROMPT = `组件映射规则完整精通

核心容器组件映射 (50+组件):
/*
=== 基础容器系列 ===
view → div(.lynx-view) {
  语义: '最基础的视图容器，类似HTML的div，支持所有布局模式',
  特性: '默认block布局，支持flex、grid、position等所有CSS布局',
  DOM: '<div class="lynx-view">',
  attributeMapping: {
    'class': 'className',
    'style': 'style', 
    'id': 'id',
    'hover-class': 'data-hover-class',
    'hover-start-time': 'data-hover-start-time',
    'hover-stay-time': 'data-hover-stay-time',
    'hover-stop-propagation': 'data-hover-stop-propagation',
    'animation': 'data-animation',
    'hidden': 'hidden',
    'bindtap': 'onClick',
    'catchtap': 'onClick',
    'data-*': 'data-*'
  },
  样式优化: {
    display: 'block',
    position: 'relative',
    boxSizing: 'border-box'
  }
}

scroll-view → div(.lynx-scroll-view) {
  语义: '可滚动视图容器，支持横向、纵向滚动',
  特性: 'overflow处理 + 滚动增强功能 + 滚动事件',
  DOM: '<div class="lynx-scroll-view">',
  attributeMapping: {
    'scroll-x': 'data-scroll-x',
    'scroll-y': 'data-scroll-y', 
    'upper-threshold': 'data-upper-threshold',
    'lower-threshold': 'data-lower-threshold',
    'scroll-top': 'scrollTop',
    'scroll-left': 'scrollLeft',
    'scroll-into-view': 'data-scroll-into-view',
    'scroll-with-animation': 'data-scroll-with-animation',
    'enable-back-to-top': 'data-enable-back-to-top',
    'enable-flex': 'data-enable-flex',
    'scroll-anchoring': 'data-scroll-anchoring',
    'refresher-enabled': 'data-refresher-enabled',
    'bindscroll': 'onScroll',
    'bindscrolltoupper': 'onScrollToUpper',
    'bindscrolltolower': 'onScrollToLower'
  },
  样式优化: {
    overflow: 'auto',
    '-webkit-overflow-scrolling': 'touch',
    scrollBehavior: 'smooth'
  }
}

swiper → div(.lynx-swiper) {
  语义: '滑块视图容器，支持轮播图、幻灯片',
  特性: '触摸滑动 + 自动播放 + 指示器',
  DOM: '<div class="lynx-swiper">',
  attributeMapping: {
    'indicator-dots': 'data-indicator-dots',
    'indicator-color': 'data-indicator-color',
    'indicator-active-color': 'data-indicator-active-color',
    'autoplay': 'data-autoplay',
    'current': 'data-current',
    'interval': 'data-interval',
    'duration': 'data-duration',
    'circular': 'data-circular',
    'vertical': 'data-vertical',
    'previous-margin': 'data-previous-margin',
    'next-margin': 'data-next-margin',
    'display-multiple-items': 'data-display-multiple-items',
    'bindchange': 'onChange',
    'bindtransition': 'onTransition',
    'bindanimationfinish': 'onAnimationFinish'
  }
}

swiper-item → div(.lynx-swiper-item) {
  语义: '滑块子项，每个滑动页面',
  特性: '滑动页面容器，支持任意内容',
  DOM: '<div class="lynx-swiper-item">',
  attributeMapping: {
    'item-id': 'data-item-id'
  },
  样式优化: {
    flex: '0 0 100%',
    width: '100%',
    height: '100%'
  }
}

movable-view → div(.lynx-movable-view) {
  语义: '可移动的视图容器',
  特性: '支持拖拽、缩放、旋转',
  DOM: '<div class="lynx-movable-view">',
  attributeMapping: {
    'direction': 'data-direction',
    'inertia': 'data-inertia',
    'out-of-bounds': 'data-out-of-bounds',
    'x': 'data-x',
    'y': 'data-y',
    'damping': 'data-damping',
    'friction': 'data-friction',
    'disabled': 'data-disabled',
    'scale': 'data-scale',
    'scale-min': 'data-scale-min',
    'scale-max': 'data-scale-max',
    'scale-value': 'data-scale-value',
    'bindchange': 'onChange',
    'bindscale': 'onScale'
  }
}

movable-area → div(.lynx-movable-area) {
  语义: '可移动区域，movable-view的父容器',
  特性: '定义movable-view的可移动范围',
  DOM: '<div class="lynx-movable-area">',
  样式优化: {
    position: 'relative',
    overflow: 'hidden'
  }
}

cover-view → div(.lynx-cover-view) {
  语义: '覆盖在原生组件上的视图容器',
  特性: '可覆盖在video、canvas、camera等原生组件上',
  DOM: '<div class="lynx-cover-view">',
  样式优化: {
    position: 'absolute',
    zIndex: 999
  }
}

cover-image → img(.lynx-cover-image) {
  语义: '覆盖在原生组件上的图片',
  特性: '可覆盖显示的图片组件',
  DOM: '<img class="lynx-cover-image">',
  attributeMapping: {
    'src': 'src',
    'bindload': 'onLoad',
    'binderror': 'onError'
  }
}

block → React.Fragment {
  语义: '逻辑分组容器，不产生DOM节点',
  特性: '纯逻辑组织，用于tt:if、tt:for等条件和循环指令',
  DOM: '无DOM节点，仅用于逻辑组织'
}

=== 高级布局组件 ===
page-container → div(.lynx-page-container) {
  语义: '页面容器，用于自定义页面切换动画',
  DOM: '<div class="lynx-page-container">',
  attributeMapping: {
    'show': 'data-show',
    'duration': 'data-duration',
    'z-index': 'style.zIndex',
    'overlay': 'data-overlay',
    'position': 'data-position',
    'round': 'data-round',
    'close-on-slide-down': 'data-close-on-slide-down',
    'bindbeforeenter': 'onBeforeEnter',
    'bindenter': 'onEnter',
    'bindafterenter': 'onAfterEnter',
    'bindbeforeleave': 'onBeforeLeave',
    'bindleave': 'onLeave',
    'bindafterleave': 'onAfterLeave',
    'bindclickoverlay': 'onClickOverlay'
  }
}

share-element → div(.lynx-share-element) {
  语义: '共享元素，用于页面间共享动画',
  DOM: '<div class="lynx-share-element">',
  attributeMapping: {
    'key': 'data-key',
    'transform': 'data-transform',
    'duration': 'data-duration'
  }
}

grid-view → div(.lynx-grid-view) {
  语义: '网格布局容器',
  DOM: '<div class="lynx-grid-view">',
  attributeMapping: {
    'column-count': 'data-column-count',
    'column-gap': 'data-column-gap',
    'row-gap': 'data-row-gap',
    'type': 'data-type'
  },
  样式优化: {
    display: 'grid',
    gridTemplateColumns: 'repeat(var(--column-count, 2), 1fr)',
    gap: 'var(--row-gap, 0) var(--column-gap, 0)'
  }
}

list-view → div(.lynx-list-view) {
  语义: '高性能列表容器',
  DOM: '<div class="lynx-list-view">',
  attributeMapping: {
    'scroll-y': 'data-scroll-y',
    'enable-back-to-top': 'data-enable-back-to-top',
    'bounces': 'data-bounces',
    'refresher-enabled': 'data-refresher-enabled',
    'refresher-threshold': 'data-refresher-threshold',
    'refresher-default-style': 'data-refresher-default-style',
    'refresher-background': 'data-refresher-background',
    'refresher-triggered': 'data-refresher-triggered',
    'bindscrolltoupper': 'onScrollToUpper',
    'bindscrolltolower': 'onScrollToLower',
    'bindrefresherrefresh': 'onRefresherRefresh',
    'bindrefresherpulling': 'onRefresherPulling',
    'bindrefresherrestore': 'onRefresherRestore',
    'bindrefresherabort': 'onRefresherAbort'
  }
}

sticky-header → div(.lynx-sticky-header) {
  语义: '粘性头部容器',
  DOM: '<div class="lynx-sticky-header">',
  attributeMapping: {
    'offset-top': 'data-offset-top'
  },
  样式优化: {
    position: 'sticky',
    top: '0',
    zIndex: 100
  }
}

sticky-section → div(.lynx-sticky-section) {
  语义: '粘性区域容器',
  DOM: '<div class="lynx-sticky-section">',
  attributeMapping: {
    'push-pinned-header': 'data-push-pinned-header'
  }
}
*/

**📝 文本显示组件映射 (20+组件)**:
/*
=== 基础文本组件 ===
text → span(.lynx-text) {
  语义: '文本显示组件，专门用于纯文本内容显示',
  特性: 'inline-block布局，支持文本样式，性能优化',
  DOM: '<span class="lynx-text">',
  attributeMapping: {
    'selectable': 'data-selectable',
    'user-select': 'style.userSelect',
    'space': 'data-space',
    'decode': 'data-decode',
    'bindtap': 'onClick',
    'bindlongpress': 'onLongPress'
  },
  spaceMapping: {
    'ensp': '中文字符空格一半大小',
    'emsp': '中文字符空格大小', 
    'nbsp': '根据字体设置的空格大小'
  },
  样式优化: {
    display: 'inline-block',
    wordBreak: 'break-all',
    whiteSpace: 'pre-wrap'
  }
}

rich-text → div(.lynx-rich-text) {
  语义: '富文本显示组件，支持HTML标签和样式',
  特性: '支持HTML标签解析、富文本渲染、自定义样式',
  DOM: '<div class="lynx-rich-text">',
  attributeMapping: {
    'nodes': 'dangerouslySetInnerHTML',
    'space': 'data-space',
    'selectable': 'data-selectable'
  },
  支持标签: [
    'div', 'p', 'span', 'strong', 'b', 'em', 'i', 'u', 'del', 's',
    'ins', 'sub', 'sup', 'br', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'hr', 'blockquote', 'pre', 'code', 'a', 'img', 'table', 'tr', 
    'td', 'th', 'tbody', 'thead', 'ol', 'ul', 'li'
  ],
  样式优化: {
    lineHeight: '1.6',
    wordBreak: 'break-word'
  }
}

label → label(.lynx-label) {
  语义: '标签组件，通常用于表单控件说明',
  特性: '可与表单控件关联，支持点击聚焦',
  DOM: '<label class="lynx-label">',
  attributeMapping: {
    'for': 'htmlFor',
    'bindtap': 'onClick'
  }
}
*/`;

export default COMPONENT_MAPPING_DETAIL_PROMPT;