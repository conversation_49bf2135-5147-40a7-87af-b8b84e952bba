/**
 * Lynx框架核心架构认知部分
 */

export const CORE_ARCHITECTURE_PROMPT = `Lynx框架核心架构认知

Lynx框架定位理解:
Lynx是字节跳动内部开发的跨平台小程序框架，支持一套代码多端运行。基于React-like语法，采用虚拟DOM和组件化开发模式。

技术栈架构:
- 模板层: 类似微信小程序的模板语法，支持条件渲染、循环渲染、事件绑定
- 样式层: TTSS (类似CSS，支持rpx响应式单位)
- 逻辑层: JavaScript (ES6+)，支持组件化和生命周期
- 数据层: 响应式数据绑定，自动UI更新

Template-Assembler v3.0.8 核心规则:
字节跳动Lynx框架使用Template-Assembler v3.0工具进行Web转换，遵循以下核心映射规则:

1. 模板汇编机制 (Template-Assembler Core)
- 将Web语法(HTML/CSS/JS)编译成原生组件指令
- 通过TemplateAssembler实现模板解析、组装、渲染
- 优化策略: 字符串池化、样式预解析、JS字节码
- 新增：智能模式选择（基础/增强）+ 词法分析器 + AST构建
- **关键能力**: reactDSL支持，targetSdkVersion版本控制
- **编译流程**: React DSL → Template Parser → Assembler → 原生指令

2. 虚拟DOM优化 (RadonElement v2.0)  
- 使用RadonElement实现高效虚拟DOM
- Element基类定义DOM元素基本接口
- ComputedCSSStyle负责CSS样式计算引擎
- LayoutNode处理布局计算
- 新增：事件处理器40+事件类型支持

3. 语法映射核心 (@byted-lynx/web-speedy-plugin完整兼容)
- HTML元素 → 原生组件的直接映射
- CSS属性 → 原生属性的精确转换
- JS事件 → 原生事件的无缝对接
- API接口 → 原生功能的高效调用
- 新增：hover-class、tagV、RPX四种转换模式

4. 双轨配置映射规范 (Template-Assembler 配置系统)
\`\`\`
lynx.config.js (原生端)           web.config.js (Web端)
├── projectType: 'lynx'      →   ├── projectType: 'static'
├── input: getCompileInput() →   ├── input: { 'pages/*/template': path }
├── output: 'output/'        →   ├── output: 'build/'
└── abilities:               →   └── abilities:
    ├── lynx: {              →       ├── lynx: true
    │   reactDSL: true       →       ├── html: { template }
    │   targetSdkVersion     →       ├── sass: { postCssLoader }
    │ }                      →       └── vendor: [/node_modules/]
    └── image: { limit, name }   →   └── image: { limit, name }
\`\`\`

**5. 编译入口映射规则 (完整模块支持)**
\`\`\`
Lynx原生编译入口                  Web H5编译入口
├── feature-example/*        →   ├── pages/main/template
├── internal-component/*     →   ├── pages/swiper/template  
├── ref-test-cases/*         →   ├── pages/texts/template
└── test/*                   →   ├── pages/form/template
                                 ├── pages/scrollview/template
                                 ├── pages/picker/template
                                 ├── pages/images/template
                                 └── pages/list/template
\`\`\`

**6. PostCSS转换管道 (pxtransform集成)**
\`\`\`javascript
// Web端样式转换
postCssLoader: {
  plugins: [
    pxtransform({
      platform: 'h5'  // 关键平台标识
    })
  ]
}
\`\`\`

**7. 渲染流水线 (企业级性能)**
\`\`\`
模板加载 → JS执行 → 虚拟DOM构建 → 样式计算 → 布局计算 → 原生渲染
        ↓
统一模板服务 → Parse5转换引擎 → iframe预览 → 在线转换验证
        ↓
lynx.config.js → ReactDSL编译 → 原生指令 → iOS/Android渲染
web.config.js  → pxtransform → HTML/CSS/JS → 浏览器渲染
\`\`\``;