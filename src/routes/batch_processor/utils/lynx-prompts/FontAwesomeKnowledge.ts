/**
 * Font Awesome Lynx Framework Integration - Claude 4 Guide
 * Font Awesome Lynx 框架集成 - Claude 4 专用指南
 *
 * 🎯 专为 Lynx 框架设计的 Font Awesome 使用指南
 * 🚫 禁止一切 HTML/CSS 语法，只使用 Lynx 语法
 *
 * @version 6.7.2
 * @updated 2025-01-08
 * @framework Lynx Only
 */

export const FONT_AWESOME_KNOWLEDGE = `Font Awesome Lynx 框架专用指南

## 🚨 TTSS字体配置 (必须强制执行)

### TTSS文件中必须包含此@font-face配置
⚠️ 警告：在每个TTSS文件开头必须添加以下字体配置，否则图标无法显示！

\\\`\\\`\\\`ttss
@font-face {
  font-family: icon;
  src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
}
\\\`\\\`\\\`

🔴 重要说明：
- 这是Font Awesome图标的唯一字体源
- 字体名称必须是'test'（不可修改）
- URL地址必须完全一致（不可替换）
- 必须放在TTSS文件的顶部位置

## 📱 Lynx图标使用规范

### 统一标准
- 全面采用Font Awesome图标库
- 严格遵循Lynx框架语法规范
- 所有图标必须使用<text>组件
- 字体名称统一使用'test'
- 图标编码使用Unicode格式
- 尺寸单位统一使用RPX

### 基础使用语法
\\\`\\\`\\\`ttml
<!-- 直接使用Unicode编码 -->
<text style="font-family: icon;">\\\\uF015</text>
<text style="font-family: icon; font-size: 32rpx;">\\\\uF002</text>

<!-- 导航组件图标 -->
<view class="nav-bar">
  <text class="nav-icon" style="font-family: icon;">\\\\uF015</text>  <!-- 首页 -->
  <text class="nav-icon" style="font-family: icon;">\\\\uF002</text>  <!-- 搜索 -->
  <text class="nav-icon" style="font-family: icon;">\\\\uF007</text>  <!-- 用户 -->
</view>

<!-- 按钮组件图标 -->
<view class="btn-group">
  <view class="btn" bindtap="handleSave">
    <text style="font-family: icon; font-size: 24rpx;">\\\\uF0C7</text>
    <text>保存</text>
  </view>
</view>

<!-- 状态指示图标 -->
<view class="status-display">
  <text tt:if="{{loading}}" style="font-family: icon; color: #999;">\\\\uF110</text>
  <text tt:elif="{{success}}" style="font-family: icon; color: #28a745;">\\\\uF00C</text>
  <text tt:else style="font-family: icon; color: #dc3545;">\\\\uF071</text>
</view>

<!-- 动态绑定使用 -->
<view tt:for="{{iconList}}">
  <text style="font-family: icon;">{{item.unicode}}</text>
</view>
\\\`\\\`\\\`

### TTSS样式定义
\\\`\\\`\\\`ttss
/* 必须的@font-face配置（放在文件顶部） */
@font-face {
  font-family: icon;
  src: url('https://lf3-static.bytednsdoc.com/obj/eden-cn/lrbseh7nuptps/search/fa-solid-900.ttf');
}

/* 图标尺寸系统 */
.icon-xs { font-family: icon; font-size: 20rpx; }
.icon-sm { font-family: icon; font-size: 24rpx; }
.icon-md { font-family: icon; font-size: 32rpx; }
.icon-lg { font-family: icon; font-size: 40rpx; }
.icon-xl { font-family: icon; font-size: 48rpx; }

/* 导航图标样式 */
.nav-icon { font-family: icon; font-size: 36rpx; }
.button-icon { font-family: icon; font-size: 24rpx; }
.title-icon { font-family: icon; font-size: 32rpx; }

/* 基础图标样式 */
.fa-icon {
  font-family: icon;
  display: inline-block;
  line-height: 1;
}
\\\`\\\`\\\`

## 🔄 常用图标Unicode表

### 导航图标
- 首页: \\\\uF015
- 搜索: \\\\uF002  
- 菜单: \\\\uF0C9
- 返回: \\\\uF060
- 关闭: \\\\uF00D

### 操作图标
- 编辑: \\\\uF044
- 删除: \\\\uF1F8
- 保存: \\\\uF0C7
- 复制: \\\\uF0C5
- 分享: \\\\uF064

### 状态图标
- 成功: \\\\uF00C
- 错误: \\\\uF00D
- 警告: \\\\uF071
- 信息: \\\\uF129
- 加载: \\\\uF110

### 内容图标
- 用户: \\\\uF007
- 文件: \\\\uF15B
- 文件夹: \\\\uF07B
- 图片: \\\\uF03E
- 视频: \\\\uF03D

## 📊 智能选择与语义匹配

### 场景自动映射
- 导航场景: 使用导航图标 (home, search, menu)
- 操作场景: 使用操作图标 (edit, delete, save)
- 状态场景: 使用状态图标 (success, error, loading)
- 内容场景: 使用内容图标 (user, file, image)

### 语义自动匹配
- 确认/成功 → \\\\uF00C
- 取消/错误 → \\\\uF00D  
- 编辑/修改 → \\\\uF044
- 删除/移除 → \\\\uF1F8
- 搜索/查找 → \\\\uF002

## 🚫 严格禁止

❌ 不使用HTML标签 (i, span, div等)
❌ 不使用CSS类名 (fa-solid, fa-regular等)
❌ 不使用Web字体文件 (woff, woff2等)
❌ 不使用CDN链接
❌ 不使用CSS变量和复杂样式
❌ 不能省略@font-face配置

## ✅ 强制要求

✅ 使用Lynx text组件
✅ 使用TTF字体文件
✅ 使用RPX单位
✅ 使用Unicode编码
✅ 使用font-family: test
✅ 使用tt:if, tt:for动态渲染
✅ TTSS文件必须包含@font-face配置

## 🎯 Claude 4 行为指导

当生成Lynx代码时必须：
1. 在TTSS文件开头添加@font-face配置
2. 只使用<text>组件显示图标
3. 只使用font-family: test
4. 只使用指定的Unicode编码
5. 只使用RPX单位控制尺寸
6. 根据语义自动选择合适图标
7. 图标与文字保持适当间距

## 🚀 性能优化与移动端适配

### 图标加载策略
1. 预加载核心图标字体文件
2. 避免频繁切换图标减少重绘
3. 使用缓存优化图标渲染性能

### 移动端适配原则
1. 最小触摸区域44rpx
2. 图标间距不小于16rpx  
3. 考虑不同设备的DPI适配
4. 支持深色模式下的图标颜色适配
`;

export default FONT_AWESOME_KNOWLEDGE;