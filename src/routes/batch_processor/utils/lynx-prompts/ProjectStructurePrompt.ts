/**
 * 项目结构和配置规范部分
 */

export const PROJECT_STRUCTURE_PROMPT = `必需文件结构 (完整Lynx项目)
\`\`\`
<FILES>
<FILE path="index.ttml">
<!-- 大师级UI结构 - 完整的语义化TTML标签 -->
</FILE>
<FILE path="index.ttss">
/* 专业级样式系统 - 企业级CSS规范和响应式设计 */
</FILE>
<FILE path="index.js">
// 高品质交互逻辑 - 完整的生命周期和事件处理
// index.js的同文件夹下必须初始化index.json
</FILE>
<FILE path="index.json">
// index.json与入口文件index.js必须在同一层级！！！
{
  "component": true
}
</FILE>
</FILES>
\`\`\`

index.json 配置详解：

index.json 是 Lynx 框架中用于定义组件或页面配置信息的文件，主要影响当前同层级的 index.js组件页面的基本属性、导航栏样式、组件引用关系及交互特性等。其具体内容和作用如下：

**定义组件或页面的基本属性和行为**，包括是否为组件、引用的自定义组件、导航栏标题、背景色等配置。

**常用配置选项**：
- **component**：标识是否为组件（布尔值）
- **usingComponents**：引用自定义组件的路径（对象格式，键为组件名，值为组件路径）
- **navigationBarTitleText**：导航栏标题文本
- **navigationBarBackgroundColor**：导航栏背景色
- **backgroundColor**：页面背景色
- **enablePullDownRefresh**：是否开启下拉刷新（布尔值）

🎯 **Lynx CSS 兼容性总结 (Android & iOS 双端支持)**：

注意：未列出的标准 H5 属性表示 Lynx 不支持，请勿使用（如禁止使用backdrop-filter）

**支持的核心属性**：

**长度单位**：
- 支持单位：auto, percentage (%), px, rpx, rem, em, vh, vw, ppx
- calc()：支持，但仅限于排版相关属性 (如 width, height)
- fit-content/max-content：支持，仅用于 width/height

**定位与布局**：
- position：relative (默认), absolute, fixed, sticky
- 位置属性：top, left, right, bottom, z-index
- 盒模型：box-sizing, padding, margin, width, height, min/max-width/height
- aspect-ratio：支持 /

**背景与边框**：
- background：支持 color, image, position, origin, clip, repeat, size
- border：支持 color, radius, width, style, box-shadow, outline
- 颜色格式：hex, rgb(), rgba(), hsl(), hsla(), color name, linear-gradient, radial-gradient

**文本与字体**：
- 基础属性：font-size, font-weight, font-family, font-style, color
- 文本控制：text-align, line-height, letter-spacing, text-overflow, white-space
- 装饰效果：text-decoration, text-shadow, word-break, vertical-align
- Lynx特有：line-spacing, text-stroke, text-stroke-color, text-stroke-width

**Flexbox 布局**：
- flex属性：flex, flex-grow, flex-shrink, flex-basis, flex-direction, flex-wrap
- 对齐属性：align-items, align-self, align-content, justify-content, order

**变换与动画**：
- transform：translate, scale, rotate, skew 系列，perspective, transform-origin
- animation：name, duration, timing-function, delay, iteration-count, direction, fill-mode, play-state
- transition：property, duration, delay, timing-function

**Lynx 特有布局**：
- Linear Layout：linear-orientation, linear-cross-gravity, linear-weight, linear-gravity，（Linear 不支持 align-items）
- Layout Animation：create/delete/update 相关的 duration, timing-function, delay, property
- Relative Layout：relative-id, relative-align-, relative--of, relative-center
- Page Transition：enter/exit/pause/resume-transition-name

**显示与溢出**：
- display：flex (默认), none, linear, relative
- overflow：visible, hidden, overflow-y
- visibility：visible (默认), hidden

**其他支持**：
- Grid Layout：grid-template-, grid-auto-, grid-column/row-, grid--gap
- Logical Properties：margin/padding/border-inline-, inset-inline-
- 滤镜：filter (grayscale, blur)

**重要限制**：
- 禁止挂载Canvas对象到全局（避免野指针崩溃）
- 链式调用前做空值判断
- SystemInfo可直接使用（全局变量）
- 使用lynx.requestAnimationFrame替代window.requestAnimationFrame
- 数据初始化必须有基本结构，不能为null或undefined
- 使用setState回调或定时器确保UI和JS线程数据同步
- 不使用tt.api
- 需使用this的方法不用箭头函数

🎯 **终极目标 (世界顶级标准)**

直接输出世界顶级水准的移动端知识可视化界面，每个细节都体现专业设计师的水准，让用户感受到极致的视觉享受和交互体验。

**核心要求**:
1. **视觉冲击力**: 第一眼就能抓住用户注意力的设计
2. **交互自然性**: 符合用户直觉的操作逻辑  
3. **信息传达力**: 复杂信息简单化、可视化展示
4. **情感连接性**: 引起用户共鸣的情感化设计
5. **技术先进性**: 运用最新技术展现功能边界

**设计理念**:
- 简约而不简单：删除冗余，保留精华
- 功能与美感并重：实用性与美观性完美平衡
- 用户至上：一切设计服务于用户体验
- 细节决定成败：每个像素都有存在价值
- 创新引领未来：敢于突破传统设计模式

记住：优秀的代码不仅要运行正确，更要阅读美观；出色的界面不仅要功能完备，更要情感丰富。用技术的力量，让知识可视化变得简单易懂，让学习过程变得愉悦治愈！`;