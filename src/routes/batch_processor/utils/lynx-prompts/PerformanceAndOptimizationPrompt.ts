/**
 * 性能优化和最佳实践部分
 */

export const PERFORMANCE_OPTIMIZATION_PROMPT = `Lynx性能优化规范

模板编译优化 (构建时优化)
- 字符串池化: 重复字符串只存储一次，减少内存占用30%
- 样式预解析: CSS在编译时解析，运行时直接应用，提升50%渲染速度
- JS字节码: JavaScript编译成字节码，提升执行效率40%
- 模板缓存: 编译结果缓存，避免重复解析，减少启动时间60%
- 依赖分析: 智能分析组件依赖，按需加载减少包体积

虚拟DOM优化 (运行时优化)
- RadonElement节点去重: 避免重复创建相同节点，节省内存
- 懒渲染标记: 屏幕外元素延迟渲染，首屏加载时间减少40%
- diff算法优化: 高效的虚拟DOM对比，更新性能提升3倍
- 批量更新: 合并多个DOM操作为单次更新，减少重排重绘
- 组件复用: 相同类型组件进行实例复用，减少创建销毁开销

渲染流水线优化 (硬件加速)
- 异步渲染: 利用RAF进行帧调度，保持60fps流畅体验
- 布局批处理: 合并布局计算，减少重排次数80%
- 样式提取: 避免运行时样式计算，直接应用预计算结果
- 硬件加速: 利用GPU加速动画和变换，CPU使用率降低50%
- 分层渲染: 复杂界面分层渲染，避免全局重绘

数据流优化 (状态管理)
- 响应式数据: 精确依赖追踪，只更新真正变化的部分
- 计算属性缓存: 计算结果缓存，避免重复计算
- 数据路径更新: 使用路径更新而非整对象替换
- 防抖节流: 高频事件使用防抖节流，减少不必要的更新
- 内存池: 对象池化技术，减少GC压力

⚡ **性能与可访问性优化 (企业级标准)**

**性能标准 (量化指标)**:
- 首屏加载: ≤ 1000ms (First Contentful Paint)
- 交互响应: ≤ 100ms (Input Delay)  
- 滚动帧率: ≥ 60fps (Frame Rate)
- 内存占用: ≤ 50MB (Memory Usage)
- 包体积: ≤ 2MB (Bundle Size)
- 图片优化: WebP格式 + 懒加载 + 渐进式
- 代码分割: 路由级 + 组件级懒加载

**可访问性标准 (WCAG 2.1 AAA级)**:
- 颜色对比: ≥ 7:1 (文字) ≥ 4.5:1 (图标)
- 键盘导航: 支持Tab键完整导航
- 屏幕阅读器: 完整的ARIA标签支持
- 字体大小: 支持200%缩放不影响功能
- 颜色信息: 不依赖颜色作为唯一信息传达方式
- 焦点指示: 清晰的焦点指示器
- 语义标签: 正确的HTML语义结构

**错误处理 (优雅降级)**:
- 网络错误: 自动重试 + 离线缓存 + 友好提示
- 接口错误: 错误码映射 + 用户友好消息
- 资源错误: 默认占位 + 重新加载选项
- 脚本错误: 错误边界 + 降级UI
- 空状态设计: 插画 + 操作建议 + 重试按钮

**资源管理 (内存优化)**:
- 图片懒加载: 交叉观察器 + 预加载策略
- 组件按需加载: 路由懒加载 + 动态import
- 内存及时释放: 定时器清理 + 事件监听器移除
- 虚拟滚动: 长列表优化 + 可视区域渲染
- 缓存策略: LRU缓存 + 过期清理

🏆 **大师级品质检验标准 (世界顶级水准)**

**界面专业度检验 (媲美顶级APP)**:
✅ 设计一致性: 统一的设计语言和视觉规范
✅ 视觉层次: 清晰的信息架构和优先级
✅ 品牌识别: 强烈的品牌特征和记忆点
✅ 创新性: 独特的设计理念和交互模式
✅ 精致度: 像素级的细节处理

**信息密度优化检验 (高效不拥挤)**:
✅ 认知负荷: 单屏信息量控制在7±2项
✅ 分层展示: 主次信息层次分明
✅ 渐进披露: 复杂信息分步骤展示
✅ 扫描友好: 符合F型阅读模式
✅ 空白利用: 合理的留白增强可读性

**排版精致度检验 (像素级完美)**:
✅ 字体层级: 完整的字体大小和权重体系
✅ 行间距: 优化的垂直韵律
✅ 对齐方式: 精确的网格对齐
✅ 间距控制: 统一的间距规范
✅ 响应式: 多尺寸屏幕完美适配

**交互流畅性检验 (手势原生感)**:
✅ 响应速度: 所有交互≤100ms响应
✅ 动画效果: 自然流畅的过渡动画
✅ 手势支持: 符合直觉的手势操作
✅ 反馈及时: 即时的视觉和触觉反馈
✅ 状态清晰: 明确的操作状态指示

**色彩和谐度检验 (专业配色)**:
✅ 色彩平衡: 主次色彩比例协调
✅ 情感传达: 色彩与内容情感匹配
✅ 对比度: 满足可访问性标准
✅ 一致性: 整体色彩体系统一
✅ 适配性: 支持深色模式和主题切换

**细节工艺检验 (微交互丰富)**:
✅ 微动画: 提升体验的细腻动效
✅ 状态变化: 清晰的状态转换
✅ 边界处理: 优雅的边界情况处理
✅ 加载体验: 流畅的加载和过渡
✅ 错误处理: 友好的错误提示和恢复`;