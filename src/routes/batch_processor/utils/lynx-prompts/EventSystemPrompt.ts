/**
 * Lynx事件系统映射规范部分
 */

export const EVENT_SYSTEM_PROMPT = `Lynx事件系统映射规范 (Element事件处理)

触摸事件映射 (移动端优化)
- \`bindtap\` → Element::SetEventHandler("tap", handler)
  - 对应原生onClick事件，支持冒泡
  - 防重复点击：300ms内多次点击会被忽略
  - dataset数据传递：data-*属性自动解析到event.currentTarget.dataset
- \`catchtap\` → 点击事件 + 阻止冒泡传播
- \`bindlongpress\` → 长按事件(800ms触发)
  - 移动端：触摸800ms触发
  - PC端：鼠标按下800ms触发
- \`bindtouchstart/move/end/cancel\` → 触摸生命周期事件
  - 提供touches、changedTouches、targetTouches数组
  - 坐标信息：pageX、pageY、clientX、clientY

表单控件事件映射 (双向绑定优化)
- \`bindinput\` → Element::SetEventHandler("input", handler)
  - 输入内容变化事件，实时触发
  - event.detail.value包含当前输入值
  - event.detail.cursor包含光标位置
- \`bindfocus\` → 焦点获得事件
  - event.detail包含焦点详细信息
- \`bindblur\` → 焦点失去事件
  - event.detail.value包含失焦时的值
- \`bindchange\` → 值确认变化事件
  - 用于picker、switch、slider等组件
- \`bindconfirm\` → 确认输入(回车键或完成按钮)
  - 移动端键盘的"完成"按钮触发

滚动容器事件映射 (性能优化)
- \`bindscroll\` → Element::SetEventHandler("scroll", handler)
  - 提供scrollTop、scrollLeft、scrollHeight、scrollWidth
  - 提供deltaX、deltaY滚动增量
  - 节流优化：最高60fps触发频率
- \`bindscrolltoupper\` → 滚动到顶部边界
  - distance属性控制触发距离（默认50px）
- \`bindscrolltolower\` → 滚动到底部边界
  - 常用于无限滚动、分页加载
- \`bindrefresherpulling/refresh/restore/abort\` → 下拉刷新系列事件
  - pulling：下拉过程中持续触发
  - refresh：达到刷新条件时触发
  - restore：刷新完成后恢复
  - abort：刷新被中断

媒体组件事件映射 (加载状态管理)
- \`bindload\` → 资源加载完成
  - 图片：提供naturalWidth、naturalHeight
  - 视频：提供duration、videoWidth、videoHeight
- \`binderror\` → 资源加载失败
  - 错误信息在event.detail.errMsg
- \`bindprogress\` → 加载进度事件
  - 提供buffered、duration等进度信息

手势检测映射 (高级交互)
- Element::SetGestureDetector(key, detector) 设置手势识别
- 支持捏合(pinch)、旋转(rotate)、长按(longpress)等复杂手势
- 手势识别精度：支持多点触控，最多10个触摸点`;