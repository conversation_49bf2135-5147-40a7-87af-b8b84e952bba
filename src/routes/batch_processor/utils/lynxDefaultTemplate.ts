/**
 * Lynx默认模板系统 - 基于优化后的MasterLevelUIPromptLoader
 * 为提示词抽屉组件提供完整的Lynx框架默认模板
 * 集成Template-Assembler v3.0和@byted-lynx/web-speedy-plugin最新规则
 */

import { getMasterLevelLynxPromptContent } from '../prompts/utils/MasterLevelUIPromptLoader';

/**
 * 默认Lynx模板类型定义
 */
export interface LynxTemplate {
  id: string;
  name: string;
  description: string;
  category: 'basic' | 'advanced' | 'enterprise' | 'demo';
  version: string;
  content: string;
  features: string[];
  estimatedTokens: number;
  exampleUsage: string;
}

/**
 * Lynx模板示例代码
 */
export const LYNX_TEMPLATE_EXAMPLES = {
  // 基础卡片示例
  basicCard: {
    ttml: `<view class="card">
  <image src="{{item.avatar}}" class="avatar" mode="aspectFill" />
  <view class="info">
    <text class="title">{{item.title}}</text>
    <text class="desc">{{item.description}}</text>
    <view class="actions">
      <button bindtap="onLike" data-id="{{item.id}}" type="primary" size="mini">
        点赞 {{item.likes}}
      </button>
    </view>
  </view>
</view>`,

    ttss: `.card {
  display: flex;
  padding: 32rpx;
  background: #ffffff;
  border-radius: 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.08);
  margin-bottom: 24rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 60rpx;
  margin-right: 24rpx;
}

.info {
  flex: 1;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 12rpx;
}

.desc {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
  margin-bottom: 24rpx;
}

.actions {
  display: flex;
  gap: 16rpx;
}`,

    js: `Card({
  data: {
    item: {
      id: '1',
      title: '示例标题',
      description: '这是一个示例描述',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b72d1e3e?w=120&q=80',
      likes: 42
    }
  },
  
  onLike(e) {
    const id = e.currentTarget.dataset.id;
    console.log('点赞项目ID:', id);
    
    this.setData({
      'item.likes': this.data.item.likes + 1
    });
    
    tt.showToast({
      title: '点赞成功',
      icon: 'success'
    });
  }
});`,
  },

  // 高级列表示例
  advancedList: {
    ttml: `<scroll-view class="container" scroll-y="{{true}}" bindscrolltolower="onLoadMore">
  <view class="header">
    <text class="title">数据列表</text>
    <view class="stats">
      <text class="count">共 {{items.length}} 项</text>
    </view>
  </view>
  
  <view class="filter-bar">
    <view class="search-box">
      <input model:value="{{searchKeyword}}" 
             placeholder="搜索内容..." 
             bindinput="onSearch" 
             class="search-input" />
    </view>
    <picker range="{{sortOptions}}" 
            bindchange="onSortChange" 
            class="sort-picker">
      <view class="picker-text">{{sortOptions[sortIndex]}}</view>
    </picker>
  </view>
  
  <view class="list">
    <view tt:for="{{filteredItems}}" 
          tt:key="{{item.id}}" 
          class="list-item {{item.highlighted ? 'highlighted' : ''}}"
          bindtap="onItemTap" 
          data-id="{{item.id}}">
      <image src="{{item.thumbnail}}" class="thumbnail" mode="aspectFit" />
      <view class="content">
        <text class="item-title">{{item.title}}</text>
        <text class="item-desc">{{item.description}}</text>
        <view class="meta">
          <text class="time">{{formatTime(item.createTime)}}</text>
          <text class="status {{item.status}}">{{getStatusText(item.status)}}</text>
        </view>
      </view>
      <view class="actions">
        <button bindtap="onEdit" 
                catchtap="true" 
                data-id="{{item.id}}" 
                size="mini" 
                type="default">编辑</button>
        <button bindtap="onDelete" 
                catchtap="true" 
                data-id="{{item.id}}" 
                size="mini" 
                type="warn">删除</button>
      </view>
    </view>
  </view>
  
  <view tt:if="{{loading}}" class="loading">
    <text>加载中...</text>
  </view>
  
  <view tt:if="{{!hasMore && items.length > 0}}" class="no-more">
    <text>没有更多数据了</text>
  </view>
  
  <view tt:if="{{items.length === 0 && !loading}}" class="empty">
    <text>暂无数据</text>
    <button bindtap="onRefresh" type="primary">刷新</button>
  </view>
</scroll-view>`,

    ttss: `.container {
  height: 100vh;
  background: #f5f5f5;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  background: #ffffff;
  border-bottom: 1rpx solid #eee;
}

.title {
  font-size: 40rpx;
  font-weight: 600;
  color: #1a1a1a;
}

.count {
  font-size: 28rpx;
  color: #666;
}

.filter-bar {
  display: flex;
  padding: 24rpx 32rpx;
  background: #ffffff;
  margin-bottom: 16rpx;
  gap: 24rpx;
}

.search-box {
  flex: 1;
}

.search-input {
  width: 100%;
  height: 80rpx;
  padding: 0 24rpx;
  background: #f8f8f8;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.sort-picker {
  padding: 20rpx 32rpx;
  background: #f8f8f8;
  border-radius: 40rpx;
}

.picker-text {
  font-size: 28rpx;
  color: #666;
}

.list {
  padding: 0 16rpx;
}

.list-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  margin-bottom: 16rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.04);
  transition: all 0.3s ease;
}

.list-item.highlighted {
  background: #f0f9ff;
  border: 2rpx solid #3b82f6;
}

.thumbnail {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 24rpx;
  background: #f0f0f0;
}

.content {
  flex: 1;
}

.item-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.item-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 16rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.time {
  font-size: 24rpx;
  color: #999;
}

.status {
  font-size: 24rpx;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  font-weight: 500;
}

.status.active {
  background: #dcfce7;
  color: #16a34a;
}

.status.pending {
  background: #fef3c7;
  color: #d97706;
}

.status.inactive {
  background: #f3f4f6;
  color: #6b7280;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  margin-left: 24rpx;
}

.loading, .no-more, .empty {
  text-align: center;
  padding: 64rpx 32rpx;
  color: #666;
}

.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}`,
  },
};

/**
 * 获取所有可用的Lynx默认模板
 */
export function getAllLynxTemplates(): LynxTemplate[] {
  return [
    {
      id: 'lynx-master-v3',
      name: 'Lynx大师级模板 v3.0',
      description:
        '基于Template-Assembler v3.0的完整企业级Lynx开发模板，包含所有最新规则和最佳实践',
      category: 'enterprise',
      version: '3.0.0',
      content: getMasterLevelLynxPromptContent(),
      features: [
        'Template-Assembler v3.0完整规则',
        '@byted-lynx/web-speedy-plugin映射表',
        'Parse5转换引擎优化',
        '40+事件类型支持',
        'RPX四种转换模式',
        '企业级性能优化',
        'Canvas高级绘图系统',
        '完整生命周期管理',
        '在线预览iframe集成',
      ],
      estimatedTokens: 15000,
      exampleUsage: '适用于企业级移动应用开发，需要完整功能和最佳性能的项目',
    },
    {
      id: 'lynx-basic-card',
      name: 'Lynx基础卡片模板',
      description: '简单易用的卡片组件模板，适合快速原型开发',
      category: 'basic',
      version: '1.0.0',
      content: `🎨 Lynx基础卡片组件模板

基于Lynx框架的简单卡片组件，展示基础用法和最佳实践。

## 使用方式
直接输出符合Lynx规范的完整代码，包含TTML、TTSS、JS三个文件。

## 核心特性
- 简洁的卡片布局
- 图片展示优化
- 基础交互事件
- 响应式设计

## 必需文件结构
<FILES>
<FILE path="index.ttml">${LYNX_TEMPLATE_EXAMPLES.basicCard.ttml}</FILE>
<FILE path="index.ttss">${LYNX_TEMPLATE_EXAMPLES.basicCard.ttss}</FILE>
<FILE path="index.js">${LYNX_TEMPLATE_EXAMPLES.basicCard.js}</FILE>
<FILE path="index.json">{"component": true}</FILE>
</FILES>`,
      features: [
        '基础TTML语法',
        '响应式TTSS样式',
        '简单事件处理',
        'RPX单位使用',
        '图片模式设置',
      ],
      estimatedTokens: 800,
      exampleUsage: '适用于学习Lynx基础语法，快速创建简单卡片组件',
    },
    {
      id: 'lynx-advanced-list',
      name: 'Lynx高级列表模板',
      description: '功能完整的列表组件模板，包含搜索、排序、分页等高级功能',
      category: 'advanced',
      version: '2.0.0',
      content: `🎨 Lynx高级列表组件模板

基于Lynx框架的高级列表组件，展示复杂交互和状态管理。

## 核心功能
- 搜索过滤
- 排序功能
- 无限滚动
- 空状态处理
- 加载状态管理

## 技术特性
- 双向数据绑定 (model:value)
- 条件渲染 (tt:if)
- 列表渲染 (tt:for)
- 事件冒泡控制 (catchtap)
- 滚动事件监听

## 必需文件结构
<FILES>
<FILE path="index.ttml">${LYNX_TEMPLATE_EXAMPLES.advancedList.ttml}</FILE>
<FILE path="index.ttss">${LYNX_TEMPLATE_EXAMPLES.advancedList.ttss}</FILE>
<FILE path="index.js">
Card({
  data: {
    items: [],
    searchKeyword: '',
    sortOptions: ['按时间排序', '按标题排序', '按状态排序'],
    sortIndex: 0,
    loading: false,
    hasMore: true
  },
  
  computed: {
    filteredItems() {
      let result = this.data.items;
      
      // 搜索过滤
      if (this.data.searchKeyword) {
        const keyword = this.data.searchKeyword.toLowerCase();
        result = result.filter(item => 
          item.title.toLowerCase().includes(keyword) ||
          item.description.toLowerCase().includes(keyword)
        );
      }
      
      return result;
    }
  },
  
  onLoad() {
    this.loadData();
  },
  
  onSearch(e) {
    this.setData({
      searchKeyword: e.detail.value
    });
  },
  
  onSortChange(e) {
    this.setData({
      sortIndex: e.detail.value
    });
    this.sortItems();
  },
  
  onItemTap(e) {
    const id = e.currentTarget.dataset.id;
    console.log('点击项目:', id);
  },
  
  onEdit(e) {
    const id = e.currentTarget.dataset.id;
    console.log('编辑项目:', id);
  },
  
  onDelete(e) {
    const id = e.currentTarget.dataset.id;
    tt.showModal({
      title: '确认删除',
      content: '确定要删除这个项目吗？',
      success: (res) => {
        if (res.confirm) {
          this.deleteItem(id);
        }
      }
    });
  },
  
  onLoadMore() {
    if (!this.data.hasMore || this.data.loading) return;
    this.loadData(true);
  },
  
  onRefresh() {
    this.setData({
      items: [],
      hasMore: true
    });
    this.loadData();
  },
  
  loadData(append = false) {
    this.setData({ loading: true });
    
    // 模拟API请求
    setTimeout(() => {
      const newItems = this.generateMockData();
      
      this.setData({
        items: append ? [...this.data.items, ...newItems] : newItems,
        loading: false,
        hasMore: newItems.length === 20
      });
    }, 1000);
  },
  
  generateMockData() {
    const mockData = [];
    for (let i = 0; i < 20; i++) {
      mockData.push({
        id: Date.now() + i,
        title: \`数据项目 \${i + 1}\`,
        description: \`这是第\${i + 1}个数据项目的描述信息\`,
        thumbnail: \`https://images.unsplash.com/photo-\${1500000000000 + i}?w=120&q=80\`,
        createTime: Date.now() - i * 86400000,
        status: ['active', 'pending', 'inactive'][i % 3],
        highlighted: i % 5 === 0
      });
    }
    return mockData;
  },
  
  formatTime(timestamp) {
    const date = new Date(timestamp);
    return \`\${date.getMonth() + 1}/\${date.getDate()}\`;
  },
  
  getStatusText(status) {
    const statusMap = {
      active: '活跃',
      pending: '待处理', 
      inactive: '非活跃'
    };
    return statusMap[status] || status;
  },
  
  sortItems() {
    const sortedItems = [...this.data.items];
    const sortType = this.data.sortIndex;
    
    sortedItems.sort((a, b) => {
      switch(sortType) {
        case 0: // 按时间
          return b.createTime - a.createTime;
        case 1: // 按标题
          return a.title.localeCompare(b.title);
        case 2: // 按状态
          return a.status.localeCompare(b.status);
        default:
          return 0;
      }
    });
    
    this.setData({ items: sortedItems });
  },
  
  deleteItem(id) {
    const items = this.data.items.filter(item => item.id !== id);
    this.setData({ items });
    
    tt.showToast({
      title: '删除成功',
      icon: 'success'
    });
  }
});
</FILE>
<FILE path="index.json">{"component": true, "styleIsolation": "isolated"}</FILE>
</FILES>`,
      features: [
        '搜索功能实现',
        '排序机制',
        '无限滚动加载',
        '状态管理',
        '空状态处理',
        '计算属性使用',
        '事件冒泡控制',
        '模拟数据生成',
      ],
      estimatedTokens: 2500,
      exampleUsage: '适用于复杂的列表页面，需要搜索、排序、分页功能的场景',
    },
    {
      id: 'lynx-demo-showcase',
      name: 'Lynx功能演示模板',
      description: '完整的Lynx功能演示，展示所有组件和特性的使用方法',
      category: 'demo',
      version: '1.5.0',
      content: `🎨 Lynx完整功能演示模板

展示Lynx框架所有核心功能和组件的综合示例。

## 演示内容
- 所有基础组件使用
- 高级交互模式
- Canvas绘图演示
- 动画效果展示
- 网络请求处理
- 系统API调用

## 组件清单
- view, text, image, button
- input, picker, switch, slider
- scroll-view, swiper, progress
- canvas, list, web-view

## 必需文件结构
<FILES>
<FILE path="index.ttml">
<scroll-view class="container" scroll-y="{{true}}">
  <view class="header">
    <text class="title">Lynx功能演示</text>
  </view>
  
  <!-- 基础组件演示 -->
  <view class="section">
    <text class="section-title">基础组件</text>
    <view class="demo-grid">
      <view class="demo-item">
        <image src="https://images.unsplash.com/photo-1500000000000?w=200&q=80" 
               class="demo-image" 
               mode="aspectFit" />
        <text class="demo-text">图片组件</text>
      </view>
      
      <view class="demo-item">
        <button bindtap="onButtonTap" type="primary">主要按钮</button>
        <button bindtap="onButtonTap" type="default" size="mini">默认按钮</button>
      </view>
    </view>
  </view>
  
  <!-- 表单组件演示 -->
  <view class="section">
    <text class="section-title">表单组件</text>
    <view class="form-demo">
      <input model:value="{{inputValue}}" 
             placeholder="请输入文本" 
             class="demo-input" />
      
      <picker range="{{pickerOptions}}" 
              bindchange="onPickerChange" 
              class="demo-picker">
        <view class="picker-display">{{pickerOptions[pickerIndex]}}</view>
      </picker>
      
      <view class="switch-row">
        <text>开关状态:</text>
        <switch model:checked="{{switchValue}}" 
                bindchange="onSwitchChange" />
      </view>
      
      <view class="slider-row">
        <text>滑块值: {{sliderValue}}</text>
        <slider model:value="{{sliderValue}}" 
                min="0" 
                max="100" 
                bindchange="onSliderChange" 
                class="demo-slider" />
      </view>
    </view>
  </view>
  
  <!-- 轮播演示 -->
  <view class="section">
    <text class="section-title">轮播组件</text>
    <swiper class="demo-swiper" 
            indicator-dots="{{true}}" 
            autoplay="{{true}}" 
            interval="3000">
      <swiper-item tt:for="{{swiperImages}}" tt:key="{{item}}">
        <image src="{{item}}" class="swiper-image" mode="aspectFill" />
      </swiper-item>
    </swiper>
  </view>
  
  <!-- 进度条演示 -->
  <view class="section">
    <text class="section-title">进度条</text>
    <progress percent="{{progressValue}}" 
              show-info="{{true}}" 
              active="{{true}}" 
              class="demo-progress" />
    <button bindtap="onProgressUpdate" type="default" size="mini">更新进度</button>
  </view>
  
  <!-- Canvas演示 -->
  <view class="section">
    <text class="section-title">Canvas绘图</text>
    <canvas name="demo-canvas" class="demo-canvas"></canvas>
    <view class="canvas-controls">
      <button bindtap="onDrawChart" type="primary" size="mini">绘制图表</button>
      <button bindtap="onClearCanvas" type="default" size="mini">清除</button>
    </view>
  </view>
</scroll-view>
</FILE>
<FILE path="index.ttss">
.container {
  height: 100vh;
  background: #f5f5f5;
}

.header {
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  text-align: center;
}

.title {
  font-size: 48rpx;
  font-weight: 600;
  color: #ffffff;
}

.section {
  margin: 32rpx 24rpx;
  padding: 32rpx;
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.06);
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1a1a1a;
  margin-bottom: 32rpx;
  display: block;
}

.demo-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24rpx;
}

.demo-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.demo-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
}

.demo-text {
  font-size: 28rpx;
  color: #666;
}

.form-demo {
  display: flex;
  flex-direction: column;
  gap: 32rpx;
}

.demo-input {
  height: 80rpx;
  padding: 0 24rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  font-size: 28rpx;
}

.demo-picker {
  height: 80rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
}

.picker-display {
  height: 100%;
  line-height: 80rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #333;
}

.switch-row, .slider-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.demo-slider {
  flex: 1;
  margin-left: 24rpx;
}

.demo-swiper {
  height: 400rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.swiper-image {
  width: 100%;
  height: 100%;
}

.demo-progress {
  margin-bottom: 24rpx;
}

.demo-canvas {
  width: 100%;
  height: 400rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
}

.canvas-controls {
  display: flex;
  gap: 16rpx;
  justify-content: center;
}
</FILE>
<FILE path="index.js">
Card({
  data: {
    inputValue: '',
    pickerOptions: ['选项1', '选项2', '选项3'],
    pickerIndex: 0,
    switchValue: false,
    sliderValue: 50,
    swiperImages: [
      'https://images.unsplash.com/photo-1500000000001?w=800&q=80',
      'https://images.unsplash.com/photo-1500000000002?w=800&q=80',
      'https://images.unsplash.com/photo-1500000000003?w=800&q=80'
    ],
    progressValue: 30,
    canvas: null,
    ctx: null
  },
  
  onReady() {
    this.initCanvas();
  },
  
  onButtonTap(e) {
    tt.showToast({
      title: '按钮被点击',
      icon: 'success'
    });
  },
  
  onPickerChange(e) {
    this.setData({
      pickerIndex: e.detail.value
    });
  },
  
  onSwitchChange(e) {
    console.log('开关状态:', e.detail.value);
  },
  
  onSliderChange(e) {
    console.log('滑块值:', e.detail.value);
  },
  
  onProgressUpdate() {
    const newValue = Math.min(100, this.data.progressValue + 20);
    this.setData({
      progressValue: newValue
    });
    
    if (newValue >= 100) {
      tt.showToast({
        title: '进度完成',
        icon: 'success'
      });
    }
  },
  
  initCanvas() {
    this.canvas = lynx.createCanvasNG();
    
    this.canvas.addEventListener("resize", ({width, height}) => {
      this.canvas.width = width * SystemInfo.pixelRatio;
      this.canvas.height = height * SystemInfo.pixelRatio;
      this.ctx = this.canvas.getContext("2d");
      this.ctx.scale(SystemInfo.pixelRatio, SystemInfo.pixelRatio);
    });
    
    this.canvas.attachToCanvasView("demo-canvas");
  },
  
  onDrawChart() {
    if (!this.ctx) return;
    
    const ctx = this.ctx;
    const width = this.canvas.width / SystemInfo.pixelRatio;
    const height = this.canvas.height / SystemInfo.pixelRatio;
    
    // 清除画布
    ctx.clearRect(0, 0, width, height);
    
    // 绘制背景
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, width, height);
    
    // 绘制柱状图
    const data = [30, 50, 80, 45, 70];
    const barWidth = width / data.length * 0.8;
    const maxValue = Math.max(...data);
    
    data.forEach((value, index) => {
      const x = index * (width / data.length) + (width / data.length - barWidth) / 2;
      const barHeight = (value / maxValue) * (height * 0.8);
      const y = height - barHeight - 20;
      
      // 绘制柱子
      ctx.fillStyle = \`hsl(\${index * 60}, 70%, 60%)\`;
      ctx.fillRect(x, y, barWidth, barHeight);
      
      // 绘制数值
      ctx.fillStyle = '#333';
      ctx.font = '14px -apple-system, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText(value.toString(), x + barWidth / 2, y - 5);
    });
    
    tt.showToast({
      title: '图表绘制完成',
      icon: 'success'
    });
  },
  
  onClearCanvas() {
    if (!this.ctx) return;
    
    const width = this.canvas.width / SystemInfo.pixelRatio;
    const height = this.canvas.height / SystemInfo.pixelRatio;
    
    this.ctx.clearRect(0, 0, width, height);
    this.ctx.fillStyle = '#f8f8f8';
    this.ctx.fillRect(0, 0, width, height);
  },
  
  onUnload() {
    if (this.canvas) {
      this.canvas.detachFromCanvasView();
    }
  }
});
</FILE>
<FILE path="index.json">{"component": true, "styleIsolation": "isolated"}</FILE>
</FILES>`,
      features: [
        '所有基础组件演示',
        '表单控件交互',
        '轮播组件使用',
        '进度条动画',
        'Canvas绘图功能',
        '完整事件处理',
        '系统API调用',
        '响应式布局',
      ],
      estimatedTokens: 4000,
      exampleUsage: '适用于学习Lynx所有功能特性，作为开发参考和演示用途',
    },
  ];
}

/**
 * 根据类型获取默认模板
 */
export function getDefaultLynxTemplate(
  category: 'basic' | 'advanced' | 'enterprise' | 'demo' = 'enterprise',
): LynxTemplate {
  const templates = getAllLynxTemplates();
  const defaultTemplate = templates.find(t => t.category === category);

  if (!defaultTemplate) {
    // 默认返回企业级模板
    return templates.find(t => t.category === 'enterprise')!;
  }

  return defaultTemplate;
}

/**
 * 获取模板使用建议
 */
export function getTemplateRecommendation(userInput: string): LynxTemplate {
  const input = userInput.toLowerCase();
  const templates = getAllLynxTemplates();

  // 关键词匹配推荐
  if (
    input.includes('列表') ||
    input.includes('搜索') ||
    input.includes('排序')
  ) {
    return templates.find(t => t.id === 'lynx-advanced-list')!;
  }

  if (
    input.includes('卡片') ||
    input.includes('简单') ||
    input.includes('基础')
  ) {
    return templates.find(t => t.id === 'lynx-basic-card')!;
  }

  if (
    input.includes('演示') ||
    input.includes('示例') ||
    input.includes('学习')
  ) {
    return templates.find(t => t.id === 'lynx-demo-showcase')!;
  }

  // 默认推荐企业级模板
  return templates.find(t => t.id === 'lynx-master-v3')!;
}

/**
 * 模板分类信息
 */
export const TEMPLATE_CATEGORIES = {
  basic: {
    name: '基础模板',
    description: '简单易用，快速上手',
    icon: '🚀',
    color: '#87ceeb',
  },
  advanced: {
    name: '高级模板',
    description: '功能丰富，适合复杂场景',
    icon: '⚡',
    color: '#3b82f6',
  },
  enterprise: {
    name: '企业级模板',
    description: '完整规范，生产环境推荐',
    icon: '🏆',
    color: '#8b5cf6',
  },
  demo: {
    name: '演示模板',
    description: '功能展示，学习参考',
    icon: '🎯',
    color: '#f59e0b',
  },
} as const;
