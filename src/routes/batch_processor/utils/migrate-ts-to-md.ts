/**
 * TS → MD 批量迁移脚本
 * 将 lynx-prompts/ 文件夹中的 TypeScript 文件转换为 Markdown 格式
 * 解决反引号转义问题，提升维护性
 */

import * as fs from 'fs';
import * as path from 'path';

// 文件映射配置
const FILE_MAPPINGS = [
  { tsFile: 'ComponentMappingPrompt.ts', mdFile: 'ComponentMapping.md', exportName: 'COMPONENT_MAPPING_PROMPT' },
  { tsFile: 'EventSystemPrompt.ts', mdFile: 'EventSystem.md', exportName: 'EVENT_SYSTEM_PROMPT' },
  { tsFile: 'StyleSystemPrompt.ts', mdFile: 'StyleSystem.md', exportName: 'STYLE_SYSTEM_PROMPT' },
  { tsFile: 'CoreArchitecturePrompt.ts', mdFile: 'CoreArchitecture.md', exportName: 'CORE_ARCHITECTURE_PROMPT' },
  { tsFile: 'DataBindingPrompt.ts', mdFile: 'DataBinding.md', exportName: 'DATA_BINDING_PROMPT' },
  { tsFile: 'JavaScriptAPIPrompt.ts', mdFile: 'JavaScriptAPI.md', exportName: 'JAVASCRIPT_API_PROMPT' },
  { tsFile: 'DesignSystemPrompt.ts', mdFile: 'DesignSystem.md', exportName: 'DESIGN_SYSTEM_PROMPT' },
  { tsFile: 'PerformanceAndOptimizationPrompt.ts', mdFile: 'Performance.md', exportName: 'PERFORMANCE_OPTIMIZATION_PROMPT' },
  { tsFile: 'ProjectStructurePrompt.ts', mdFile: 'ProjectStructure.md', exportName: 'PROJECT_STRUCTURE_PROMPT' },
  { tsFile: 'CanvasSystemPrompt.ts', mdFile: 'CanvasSystem.md', exportName: 'CANVAS_SYSTEM_PROMPT' },
  { tsFile: 'ComponentMappingDetailPrompt.ts', mdFile: 'ComponentDetail.md', exportName: 'COMPONENT_MAPPING_DETAIL_PROMPT' },
];

// 路径配置
const LYNX_PROMPTS_DIR = path.join(__dirname, 'lynx-prompts');
const LYNX_PROMPTS_MD_DIR = path.join(__dirname, 'lynx-prompts-md');

/**
 * 提取 TypeScript 文件中的 prompt 内容
 */
function extractPromptContent(tsFilePath: string, exportName: string): string {
  try {
    const content = fs.readFileSync(tsFilePath, 'utf-8');
    
    // 匹配导出的模板字符串
    const regex = new RegExp(`export const ${exportName} = \`([\\s\\S]*?)\`;`);
    const match = content.match(regex);
    
    if (match && match[1]) {
      // 处理转义字符
      let promptContent = match[1]
        .replace(/\\`/g, '`')  // 反引号转义
        .replace(/\\\\/g, '\\')  // 反斜杠转义
        .replace(/\\n/g, '\n')  // 换行符
        .replace(/\\t/g, '\t')  // 制表符
        .trim();
      
      return promptContent;
    }
    
    throw new Error(`无法在 ${tsFilePath} 中找到 ${exportName}`);
  } catch (error) {
    console.error(`提取内容失败: ${tsFilePath}`, error);
    return '';
  }
}

/**
 * 生成 Markdown 文件内容
 */
function generateMdContent(title: string, content: string, tsFileName: string): string {
  const header = `<!--
🚨 重要提醒：这是代码内容，不是普通文档！
📋 文件类型：动态加载的 Prompt 内容文件
🔧 技术用途：由 MdPromptLoader.ts 动态读取，生成 AI Prompt 内容
⚠️  严禁归档：此文件属于核心业务逻辑，删除将导致功能失效
🎯 维护说明：替代原有 ${tsFileName}，解决反引号转义问题
-->

`;

  // 如果内容已经有标题，直接使用；否则添加标题
  if (content.startsWith('#')) {
    return header + content;
  } else {
    return header + `# ${title}\n\n${content}`;
  }
}

/**
 * 批量转换文件
 */
function migrateFiles() {
  console.log('🚀 开始 TS → MD 批量迁移...\n');
  
  // 确保目标目录存在
  if (!fs.existsSync(LYNX_PROMPTS_MD_DIR)) {
    fs.mkdirSync(LYNX_PROMPTS_MD_DIR, { recursive: true });
    console.log(`✓ 创建目录: ${LYNX_PROMPTS_MD_DIR}`);
  }
  
  const results = {
    success: 0,
    failed: 0,
    skipped: 0,
    details: []
  };
  
  for (const mapping of FILE_MAPPINGS) {
    const tsFilePath = path.join(LYNX_PROMPTS_DIR, mapping.tsFile);
    const mdFilePath = path.join(LYNX_PROMPTS_MD_DIR, mapping.mdFile);
    
    try {
      // 检查 TS 文件是否存在
      if (!fs.existsSync(tsFilePath)) {
        console.log(`⚠️  跳过: ${mapping.tsFile} (文件不存在)`);
        results.skipped++;
        results.details.push({ file: mapping.tsFile, status: 'skipped', reason: '文件不存在' });
        continue;
      }
      
      // 检查 MD 文件是否已存在
      if (fs.existsSync(mdFilePath)) {
        console.log(`⚠️  跳过: ${mapping.mdFile} (MD文件已存在)`);
        results.skipped++;
        results.details.push({ file: mapping.mdFile, status: 'skipped', reason: 'MD文件已存在' });
        continue;
      }
      
      // 提取内容
      const promptContent = extractPromptContent(tsFilePath, mapping.exportName);
      if (!promptContent) {
        throw new Error('内容提取失败');
      }
      
      // 生成标题
      const title = promptContent.split('\n')[0] || mapping.mdFile.replace('.md', '');
      
      // 生成 MD 内容
      const mdContent = generateMdContent(title, promptContent, mapping.tsFile);
      
      // 写入 MD 文件
      fs.writeFileSync(mdFilePath, mdContent, 'utf-8');
      
      console.log(`✅ 转换成功: ${mapping.tsFile} → ${mapping.mdFile}`);
      results.success++;
      results.details.push({ file: mapping.mdFile, status: 'success', size: mdContent.length });
      
    } catch (error) {
      console.error(`❌ 转换失败: ${mapping.tsFile} →`, error.message);
      results.failed++;
      results.details.push({ file: mapping.tsFile, status: 'failed', error: error.message });
    }
  }
  
  // 输出汇总
  console.log('\n📊 迁移结果汇总:');
  console.log(`✅ 成功: ${results.success} 个文件`);
  console.log(`❌ 失败: ${results.failed} 个文件`);
  console.log(`⚠️  跳过: ${results.skipped} 个文件`);
  console.log(`📁 目标目录: ${LYNX_PROMPTS_MD_DIR}`);
  
  return results;
}

/**
 * 清理已转换的 TS 文件 (谨慎操作)
 */
function cleanupTsFiles(dryRun = true) {
  console.log(`\n🗑️  ${dryRun ? '模拟' : '执行'}清理已转换的 TS 文件...`);
  
  const filesToDelete = [];
  
  for (const mapping of FILE_MAPPINGS) {
    const tsFilePath = path.join(LYNX_PROMPTS_DIR, mapping.tsFile);
    const mdFilePath = path.join(LYNX_PROMPTS_MD_DIR, mapping.mdFile);
    
    // 只删除已成功转换的文件
    if (fs.existsSync(tsFilePath) && fs.existsSync(mdFilePath)) {
      filesToDelete.push(tsFilePath);
      
      if (dryRun) {
        console.log(`🔍 将删除: ${mapping.tsFile}`);
      } else {
        fs.unlinkSync(tsFilePath);
        console.log(`🗑️  已删除: ${mapping.tsFile}`);
      }
    }
  }
  
  if (dryRun) {
    console.log(`\n⚠️  这是模拟运行，实际删除请设置 dryRun = false`);
    console.log(`📝 将删除 ${filesToDelete.length} 个文件`);
  } else {
    console.log(`\n✅ 清理完成，删除了 ${filesToDelete.length} 个 TS 文件`);
  }
  
  return filesToDelete;
}

// 执行迁移
if (require.main === module) {
  const results = migrateFiles();
  
  // 如果迁移成功，询问是否清理 TS 文件
  if (results.success > 0) {
    console.log('\n🤔 是否清理已转换的 TS 文件？');
    console.log('💡 建议先验证 MD 文件正确性后再清理');
    
    // 先进行模拟清理，显示将要删除的文件
    cleanupTsFiles(true);
  }
}

export { migrateFiles, cleanupTsFiles, FILE_MAPPINGS };