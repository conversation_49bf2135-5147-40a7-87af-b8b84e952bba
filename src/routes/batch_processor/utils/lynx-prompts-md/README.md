<!--
🚨 重要提醒：这是代码内容目录，不是普通文档！
📋 文件类型：动态加载的 Prompt 内容文件目录
🔧 技术用途：所有 .md 文件由 MdPromptLoader.ts 动态读取，生成 AI Prompt 内容
⚠️  严禁归档：此目录及其下所有文件属于核心业务逻辑，删除将导致功能失效
🎯 维护说明：替代原有 lynx-prompts/ 文件夹中的 .ts 文件，解决反引号转义问题
-->

# Lynx Prompts Markdown 文件目录

## 📁 文件结构说明

本目录包含 Lynx 框架的所有 Prompt 内容文件，采用 Markdown 格式以解决 TypeScript 文件中的反引号转义问题。

### 🔄 已完成转换的文件

- ✅ `ComponentMapping.md` - DOM元素映射规范
- ✅ `EventSystem.md` - 事件系统映射规范  
- ✅ `StyleSystem.md` - CSS属性映射规范
- ✅ `CoreArchitecture.md` - 核心架构认知

### 🚧 待转换的文件

- ⏳ `DataBinding.md` - 数据绑定系统
- ⏳ `JavaScriptAPI.md` - JavaScript API映射
- ⏳ `Performance.md` - 性能优化规范
- ⏳ `ProjectStructure.md` - 项目结构配置
- ⏳ `CanvasSystem.md` - Canvas高级应用
- ⏳ `ComponentDetail.md` - 详细组件映射规则

## 🔧 技术实现

### 动态加载机制

所有 `.md` 文件通过 `MdPromptLoader.ts` 动态加载：

```typescript
// 单个文件加载
const content = await loadMdContent('./ComponentMapping.md');

// 批量组合加载
const combinedContent = await loadMultipleMdContents([
  'componentMapping',
  'eventSystem', 
  'styleSystem'
]);
```

### 向后兼容性

系统同时支持 TypeScript 和 Markdown 两种格式：

- 🟢 **Markdown 版本** (推荐): 无转义问题，维护性更好
- 🔶 **TypeScript 版本** (回退): 作为备用方案，确保兼容性

### 模板选择器

用户可以在模板选择器中选择：

1. **LYNX 大师级UI生成 (TypeScript版)** - 原有版本
2. **LYNX 大师级UI生成 (Markdown优化版)** - 推荐版本

## ⚠️ 重要说明

### 文件性质强调

**这些不是普通的文档文件！** 它们是：

- 🔧 **代码组件**: 核心业务逻辑的一部分
- 📦 **运行时依赖**: 系统运行必需的内容文件
- 🚀 **生产环境**: 直接影响 AI 生成质量
- 🛡️ **受保护内容**: 任何修改都需要谨慎测试

### 维护指南

1. **编辑规范**: 保持 Markdown 格式规范，避免语法错误
2. **内容完整性**: 确保所有技术规则和映射关系完整
3. **测试验证**: 修改后需要验证动态加载是否正常
4. **版本同步**: 如果修改内容，考虑是否需要同步更新 TypeScript 版本

## 🎯 优势总结

相比原有的 TypeScript 文件格式，Markdown 版本提供：

- ✅ **无转义问题**: 原生支持代码块和反引号
- ✅ **更好可读性**: 语法高亮和结构化显示
- ✅ **易于维护**: IDE 完整支持，预览功能
- ✅ **模块化管理**: 独立文件，便于分工维护
- ✅ **性能优化**: 支持懒加载和缓存机制