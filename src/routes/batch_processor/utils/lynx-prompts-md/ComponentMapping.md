# Lynx DOM元素映射规范

## 基础容器组件映射 (完整@byted-lynx/web-speedy-plugin兼容)

- `view` → div(.lynx-view): 基本视图容器，支持所有子元素
  - 对应RadonElement的is_view()判断
  - 支持hover-class、animation、catch-move、disable-scroll属性
  - 映射函数: Element::SetAttribute(), InsertNode(), RemoveNode()
  - 新增：tagV版本控制、web-speedy兼容模式

- `text` → span(.lynx-text): 文本显示组件，只支持文本内容
  - 对应RadonElement的is_text()判断
  - 支持selectable、user-select、decode、space属性
  - 样式计算: ComputedCSSStyle::SetColor(), SetFontSize()
  - 特殊处理：decode="{{true}}"支持&nbsp;、&lt;、&gt;、&amp;、&ensp;、&emsp;

- `image` → img(.lynx-image): 图片显示组件，无子元素
  - 对应RadonElement的is_image()判断
  - mode→objectFit映射: scaleToFill|aspectFit|aspectFill|widthFix|heightFix
  - lazy-load→loading, fade-in, webp, show-menu-by-longpress支持
  - 布局计算: LayoutNode::CalculateSize()
  - 默认样式：max-width: 100%; height: auto; display: block;

- `scroll-view` → div(.lynx-scroll-view): 滚动视图容器
  - 支持scroll-x/y、scroll-into-view、upper-threshold、lower-threshold
  - 滚动事件: bindscroll→onScroll、bindscrolltoupper/lower
  - CSS实现: overflow-x/y: auto; 
  - 性能优化: 支持scroll-anchoring、enable-passive

- `input` → input(.lynx-input): 输入框组件
  - type完整映射: text|number|idcard|digit|password|safe-password|search|tel|url|email
  - 双向绑定: model:value="{{inputValue}}"自动处理
  - 事件映射: bindinput→onInput, bindfocus→onFocus, bindblur→onBlur
  - 特殊属性: confirm-type、confirm-hold、cursor-spacing、selection-start/end

## 高级交互组件映射 (企业级支持)

- `button` → button(.lynx-button): 按钮组件
  - type映射: default|primary|warn → 对应CSS类
  - size映射: default|mini → 字体和内边距调整
  - hover-class模拟: :hover伪类 + CSS transition
  - 开放数据: open-type="getUserInfo|getPhoneNumber|contact|share"

- `swiper` → div(.lynx-swiper): 轮播容器
  - indicator-dots、autoplay、interval、duration、circular、vertical全支持
  - CSS实现: display: flex; overflow: hidden; transform: translateX()
  - JS控制: 自动播放定时器 + 触摸手势识别
  - 无障碍: 自动添加ARIA标签和键盘导航

- `progress` → progress(.lynx-progress): 进度条组件
  - percent、show-info、stroke-width、active-color、background-color
  - CSS实现: linear-gradient背景 + width百分比动画
  - 活动状态: active="{{true}}"时添加动画效果

- `picker` → select(.lynx-picker): 选择器组件
  - mode完整支持: selector|multiSelector|time|date|region
  - range数据绑定、value双向绑定
  - 自定义样式: disabled状态、placeholder显示
  - 移动端适配: 触发原生选择器或自定义浮层

- `canvas` → canvas(.lynx-canvas): 画布组件
  - name属性绑定：通过lynx.createCanvasNG()获取上下文
  - 2D/WebGL支持：getContext("2d")|getContext("webgl")
  - 响应式尺寸：width/height自动适配devicePixelRatio
  - 事件支持：bindtouchstart/move/end触摸绘制

## 列表性能优化组件

- `list` → div(.lynx-list): 高性能列表容器
  - scroll-direction: horizontal|vertical
  - item-count: 总数据条数（虚拟滚动优化）
  - buffer-size: 缓冲区大小（默认10）
  - bindscroll: 滚动事件（含scrollTop、scrollLeft、deltaX、deltaY）
  - 虚拟渲染：只渲染可见区域+缓冲区内容

- `list-item` → div(.lynx-list-item): 列表项组件
  - type属性：用于不同样式的list-item
  - data-*传递：所有data-属性直接传递给DOM
  - 复用优化：相同type的item进行DOM复用