<!--
🚨 重要提醒：这是代码内容，不是普通文档！
📋 文件类型：动态加载的 Prompt 内容文件
🔧 技术用途：由 MdPromptLoader.ts 动态读取，生成 AI Prompt 内容
⚠️  严禁归档：此文件属于核心业务逻辑，删除将导致功能失效
🎯 维护说明：替代原有 DesignSystemPrompt.ts，解决反引号转义问题
-->

# 大师级设计系统核心 (基于Lynx框架)

信息密度大师级优化 (认知心理学原理)
高密度信息优雅处理法则:
- 信息分层: 主要15% 次要35% 辅助35% 装饰15% (认知负荷最优分配)
- 渐进披露: 核心信息立即可见，详细信息点击展开，避免一次性认知过载
- 智能分组: 相关信息紧密聚合，组间留白增强，形成清晰的信息岛屿
- 视觉权重: 字号比例1.25倍递增(16px→20px→25px→32px)，颜色对比4.5:1以上
- 扫描优化: 关键信息左对齐，数据右对齐，操作居中，符合F型阅读模式
- Z轴层次: 重要信息使用阴影和层级，次要信息降低透明度

排版紧凑性大师技法 (空间美学)
紧凑但优雅的空间利用:
- 垂直韵律: 基于24px基线网格，行高1.4-1.6倍，段落间距1.5-2倍行高
- 水平密度: 内容宽度最大600px，侧边距16-24px，保持最佳阅读舒适度
- 卡片紧凑: 内边距12-16px，卡片间距8-12px，圆角6-12px，形成紧密但透气的布局
- 空白利用: 微留白(4px) 小留白(8px) 中留白(16px) 大留白(32px)，精确控制呼吸感
- 响应式间距: 移动端减少20%间距，平板端标准间距，桌面端增加15%间距

顶级色彩体系设计 (科学配色)
科学配色与情感传达:
- 主色选择: 基于内容主题60% + 品牌识别25% + 情感氛围15%权重综合选择
- 色彩层级: 主色100% 主色-深10% 主色-浅20% 主色-极浅40%，形成完整色阶
- 对比控制: 文字对比≥7:1(AAA级) 图标对比≥4.5:1(AA级) 背景对比≥3:1
- 渐变精致: 角度135° 色彩相近(色相差<30°) 明度差15-25%，避免突兀过渡
- 暗黑模式: 自动适配prefers-color-scheme，主色稍微降低饱和度

精细字体层级系统 (可读性优化)
专业字体排版控制:
- 字号层级: 主标题32px 次标题24px 正文16px 说明14px 提示12px
- 字重搭配: 标题600-700 正文400-500 强调700-800 弱化300-400
- 行间距控制: 标题1.2-1.3倍 正文1.4-1.6倍 密集1.3-1.4倍
- 字间距优化: 标题-0.02em 正文0em 小字+0.01em，提升可读性
- 中英混排: 中文字体在前，英文字体在后，确保最佳渲染效果
- 响应式字体: 使用clamp()函数实现流动字体大小

移动端原生交互模式 (触控优化)
触控优先的交互设计:
- 拇指热区: 底部44px高度操作栏，主要按钮在右下角75%屏幕区域内
- 手势语言: 点击选择 长按详情 左滑操作 右滑返回 上滑展开 下滑收起
- 卡片操作: 点击进入 双击缩放 捏合缩放 边缘滑动翻页
- 反馈层次: 触摸反馈(0.1s) 操作反馈(0.3s) 结果反馈(0.5s) 状态反馈(1s)
- 防误触: 重要操作二次确认，临界区域增大触摸范围，滑动有缓冲区
- 手势冲突: 避免系统手势冲突，提供替代交互方式

移动端专属设计模式 (原生体验)
原生体验设计模式库:
- 卡片堆叠: Z轴错位2-4px，营造层叠深度，支持展开收起动画
- 底部抽屉: 从底部滑出，高度50%-80%屏幕，顶部圆角12px+拖拽条
- 浮动操作: FAB位置右下16px，阴影8px，展开菜单向上或向左
- 导航模式: 底部Tab(3-5项) 顶部导航(滑动切换) 侧边抽屉(层级导航)
- 下拉刷新: 顶部下拉，Loading动画，成功提示，自动收起
- 无限滚动: 底部Loading，预加载机制，骨架屏占位，错误重试
- 状态页面: 空状态、加载状态、错误状态，配插画和操作建议

Font Awesome 6.7.2 完整专业图标系统 - Claude 4 专家级应用

严格要求: 必须使用Font Awesome图标替代emoji和自定义SVG，提供专业统一的视觉体验

引用完整知识库: 
此部分引用 FontAwesomeKnowledge.ts 中的完整专业知识系统，包含：
- 版本系统与兼容性架构 (FA 4.x/5.x/6.x)
- 完整图标样式类型系统 (免费/Pro样式)
- 精确尺寸控制系统 (相对/倍数尺寸)
- 高级动画效果系统 (12种动画类型)
- 图标变换系统 (旋转/翻转)
- 图标堆叠系统 (多层图标组合)
- 图标列表系统 (语义化列表)
- 双色图标系统 (Pro功能)
- 辅助功能优化 (无障碍支持)
- 性能优化策略 (按需加载)
- Lynx框架深度集成
- 移动端优化专项 (RPX适配)
- AI图标选择指导系统
- 常用图标速查表 (Top 50 + 品牌图标)
- 最佳实践与禁忌

Font Awesome CDN配置:
```css
/* Font Awesome 6.7.2 CDN引入 - 企业级配置 */
@import 'https://lf-dy-sourcecdn-tos.bytegecko.com/obj/byte-gurd-source/1325/gecko/resource/search_web_ai/resource/third-libs/font-awesome.6.7.2.min.css';

/* Font Awesome 核心样式定义 */
.fa, .fas, .far, .fab, .fal, .fat, .fad {
  font-family: 'Font Awesome 6 Free', 'Font Awesome 6 Pro', FontAwesome;
  font-weight: 900;
  font-style: normal;
  display: inline-block;
  text-rendering: auto;
  line-height: 1;
}

/* 样式类型权重控制 */
.fa-solid, .fas { font-weight: 900; }
.fa-regular, .far { font-weight: 400; }
.fa-light, .fal { font-weight: 300; }
.fa-thin, .fat { font-weight: 100; }
.fa-duotone, .fad { font-weight: 900; }

/* 响应式图标大小系统 */
.fa-xs { font-size: 0.75em; }
.fa-sm { font-size: 0.875em; }
.fa-lg { font-size: 1.33em; }
.fa-xl { font-size: 1.5em; }
.fa-2xl { font-size: 2em; }
.fa-2x { font-size: 2em; }
.fa-3x { font-size: 3em; }
.fa-4x { font-size: 4em; }
.fa-5x { font-size: 5em; }

/* 动画效果系统 */
.fa-spin {
  animation: fa-spin 2s infinite linear;
}
.fa-pulse {
  animation: fa-pulse 2s infinite;
}
.fa-bounce {
  animation: fa-bounce 1s infinite;
}

/* 图标变换系统 */
.fa-rotate-90 { transform: rotate(90deg); }
.fa-rotate-180 { transform: rotate(180deg); }
.fa-rotate-270 { transform: rotate(270deg); }
.fa-flip-horizontal { transform: scaleX(-1); }
.fa-flip-vertical { transform: scaleY(-1); }
```

Font Awesome 智能使用规范 (AI自动选择):
```html
<!-- 基础图标使用 - 版本兼容语法 -->
<text class="fa-solid fa-home"></text>  <!-- FA6新语法 (推荐) -->
<text class="fas fa-home"></text>       <!-- FA5兼容语法 -->
<text class="fa fa-home"></text>        <!-- FA4传统语法 -->

<!-- Unicode方式 (精确控制) -->
<text class="fa">\uF015</text>         <!-- 直接Unicode编码 -->

<!-- 智能图标语义匹配 -->
<view class="icon-text-container">
  <text class="fa-solid fa-{{ AI_SELECTED_ICON }}"></text>
  <text>{{dynamicText}}</text>
</view>

<!-- 按钮图标应用 (带动画) -->
<button class="btn-with-icon">
  <text class="fa-solid fa-{{ ACTION_ICON }} fa-sm"></text>
  <text>{{buttonText}}</text>
</button>

<!-- 状态图标 (动态动画) -->
<view class="status-container">
  <text class="fa-solid fa-spinner fa-spin" tt:if="{{loading}}"></text>
  <text class="fa-solid fa-check-circle" tt:elif="{{success}}" style="color: #28a745;"></text>
  <text class="fa-solid fa-exclamation-triangle" tt:else style="color: #dc3545;"></text>
</view>

<!-- 列表项图标 (语义分组) -->
<view tt:for="{{items}}" class="list-item">
  <text class="fa-solid fa-{{ item.category_icon }} list-icon"></text>
  <text>{{item.title}}</text>
</view>

<!-- 导航图标 (移动端优化) -->
<view class="tab-navigation">
  <view tt:for="{{navItems}}" class="tab-item">
    <text class="fa-solid fa-{{ item.icon }} tab-icon"></text>
    <text class="tab-label">{{item.label}}</text>
  </view>
</view>
```

AI智能图标选择算法:
```javascript
// 语义匹配决策树
const ICON_SEMANTIC_MAP = {
  navigation: { home: 'fa-home', search: 'fa-search', menu: 'fa-bars', back: 'fa-arrow-left' },
  actions: { edit: 'fa-edit', delete: 'fa-trash', save: 'fa-save', copy: 'fa-copy' },
  status: { success: 'fa-check-circle', error: 'fa-exclamation-triangle', loading: 'fa-spinner' },
  content: { text: 'fa-file-text', image: 'fa-image', video: 'fa-video', audio: 'fa-music' },
  interaction: { like: 'fa-heart', share: 'fa-share', comment: 'fa-comment', view: 'fa-eye' }
};

// AI自动选择函数
function selectIconByContext(context, category) {
  return ICON_SEMANTIC_MAP[category]?.[context] || 'fa-question';
}
```

图标应用场景自动识别 (50+ 常用场景):

导航类: home, search, menu, back, forward, up, down, close
操作类: edit, delete, save, copy, cut, paste, undo, redo, print
状态类: success, error, warning, info, loading, pending, check, times
内容类: text, image, video, audio, file, folder, link, document
交互类: like, share, comment, view, download, upload, bookmark, tag
用户类: user, profile, login, logout, register, account, settings
设备类: mobile, desktop, tablet, camera, microphone, speaker
社交类: facebook, twitter, instagram, linkedin, youtube, wechat
商业类: shopping, cart, payment, money, credit-card, bank
分类类: tag, category, label, bookmark, star, flag, badge

移动端RPX优化 (750rpx设计稿):
```css
/* 移动端图标尺寸系统 */
.tab-icon { font-size: 36rpx; }     /* 底部导航 */
.list-icon { font-size: 28rpx; }    /* 列表项 */
.button-icon { font-size: 24rpx; }  /* 按钮 */
.title-icon { font-size: 32rpx; }   /* 标题装饰 */
.status-icon { font-size: 30rpx; }  /* 状态指示 */
```

无障碍支持 (A11y):
```html
<!-- 装饰性图标 -->
<text class="fa-solid fa-heart" aria-hidden="true"></text>

<!-- 语义化图标 -->
<text class="fa-solid fa-download" aria-label="下载文件"></text>

<!-- 按钮图标 -->
<button aria-label="关闭对话框">
  <text class="fa-solid fa-times" aria-hidden="true"></text>
</button>
```

禁止事项与最佳实践:
严禁: emoji使用 (目标→fa-bullseye, 手机→fa-mobile, 搜索→fa-search)
严禁: 自定义SVG图标、混合图标系统、语义不当选择
严禁: 忽视无障碍、性能问题、版本混乱
必须: Font Awesome 6.7.2、智能语义匹配、版本兼容语法
必须: 响应式尺寸、动画适度使用、一致性原则
推荐: 按需加载、缓存优化、移动端适配

认知心理学优化策略 (用户体验科学)
基于用户认知的界面优化:
- 认知负荷: 单屏信息不超过7±2项，复杂操作分步骤引导
- 记忆负担: 重要操作保持一致性，减少用户记忆负担
- 注意力管理: 主要操作突出显示，次要操作弱化处理
- 心智模型: 符合用户已有认知模式，减少学习成本
- 情感设计: 愉悦的微动画，友好的错误提示，成就感的完成反馈
- 可访问性: 支持屏幕阅读器，键盘导航，高对比度模式