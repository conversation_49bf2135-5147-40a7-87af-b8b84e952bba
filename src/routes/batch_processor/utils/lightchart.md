使用案例：
index.json:
```json
{
  "usingComponents": {
    "lightcharts-canvas": "@byted/lynx-lightcharts/lightcharts-canvas/lightcharts-canvas"
  }
}
```

index.ttml
```html
<lightcharts-canvas bindinitchart="bindinitchart" style="width: 100%;height:100%" canvasName="test" useKrypton={{SystemInfo.enableKrypton}}/>
```

index.js:
```javascript
import LynxChart from '@byted/lynx-lightcharts/src/chart';
import { setImageLoader } from '@byted/lynx-lightcharts/lightcharts'


if (SystemInfo.enableKrypton) {
  const lynxImageLoader = (src: string, callback: Function) => {
    const image = lynx.krypton.createImage(src);
    image.onload = (): void => {
      callback(image);
    };
  }
  setImageLoader(lynxImageLoader);
}

const data =  [
  { year: '1991', value: 3 },
  { year: '1992', value: 4 },
  { year: '1993', value: 3.5 },
  { year: '1994', value: 5 },
  { year: '1995', value: 4.9 },
  { year: '1996', value: 6 },
  { year: '1997', value: 7 },
  { year: '1998', value: 9 },
  { year: '1999', value: 13 },
];
const option =  {
  data,
  tooltip:{borderWidth: 1,borderColord: '#aaa'},
  xAxis: {
     type: 'category',
   },
  yAxis: {
    type: 'value',
  },
  series: [
    {
      type: 'line',
      name: "数值",
      encode: {
        x: 'year',
        y: 'value'
      },
    }
  ]
}
Card({
  data: {},
  methods: {
    bindinitchart: function (msg) {
      const chart = new LynxChart(msg);
      // 更多配置详见Lightcharts官网：https://lightcharts.bytedance.net/sample
      chart.setOption(option);
    },
  },
});
```

在Lynx中使用Lightcharts图表库 
原理：
在已安装好 @byted/lynx-lightcharts的基础上，通过修改 lynx.config.js 将内置的 lightcharts.js 解析到自定义的模块中。
步骤一：按需构建图表
构建地址：http://************:3000/
选择对应的 lightcharts 版本和所需图表及组件，下载对应的 lynx版 lightcharts.js 文件。
[图片]
步骤二：lynx项目配置
将下载好的文件拷贝到 lynx 项目中，　并配置 lynx.config.js 的 resolve.alias，　将 lightcharts 解析到自定义构建的文件中
lynx.config.js
/**
 * @type {import('@byted-lynx/lynx-speedy').UserConfig}
 */
const path = require('path')

const config = {
  input: {
    input: './index.tsx',
  },
  encode: {
    qjsCheck: false,
  },
  resolve: {
    alias: {
      "../lightcharts": path.resolve(__dirname, 'lightcharts.js'), // 对应自定义构建文件
    }
  }
}

module.exports = config;

注意事项
lightcharts内置对象引入方式
Lightcharts导出的成员列表：　https://okee.bytedance.net/charts/guide/lightcharts
此时如需引用lightcharts对象，比如element，等api, 直接引用按需构建的lightcharts，避免打包成两份
// 从自己的项目工程中引入下载好的lightcharts
import * as lightcharts from '../myproject/lib/lightcharts.js' 
dts类型文件
如需ts提示，可同目录下新增lightcharts.d.ts文件
lightcharts.d.ts
export * from '@byted/lightcharts'


canvas拾取方案 
G 4.0 的拾取方案设计 · 语雀
简介
light-render是一个支持svg/canvas的2d渲染引擎,  支撑lightcharts的底层渲染, 提供了虚拟dom和事件的封装,提供元素动画和基于 diff 的全局过渡动画(global transition )。目前已开源
gitlab: https://code.byted.org/liwensheng.001/render-next
github: https://github.com/oceanengine/okeevis-render
渲染引擎的作用
封装 canvas / svg能力，屏蔽底层 API 细节，提升开发效率
核心功能: 
- 图形绘制
- 事件交互,参考 w3c 冒泡模型
- 动画
- 组件化能力（开发中, jsx + hooks)
基本用法 Usage
import { Render, Group, Polyline, Circle, Path, Line } from '@byted/light-render';

cosnt render = new Render(dom)
const group = new Group();
group.add(new Circle({cx: 0, cy: 0, radius:100, fill: 'red'}));
group.addAll([new Cirlce(), new Line()]);
render.add(group)

Renderer实例 api
- constructor(dom: HTMLElement, {dpr: number})
- refresh()
- refreshImmediately()
- resize(width: number, height: number)
- updateAll(list: Element[])
公共实例属性
- readonly parentNode
- readonly type
画布坐标
[图片]
[图片]

公共方法 Common Method
- setAttr({x: 0, y: 0}); // 更新多个属性
- setAttr('x', 0) // 更新某个属性
- show()
- hide()
- getBBox()  // 获取原始包围盒(不考虑矩阵变换)
- getBoundingClentRect()  // 获取包围盒(考虑矩阵变换)
- animateTo()
- stopAllAnimation()
- replaceWith new
公共attr属性 Common Attributes
new Element(attr)
绘图属性 Style Attributes
标黄属性为可继承属性, 可以写在Group上(性能更好, 也符合复用逻辑)
- key 类似react, vue的key , track by(非必填)
- ref 类似react的ref, 可以自动挂载引用(ref.current)
- colornew
- fill 填充 可以使用  string | LinearGradient | RadialGraidnet | Pattern | 'currentColor' | 'none'
- stroke 描边
- lineWidth 线宽
- pickingBuffer 扩大描边的事件触发范围,默认0
- lineDash: number[]虚线
- lineDashOffset 虚线偏移
- opacity 透明度 [0-1]
- fillOpacity
- strokeOpacity
- clip 剪切 Shape | Ref<Shape>
- rotation 旋转弧度（rad)
- originX 旋转中心
- originY 旋转中心
- scaleX  
- scaleY
- translateX
- translateY
- draggable
- cursor:  鼠标hover指标形状
- display: true/false, 是否展示
-  shadowBlur: number;
-  shadowColor: string;
-  shadowOffsetX: number;
-  shadowOffsetY: number
- blendMode
- markerStart: Marker
- markerEnd: Marker
- pointerEvents: 'none' | 'auto' | 'bounding-box'
过渡动画属性
group.updateAll(elementList: Element[])
-   transitionProperty?: 'all' | 'none' 
-   transitionEase?: EasingName;
-   transitionDuration?: number;
-   transitionDelay?: number;
事件属性  Event Attributes
除在属性里添加事件外, 还可以使用element.on('click', callback)添加多个事件监听, 事件模型遵循浏览器冒泡模型
- onClick
- onDblClick
- onMouseOver
- onMouseEnter
- onMouseLeave
- onMouseOut
- onMouseDown
- onMouseUp
- onMouseMove
- onContextMenu
- onWheel
- onDrag
- onDragStart
- onDragEnd
- onDragLeave
- onDragOver
- onDrop
- onTouchStart
- onTouchMove
- onTouchEnd
事件回调参数 Event Callback Param
event
- type
- x
- y
- target
- timeStamp
- original(原始html event)
ScrollView
滚动容器, 会剪切内容,  并提供滚动条交互
配置参数 
interface ScrollViewAttr {
 // 滚动区域包围矩形
  x: number;
  y: number;
  width: number;
  height: number;
  
  // 滚动内容的区域
  scrollWidth?: number;
  scrollHeight?: number;
  
  // 纵向/横向滚动开关
  scrollX?: boolean;
  scrollY?: boolean;
  
  
  
  //滚动事件回调
  onScroll?: Function;
  
  // 是否显示滚动条 false不显示 true一直显示 hover进入区域时显示 scrolling滚动时显示
  showScrollBar?: boolean | 'hover' | 'scrolling'; 
  
  // 滚动条宽度
  scrollBarSize?: number;
  
  // 滚动条(中间的条)颜色
  scrollThumbColor?: string;
  // 高亮颜色
  scrollThumbHoverColor?: string;
  
  // 滚动条滑槽颜色(底部)
  scrollTrackColor?: string;
}
[图片]

实例属性与方法 
scrollLeft 可读可写   向左侧滚动了多少
scrollTop 可读可写  向上侧滚动了多少
scrollBy(dx, dy)  相对滚动
scrollTo(left, top)  绝对滚动
addContent(element) 往滚动区域中添加子元素
案例 
import Render from '../src/render';
import ScrollView from '../src/shapes/ScrollView';
import Text from '../src/shapes/Text';

const dom = document.getElementById('root') as HTMLDivElement;
const render = new Render(dom, {renderer: 'canvas'});



const scrollView = new ScrollView({
  x: 100,
  y: 100,
  width: 200,
  height: 200,
  scrollX: true,
  scrollY: true,
  scrollWidth: 410,
  scrollHeight: 800,
  showScrollBar: true,
});

scrollView.addContent(new Text({
  x: 100,
  y: 100,
  textAlign: 'left',
  textBaseline: 'top',
  fontSize: 40,
  text: '01234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789012345678901234567890123456789',
  fill: '#333',
  truncate: {
    outerWidth: 400,
    outerHeight: 400,
  }
}));


render.add(scrollView)
Group 分组
group支持所有公共属性及Text文字样式属性
group的作用
1. 分组管理元素, 
2. 公共的样式属性fill ,stroke等
3. 整体的矩阵变换,如旋转/缩放
4. 事件冒泡, 可以监听子元素的冒泡过来的事件
属性 
getter childNodes
方法
- add  添加元素
- addAll 批量添加元素数组
- remove(child) 移除子元素
- clear() 清空子元素
- childAt(index) 获取第N个子元素
cosnt group = new Group();
// 添加一个图形
group.add(new Line({x1,x2,y1,y2});
// 添加多个图形
group.addAll([1,2,3].map(item => new Line());
Line 直线
配置项
- x1
- y1
- x2
- y2
const line = new Line({x1:0,x2:0,y1:100,y2:100});
Rect 矩形
配置项
- x
- y
- width
- height
- r 圆角

Arc 弧线
- cx
- cy
- radius 半径
- start 起点弧度
- end 终点弧度
- closePath 弧线是否闭合
Circle 圆
- cx
- cy
- radius
Ellipse 椭圆
- cx
- cy
- rx
- ry
Polyline 折线(不封闭)
- pointList: Array<{x: number;y:numbver}>
- smooth: boolean;是否使用光滑曲线连接
- smoothType: spline | bezeir
- borderRadius ^0.9.72
Polygon 多边形(封闭)
- pointList Array<{x: number;y:numbver}>
- smooth: boolean;是否使用光滑曲线连接
- smoothType: spline | bezeir
- borderRadius ^0.9.72
Sector 扇形
- cx
- cy
- start 起点角度(弧度制)
- end 终点角度(弧度制)
- radius 外径
- radiusI 内径
- round 是否两端加圆角默认false
- connerRadius 圆角 (0.9.72废弃) 
- borderRadius ^0.9.72
Text文本与富文本
lightcharts富文本设计 
标黄属性可继承, 可放在group上
- x
- y
- text
- fill
- fontFamily
- fontSize
- textAlign
- textBaseline
- fontWeight
- fontStyle

RichText
DomNode
TextPath 文本路径
export interface TextPathAttr extends CommonAttr {
  text?: string | number;
  path?: Shape;
  fontSize?: number;
  startOffset?: number;
  fontWeight?: string | number;
  fontFamily?: string;
  fontVariant?: string;
  fontStyle?: 'normal' | 'italic' | 'oblique';
}
Marker(首尾箭头标记)
- Shape: Element
- x
- y
- width
- height
- orient 'auto' | 'auto-start-reverse' | number;
- markerUnits 'strokeWidth' | 'userSpaceOnUse';
RichText
v1.1.0起支持(尚未发布)
配置项同Text,但是text属性是富文本的内容, 详见lightcharts富文本设计 
Image图片
- x 
- y 
- width
- height
- src
- preserveAspectRatio https://developer.mozilla.org/zh-CN/docs/Web/SVG/Attribute/preserveAspectRatio
Path 自绘路径
配置项, brush参数中的ctx可以使用以下部分canvas2d ctx中的方法
new Path({
    brush:ctx => {
        ctx.moveTo(0, 0);
        ctx.lineTo(100, 100);
    }
})
CanvasRenderingContext2D.closePath()使笔点返回到当前子路径的起始点。它尝试从当前点到起始点绘制一条直线。如果图形已经是封闭的或者只有一个点，那么此方法不会做任何操作。
CanvasRenderingContext2D.moveTo()将一个新的子路径的起始点移动到(x，y)坐标。
CanvasRenderingContext2D.lineTo()使用直线连接子路径的最后的点到x,y坐标。
CanvasRenderingContext2D.bezierCurveTo()添加一个3次贝赛尔曲线路径。该方法需要三个点。 第一、第二个点是控制点，第三个点是结束点。起始点是当前路径的最后一个点，绘制贝赛尔曲线前，可以通过调用 moveTo() 进行修改。
CanvasRenderingContext2D.quadraticCurveTo()添加一个2次贝赛尔曲线路径。
CanvasRenderingContext2D.arc()绘制一段圆弧路径， 圆弧路径的圆心在 (x, y) 位置，半径为 r ，根据anticlockwise （默认为顺时针）指定的方向从 startAngle 开始绘制，到 endAngle 结束。
CanvasRenderingContext2D.arcTo()根据控制点和半径绘制圆弧路径，使用当前的描点(前一个moveTo或lineTo等函数的止点)。根据当前描点与给定的控制点1连接的直线，和控制点1与控制点2连接的直线，作为使用指定半径的圆的切线，画出两条切线之间的弧线路径。
CanvasRenderingContext2D.ellipse() 添加一个椭圆路径，椭圆的圆心在（x,y）位置，半径分别是radiusX 和 radiusY ，按照anticlockwise （默认顺时针）指定的方向，从 startAngle  开始绘制，到 endAngle 结束。
CanvasRenderingContext2D.rect()
Path.connect
Path.   Svg 从svg path解析
const path = Path.fromSvgPath(path: string, attr)
CompundPath组合路径
new CompoundPath({
shapes: [shape1, shape2]
})
animate动画
- 开启过渡动画
new Line().animateTo({x1: 100});
- 停止动画
stopAnimation(gotoEnd: boolean);
- 路径动画
  animateMotion({path, rotate, during, ease,callback, delay})
Use 复用元素(todo)
// todo
Color颜色处理(todo)


 



引言
Light Chart是字节跳动OKee Design团队开发的高性能JavaScript图表库，专为Lynx环境设计，提供了丰富的图表类型和配置选项。作为一款内部图表库，Light Chart具有体积小、性能高的特点，能够满足移动端和Web应用的数据可视化需求，特别适合在字节跳动的产品生态中使用。
本指南旨在帮助开发者快速了解Light Chart的基本信息、使用方法、API配置以及最佳实践，使开发者能够高效地将Light Chart集成到自己的项目中，创建出美观、高效的数据可视化图表。
基本信息
版本与兼容性
Light Chart目前已全面适配Lynx平台，除韦恩图外，其它所有图表和组件都可以在Lynx中正常使用（词云图使用wordCloudFast），并且支持H5降级（自动判断，不需要额外处理）。
主要特点：
- 专为Lynx环境优化
- 体积小，加载快
- 性能高，渲染速度快
- 自动H5降级支持
- 丰富的图表类型
开发团队：
- 字节跳动OKee Design团队
- 提供持续的更新和技术支持
- 针对内部业务场景持续优化
支持的图表类型
Light Chart支持多种常见的图表类型，满足不同的数据可视化需求：
- 基础图表：柱状图、折线图、饼图、散点图
- 复合图表：折柱混合图、多坐标系图表
- 特殊图表：仪表盘、雷达图、热力图、树图
- 统计图表：箱线图、漏斗图、桑基图
TTML使用方法
Light Chart可以通过两种方式在项目中使用：TTML和ReactLynx。以下重点介绍TTML的集成方式。
安装与引入
在TTML项目中，可以使用npm包管理器引入Light Chart：
# 使用npm安装
npm install @bytedance/lightcharts

TTML集成基本流程
在TTML中使用Light Chart需要完成以下三个步骤：
TTML集成Light Chart的三个关键步骤：
1. 在index.json文件中引入Lightcharts组件
2. 在ttml文件中设置组件并绑定回调函数
3. 在js文件中创建图表实例并配置
1. JSON配置
在index.json文件中引入Light Chart组件：
{
  "usingComponents": {
    "light-chart": "@bytedance/lightcharts"
  }
}

2. TTML配置
在.ttml文件中添加组件并绑定回调函数：
<light-chart 
  id="myChart" 
  style="width: 100%; height: 300px;" 
  bindready="onChartReady">
</light-chart>

3. JS回调函数实现
在.js文件中实现回调函数，创建图表实例：
Card({
  data: {
    // 页面数据
  },
  onChartReady(e) {
    // 获取图表实例
    const chart = e.detail.chart;
    
    // 创建图表配置
    const option = {
      title: {
        text: '销售数据'
      },
      xAxis: {
        type: 'category',
        data: ['一月', '二月', '三月', '四月', '五月']
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        data: [120, 200, 150, 80, 70],
        type: 'bar'
      }]
    };
    
    // 设置图表配置
    chart.setOption(option);
  }
})

集成注意事项
在Lynx环境中使用Light Chart时，需要注意以下几点：
1. 在体验Demo时需要加上schema参数：enable_canvas=1&enable_canvas_optimize=1&group={gecko channel}
2. 确保设备支持Canvas渲染
3. 可以使用tt.canIUse('light-chart')检查设备是否支持Light Chart组件
API详细使用方式
Light Chart提供了丰富的API和配置选项，可以满足各种图表需求。本节将详细介绍Light Chart的API使用方式，包括函数签名、参数说明、返回值、API调用时序和生命周期以及常见错误处理方法。
核心API函数签名、参数说明和返回值
setOption(option, [notMerge], [lazyUpdate])
设置图表的配置项和数据，是使用Light Chart的主要方法。
函数签名：
setOption(option: Object, notMerge?: boolean, lazyUpdate?: boolean): void

参数说明：
- option: Object：图表的配置项和数据，详细配置参见配置项手册
- notMerge?: boolean：可选，是否不与之前设置的option进行合并，默认为false（合并）
- lazyUpdate?: boolean：可选，是否在本次更新后不立即刷新画布，默认为false（立即刷新）
返回值： 无
使用示例：
// 基本使用
chart.setOption({
  title: { text: '销售数据' },
  xAxis: { data: ['一月', '二月', '三月'] },
  series: [{ type: 'bar', data: [5, 20, 36] }]
});

// 不合并之前的配置
chart.setOption(newOption, true);

// 设置后不立即刷新
chart.setOption(newOption, false, true);

getWidth()
获取图表实例的宽度。
函数签名：
getWidth(): number

参数说明： 无
返回值： number - 图表的宽度（像素）
使用示例：
const width = chart.getWidth();
console.log('图表宽度：', width);

getHeight()
获取图表实例的高度。
函数签名：
getHeight(): number

参数说明： 无
返回值： number - 图表的高度（像素）
使用示例：
const height = chart.getHeight();
console.log('图表高度：', height);

resize([opts])
重新调整图表的大小，在容器大小发生变化时调用。
函数签名：
resize(opts?: {
  width?: number | string,
  height?: number | string,
  silent?: boolean
}): void

参数说明：
- opts?: Object：可选的配置项
  - width?: number | string：可选，图表的宽度，支持像素值或百分比
  - height?: number | string：可选，图表的高度，支持像素值或百分比
  - silent?: boolean：可选，是否禁止抛出事件，默认为false
返回值： 无
使用示例：
// 无参数调用，使用容器当前大小
chart.resize();

// 指定宽高
chart.resize({
  width: 800,
  height: 400
});

// 使用百分比
chart.resize({
  width: '100%',
  height: '300px'
});

// 静默调整大小（不触发事件）
chart.resize({ silent: true });

clear()
清空当前实例，会移除实例中所有的组件和图表。
函数签名：
clear(): void

参数说明： 无
返回值： 无
使用示例：
// 清空图表
chart.clear();
// 清空后可以重新设置option
chart.setOption(newOption);

dispose()
销毁实例，释放资源。调用后实例无法再被使用。
函数签名：
dispose(): void

参数说明： 无
返回值： 无
使用示例：
// 销毁图表实例
chart.dispose();

getOption()
获取当前实例中维护的option对象，返回的option对象中包含了用户传入的配置项以及Light Chart内部的默认配置项。
函数签名：
getOption(): Object

参数说明： 无
返回值： Object - 当前图表的完整配置项
使用示例：
const option = chart.getOption();
console.log('当前配置：', option);

getDom()
获取图表的容器DOM节点。
函数签名：
getDom(): HTMLElement

参数说明： 无
返回值： HTMLElement - 图表的容器DOM元素
使用示例：
const container = chart.getDom();
console.log('图表容器：', container);

on(eventName, handler, context)
绑定事件处理函数。
函数签名：
on(eventName: string, handler: Function, context?: Object): void

参数说明：
- eventName: string：事件名称
- handler: Function：事件处理函数
- context?: Object：可选，事件处理函数内部的this指向
返回值： 无
使用示例：
// 绑定点击事件
chart.on('click', function(params) {
  console.log('点击了图表：', params);
});

// 使用自定义上下文
const customContext = { name: 'myChart' };
chart.on('mouseover', function(params) {
  console.log(`${this.name}的鼠标悬停事件：`, params);
}, customContext);

off(eventName, handler)
解绑事件处理函数。
函数签名：
off(eventName: string, handler?: Function): void

参数说明：
- eventName: string：事件名称
- handler?: Function：可选，事件处理函数，如果不传则解绑该事件名称下的所有处理函数
返回值： 无
使用示例：
// 定义事件处理函数
function clickHandler(params) {
  console.log('点击了图表：', params);
}

// 绑定事件
chart.on('click', clickHandler);

// 解绑特定事件处理函数
chart.off('click', clickHandler);

// 解绑所有click事件
chart.off('click');

dispatchAction(payload)
触发图表的行为，例如图例的切换、提示框的显示等。
函数签名：
dispatchAction(payload: Object): void

参数说明：
- payload: Object：动作对象，包含动作类型和目标数据
返回值： 无
使用示例：
// 显示提示框
chart.dispatchAction({
  type: 'showTip',
  seriesIndex: 0,
  dataIndex: 2
});

// 高亮某个数据项
chart.dispatchAction({
  type: 'highlight',
  seriesIndex: 0,
  dataIndex: 1
});

// 切换图例选中状态
chart.dispatchAction({
  type: 'legendToggleSelect',
  name: '销售额'
});

API调用时序和生命周期
Light Chart图表实例的生命周期包括创建、配置、更新和销毁四个主要阶段。了解这些阶段和相关的API调用时序，有助于正确管理图表实例并优化性能。
图表实例生命周期
┌─────────────────┐
│  实例创建阶段   │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  配置设置阶段   │
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  数据更新阶段   │◄───┐
└────────┬────────┘    │
         │             │
         ▼             │
┌─────────────────┐    │
│  事件交互阶段   │────┘
└────────┬────────┘
         │
         ▼
┌─────────────────┐
│  实例销毁阶段   │
└─────────────────┘

1. 实例创建阶段
在TTML环境中，Light Chart实例的创建是通过组件的ready事件触发的：
Page({
  onChartReady(e) {
    // 获取图表实例
    const chart = e.detail.chart;
    // 此时图表实例已创建完成，可以进行配置
  }
})

2. 配置设置阶段
在获取图表实例后，通过setOption方法设置图表的初始配置：
onChartReady(e) {
  const chart = e.detail.chart;
  // 设置初始配置
  chart.setOption({
    // 图表配置...
  });
  
  // 可以绑定事件
  chart.on('click', this.handleChartClick);
}

3. 数据更新阶段
当需要更新图表数据时，再次调用setOption方法：
// 更新数据
updateChart(newData) {
  const chart = this.chart;
  chart.setOption({
    series: [{
      data: newData
    }]
  });
}

默认情况下，setOption会合并新旧配置。如果需要完全替换配置，可以设置notMerge参数为true：
chart.setOption(newOption, true);

4. 事件交互阶段
在图表的使用过程中，可以通过事件系统响应用户交互：
// 绑定点击事件
chart.on('click', function(params) {
  console.log('点击了图表元素：', params);
});

// 主动触发行为
chart.dispatchAction({
  type: 'showTip',
  seriesIndex: 0,
  dataIndex: 2
});

5. 实例销毁阶段
当不再需要图表时，应该调用dispose方法销毁实例，释放资源：
// 页面卸载时销毁图表实例
onUnload() {
  if (this.chart) {
    this.chart.dispose();
    this.chart = null;
  }
}

生命周期钩子函数
Light Chart提供了多个生命周期相关的事件，可以通过on方法监听：
// 渲染完成事件
chart.on('rendered', function() {
  console.log('图表渲染完成');
});

// 配置更新事件
chart.on('updated', function() {
  console.log('图表配置已更新');
});

主要的生命周期事件包括：
事件名称
触发时机
用途
rendered
图表首次渲染完成时
可以在此时获取图表的渲染信息
updated
图表通过setOption更新后
可以在此时执行更新后的逻辑
resize
图表大小改变后
可以在此时执行调整后的逻辑
常见错误处理方法
使用Light Chart时可能遇到各种问题，以下是常见错误及其处理方法：
1. 图表无法显示
可能原因：
- 容器元素尺寸为0
- Canvas不被支持
- 配置项错误
解决方法：
// 检查容器尺寸
const width = chart.getWidth();
const height = chart.getHeight();
if (width === 0 || height === 0) {
  console.error('图表容器尺寸为0，无法正常显示');
  // 设置容器尺寸
  chart.resize({
    width: 300,
    height: 200
  });
}

// 检查Canvas支持
if (!tt.canIUse('light-chart')) {
  console.error('当前环境不支持Light Chart');
  // 提供降级方案，如使用表格展示数据
}

// 检查配置项
try {
  chart.setOption(option);
} catch (e) {
  console.error('配置项错误：', e);
  // 使用默认配置
  chart.setOption(defaultOption);
}

2. 数据更新后图表不刷新
可能原因：
- 引用类型数据没有变化
- 使用了lazyUpdate但没有手动刷新
解决方法：
// 确保传入新的数据对象
const newData = [...oldData]; // 创建数据的副本
newData[0] = 100; // 修改数据

// 更新图表
chart.setOption({
  series: [{
    data: newData
  }]
});

// 如果使用了lazyUpdate，需要手动刷新
chart.setOption(option, false, true); // 设置lazyUpdate为true
// 稍后手动刷新
setTimeout(() => {
  chart.resize(); // 触发刷新
}, 100);

3. 内存泄漏问题
可能原因：
- 没有正确销毁图表实例
- 事件监听器没有解绑
解决方法：
// 在页面卸载时销毁图表实例
onUnload() {
  if (this.chart) {
    // 解绑所有事件
    this.chart.off('click');
    this.chart.off('mouseover');
    
    // 销毁实例
    this.chart.dispose();
    this.chart = null;
  }
}

4. 移动端性能问题
可能原因：
- 数据量过大
- 更新频率过高
- 图表配置过于复杂
解决方法：
// 数据抽样
function sampleData(data, step) {
  const result = [];
  for (let i = 0; i < data.length; i += step) {
    result.push(data[i]);
  }
  return result;
}

// 使用节流函数控制更新频率
function throttle(fn, delay) {
  let timer = null;
  return function() {
    if (timer) return;
    timer = setTimeout(() => {
      fn.apply(this, arguments);
      timer = null;
    }, delay);
  };
}

// 简化图表配置
const simpleOption = {
  animation: false, // 关闭动画提高性能
  series: [{
    data: sampleData(rawData, 5), // 数据抽样
    type: 'line',
    symbol: 'none' // 不显示标记点以提高性能
  }]
};

// 使用节流函数更新图表
const throttledUpdate = throttle(function(data) {
  chart.setOption({
    series: [{
      data: data
    }]
  });
}, 300); // 300ms内只更新一次

// 调用更新
throttledUpdate(newData);

5. 交互事件不响应
可能原因：
- 事件绑定错误
- 图表层级问题
- 触摸区域过小
解决方法：
// 正确绑定事件
chart.on('click', function(params) {
  console.log('点击事件参数：', params);
});

// 增大触摸区域
chart.setOption({
  series: [{
    symbolSize: 20, // 增大标记点大小
    emphasis: {
      itemStyle: {
        borderWidth: 5 // 增大高亮效果
      }
    }
  }]
});

// 检查事件是否正确触发
chart.on('click', function() {
  console.log('事件已触发');
});

错误处理最佳实践
1. 使用try-catch捕获错误
try {
  chart.setOption(option);
} catch (e) {
  console.error('图表配置错误：', e);
  // 错误处理逻辑
}

2. 提供降级方案
if (!tt.canIUse('light-chart')) {
  // 使用表格或其他方式展示数据
  this.showDataTable(data);
} else {
  // 正常使用图表
  this.renderChart(data);
}

3. 添加日志记录
function initChart() {
  console.log('开始初始化图表');
  const chart = e.detail.chart;
  console.log('图表实例创建成功');

  try {
    chart.setOption(option);
    console.log('图表配置设置成功');
  } catch (e) {
    console.error('图表配置失败：', e);
  }
}

4. 实现完整的错误监控
// 监控图表错误
chart.on('error', function(e) {
  console.error('图表运行时错误：', e);
  // 上报错误
  reportError({
    type: 'ChartError',
    message: e.message,
    stack: e.stack
  });
});

常见图表类型完整代码示例
本节提供了Light Chart中常见图表类型的完整实现示例，包括JSON配置、TTML组件设置和JS回调函数实现。通过这些示例，开发者可以快速掌握如何使用Light Chart创建各种类型的图表。
柱状图的完整实现示例
柱状图是最常用的图表类型之一，适合展示分类数据的数量比较。
项目结构
my-project/
  ├── pages/
  │   └── bar-chart/
  │       ├── index.json  // 组件配置
  │       ├── index.js    // 页面逻辑
  │       ├── index.ttml  // 页面模板
  │       └── index.ttss  // 页面样式

1. JSON配置（index.json）
{
  "usingComponents": {
    "light-chart": "@bytedance/lightcharts"
  }
}

2. TTML组件设置（index.ttml）
<view class="container">
  <view class="chart-title">月度销售数据分析</view>
  
  <light-chart 
    id="barChart" 
    style="width: 100%; height: 350px;" 
    bindready="onChartReady"
    bindclick="onChartClick">
  </light-chart>
  
  <view class="chart-description">
    <text>数据来源：公司销售部门</text>
    <text>更新时间：{{updateTime}}</text>
  </view>
</view>

3. JS回调函数实现（index.js）
Page({
  data: {
    chartData: [
      { month: '一月', sales: 120, target: 100 },
      { month: '二月', sales: 200, target: 150 },
      { month: '三月', sales: 150, target: 180 },
      { month: '四月', sales: 80, target: 120 },
      { month: '五月', sales: 70, target: 110 },
      { month: '六月', sales: 110, target: 130 }
    ],
    updateTime: '2023-06-30'
  },
  
  // 图表实例
  barChart: null,
  
  onLoad() {
    // 页面加载时的逻辑
  },
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.barChart = e.detail.chart;
    
    // 处理数据
    const { chartData } = this.data;
    const months = chartData.map(item => item.month);
    const salesData = chartData.map(item => item.sales);
    const targetData = chartData.map(item => item.target);
    
    // 创建图表配置
    const option = {
      title: {
        text: '月度销售数据',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function(params) {
          const salesItem = params[0];
          const targetItem = params[1];
          return `${salesItem.name}<br/>
                 ${salesItem.seriesName}: ${salesItem.value}<br/>
                 ${targetItem.seriesName}: ${targetItem.value}<br/>
                 完成率: ${Math.round(salesItem.value / targetItem.value * 100)}%`;
        }
      },
      legend: {
        data: ['销售额', '目标值'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months,
        axisLabel: {
          interval: 0,
          rotate: 30
        }
      },
      yAxis: {
        type: 'value',
        name: '金额（万元）'
      },
      series: [
        {
          name: '销售额',
          type: 'bar',
          data: salesData,
          itemStyle: {
            color: '#5470c6'
          },
          barWidth: '40%',
          emphasis: {
            itemStyle: {
              color: '#3a56b4'
            }
          }
        },
        {
          name: '目标值',
          type: 'bar',
          data: targetData,
          itemStyle: {
            color: '#91cc75'
          },
          barWidth: '40%',
          emphasis: {
            itemStyle: {
              color: '#6fb556'
            }
          }
        }
      ]
    };
    
    // 设置图表配置
    this.barChart.setOption(option);
  },
  
  // 图表点击事件处理
  onChartClick(e) {
    const { componentType, seriesType, seriesIndex, dataIndex } = e.detail;
    
    if (seriesType === 'bar') {
      const seriesName = seriesIndex === 0 ? '销售额' : '目标值';
      const month = this.data.chartData[dataIndex].month;
      const value = seriesIndex === 0 
        ? this.data.chartData[dataIndex].sales 
        : this.data.chartData[dataIndex].target;
      
      tt.showToast({
        title: `${month}${seriesName}：${value}万元`,
        icon: 'none'
      });
    }
  },
  
  // 页面卸载时销毁图表实例
  onUnload() {
    if (this.barChart) {
      this.barChart.dispose();
      this.barChart = null;
    }
  }
});

4. 样式设置（index.ttss）
.container {
  padding: 16px;
  background-color: #fff;
}

.chart-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
}

.chart-description {
  margin-top: 16px;
  font-size: 12px;
  color: #666;
  display: flex;
  flex-direction: column;
  align-items: center;
}

折线图的完整实现示例
折线图适合展示连续的时序数据，特别是趋势变化。
项目结构
my-project/
  ├── pages/
  │   └── line-chart/
  │       ├── index.json  // 组件配置
  │       ├── index.js    // 页面逻辑
  │       ├── index.ttml  // 页面模板
  │       └── index.ttss  // 页面样式

1. JSON配置（index.json）
{
  "usingComponents": {
    "light-chart": "@bytedance/lightcharts"
  }
}

2. TTML组件设置（index.ttml）
<view class="container">
  <view class="chart-title">产品销售趋势分析</view>
  
  <view class="control-panel">
    <view class="time-selector">
      <view class="{{timeRange === 'week' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="week">周</view>
      <view class="{{timeRange === 'month' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="month">月</view>
      <view class="{{timeRange === 'year' ? 'active' : ''}}" bindtap="switchTimeRange" data-range="year">年</view>
    </view>
  </view>
  
  <light-chart 
    id="lineChart" 
    style="width: 100%; height: 350px;" 
    bindready="onChartReady">
  </light-chart>
  
  <view class="chart-description">
    <text>数据更新时间：{{updateTime}}</text>
  </view>
</view>

3. JS回调函数实现（index.js）
Page({
  data: {
    timeRange: 'month',
    updateTime: '2023-06-30 15:30',
    weekData: {
      xAxis: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      series: [
        { name: '产品A', data: [120, 132, 101, 134, 90, 230, 210] },
        { name: '产品B', data: [220, 182, 191, 234, 290, 330, 310] }
      ]
    },
    monthData: {
      xAxis: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      series: [
        { name: '产品A', data: [320, 332, 301, 334, 390, 330, 320, 332, 301, 334, 390, 330] },
        { name: '产品B', data: [820, 932, 901, 934, 1290, 1330, 1320, 1332, 1301, 1334, 1390, 1330] }
      ]
    },
    yearData: {
      xAxis: ['2018', '2019', '2020', '2021', '2022', '2023'],
      series: [
        { name: '产品A', data: [1500, 2320, 2010, 3540, 3900, 3300] },
        { name: '产品B', data: [2500, 3320, 3010, 4540, 4900, 4300] }
      ]
    }
  },
  
  // 图表实例
  lineChart: null,
  
  onLoad() {
    // 页面加载时的逻辑
  },
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.lineChart = e.detail.chart;
    
    // 初始渲染图表
    this.renderChart();
  },
  
  // 切换时间范围
  switchTimeRange(e) {
    const timeRange = e.currentTarget.dataset.range;
    this.setData({ timeRange });
    
    // 重新渲染图表
    this.renderChart();
  },
  
  // 渲染图表
  renderChart() {
    if (!this.lineChart) return;
    
    // 根据当前选择的时间范围获取数据
    const { timeRange } = this.data;
    const chartData = this.data[`${timeRange}Data`];
    
    // 创建图表配置
    const option = {
      title: {
        text: `产品销售趋势（${this.getTimeRangeTitle(timeRange)}）`,
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: '#cccccc',
            width: 1,
            type: 'dashed'
          }
        }
      },
      legend: {
        data: ['产品A', '产品B'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: chartData.xAxis,
        axisLabel: {
          interval: timeRange === 'month' ? 1 : 0,
          rotate: timeRange === 'month' ? 30 : 0
        }
      },
      yAxis: {
        type: 'value',
        name: '销售量（件）',
        axisLine: {
          show: true
        },
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series: [
        {
          name: '产品A',
          type: 'line',
          data: chartData.series[0].data,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#5470c6'
          },
          lineStyle: {
            width: 3
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(84, 112, 198, 0.5)' },
                { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
              ]
            }
          }
        },
        {
          name: '产品B',
          type: 'line',
          data: chartData.series[1].data,
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#91cc75'
          },
          lineStyle: {
            width: 3
          }
        }
      ]
    };
    
    // 设置图表配置
    this.lineChart.setOption(option, true);
  },
  
  // 获取时间范围标题
  getTimeRangeTitle(range) {
    const titles = {
      'week': '周视图',
      'month': '月视图',
      'year': '年视图'
    };
    return titles[range] || '';
  },
  
  // 页面卸载时销毁图表实例
  onUnload() {
    if (this.lineChart) {
      this.lineChart.dispose();
      this.lineChart = null;
    }
  }
});

4. 样式设置（index.ttss）
.container {
  padding: 16px;
  background-color: #fff;
}

.chart-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
}

.control-panel {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.time-selector {
  display: flex;
  border-radius: 4px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
}

.time-selector view {
  padding: 6px 16px;
  font-size: 14px;
  background-color: #f5f5f5;
  color: #333;
}

.time-selector view.active {
  background-color: #5470c6;
  color: #fff;
}

.chart-description {
  margin-top: 16px;
  font-size: 12px;
  color: #666;
  text-align: center;
}

饼图的完整实现示例
饼图适合展示部分与整体的关系，如市场份额、占比分析等。
项目结构
my-project/
  ├── pages/
  │   └── pie-chart/
  │       ├── index.json  // 组件配置
  │       ├── index.js    // 页面逻辑
  │       ├── index.ttml  // 页面模板
  │       └── index.ttss  // 页面样式

1. JSON配置（index.json）
{
  "usingComponents": {
    "light-chart": "@bytedance/lightcharts"
  }
}

2. TTML组件设置（index.ttml）
<view class="container">
  <view class="chart-title">产品销售占比分析</view>
  
  <view class="chart-container">
    <light-chart 
      id="pieChart" 
      style="width: 100%; height: 350px;" 
      bindready="onChartReady"
      bindclick="onChartClick">
    </light-chart>
  </view>
  
  <view class="data-summary">
    <view class="summary-title">销售总额</view>
    <view class="summary-value">{{totalSales}}万元</view>
    <view class="summary-item" tt:for="{{pieData}}" tt:key="name">
      <view class="item-color" style="background-color: {{item.color}};"></view>
      <view class="item-name">{{item.name}}</view>
      <view class="item-value">{{item.value}}万元</view>
      <view class="item-percent">{{item.percent}}%</view>
    </view>
  </view>
</view>

3. JS回调函数实现（index.js）
Page({
  data: {
    pieData: [
      { name: '智能手机', value: 1048, color: '#5470c6', percent: 44.3 },
      { name: '平板电脑', value: 735, color: '#91cc75', percent: 31.1 },
      { name: '智能手表', value: 580, color: '#fac858', percent: 24.6 },
      { name: '智能音箱', value: 484, color: '#ee6666', percent: 20.5 },
      { name: '其他设备', value: 300, color: '#73c0de', percent: 12.7 }
    ],
    totalSales: 3147
  },
  
  // 图表实例
  pieChart: null,
  
  onLoad() {
    // 计算百分比
    this.calculatePercent();
  },
  
  // 计算百分比
  calculatePercent() {
    const { pieData, totalSales } = this.data;
    const newPieData = pieData.map(item => {
      return {
        ...item,
        percent: ((item.value / totalSales) * 100).toFixed(1)
      };
    });
    
    this.setData({ pieData: newPieData });
  },
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.pieChart = e.detail.chart;
    
    // 渲染图表
    this.renderPieChart();
  },
  
  // 渲染饼图
  renderPieChart() {
    if (!this.pieChart) return;
    
    const { pieData } = this.data;
    const colors = pieData.map(item => item.color);
    
    // 创建图表配置
    const option = {
      color: colors,
      title: {
        text: '产品销售占比',
        left: 'center',
        top: 10,
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c}万元 ({d}%)'
      },
      legend: {
        orient: 'horizontal',
        bottom: 10,
        data: pieData.map(item => item.name)
      },
      series: [
        {
          name: '销售额',
          type: 'pie',
          radius: ['40%', '70%'], // 环形图
          center: ['50%', '50%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2
          },
          label: {
            show: false,
            position: 'center'
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '18',
              fontWeight: 'bold'
            }
          },
          labelLine: {
            show: false
          },
          data: pieData.map(item => ({
            name: item.name,
            value: item.value
          }))
        }
      ]
    };
    
    // 设置图表配置
    this.pieChart.setOption(option);
  },
  
  // 图表点击事件处理
  onChartClick(e) {
    const { seriesType, dataIndex } = e.detail;
    
    if (seriesType === 'pie') {
      const item = this.data.pieData[dataIndex];
      
      tt.showToast({
        title: `${item.name}: ${item.value}万元 (${item.percent}%)`,
        icon: 'none'
      });
    }
  },
  
  // 页面卸载时销毁图表实例
  onUnload() {
    if (this.pieChart) {
      this.pieChart.dispose();
      this.pieChart = null;
    }
  }
});

4. 样式设置（index.ttss）
.container {
  padding: 16px;
  background-color: #fff;
}

.chart-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
}

.chart-container {
  margin-bottom: 20px;
}

.data-summary {
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.summary-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 16px;
}

.summary-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.item-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.item-name {
  flex: 1;
  font-size: 14px;
}

.item-value {
  font-size: 14px;
  font-weight: bold;
  margin-right: 8px;
}

.item-percent {
  font-size: 14px;
  color: #666;
  width: 50px;
  text-align: right;
}

组合图表的实现方法
组合图表可以在同一个图表中展示不同类型的数据，如柱状图和折线图的组合，多坐标轴的组合等。
项目结构
my-project/
  ├── pages/
  │   └── combo-chart/
  │       ├── index.json  // 组件配置
  │       ├── index.js    // 页面逻辑
  │       ├── index.ttml  // 页面模板
  │       └── index.ttss  // 页面样式

1. JSON配置（index.json）
{
  "usingComponents": {
    "light-chart": "@bytedance/lightcharts"
  }
}

2. TTML组件设置（index.ttml）
<view class="container">
  <view class="chart-title">销售额与利润率分析</view>
  
  <view class="chart-tabs">
    <view class="tab {{activeTab === 'combo' ? 'active' : ''}}" bindtap="switchTab" data-tab="combo">销售与利润</view>
    <view class="tab {{activeTab === 'multi' ? 'active' : ''}}" bindtap="switchTab" data-tab="multi">多坐标轴</view>
  </view>
  
  <light-chart 
    id="comboChart" 
    style="width: 100%; height: 400px;" 
    bindready="onChartReady">
  </light-chart>
  
  <view class="chart-description">
    <text>数据来源：2023年季度财务报告</text>
  </view>
</view>

3. JS回调函数实现（index.js）
Page({
  data: {
    activeTab: 'combo',
    salesData: [120, 200, 150, 80, 70, 110, 130],
    profitData: [20, 30, 25, 15, 10, 20, 25],
    profitRateData: [0.17, 0.15, 0.17, 0.19, 0.14, 0.18, 0.19],
    months: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
    
    // 多坐标轴数据
    temperature: [10, 15, 20, 25, 22, 28, 30],
    rainfall: [20, 30, 10, 35, 40, 25, 15],
    humidity: [40, 50, 60, 70, 65, 55, 45]
  },
  
  // 图表实例
  comboChart: null,
  
  onLoad() {
    // 页面加载时的逻辑
  },
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.comboChart = e.detail.chart;
    
    // 初始渲染
    this.renderChart();
  },
  
  // 切换标签页
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ activeTab: tab });
    
    // 重新渲染图表
    this.renderChart();
  },
  
  // 渲染图表
  renderChart() {
    if (!this.comboChart) return;
    
    const { activeTab } = this.data;
    
    if (activeTab === 'combo') {
      this.renderComboChart();
    } else {
      this.renderMultiAxisChart();
    }
  },
  
  // 渲染柱状图和折线图组合
  renderComboChart() {
    const { salesData, profitRateData, months } = this.data;
    
    const option = {
      title: {
        text: '销售额与利润率分析',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999'
          }
        },
        formatter: function(params) {
          let result = `${params[0].name}<br/>`;
          params.forEach(param => {
            if (param.seriesName === '销售额') {
              result += `${param.seriesName}: ${param.value}万元<br/>`;
            } else {
              result += `${param.seriesName}: ${(param.value * 100).toFixed(1)}%<br/>`;
            }
          });
          return result;
        }
      },
      legend: {
        data: ['销售额', '利润率'],
        bottom: 10
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: months,
        axisPointer: {
          type: 'shadow'
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '销售额（万元）',
          position: 'left',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#5470c6'
            }
          }
        },
        {
          type: 'value',
          name: '利润率',
          position: 'right',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#91cc75'
            }
          },
          axisLabel: {
            formatter: '{value * 100}%'
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '销售额',
          type: 'bar',
          data: salesData,
          itemStyle: {
            color: '#5470c6'
          },
          barWidth: '40%'
        },
        {
          name: '利润率',
          type: 'line',
          yAxisIndex: 1,
          data: profitRateData,
          itemStyle: {
            color: '#91cc75'
          },
          symbol: 'circle',
          symbolSize: 8,
          smooth: true,
          lineStyle: {
            width: 3
          }
        }
      ]
    };
    
    this.comboChart.setOption(option, true);
  },
  
  // 渲染多坐标轴图表
  renderMultiAxisChart() {
    const { temperature, rainfall, humidity, months } = this.data;
    
    const option = {
      title: {
        text: '气象数据多维分析',
        left: 'center',
        textStyle: {
          fontSize: 16,
          fontWeight: 'bold'
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['温度', '降雨量', '湿度'],
        bottom: 10
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '15%'
      },
      xAxis: {
        type: 'category',
        data: months
      },
      yAxis: [
        {
          type: 'value',
          name: '温度（°C）',
          position: 'left',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#ee6666'
            }
          }
        },
        {
          type: 'value',
          name: '降雨量（mm）',
          position: 'right',
          axisLine: {
            show: true,
            lineStyle: {
              color: '#5470c6'
            }
          }
        },
        {
          type: 'value',
          name: '湿度（%）',
          position: 'right',
          offset: 80,
          axisLine: {
            show: true,
            lineStyle: {
              color: '#91cc75'
            }
          },
          min: 0,
          max: 100
        }
      ],
      series: [
        {
          name: '温度',
          type: 'line',
          yAxisIndex: 0,
          data: temperature,
          itemStyle: {
            color: '#ee6666'
          },
          smooth: true,
          lineStyle: {
            width: 3
          }
        },
        {
          name: '降雨量',
          type: 'bar',
          yAxisIndex: 1,
          data: rainfall,
          itemStyle: {
            color: '#5470c6'
          },
          barWidth: '40%'
        },
        {
          name: '湿度',
          type: 'line',
          yAxisIndex: 2,
          data: humidity,
          itemStyle: {
            color: '#91cc75'
          },
          smooth: true,
          lineStyle: {
            width: 3,
            type: 'dashed'
          }
        }
      ]
    };
    
    this.comboChart.setOption(option, true);
  },
  
  // 页面卸载时销毁图表实例
  onUnload() {
    if (this.comboChart) {
      this.comboChart.dispose();
      this.comboChart = null;
    }
  }
});

4. 样式设置（index.ttss）
.container {
  padding: 16px;
  background-color: #fff;
}

.chart-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
}

.chart-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.tab {
  padding: 8px 16px;
  font-size: 14px;
  color: #666;
  position: relative;
  cursor: pointer;
}

.tab.active {
  color: #5470c6;
  font-weight: bold;
}

.tab.active:after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #5470c6;
}

.chart-description {
  margin-top: 16px;
  font-size: 12px;
  color: #666;
  text-align: center;
}

高级功能使用示例
本节提供了Light Chart的高级功能使用示例，包括动态数据更新方法、交互事件处理、自定义样式和主题以及响应式布局实现。通过这些示例，开发者可以更好地理解如何使用Light Chart实现复杂的数据可视化需求。
动态数据更新方法
在实际应用中，图表数据往往需要动态更新，如实时监控、数据轮询等场景。Light Chart提供了灵活的数据更新机制，可以轻松实现动态数据更新。
实时数据更新
以下示例展示了如何实现实时数据更新：
Page({
  data: {
    chartData: Array(60).fill(0),
    timer: null
  },
  
  // 图表实例
  chart: null,
  
  onLoad() {
    // 初始化数据
    this.initData();
  },
  
  // 初始化数据
  initData() {
    const chartData = this.data.chartData.map(() => Math.round(Math.random() * 100));
    this.setData({ chartData });
  },
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.chart = e.detail.chart;
    
    // 初始渲染
    this.renderChart();
    
    // 开始数据更新
    this.startDataUpdate();
  },
  
  // 渲染图表
  renderChart() {
    if (!this.chart)  return;
    
    const option = {
      title: {
        text: '实时数据监控',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#283b56'
          }
        }
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: Array(60).fill(0).map((_, i) => i)
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100
      },
      series: [{
        name: '实时数据',
        type: 'line',
        data: this.data.chartData,
        smooth: true,
        symbol: 'none',
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(84, 112, 198, 0.5)' },
              { offset: 1, color: 'rgba(84, 112, 198, 0.1)' }
            ]
          }
        }
      }]
    };
    
    this.chart.setOption(option);
  },
  
  // 开始数据更新
  startDataUpdate() {
    // 清除可能存在的定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    
    // 创建新的定时器，每秒更新一次数据
    const timer = setInterval(() => {
      this.updateData();
    }, 1000);
    
    this.setData({ timer });
  },
  
  // 更新数据
  updateData() {
    if (!this.chart) return;
    
    // 获取当前数据
    const chartData = [...this.data.chartData];
    
    // 移除第一个数据点，并添加一个新的数据点
    chartData.shift();
    chartData.push(Math.round(Math.random() * 100));
    
    // 更新数据状态
    this.setData({ chartData });
    
    // 更新图表
    this.chart.setOption({
      series: [{
        data: chartData
      }]
    });
  },
  
  // 页面卸载时清除定时器并销毁图表实例
  onUnload() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
});

数据轮询更新
对于需要从服务器定期获取数据的场景，可以使用数据轮询更新：
Page({
  data: {
    chartData: [],
    pollingInterval: 30000, // 30秒轮询一次
    timer: null,
    lastUpdateTime: ''
  },
  
  // 图表实例
  chart: null,
  
  onLoad() {
    // 首次加载数据
    this.fetchData();
  },
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.chart = e.detail.chart;
    
    // 如果已有数据，则渲染图表
    if (this.data.chartData.length > 0) {
      this.renderChart();
    }
    
    // 开始数据轮询
    this.startPolling();
  },
  
  // 开始数据轮询
  startPolling() {
    // 清除可能存在的定时器
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    
    // 创建新的定时器
    const timer = setInterval(() => {
      this.fetchData();
    }, this.data.pollingInterval);
    
    this.setData({ timer });
  },
  
  // 从服务器获取数据
  fetchData() {
    // 显示加载提示
    tt.showLoading({
      title: '加载数据中...'
    });
    
    // 模拟API请求
    setTimeout(() => {
      // 生成模拟数据
      const now = new Date();
      const chartData = [];
      
      for (let i = 0; i < 24; i++) {
        chartData.push({
          time: `${i}:00`,
          value: Math.round(Math.random() * 1000)
        });
      }
      
      // 更新数据和时间
      this.setData({
        chartData,
        lastUpdateTime: now.toLocaleTimeString()
      });
      
      // 渲染或更新图表
      if (this.chart) {
        this.renderChart();
      }
      
      // 隐藏加载提示
      tt.hideLoading();
    }, 1000);
  },
  
  // 渲染图表
  renderChart() {
    if (!this.chart) return;
    
    const { chartData, lastUpdateTime } = this.data;
    
    const option = {
      title: {
        text: '每小时销售数据',
        subtext: `最后更新时间: ${lastUpdateTime}`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}: {c} 件'
      },
      xAxis: {
        type: 'category',
        data: chartData.map(item => item.time),
        axisLabel: {
          interval: 2
        }
      },
      yAxis: {
        type: 'value',
        name: '销售量（件）'
      },
      series: [{
        name: '销售量',
        type: 'bar',
        data: chartData.map(item => item.value),
        itemStyle: {
          color: function(params) {
            // 根据数值大小设置不同颜色
            const value = params.value;
            if (value > 800) return '#ee6666';
            if (value > 500) return '#5470c6';
            return '#91cc75';
          }
        }
      }]
    };
    
    this.chart.setOption(option, true);
  },
  
  // 手动刷新数据
  refreshData() {
    this.fetchData();
  },
  
  // 页面卸载时清除定时器并销毁图表实例
  onUnload() {
    if (this.data.timer) {
      clearInterval(this.data.timer);
    }
    
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
});

大数据量更新的性能优化
当需要处理和更新大量数据时，需要考虑性能优化：
Page({
  data: {
    rawData: [], // 原始完整数据
    displayData: [], // 当前显示的数据
    dataSize: 10000, // 原始数据量
    displaySize: 100, // 显示数据量
    currentIndex: 0 // 当前显示数据的起始索引
  },
  
  // 图表实例
  chart: null,
  
  onLoad() {
    // 生成大量模拟数据
    this.generateData();
  },
  
  // 生成模拟数据
  generateData() {
    const rawData = [];
    
    for (let i = 0; i < this.data.dataSize; i++) {
      rawData.push({
        x: i,
        y: Math.sin(i / 100) * 100 + Math.random() * 20
      });
    }
    
    // 初始化显示数据
    const displayData = rawData.slice(0, this.data.displaySize);
    
    this.setData({
      rawData,
      displayData
    });
  },
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.chart = e.detail.chart;
    
    // 渲染图表
    this.renderChart();
  },
  
  // 渲染图表
  renderChart() {
    if (!this.chart) return;
    
    const { displayData, currentIndex, displaySize, dataSize } = this.data;
    
    const option = {
      title: {
        text: '大数据量展示优化',
        subtext: `当前显示: ${currentIndex} - ${currentIndex + displaySize} (共${dataSize}条)`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params) {
          return `索引: ${params[0].value[0]}<br/>值: ${params[0].value[1].toFixed(2)}`;
        }
      },
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          type: 'slider',
          start: 0,
          end: 100
        }
      ],
      xAxis: {
        type: 'value',
        min: currentIndex,
        max: currentIndex + displaySize - 1
      },
      yAxis: {
        type: 'value'
      },
      series: [{
        type: 'line',
        data: displayData.map(item => [item.x, item.y]),
        symbol: 'none',
        sampling: 'lttb', // 使用LTTB采样算法提高性能
        animation: false // 关闭动画提高性能
      }]
    };
    
    this.chart.setOption(option, true);
  },
  
  // 加载下一页数据
  loadNextPage() {
    const { rawData, currentIndex, displaySize, dataSize } = this.data;
    
    // 计算新的起始索引，确保不超出数据范围
    const newIndex = Math.min(currentIndex + displaySize, dataSize - displaySize);
    
    // 如果已经是最后一页，则不处理
    if (newIndex === currentIndex) return;
    
    // 获取新的显示数据
    const displayData = rawData.slice(newIndex, newIndex + displaySize);
    
    // 更新状态
    this.setData({
      currentIndex: newIndex,
      displayData
    });
    
    // 更新图表
    this.renderChart();
  },
  
  // 加载上一页数据
  loadPrevPage() {
    const { rawData, currentIndex, displaySize } = this.data;
    
    // 计算新的起始索引，确保不小于0
    const newIndex = Math.max(currentIndex - displaySize, 0);
    
    // 如果已经是第一页，则不处理
    if (newIndex === currentIndex) return;
    
    // 获取新的显示数据
    const displayData = rawData.slice(newIndex, newIndex + displaySize);
    
    // 更新状态
    this.setData({
      currentIndex: newIndex,
      displayData
    });
    
    // 更新图表
    this.renderChart();
  },
  
  // 页面卸载时销毁图表实例
  onUnload() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
});

交互事件处理
Light Chart提供了丰富的交互事件，可以实现点击、触摸等交互功能，增强用户体验。
点击事件处理
Page({
  data: {
    pieData: [
      { name: '直接访问', value: 335 },
      { name: '邮件营销', value: 310 },
      { name: '联盟广告', value: 234 },
      { name: '视频广告', value: 135 },
      { name: '搜索引擎', value: 1548 }
    ],
    selectedItem: null
  },
  
  // 图表实例
  chart: null,
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.chart = e.detail.chart;
    
    // 渲染图表
    this.renderChart();
    
    // 绑定点击事件
    this.chart.on('click', this.handleChartClick.bind(this));
  },
  
  // 渲染图表
  renderChart() {
    if (!this.chart) return;
    
    const { pieData, selectedItem } = this.data;
    
    const option = {
      title: {
        text: '访问来源',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: pieData.map(item => item.name)
      },
      series: [
        {
          name: '访问来源',
          type: 'pie',
          radius: '50%',
          center: ['50%', '60%'],
          data: pieData.map(item => {
            // 如果有选中项，则突出显示
            if (selectedItem && item.name === selectedItem.name) {
              return {
                name: item.name,
                value: item.value,
                itemStyle: {
                  color: '#5470c6',
                  borderWidth: 3,
                  borderColor: '#fff'
                },
                emphasis: {
                  scale: true,
                  scaleSize: 15
                }
              };
            }
            return item;
          }),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    
    this.chart.setOption(option);
  },
  
  // 处理图表点击事件
  handleChartClick(params) {
    // 只处理饼图的点击事件
    if (params.componentType === 'series' && params.seriesType === 'pie') {
      const selectedItem = {
        name: params.name,
        value: params.value,
        percent: params.percent
      };
      
      // 更新选中项
      this.setData({ selectedItem });
      
      // 更新图表
      this.renderChart();
      
      // 显示详细信息
      tt.showModal({
        title: '数据详情',
        content: `${selectedItem.name}: ${selectedItem.value} (${selectedItem.percent}%)`,
        showCancel: false
      });
    }
  },
  
  // 清除选中状态
  clearSelection() {
    this.setData({ selectedItem: null });
    this.renderChart();
  },
  
  // 页面卸载时销毁图表实例
  onUnload() {
    if (this.chart) {
      // 解绑事件
      this.chart.off('click');
      
      // 销毁实例
      this.chart.dispose();
      this.chart = null;
    }
  }
});

图表联动实现
多个图表之间的联动可以提供更丰富的数据展示：
Page({
  data: {
    pieData: [
      { name: '直接访问', value: 335 },
      { name: '邮件营销', value: 310 },
      { name: '联盟广告', value: 234 },
      { name: '视频广告', value: 135 },
      { name: '搜索引擎', value: 1548 }
    ],
    barData: {
      '直接访问': [120, 132, 101, 134, 90, 230, 210],
      '邮件营销': [220, 182, 191, 234, 290, 330, 310],
      '联盟广告': [150, 232, 201, 154, 190, 330, 410],
      '视频广告': [98, 77, 101, 99, 40, 30, 94],
      '搜索引擎': [320, 332, 301, 334, 390, 330, 320]
    },
    days: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
    selectedSource: '搜索引擎' // 默认选中的数据源
  },
  
  // 图表实例
  pieChart: null,
  barChart: null,
  
  onLoad() {
    // 页面加载时的逻辑
  },
  
  // 饼图准备就绪回调
  onPieChartReady(e) {
    // 获取饼图实例
    this.pieChart = e.detail.chart;
    
    // 渲染饼图
    this.renderPieChart();
    
    // 绑定点击事件
    this.pieChart.on('click', this.handlePieChartClick.bind(this));
  },
  
  // 柱状图准备就绪回调
  onBarChartReady(e) {
    // 获取柱状图实例
    this.barChart = e.detail.chart;
    
    // 渲染柱状图
    this.renderBarChart();
  },
  
  // 渲染饼图
  renderPieChart() {
    if (!this.pieChart) return;
    
    const { pieData, selectedSource } = this.data;
    
    const option = {
      title: {
        text: '访问来源分布',
        left: 'center'
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: pieData.map(item => item.name)
      },
      series: [
        {
          name: '访问来源',
          type: 'pie',
          radius: '50%',
          center: ['50%', '60%'],
          data: pieData.map(item => {
            // 如果是选中的数据源，则突出显示
            if (item.name === selectedSource) {
              return {
                name: item.name,
                value: item.value,
                itemStyle: {
                  color: '#5470c6',
                  borderWidth: 3,
                  borderColor: '#fff'
                }
              };
            }
            return item;
          }),
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    };
    
    this.pieChart.setOption(option);
  },
  
  // 渲染柱状图
  renderBarChart() {
    if (!this.barChart) return;
    
    const { barData, days, selectedSource } = this.data;
    
    const option = {
      title: {
        text: `${selectedSource}一周趋势`,
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: days
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: selectedSource,
          type: 'bar',
          data: barData[selectedSource],
          itemStyle: {
            color: '#5470c6'
          },
          barWidth: '40%'
        }
      ]
    };
    
    this.barChart.setOption(option, true);
  },
  
  // 处理饼图点击事件
  handlePieChartClick(params) {
    if (params.componentType === 'series' && params.seriesType === 'pie') {
      const selectedSource = params.name;
      
      // 更新选中的数据源
      this.setData({ selectedSource });
      
      // 更新饼图和柱状图
      this.renderPieChart();
      this.renderBarChart();
    }
  },
  
  // 页面卸载时销毁图表实例
  onUnload() {
    if (this.pieChart) {
      this.pieChart.off('click');
      this.pieChart.dispose();
      this.pieChart = null;
    }
    
    if (this.barChart) {
      this.barChart.dispose();
      this.barChart = null;
    }
  }
});

自定义样式和主题
Light Chart提供了丰富的样式和主题定制功能，可以根据需要自定义图表的外观。
主题配置方法
Page({
  data: {
    themes: {
      'default': {
        color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],
        backgroundColor: '#ffffff',
        textStyle: {}
      },
      'dark': {
        color: ['#4992ff', '#7cffb2', '#fddd60', '#ff6e76', '#58d9f9', '#05c091', '#ff9f7f', '#8d48e3', '#dd79ff'],
        backgroundColor: '#333333',
        textStyle: {
          color: '#ffffff'
        }
      },
      'vintage': {
        color: ['#d87c7c', '#919e8b', '#d7ab82', '#6e7074', '#61a0a8', '#efa18d', '#787464', '#cc7e63', '#724e58'],
        backgroundColor: '#fef8ef',
        textStyle: {
          color: '#333333'
        }
      },
      'roma': {
        color: ['#e01f54', '#001852', '#f5e8c8', '#b8d2c7', '#c6b38e', '#a4d8c2', '#f3d999', '#d3758f', '#dcc392'],
        backgroundColor: '#f7f7f7',
        textStyle: {
          color: '#333333'
        }
      }
    },
    currentTheme: 'default'
  },
  
  // 图表实例
  chart: null,
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.chart = e.detail.chart;
    
    // 渲染图表
    this.renderChart();
  },
  
  // 渲染图表
  renderChart() {
    if (!this.chart) return;
    
    const { currentTheme, themes } = this.data;
    const theme = themes[currentTheme];
    
    const option = {
      color: theme.color,
      backgroundColor: theme.backgroundColor,
      textStyle: theme.textStyle,
      title: {
        text: '主题演示',
        left: 'center',
        textStyle: {
          color: theme.textStyle.color
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: ['销售额', '利润', '增长率'],
        bottom: 10,
        textStyle: {
          color: theme.textStyle.color
        }
      },
      xAxis: {
        type: 'category',
        data: ['1月', '2月', '3月', '4月', '5月', '6月'],
        axisLine: {
          lineStyle: {
            color: theme.textStyle.color || '#333'
          }
        },
        axisLabel: {
          color: theme.textStyle.color
        }
      },
      yAxis: [
        {
          type: 'value',
          name: '金额',
          position: 'left',
          axisLine: {
            lineStyle: {
              color: theme.color[0]
            }
          },
          axisLabel: {
            color: theme.textStyle.color
          },
          splitLine: {
            lineStyle: {
              color: theme.backgroundColor === '#ffffff' ? '#eee' : '#555'
            }
          }
        },
        {
          type: 'value',
          name: '增长率',
          position: 'right',
          axisLine: {
            lineStyle: {
              color: theme.color[2]
            }
          },
          axisLabel: {
            color: theme.textStyle.color,
            formatter: '{value}%'
          },
          splitLine: {
            show: false
          }
        }
      ],
      series: [
        {
          name: '销售额',
          type: 'bar',
          data: [120, 180, 150, 200, 170, 210]
        },
        {
          name: '利润',
          type: 'bar',
          data: [20, 40, 35, 45, 40, 50]
        },
        {
          name: '增长率',
          type: 'line',
          yAxisIndex: 1,
          data: [10, 15, 8, 20, 12, 18]
        }
      ]
    };
    
    this.chart.setOption(option, true);
  },
  
  // 切换主题
  switchTheme(e) {
    const theme = e.currentTarget.dataset.theme;
    
    this.setData({ currentTheme: theme });
    this.renderChart();
  },
  
  // 页面卸载时销毁图表实例
  onUnload() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
});

响应式布局实现
在不同尺寸的设备上，图表需要能够自适应调整，以提供良好的用户体验。
屏幕尺寸适配方法
Page({
  data: {
    chartData: [
      { name: '产品A', value: 200 },
      { name: '产品B', value: 180 },
      { name: '产品C', value: 150 },
      { name: '产品D', value: 120 },
      { name: '产品E', value: 100 }
    ],
    screenWidth: 375, // 默认屏幕宽度
    screenHeight: 667, // 默认屏幕高度
    isLandscape: false // 是否横屏
  },
  
  // 图表实例
  chart: null,
  
  onLoad() {
    // 获取设备信息
    this.getDeviceInfo();
    
    // 监听屏幕旋转
    tt.onDeviceOrientationChange(this.handleOrientationChange.bind(this));
  },
  
  // 获取设备信息
  getDeviceInfo() {
    try {
      const systemInfo = tt.getSystemInfoSync();
      
      this.setData({
        screenWidth: systemInfo.windowWidth,
        screenHeight: systemInfo.windowHeight,
        isLandscape: systemInfo.windowWidth > systemInfo.windowHeight
      });
    } catch (e) {
      console.error('获取设备信息失败：', e);
    }
  },
  
  // 处理屏幕旋转
  handleOrientationChange(res) {
    // 更新屏幕方向
    this.setData({
      isLandscape: res.value === 'landscape'
    });
    
    // 重新渲染图表
    if (this.chart) {
      // 延迟一下，确保DOM已更新
      setTimeout(() => {
        this.chart.resize();
        this.renderChart();
      }, 300);
    }
  },
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.chart = e.detail.chart;
    
    // 渲染图表
    this.renderChart();
  },
  
  // 渲染图表
  renderChart() {
    if (!this.chart) return;
    
    const { chartData, screenWidth, isLandscape } = this.data;
    
    // 准备数据
    const names = chartData.map(item => item.name);
    const values = chartData.map(item => item.value);
    
    // 根据屏幕尺寸和方向调整配置
    const fontSize = screenWidth < 360 ? 10 : (screenWidth < 768 ? 12 : 14);
    const barWidth = screenWidth < 360 ? 15 : (screenWidth < 768 ? 20 : 30);
    const legendPosition = isLandscape ? 'right' : 'bottom';
    
    // 创建图表配置
    const option = {
      title: {
        text: '产品销售数据',
        left: 'center',
        textStyle: {
          fontSize: fontSize + 4
        }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      legend: {
        data: ['销售量'],
        orient: isLandscape ? 'vertical' : 'horizontal',
        [legendPosition]: isLandscape ? '10%' : 10
      },
      grid: {
        left: '3%',
        right: isLandscape ? '15%' : '4%',
        bottom: isLandscape ? '3%' : '15%',
        top: '15%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: names,
        axisLabel: {
          interval: 0,
          rotate: isLandscape ? 0 : 30,
          fontSize: fontSize
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: fontSize
        }
      },
      series: [
        {
          name: '销售量',
          type: 'bar',
          data: values,
          barWidth: barWidth,
          label: {
            show: true,
            position: 'top',
            fontSize: fontSize
          }
        }
      ]
    };
    
    this.chart.setOption(option, true);
  },
  
  // 页面大小变化时调整图表
  onResize() {
    if (this.chart) {
      this.chart.resize();
    }
  },
  
  // 页面卸载时销毁图表实例和事件监听
  onUnload() {
    // 移除屏幕旋转监听
    tt.offDeviceOrientationChange(this.handleOrientationChange);
    
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
});

动态调整图表大小
Page({
  data: {
    chartData: [
      { name: '产品A', value: 200 },
      { name: '产品B', value: 180 },
      { name: '产品C', value: 150 },
      { name: '产品D', value: 120 },
      { name: '产品E', value: 100 }
    ],
    chartHeight: 300,
    isExpanded: false
  },
  
  // 图表实例
  chart: null,
  
  // 图表准备就绪回调
  onChartReady(e) {
    // 获取图表实例
    this.chart = e.detail.chart;
    
    // 渲染图表
    this.renderChart();
  },
  
  // 渲染图表
  renderChart() {
    if (!this.chart) return;
    
    const { chartData } = this.data;
    
    // 准备数据
    const names = chartData.map(item => item.name);
    const values = chartData.map(item => item.value);
    
    // 创建图表配置
    const option = {
      title: {
        text: '产品销售数据',
        left: 'center'
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        }
      },
      xAxis: {
        type: 'category',
        data: names,
        axisLabel: {
          interval: 0
        }
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '销售量',
          type: 'bar',
          data: values,
          barWidth: '40%',
          label: {
            show: true,
            position: 'top'
          }
        }
      ]
    };
    
    this.chart.setOption(option);
  },
  
  // 切换图表大小
  toggleChartSize() {
    const isExpanded = !this.data.isExpanded;
    const chartHeight = isExpanded ? 500 : 300;
    
    this.setData({
      isExpanded,
      chartHeight
    });
    
    // 延迟一下，确保DOM已更新
    setTimeout(() => {
      if (this.chart) {
        this.chart.resize();
      }
    }, 300);
  },
  
  // 页面卸载时销毁图表实例
  onUnload() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
});

配置选项
Light Chart提供了丰富的配置选项，可以满足各种图表需求。
标题配置
title: {
  text: '标题文本',
  subtext: '副标题文本',
  left: 'center',
  textStyle: {
    color: '#333',
    fontSize: 18
  }
}

图例配置
legend: {
  data: ['销售', '利润'],
  orient: 'horizontal',
  bottom: 10
}

坐标轴配置
xAxis: {
  type: 'category',
  data: ['一月', '二月', '三月'],
  axisLabel: {
    rotate: 45
  }
},
yAxis: {
  type: 'value',
  min: 0,
  max: 300
}

系列配置
series: [
  {
    name: '销售',
    type: 'bar',
    data: [120, 200, 150],
    itemStyle: {
      color: '#5470c6'
    }
  },
  {
    name: '利润',
    type: 'line',
    data: [20, 50, 30],
    itemStyle: {
      color: '#91cc75'
    }
  }
]

提示框配置
tooltip: {
  trigger: 'axis',
  formatter: '{b}: {c}'
}

图表示例
以下是几个常见图表类型的配置示例：
柱状图示例：
const option = {
  title: {
    text: '月度销售数据'
  },
  tooltip: {
    trigger: 'axis'
  },
  xAxis: {
    type: 'category',
    data: ['一月', '二月', '三月', '四月', '五月', '六月']
  },
  yAxis: {
    type: 'value'
  },
  series: [{
    data: [120, 200, 150, 80, 70, 110],
    type: 'bar',
    showBackground: true,
    backgroundStyle: {
      color: 'rgba(180, 180, 180, 0.2)'
    }
  }]
};

饼图示例：
const option = {
  title: {
    text: '销售占比',
    left: 'center'
  },
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '访问来源',
      type: 'pie',
      radius: '50%',
      data: [
        {value: 335, name: '直接访问'},
        {value: 310, name: '邮件营销'},
        {value: 234, name: '联盟广告'},
        {value: 135, name: '视频广告'},
        {value: 1548, name: '搜索引擎'}
      ]
    }
  ]
};

与开源图表库的比较
Light Chart作为字节跳动内部开发的图表库，与主流开源图表库（如ECharts）相比具有一些特点和差异。
与ECharts的主要差异
特性
Light Chart
ECharts
适用环境
专为Lynx环境设计
通用Web环境
体积大小
体积更小，适合移动端
体积相对较大
性能表现
在Lynx环境中性能更优
在通用Web环境中性能优秀
集成便捷性
与TTML的集成更加便捷
需要额外适配Lynx环境
功能完整性
针对内部业务场景优化
功能更全面，社区支持更丰富
自动降级
支持H5自动降级
需要手动处理兼容性
性能优势
Light Chart针对Lynx环境进行了专门优化，在移动端有更好的性能表现：
Light Chart的性能优势：
1. 高效渲染: 使用Canvas优化技术，渲染速度更快
2. 内存占用低: 针对移动设备进行了内存优化
3. 启动速度快: 相比开源图表库，启动和初始化时间更短
适配性优势
1. Lynx环境专属: 专为字节跳动内部Lynx环境设计，与Lynx深度集成
2. 自动降级: 支持H5自动降级，不需要额外处理
3. TTML集成: 提供了与TTML的无缝集成方案
功能特点
1. 配置丰富: 提供700多个配置选项，满足各种可视化需求
2. 专业图表: 针对业务场景提供了专业的图表类型
3. 简化API: 相比开源图表库，API设计更加简洁易用
最佳实践
性能优化
数据量控制
在移动端环境中，过大的数据量会导致渲染性能下降，建议：
1. 数据抽样: 对于大量数据点，可以使用抽样技术减少渲染点数
// 数据抽样示例
function sampleData(data, sampleRate) {
  const result = [];
  for (let i = 0; i < data.length; i += sampleRate) {
    result.push(data[i]);
  }
  return result;
}

// 使用示例
const originalData = [...]; // 假设有1000个数据点
const sampledData = sampleData(originalData, 5); // 每5个点取1个，减少到200个点

2. 分段加载: 对于时间序列数据，可以先加载最近的数据，然后按需加载历史数据
按需渲染
1. 可视区域渲染: 只渲染当前可视区域的图表，其他区域延迟加载
2. 节流更新: 对于频繁更新的数据，使用节流技术控制更新频率
交互优化
触摸事件优化
移动端环境下，触摸事件的优化对用户体验至关重要：
1. 合适的触摸区域: 确保图表中可点击元素有足够大的触摸区域
2. 减少交互延迟: 优化触摸响应速度
自适应布局
确保图表在不同尺寸的设备上都能良好显示：
自适应布局的关键点：
1. 根据屏幕宽度调整图表配置（字体大小、图例位置等）
2. 监听窗口大小变化，及时调用resize()方法
3. 为不同设备类型（手机、平板）准备不同的布局方案
业务场景优化建议
数据看板场景
对于数据看板类应用，建议：
1. 关键指标突出: 使用仪表盘或数字卡片突出显示关键指标
2. 多图表联动: 实现多图表之间的联动交互
实时监控场景
对于实时数据监控场景，建议：
1. 数据缓冲: 使用缓冲区存储历史数据，避免频繁更新导致的性能问题
2. 异常突出显示: 对异常数据进行突出显示
注意事项
1. 兼容性检查: 使用tt.canIUse('light-chart')检查设备支持情况
2. 内存管理: 不再使用的图表实例应调用dispose()方法释放资源
3. 事件处理: 合理使用事件监听，避免过多的事件绑定
4. 降级方案: 虽然Light Chart支持自动降级，但仍建议为不支持Canvas的设备准备替代方案
5. 主题定制: 根据应用的整体设计风格，定制图表主题以保持视觉一致性
结论
Light Chart作为字节跳动内部开发的图表库，在Lynx环境中具有显著的性能和集成优势。它提供了丰富的图表类型和配置选项，能够满足各种数据可视化需求。
适用场景
最适合：
- Lynx应用
- 字节跳动内部产品
- TTML项目
- 对性能要求高的移动端应用
优势场景：
- 数据分析看板
- 实时数据监控
- 电商销售分析
- 用户行为可视化
特色功能：
- 自动H5降级
- 高性能渲染
- 与Lynx深度集成
- 简洁API设计
总结
Light Chart是字节跳动内部项目，特别是Lynx环境下数据可视化的理想选择。它在性能、体积和集成便捷性方面具有明显优势，能够帮助开发者快速构建高性能的数据可视化应用。通过遵循本指南中的最佳实践，开发者可以充分发挥Light Chart的潜力，创建出美观、高效的数据可视化图表。
参考资源
- Light Chart官方文档
- TTML Lightcharts Demo(Lynx Playground)
- 在Lynx中使用Lightcharts图表库