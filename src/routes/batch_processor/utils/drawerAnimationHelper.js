/**
 * 抽屉动画助手工具
 * 提供统一的抽屉组件动画效果和样式
 *
 * @version 1.2.0
 * <AUTHOR> Agent
 * @updated 2025-06-20 - 修复重复动画问题
 */

/**
 * 获取抽屉容器样式 - 平滑左侧滑入动画
 * @param {boolean} isOpen - 抽屉是否打开
 * @param {boolean} isClosing - 抽屉是否正在关闭
 * @param {boolean} isAnimating - 抽屉是否处于动画状态
 * @param {string} width - 抽屉宽度
 * @returns {Object} 样式对象
 */
export const getDrawerContainerStyle = (
  isOpen,
  isClosing,
  isAnimating,
  width = '400px',
) => ({
  width,
  height: '100%',
  maxHeight: '100vh',
  // 从左侧滑入滑出 - 从左边滑入到左边滑出
  transform: isClosing
    ? 'translateX(-100%)' // 滑出到左边（隐藏）
    : isOpen
      ? 'translateX(0)' // 完全显示
      : 'translateX(-100%)', // 初始状态隐藏在左边
  // 使用更流畅的缓动函数
  transition: isClosing
    ? 'transform 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53), opacity 0.2s ease-in'
    : 'transform 0.4s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s ease-out',
  opacity: isClosing ? 0 : 1,
  transformOrigin: 'center center',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: 'transparent', // 背景由CSS类控制
  visibility: 'visible',
});

/**
 * 获取抽屉遮罩层样式 - 平滑淡入淡出
 * @param {boolean} isClosing - 抽屉是否正在关闭
 * @param {boolean} isAnimating - 抽屉是否处于动画状态
 * @returns {Object} 样式对象
 */
export const getDrawerOverlayStyle = (isClosing, isAnimating) => ({
  background:
    'radial-gradient(ellipse at center, rgba(0, 0, 0, 0.4) 0%, rgba(0, 0, 0, 0.28) 70%, rgba(0, 0, 0, 0.18) 100%)',
  backdropFilter: 'blur(12px) saturate(110%)',
  // 使用更平滑的过渡
  transition: isClosing
    ? 'opacity 0.2s ease-in, backdrop-filter 0.2s ease-in'
    : 'opacity 0.3s ease-out, backdrop-filter 0.3s ease-out',
  opacity: isClosing ? 0 : 1,
});

/**
 * 获取左侧抽屉遮罩层样式 - 半透明遮罩
 * @param {boolean} isClosing - 抽屉是否正在关闭
 * @param {boolean} isAnimating - 抽屉是否处于动画状态
 * @returns {Object} 样式对象
 */
export const getLeftDrawerOverlayStyle = (isClosing, isAnimating) => ({
  background: 'rgba(0, 0, 0, 0.3)', // 半透明黑色遮罩
  backdropFilter: 'blur(4px)', // 轻微模糊效果
  // 使用更平滑的过渡
  transition: isClosing
    ? 'opacity 0.2s ease-in, backdrop-filter 0.2s ease-in'
    : 'opacity 0.3s ease-out, backdrop-filter 0.3s ease-out',
  opacity: isClosing ? 0 : 1,
});

/**
 * 获取右侧定位但左滑动画的抽屉容器样式 - 从左侧滑入到右侧定位
 * @param {boolean} isOpen - 抽屉是否打开
 * @param {boolean} isClosing - 抽屉是否正在关闭
 * @param {boolean} isAnimating - 抽屉是否处于动画状态
 * @param {string} width - 抽屉宽度
 * @returns {Object} 样式对象
 */
export const getRightPositionLeftSlideContainerStyle = (
  isOpen,
  isClosing,
  isAnimating,
  width = '400px',
) => ({
  width,
  height: '100%',
  maxHeight: '100vh',
  // 使用左侧滑动动画：从左边滑入到右边，关闭时向左滑出
  transform: isClosing
    ? 'translateX(-100%)' // 滑出到左边（隐藏）
    : isOpen
      ? 'translateX(0)' // 完全显示在右侧
      : 'translateX(-100%)', // 初始状态隐藏在左边
  // 使用更流畅的缓动函数
  transition: isClosing
    ? 'transform 0.35s cubic-bezier(0.55, 0.085, 0.68, 0.53), opacity 0.2s ease-in'
    : 'transform 0.45s cubic-bezier(0.23, 1, 0.32, 1), opacity 0.3s ease-out',
  opacity: isClosing ? 0 : 1,
  transformOrigin: 'center center',
  display: 'flex',
  flexDirection: 'column',
  backgroundColor: 'transparent', // 背景由CSS类控制
  visibility: 'visible',
});

/**
 * 获取抽屉标题栏样式 - 紧凑优化的间距
 * @returns {Object} 样式对象
 */
export const getDrawerHeaderStyle = () => ({
  background:
    'linear-gradient(135deg, rgba(252, 228, 236, 0.3) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(227, 242, 253, 0.3) 100%)',
  borderBottom: '1px solid rgba(233, 30, 99, 0.08)',
  backdropFilter: 'blur(10px)',
  padding: '12px 16px', // 紧凑的头部间距
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  position: 'relative',
});

/**
 * 获取抽屉标题文本样式
 * 提供动态样式补充，主要样式由 .enhanced-drawer-title 类提供
 * @returns {Object} 样式对象
 */
export const getDrawerTitleStyle = () => ({
  margin: 0,
  padding: 0,
});

/**
 * 获取抽屉内容区样式 - 紧凑优化的间距
 * @returns {Object} 样式对象
 */
export const getDrawerContentStyle = () => ({
  display: 'flex',
  flexDirection: 'column',
  flex: '1 1 auto',
  minHeight: '0',
  overflow: 'hidden',
  padding: '12px 16px', // 紧凑的内容区间距
  gap: '12px', // 紧凑的元素间距
});

/**
 * 获取关闭按钮样式
 * @returns {Object} 样式对象
 */
export const getCloseButtonStyle = () => ({
  background: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(10px)',
  border: '1px solid rgba(233, 30, 99, 0.1)',
  boxShadow: '0 2px 8px rgba(233, 30, 99, 0.1)',
  borderRadius: '50%',
  width: '36px',
  height: '36px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  cursor: 'pointer',
  transition: 'all 0.25s cubic-bezier(0.23, 1, 0.32, 1)',
});

/**
 * 获取鼠标悬停时的关闭按钮样式
 * @returns {Object} 样式对象
 */
export const getCloseButtonHoverStyle = () => ({
  background: 'rgba(252, 228, 236, 0.8)',
  borderColor: 'rgba(233, 30, 99, 0.3)',
  transform: 'translateY(-1px)',
  boxShadow: '0 4px 12px rgba(233, 30, 99, 0.2)',
});

/**
 * 获取鼠标离开时的关闭按钮样式
 * @returns {Object} 样式对象
 */
export const getCloseButtonNormalStyle = () => ({
  background: 'rgba(255, 255, 255, 0.9)',
  borderColor: 'rgba(233, 30, 99, 0.1)',
  transform: 'translateY(0)',
  boxShadow: '0 2px 8px rgba(233, 30, 99, 0.1)',
});

/**
 * 获取侧滑指示器样式
 * 为指示器提供动态样式，主要样式由 .slide-indicator 类提供
 * @param {boolean} isAnimating - 是否处于动画状态
 * @returns {Object} 样式对象
 */
export const getSlideIndicatorStyle = isAnimating => ({
  animation: isAnimating
    ? 'none'
    : 'slideIndicatorBreath 3s ease-in-out infinite',
  opacity: isAnimating ? 0.5 : 0.8,
});

/**
 * 获取标题容器样式
 * @returns {Object} 样式对象
 */
export const getTitleContainerStyle = () => ({
  display: 'flex',
  alignItems: 'center',
  gap: '0.75rem',
});

/**
 * 获取CSS动画样式
 * @returns {string} CSS样式字符串
 */
export const getAnimationStyles = () => `
    /* 动画定义已移至CSS文件，此处移除以避免重复 */
  `;

/**
 * 获取列表项悬停样式
 * @returns {Object} 样式对象
 */
export const getListItemHoverStyle = () => ({
  transform: 'translateY(-2px)',
  boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1), 0 3px 10px rgba(0, 0, 0, 0.08)',
  borderColor: 'rgba(99, 102, 241, 0.3)',
  background: 'rgba(255, 255, 255, 1)',
});

/**
 * 获取列表项普通样式
 * @returns {Object} 样式对象
 */
export const getListItemNormalStyle = () => ({
  transform: 'translateY(0)',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1)',
  borderColor: 'rgba(99, 102, 241, 0.1)',
  background: 'rgba(255, 255, 255, 0.9)',
});

export default {
  getDrawerContainerStyle,
  getRightPositionLeftSlideContainerStyle,
  getDrawerOverlayStyle,
  getLeftDrawerOverlayStyle,
  getDrawerHeaderStyle,
  getDrawerContentStyle,
  getCloseButtonStyle,
  getCloseButtonHoverStyle,
  getCloseButtonNormalStyle,
  getAnimationStyles,
  getListItemHoverStyle,
  getListItemNormalStyle,
  getDrawerTitleStyle,
  getSlideIndicatorStyle,
  getTitleContainerStyle,
};
