/**
 * streamParser.js
 *
 * 负责解析流式数据响应，提取并处理AI生成的代码块
 * Stream response parser for AI-generated code blocks extraction and processing
 *
 * 主要功能 (Main Features):
 * 1. 解析流式响应JSON (Parse streaming JSON responses)
 * 2. 提取增量内容 (Extract incremental content)
 * 3. 识别并提取代码块 (Identify and extract code blocks)
 * 4. 解析代码结构（文件划分）(Parse code structure with file separation)
 *
 * 这是批量处理核心工作流的重要环节: AI请求 -> 流式响应 -> 解析代码 -> 上传
 * Critical component of batch processing workflow: AI request -> Stream response -> Parse code -> Upload
 */

// Constants for avoiding magic numbers
const STREAM_PARSER_CONSTANTS = {
  PREVIEW_LENGTH: {
    SHORT: 50,
    MEDIUM: 100,
    LONG: 200,
    EXTENDED: 300,
    FULL: 500,
  },
  LIMITS: {
    MAX_MATCHES: 100,
    MAX_SAMPLE_LINES: 3,
    MIN_SPLIT_PARTS: 2,
    MAX_FILE_LINES: 500,
  },
  TIMEOUTS: {
    PERFORMANCE_THRESHOLD: 2,
  },
};

/**
 * Process a single line of stream data and extract content
 * 处理单行流数据并提取内容
 * @param {string} line - Single line of stream response
 * @param {number} lineNumber - Line number for debugging
 * @param {Object} stats - Statistics object to update
 * @returns {string|null} Extracted content or null if invalid
 */
function processStreamLine(line, lineNumber, stats) {
  const trimmedLine = line.trim();

  // Skip non-JSON lines
  if (!trimmedLine.startsWith('{"id"')) {
    if (
      stats.sampleInvalidLines.length <
        STREAM_PARSER_CONSTANTS.LIMITS.MAX_SAMPLE_LINES &&
      trimmedLine.length > 0
    ) {
      stats.sampleInvalidLines.push({
        lineNumber,
        content:
          trimmedLine.length > STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.MEDIUM
            ? `${trimmedLine.substring(0, STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.MEDIUM)}...`
            : trimmedLine,
        reason: '不以{"id"开头',
      });
    }
    return null;
  }

  try {
    const jsonData = JSON.parse(trimmedLine);
    stats.validJsonLines++;

    if (jsonData.choices?.[0]?.delta?.content) {
      const { content } = jsonData.choices[0].delta;

      // Collect valid line samples
      if (
        stats.sampleValidLines.length <
        STREAM_PARSER_CONSTANTS.LIMITS.MAX_SAMPLE_LINES
      ) {
        stats.sampleValidLines.push({
          lineNumber,
          content,
          fullJson:
            trimmedLine.length > STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.LONG
              ? `${trimmedLine.substring(0, STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.LONG)}...`
              : trimmedLine,
        });
      }

      return content;
    } else {
      // Valid JSON but no delta.content
      if (
        stats.sampleInvalidLines.length <
        STREAM_PARSER_CONSTANTS.LIMITS.MAX_SAMPLE_LINES
      ) {
        stats.sampleInvalidLines.push({
          lineNumber,
          content:
            trimmedLine.length > STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.MEDIUM
              ? `${trimmedLine.substring(0, STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.MEDIUM)}...`
              : trimmedLine,
          reason: '缺少choices[0].delta.content',
        });
      }
    }
  } catch (e) {
    // JSON parsing error
    if (
      stats.sampleInvalidLines.length <
      STREAM_PARSER_CONSTANTS.LIMITS.MAX_SAMPLE_LINES
    ) {
      stats.sampleInvalidLines.push({
        lineNumber,
        content:
          trimmedLine.length > STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.MEDIUM
            ? `${trimmedLine.substring(0, STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.MEDIUM)}...`
            : trimmedLine,
        reason: `JSON解析错误: ${e.message}`,
      });
    }
    console.warn(`[streamParser] JSON解析错误: 行${lineNumber}`, e.message);
  }

  return null;
}

/**
 * Log parsing statistics and samples
 * 记录解析统计信息和样本
 * @param {Object} stats - Statistics object
 * @param {string} result - Final parsed result
 * @param {number} processingTime - Time taken for processing
 */
function logParsingStats(stats, result, processingTime) {
  console.log('[streamParser] ✅ 解析完成统计:');
  console.log(`[streamParser] 📊 总行数: ${stats.totalLines}`);
  console.log(`[streamParser] 📊 有效JSON行数: ${stats.validJsonLines}`);
  console.log(`[streamParser] 📊 提取内容片段数: ${stats.contentPartsCount}`);
  console.log(`[streamParser] 📏 提取内容总长度: ${result.length} 字符`);
  console.log(
    `[streamParser] ⏱️ 解析耗时: ${processingTime.toFixed(STREAM_PARSER_CONSTANTS.TIMEOUTS.PERFORMANCE_THRESHOLD)}ms`,
  );

  // Log valid line samples
  if (stats.sampleValidLines.length > 0) {
    console.log('[streamParser] 📋 有效行样本:');
    stats.sampleValidLines.forEach((sample, index) => {
      console.log(
        `[streamParser]   样本${index + 1} (行${sample.lineNumber}): "${sample.content}"`,
      );
      console.log(`[streamParser]   完整JSON: ${sample.fullJson}`);
    });
  }

  // Log invalid line samples
  if (stats.sampleInvalidLines.length > 0) {
    console.log('[streamParser] ⚠️ 无效行样本:');
    stats.sampleInvalidLines.forEach((sample, index) => {
      console.log(
        `[streamParser]   样本${index + 1} (行${sample.lineNumber}): ${sample.content}`,
      );
      console.log(`[streamParser]   原因: ${sample.reason}`);
    });
  }

  // Log content preview
  if (result.length > 0) {
    console.log(
      '[streamParser] 📝 提取内容前300字符:',
      result.substring(0, STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.EXTENDED),
    );
    console.log(
      '[streamParser] 📝 提取内容后300字符:',
      result.substring(
        Math.max(
          0,
          result.length - STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.EXTENDED,
        ),
      ),
    );
  } else {
    console.warn('[streamParser] ⚠️ 未提取到任何内容');
  }
}

/**
 * 从流式响应中提取增量内容
 * Extract incremental content from streaming response
 * 基于Python版本的extract_delta_content.py实现
 * Based on Python version extract_delta_content.py implementation
 *
 * 流数据通常格式为一系列JSON行，每行包含增量内容:
 * Stream data format: series of JSON lines, each containing incremental content:
 * {"id":"xxx","choices":[{"delta":{"content":"代码片段1"}}]}
 * {"id":"xxx","choices":[{"delta":{"content":"代码片段2"}}]}
 *
 * @param {string} streamResponse 完整的流式响应文本 (Complete streaming response text)
 * @returns {string} 拼接后的完整内容 (Concatenated complete content)
 */
function parseStreamData(streamResponse) {
  try {
    // 记录开始解析时间，用于性能分析 (Record start time for performance analysis)
    const startTime = performance.now();

    // 🔍 调试日志：输出原始数据信息 (Debug log: output raw data info)
    console.log('[streamParser] 🔍 开始解析流数据');
    console.log(
      `[streamParser] 📊 原始数据长度: ${streamResponse.length} 字符`,
    );
    console.log(
      '[streamParser] 📝 原始数据前500字符:',
      streamResponse.substring(0, STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.FULL),
    );
    console.log(
      '[streamParser] 📝 原始数据后500字符:',
      streamResponse.substring(
        Math.max(
          0,
          streamResponse.length - STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.FULL,
        ),
      ),
    );

    // 按行分割流响应 (Split stream response by lines)
    const lines = streamResponse.split('\n');
    const contentParts = [];

    // Initialize statistics object
    const stats = {
      totalLines: 0,
      validJsonLines: 0,
      sampleValidLines: [],
      sampleInvalidLines: [],
      contentPartsCount: 0,
    };

    console.log(`[streamParser] 📋 分割后共 ${lines.length} 行`);

    // Process each line of the stream response
    for (const line of lines) {
      stats.totalLines++;
      const content = processStreamLine(line, stats.totalLines, stats);

      if (content !== null) {
        contentParts.push(content);
        stats.contentPartsCount++;
      }
    }

    // 🔧 拼接所有内容片段 - 严格保持原始格式
    // 关键修复：直接拼接，绝对不添加任何额外字符，完全保持AI输出的原始格式
    // 这是保持缩进和换行符的最关键步骤
    // Key fix: Direct concatenation, preserve original AI output format exactly
    const result = contentParts.join('');
    const endTime = performance.now();

    // Log parsing statistics
    logParsingStats(stats, result, endTime - startTime);

    return result;
  } catch (error) {
    console.error('[streamParser] ❌ 解析流响应失败:', error);
    console.error('[streamParser] 📊 错误详情:', {
      message: error.message,
      stack: error.stack,
      inputLength: streamResponse?.length || 0,
    });
    return '';
  }
}

/**
 * 从文本中提取指定语言的代码块
 *
 * 代码块格式: ```语言\n代码内容\n```
 *
 * @param {string} content 完整的文本内容
 * @param {string} language 代码块语言标识符 (如 'lynx', 'javascript' 等)
 * @returns {string[]} 提取的代码块数组
 */
function extractCodeBlocks(content, language) {
  language = language || 'lynx';
  console.log(
    `[streamParser] 提取${language}代码块，内容长度: ${content.length}`,
  );

  const codeBlocks = [];
  const regex = new RegExp(`\`\`\`${language}([\\s\\S]*?)\`\`\``, 'g');
  let match;
  let matchCount = 0;
  let lastIndex = 0;

  while ((match = regex.exec(content)) !== null) {
    matchCount++;

    // 防止无限循环：如果正则表达式没有前进，强制退出
    if (regex.lastIndex === lastIndex) {
      console.warn('[streamParser] 检测到正则表达式无限循环，强制退出');
      break;
    }
    lastIndex = regex.lastIndex;

    // 防止过多匹配导致性能问题 (Prevent too many matches causing performance issues)
    if (matchCount > STREAM_PARSER_CONSTANTS.LIMITS.MAX_MATCHES) {
      console.warn(`[streamParser] 匹配数量过多 (${matchCount})，停止匹配`);
      break;
    }

    if (match[1]) {
      // 🔧 关键修复：完全保留原始格式，不做任何修改
      // 问题根源：移除换行符的操作破坏了代码的缩进和格式
      const codeBlock = match[1];

      // 🚨 重要：不再移除任何换行符，完全保持代码块内的原始格式
      // 之前的换行符移除逻辑是导致格式丢失的根本原因
      // 代码块内的内容应该完全按照AI输出的格式保留，包括所有换行符和缩进

      codeBlocks.push(codeBlock);

      if (process.env.NODE_ENV !== 'production') {
        console.debug(
          `[streamParser] 匹配代码块 #${matchCount}: ${codeBlock.substring(0, STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.SHORT)}...`,
        );
        console.debug(
          `[streamParser] 🔍 代码块格式检查 - 包含换行符: ${codeBlock.indexOf('\n') !== -1}, 包含缩进: ${/^\s+/m.test(codeBlock)}`,
        );
      }
    }
  }

  console.log(`[streamParser] 共提取${codeBlocks.length}个${language}代码块`);
  return codeBlocks;
}

/**
 * 从流数据中提取HTML代码
 *
 * 支持多种HTML代码格式：
 * 1. 直接的HTML内容（最常见）
 * 2. ```html 代码块
 * 3. ```javascript 代码块（包含HTML）
 * 4. 其他语言代码块
 *
 * @param {string} content 完整的流数据内容
 * @returns {object} 处理结果对象: {success, extractedContent, error, metadata}
 */
function extractHTMLCode(content) {
  try {
    const startTime = performance.now();

    console.log('[streamParser] 🔍 开始提取HTML代码');
    console.log(`[streamParser] 📊 输入内容长度: ${content.length} 字符`);

    // 检查内容中包含的HTML关键标识符
    const indicators = {
      html代码块: content.indexOf('```html') !== -1,
      javascript代码块: content.indexOf('```javascript') !== -1,
      js代码块: content.indexOf('```js') !== -1,
      DOCTYPE声明: content.indexOf('<!DOCTYPE html>') !== -1,
      html标签: content.indexOf('<html') !== -1,
      div标签: content.indexOf('<div') !== -1,
      script标签: content.indexOf('<script') !== -1,
      style标签: content.indexOf('<style') !== -1,
      meta标签: content.indexOf('<meta') !== -1,
    };

    console.log('[streamParser] 🔍 HTML内容特征分析:', indicators);

    let extractedContent = '';
    let strategy = '';

    // 策略1: 提取 html 代码块
    const htmlCodeBlocks = extractCodeBlocks(content, 'html');
    if (htmlCodeBlocks.length > 0) {
      extractedContent =
        htmlCodeBlocks.length === 1
          ? htmlCodeBlocks[0]
          : htmlCodeBlocks.join('\n\n');
      strategy = 'html代码块';
      console.log(
        `[streamParser] ✅ 策略1成功: 找到 ${htmlCodeBlocks.length} 个html代码块`,
      );
    } else {
      // 策略2: 提取 javascript 代码块（可能包含HTML字符串）
      const jsCodeBlocks = extractCodeBlocks(content, 'javascript');
      if (jsCodeBlocks.length > 0) {
        extractedContent =
          jsCodeBlocks.length === 1
            ? jsCodeBlocks[0]
            : jsCodeBlocks.join('\n\n');
        strategy = 'javascript代码块';
        console.log(
          `[streamParser] ✅ 策略2成功: 找到 ${jsCodeBlocks.length} 个javascript代码块`,
        );
      } else {
        // 策略3: 检查直接HTML内容
        if (content.trim()) {
          const htmlIndicators = [
            '<!DOCTYPE html>',
            '<html',
            '<head',
            '<body',
            '<div',
            '<span',
            '<p>',
            '<h1>',
            '<h2>',
            '<h3>',
            '<meta',
            '<title>',
            '<script',
            '<style',
            'class=',
            'id=',
            'data-',
          ];

          const foundIndicators = htmlIndicators.filter(
            indicator => content.indexOf(indicator) !== -1,
          );

          if (foundIndicators.length > 0) {
            extractedContent = content;
            strategy = '直接HTML内容识别';
            console.log('[streamParser] ✅ 策略3成功: 直接识别HTML内容');
            console.log('[streamParser] 🔍 找到的HTML特征:', foundIndicators);
          }
        }
      }
    }

    if (!extractedContent) {
      console.warn(
        '[streamParser] ⚠️ 所有策略都失败，未找到任何可识别的HTML内容',
      );
      return {
        success: false,
        error: 'No recognizable HTML content found',
      };
    }

    const endTime = performance.now();
    console.log('[streamParser] ✅ 提取HTML代码成功!');
    console.log(`[streamParser] 📊 使用策略: ${strategy}`);
    console.log(
      `[streamParser] 📏 提取内容长度: ${extractedContent.length} 字符`,
    );
    console.log(
      `[streamParser] ⏱️ 处理耗时: ${(endTime - startTime).toFixed(2)}ms`,
    );

    return {
      success: true,
      extractedContent,
      metadata: {
        strategy,
        codeBlocks: htmlCodeBlocks.length || 1,
        totalLength: extractedContent.length,
        processingTime: endTime - startTime,
      },
    };
  } catch (error) {
    console.error('[streamParser] ❌ 提取HTML代码失败:', error);
    return {
      success: false,
      error: error.message || String(error),
    };
  }
}

/**
 * 从流数据中提取Lynx代码
 *
 * 按照 realUploadProcessor.js 的逻辑，支持多种代码格式：
 * 1. 直接的代码内容（最常见）
 * 2. ```lynx 代码块
 * 3. ```javascript 代码块
 * 4. 其他语言代码块
 *
 * @param {string} content 完整的流数据内容
 * @returns {object} 处理结果对象: {success, extractedContent, error, metadata}
 */
function extractLynxCode(content) {
  try {
    const startTime = performance.now();

    // 🔍 调试日志：输入内容分析
    console.log('[streamParser] 🔍 开始提取Lynx代码');
    console.log(`[streamParser] 📊 输入内容长度: ${content.length} 字符`);
    console.log(
      '[streamParser] 📝 输入内容前300字符:',
      content.substring(0, STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.EXTENDED),
    );
    console.log(
      '[streamParser] 📝 输入内容后300字符:',
      content.substring(
        Math.max(
          0,
          content.length - STREAM_PARSER_CONSTANTS.PREVIEW_LENGTH.EXTENDED,
        ),
      ),
    );

    // 检查内容中包含的关键标识符
    const indicators = {
      lynx代码块: content.indexOf('```lynx') !== -1,
      javascript代码块: content.indexOf('```javascript') !== -1,
      js代码块: content.indexOf('```js') !== -1,
      jsx代码块: content.indexOf('```jsx') !== -1,
      typescript代码块: content.indexOf('```typescript') !== -1,
      ts代码块: content.indexOf('```ts') !== -1,
      tsx代码块: content.indexOf('```tsx') !== -1,
      FILE标签: content.indexOf('<FILE') !== -1,
      文件分隔符: content.indexOf('// ---') !== -1,
      粗体文件名: content.indexOf('**') !== -1 && content.indexOf('.') !== -1,
      import语句: content.indexOf('import ') !== -1,
      export语句: content.indexOf('export ') !== -1,
      function关键字: content.indexOf('function ') !== -1,
      React组件:
        content.indexOf('React') !== -1 || content.indexOf('Component') !== -1,
      View组件: content.indexOf('View') !== -1,
      StyleSheet: content.indexOf('StyleSheet') !== -1,
    };

    console.log('[streamParser] 🔍 内容特征分析:', indicators);

    // 策略1: 首先尝试提取 lynx 代码块
    const lynxCodeBlocks = extractCodeBlocks(content, 'lynx');
    let extractedContent = '';
    let strategy = '';

    if (lynxCodeBlocks.length > 0) {
      // 如果只有一个代码块，直接使用；如果有多个，用双换行符分隔
      extractedContent =
        lynxCodeBlocks.length === 1
          ? lynxCodeBlocks[0]
          : lynxCodeBlocks.join('\n\n');
      strategy = 'lynx代码块';
      console.log(
        `[streamParser] ✅ 策略1成功: 找到 ${lynxCodeBlocks.length} 个lynx代码块`,
      );
      console.log(
        '[streamParser] 📝 lynx代码块内容前200字符:',
        extractedContent.substring(0, 200),
      );
    } else {
      console.log('[streamParser] ❌ 策略1失败: 未找到lynx代码块');

      // 策略2: 尝试提取 javascript 代码块
      const jsCodeBlocks = extractCodeBlocks(content, 'javascript');
      if (jsCodeBlocks.length > 0) {
        // 如果只有一个代码块，直接使用；如果有多个，用双换行符分隔
        extractedContent =
          jsCodeBlocks.length === 1
            ? jsCodeBlocks[0]
            : jsCodeBlocks.join('\n\n');
        strategy = 'javascript代码块';
        console.log(
          `[streamParser] ✅ 策略2成功: 找到 ${jsCodeBlocks.length} 个javascript代码块`,
        );
        console.log(
          '[streamParser] 📝 javascript代码块内容前200字符:',
          extractedContent.substring(0, 200),
        );
      } else {
        console.log('[streamParser] ❌ 策略2失败: 未找到javascript代码块');

        // 策略3: 尝试提取其他常见代码块
        const commonLanguages = ['js', 'jsx', 'typescript', 'ts', 'tsx'];
        let found = false;
        for (const lang of commonLanguages) {
          const blocks = extractCodeBlocks(content, lang);
          if (blocks.length > 0) {
            // 如果只有一个代码块，直接使用；如果有多个，用双换行符分隔
            extractedContent =
              blocks.length === 1 ? blocks[0] : blocks.join('\n\n');
            strategy = `${lang}代码块`;
            console.log(
              `[streamParser] ✅ 策略3成功: 找到 ${blocks.length} 个${lang}代码块`,
            );
            console.log(
              `[streamParser] 📝 ${lang}代码块内容前200字符:`,
              extractedContent.substring(0, 200),
            );
            found = true;
            break;
          }
        }

        if (!found) {
          console.log('[streamParser] ❌ 策略3失败: 未找到常见语言代码块');
        }

        // 策略4: 如果没有找到任何代码块，但内容看起来像代码，直接使用
        if (!extractedContent && content.trim()) {
          console.log('[streamParser] 🔍 尝试策略4: 直接内容识别');

          // 检查内容是否包含代码特征
          const codeIndicators = [
            'import ',
            'export ',
            'function ',
            'const ',
            'let ',
            'var ',
            'class ',
            'interface ',
            'type ',
            'View',
            'Text',
            'StyleSheet',
            'React',
            'Component',
            '<FILE',
            '// ---',
            '**',
            'module.exports',
            // 添加更多HTML/XML特征，因为您的示例是HTML代码
            '<view',
            '<div',
            '<span',
            'class=',
            'data-',
            '{{',
            '}}',
          ];

          const foundIndicators = codeIndicators.filter(
            indicator => content.indexOf(indicator) !== -1,
          );
          console.log('[streamParser] 🔍 找到的代码特征:', foundIndicators);

          const hasCodeFeatures = foundIndicators.length > 0;

          if (hasCodeFeatures) {
            // 🔧 关键修复：对于直接内容识别，完全保持原始格式
            // 绝对不做任何格式修改，保持AI输出的完整性
            extractedContent = content;

            // 移除这些可能破坏格式的处理
            // 注释掉原来的首尾换行符移除逻辑，因为这可能破坏真实的代码格式
            /*
            if (extractedContent.startsWith('\n')) {
              extractedContent = extractedContent.substring(1);
            }
            if (extractedContent.endsWith('\n')) {
              extractedContent = extractedContent.substring(0, extractedContent.length - 1);
            }
            */

            strategy = '直接内容识别';
            console.log('[streamParser] ✅ 策略4成功: 直接使用内容作为代码');
            console.log(
              '[streamParser] 📝 直接识别内容前200字符:',
              extractedContent.substring(0, 200),
            );
            console.log(
              `[streamParser] 🔍 直接识别格式检查 - 包含换行符: ${extractedContent.indexOf('\n') !== -1}, 包含缩进: ${/^\s+/m.test(extractedContent)}`,
            );
          } else {
            console.log('[streamParser] ❌ 策略4失败: 内容不包含代码特征');
          }
        }
      }
    }

    if (!extractedContent) {
      console.warn(
        '[streamParser] ⚠️ 所有策略都失败，未找到任何可识别的代码内容',
      );
      console.log(
        '[streamParser] 📊 最终分析: 输入内容可能不包含有效的代码格式',
      );
      return {
        success: false,
        error: 'No recognizable code content found',
      };
    }

    const endTime = performance.now();
    console.log('[streamParser] ✅ 提取Lynx代码成功!');
    console.log(`[streamParser] 📊 使用策略: ${strategy}`);
    console.log(
      `[streamParser] 📏 提取内容长度: ${extractedContent.length} 字符`,
    );
    console.log(
      `[streamParser] ⏱️ 处理耗时: ${(endTime - startTime).toFixed(2)}ms`,
    );
    console.log(
      '[streamParser] 📝 最终提取内容前200字符:',
      extractedContent.substring(0, 200),
    );
    console.log(
      '[streamParser] 📝 最终提取内容后200字符:',
      extractedContent.substring(Math.max(0, extractedContent.length - 200)),
    );

    return {
      success: true,
      extractedContent,
      metadata: {
        strategy,
        codeBlocks: lynxCodeBlocks.length || 1,
        totalLength: extractedContent.length,
        processingTime: endTime - startTime,
      },
    };
  } catch (error) {
    console.error('[streamParser] ❌ 提取Lynx代码失败:', error);
    console.error('[streamParser] 📊 错误详情:', {
      message: error.message,
      stack: error.stack,
      inputLength: content?.length || 0,
    });
    return {
      success: false,
      error: error.message || String(error),
    };
  }
}

/**
 * 解析Lynx代码结构，提取文件路径和内容
 * 按照 realUploadProcessor.js 的逻辑实现
 *
 * 支持多种文件解析策略，按优先级排序:
 * 1. <FILE>标签格式 - <FILE path="src/App.js">内容</FILE>
 * 2. **filename** 格式 - **src/App.js** 后跟代码块
 * 3. // --- filename --- 格式
 * 4. 单文件模式 - 整个内容作为一个文件
 *
 * @param {string} code Lynx代码内容
 * @returns {{ [path: string]: string }} 文件结构对象，键为文件路径，值为文件内容
 */
function parseLynxCodeStructure(code) {
  /** @type {{ [path: string]: string }} */
  const files = {};

  try {
    // 🔍 调试日志：输入分析
    console.log('[streamParser] 🔍 开始解析Lynx代码结构');
    console.log(`[streamParser] 📊 输入代码长度: ${code.length} 字符`);
    console.log('[streamParser] 📝 输入代码前300字符:', code.substring(0, 300));
    console.log(
      '[streamParser] 📝 输入代码后300字符:',
      code.substring(Math.max(0, code.length - 300)),
    );

    // 检查各种格式标识符
    const formatIndicators = {
      FILE标签: code.indexOf('<FILE') !== -1,
      粗体文件名: code.indexOf('**') !== -1 && code.indexOf('```') !== -1,
      分隔符格式: code.indexOf('// ---') !== -1,
      代码块: code.indexOf('```') !== -1,
      单行注释: code.indexOf('//') !== -1,
      多行注释: code.indexOf('/*') !== -1,
      import语句: code.indexOf('import') !== -1,
      export语句: code.indexOf('export') !== -1,
    };

    console.log('[streamParser] 🔍 格式特征分析:', formatIndicators);

    // 策略1: 解析 <FILE> 格式 (支持 path 和 name 属性)
    if (code.indexOf('<FILE') !== -1) {
      console.log('[streamParser] ✅ 使用策略1: <FILE>标签格式');

      // 同时匹配 path 和 name 属性
      const fileMatches = code.match(
        /<FILE[^>]*(?:path|name)="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/g,
      );
      console.log(
        `[streamParser] 📊 找到 ${fileMatches?.length || 0} 个FILE标签匹配`,
      );

      if (fileMatches) {
        fileMatches.forEach((match, index) => {
          console.log(
            `[streamParser] 🔍 处理FILE标签 ${index + 1}:`,
            `${match.substring(0, 100)}...`,
          );

          // 优先匹配 path 属性，如果没有则匹配 name 属性
          let pathMatch = match.match(/path="([^"]+)"/);
          if (!pathMatch) {
            pathMatch = match.match(/name="([^"]+)"/);
          }

          const contentMatch = match.match(/<FILE[^>]*>([\s\S]*?)<\/FILE>/);

          if (pathMatch && contentMatch) {
            const filePath = pathMatch[1];
            // 🔧 关键修复：完全保留原始格式，不做任何修改
            // 问题根源：移除换行符的操作破坏了代码的缩进和格式
            const fileContent = contentMatch[1];

            // 🚨 重要：不再移除任何换行符，完全保持FILE标签内的原始格式
            // 之前的换行符移除逻辑是导致格式丢失的根本原因
            // FILE标签内的内容应该完全按照AI输出的格式保留

            files[filePath] = fileContent;
            console.log(
              `[streamParser] ✅ 策略1提取文件: ${filePath} (${fileContent.length} 字符)`,
            );
            console.log(
              '[streamParser] 📝 文件内容前100字符:',
              fileContent.substring(0, 100),
            );
            console.log(
              `[streamParser] 🔍 文件格式检查 - 包含换行符: ${fileContent.indexOf('\n') !== -1}, 包含缩进: ${/^\s+/m.test(fileContent)}`,
            );
          } else {
            console.log(
              '[streamParser] ❌ FILE标签格式不正确:',
              match.substring(0, 200),
            );
          }
        });

        if (Object.keys(files).length > 0) {
          console.log(
            `[streamParser] ✅ 策略1成功，提取了 ${Object.keys(files).length} 个文件`,
          );
          return files;
        }
      }
    }

    // 策略2: 解析 **filename** 格式 (与 realUploadProcessor.js 保持一致)
    if (code.indexOf('**') !== -1 && code.indexOf('```') !== -1) {
      console.log('[streamParser] ✅ 使用策略2: **filename** 格式');
      const sections = code.split(/\*\*([^*]+)\*\*/);
      console.log(`[streamParser] 📊 分割后得到 ${sections.length} 个部分`);

      for (let i = 1; i < sections.length; i += 2) {
        const filename = sections[i].trim();
        const nextSection = sections[i + 1];
        console.log(`[streamParser] 🔍 处理文件名: "${filename}"`);
        console.log(
          '[streamParser] 📝 后续内容前100字符:',
          nextSection?.substring(0, 100),
        );

        if (nextSection && nextSection.indexOf('```') !== -1) {
          const codeMatch = nextSection.match(/```[^\n]*\n([\s\S]*?)```/);
          if (codeMatch) {
            // 🔧 关键修复：完全保留原始格式，不做任何修改
            // 问题根源：移除换行符的操作破坏了代码的缩进和格式
            const fileContent = codeMatch[1];

            // 🚨 重要：不再移除任何换行符，完全保持代码块内的原始格式
            // 之前的换行符移除逻辑是导致格式丢失的根本原因
            files[filename] = fileContent;
            console.log(
              `[streamParser] ✅ 策略2提取文件: ${filename} (${fileContent.length} 字符)`,
            );
            console.log(
              '[streamParser] 📝 文件内容前100字符:',
              fileContent.substring(0, 100),
            );
            console.log(
              `[streamParser] 🔍 文件格式检查 - 包含换行符: ${fileContent.indexOf('\n') !== -1}, 包含缩进: ${/^\s+/m.test(fileContent)}`,
            );
          } else {
            console.log('[streamParser] ❌ 未找到对应的代码块');
          }
        }
      }

      if (Object.keys(files).length > 0) {
        console.log(
          `[streamParser] ✅ 策略2成功，提取了 ${Object.keys(files).length} 个文件`,
        );
        return files;
      } else {
        console.log('[streamParser] ❌ 策略2失败，未提取到文件');
      }
    }

    // 策略3: 解析 // --- filename --- 格式 (与 realUploadProcessor.js 保持一致)
    if (code.indexOf('// ---') !== -1) {
      console.log('[streamParser] ✅ 使用策略3: // --- filename --- 格式');
      const sections = code.split(/\/\/ --- ([^-]+) ---/);
      console.log(`[streamParser] 📊 分割后得到 ${sections.length} 个部分`);

      for (let i = 1; i < sections.length; i += 2) {
        const filename = sections[i].trim();
        const fileContent = sections[i + 1];
        console.log(`[streamParser] 🔍 处理文件名: "${filename}"`);
        console.log(
          '[streamParser] 📝 文件内容前100字符:',
          fileContent?.substring(0, 100),
        );

        if (fileContent) {
          // 🔧 关键修复：完全保留原始格式，不做任何修改
          // 问题根源：移除换行符的操作破坏了代码的缩进和格式
          const processedContent = fileContent;

          // 🚨 重要：不再移除任何换行符，完全保持分隔符格式内的原始格式
          // 之前的换行符移除逻辑是导致格式丢失的根本原因
          files[filename] = processedContent;
          console.log(
            `[streamParser] ✅ 策略3提取文件: ${filename} (${processedContent.length} 字符)`,
          );
          console.log(
            `[streamParser] 🔍 文件格式检查 - 包含换行符: ${processedContent.indexOf('\n') !== -1}, 包含缩进: ${/^\s+/m.test(processedContent)}`,
          );
        }
      }

      if (Object.keys(files).length > 0) {
        console.log(
          `[streamParser] ✅ 策略3成功，提取了 ${Object.keys(files).length} 个文件`,
        );
        return files;
      } else {
        console.log('[streamParser] ❌ 策略3失败，未提取到文件');
      }
    }

    // 策略4: 单文件模式 - 如果没有找到文件分隔符，将整个内容作为一个文件
    if (Object.keys(files).length === 0) {
      console.log('[streamParser] ✅ 使用策略4: 单文件模式');

      // 尝试从内容推断文件名和类型
      let fileName = 'index.js'; // 默认文件名

      // 🔧 关键修复：智能处理代码块和纯代码内容
      let fileContent = code;

      // 如果输入包含代码块标记，先尝试提取代码块内容
      const codeBlockMatch = code.match(
        /```(?:javascript|js|lynx|jsx|typescript|ts|tsx)?\n([\s\S]*?)```/,
      );
      if (codeBlockMatch) {
        // 找到代码块，提取其中的代码内容
        fileContent = codeBlockMatch[1];
        console.log(
          `[streamParser] 🔍 策略4检测到代码块，提取代码块内容，长度: ${fileContent.length}`,
        );
        console.log(
          '[streamParser] 📝 代码块内容前100字符:',
          fileContent.substring(0, 100),
        );
      } else {
        // 没有代码块标记，直接使用整个内容
        fileContent = code;
        console.log(
          `[streamParser] 🔍 策略4使用整个内容，长度: ${fileContent.length}`,
        );
      }

      // 🚨 重要：完全保留提取出的代码格式，不做任何修改

      // 从注释中提取文件名
      const commentMatch = code.match(/^\/\/\s*(.+?)(?:\n|$)/);
      if (commentMatch) {
        const comment = commentMatch[1].trim();
        console.log(`[streamParser] 🔍 找到注释: "${comment}"`);
        // 如果注释看起来像文件名，使用它
        if (
          comment &&
          comment.indexOf('.') !== -1 &&
          comment.indexOf(' ') === -1
        ) {
          fileName = comment;
          console.log(`[streamParser] ✅ 从注释推断文件名: ${fileName}`);
        }
      }

      // 根据代码内容判断文件类型
      const contentAnalysis = {
        hasStyleSheet:
          code.indexOf('StyleSheet.create') !== -1 ||
          code.indexOf('Styles.create') !== -1,
        hasJSX:
          code.indexOf('<View') !== -1 ||
          code.indexOf('<Text') !== -1 ||
          code.indexOf('JSX') !== -1,
        hasReact:
          code.indexOf('React') !== -1 || code.indexOf('Component') !== -1,
        hasImport: code.indexOf('import') !== -1,
        hasExport: code.indexOf('export') !== -1,
      };

      console.log('[streamParser] 🔍 内容类型分析:', contentAnalysis);

      if (contentAnalysis.hasStyleSheet) {
        if (!fileName.endsWith('.js') && !fileName.endsWith('.jsx')) {
          fileName = `${fileName.replace(/\.[^.]*$/, '')}.js`;
          console.log(
            `[streamParser] 📝 检测到StyleSheet，设置文件类型为: ${fileName}`,
          );
        }
      } else if (contentAnalysis.hasJSX) {
        if (!fileName.endsWith('.jsx') && !fileName.endsWith('.js')) {
          fileName = `${fileName.replace(/\.[^.]*$/, '')}.jsx`;
          console.log(
            `[streamParser] 📝 检测到JSX，设置文件类型为: ${fileName}`,
          );
        }
      }

      files[fileName] = fileContent;
      console.log(
        `[streamParser] ✅ 策略4成功: 单文件模式 -> ${fileName} (${fileContent.length} 字符)`,
      );
      console.log(
        '[streamParser] 📝 文件内容前200字符:',
        fileContent.substring(0, 200),
      );
      console.log(
        `[streamParser] 🔍 文件格式检查 - 包含换行符: ${fileContent.indexOf('\n') !== -1}, 包含缩进: ${/^\s+/m.test(fileContent)}`,
      );
    }

    const fileCount = Object.keys(files).length;
    console.log(`[streamParser] ✅ 解析完成，共提取 ${fileCount} 个文件`);

    // 输出所有文件的摘要
    Object.entries(files).forEach(([path, content]) => {
      console.log(`[streamParser] 📄 文件: ${path} (${content.length} 字符)`);
      console.log(
        `[streamParser] 📝 内容摘要: ${content.substring(0, 100)}...`,
      );
    });

    return files;
  } catch (error) {
    console.error('[streamParser] ❌ 解析Lynx代码结构失败:', error);
    console.error('[streamParser] 📊 错误详情:', {
      message: error.message,
      stack: error.stack,
      inputLength: code?.length || 0,
    });
    return {
      success: false,
      error: error.message || String(error),
    };
  }
}

// 这些辅助函数已被移除，因为它们从未被调用

// CommonJS 导出
module.exports = {
  parseStreamData,
  extractCodeBlocks,
  extractLynxCode,
  extractHTMLCode,
  parseLynxCodeStructure,
};
