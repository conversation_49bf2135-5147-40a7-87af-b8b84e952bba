/**
 * 任务优先级枚举
 */
export enum JobPriority {
  LOW = 0,
  NORMAL = 1,
  HIGH = 2,
  CRITICAL = 3,
}

/**
 * 任务状态枚举
 */
export enum JobStatus {
  QUEUED = 'queued',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

/**
 * 任务接口定义
 */
export interface Job<T = any> {
  id: string;
  execute: () => Promise<T>;
  priority: JobPriority;
  weight: number;
  metadata?: Record<string, any>;
  onProgress?: (progress: number) => void;
  onComplete?: (result: T) => void;
  onError?: (error: Error) => void;
  status: JobStatus;
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  result?: T;
  error?: Error;
  retryCount: number;
  maxRetries: number;
}

/**
 * 并发管理器配置接口
 */
export interface ConcurrencyManagerConfig {
  maxConcurrent: number;
  maxWeight: number;
  rateLimit: {
    requestsPerInterval: number;
    intervalMs: number;
  };
  retryOptions: {
    maxRetries: number;
    retryDelayMs: number;
    exponentialBackoff: boolean;
  };
  priorityBoost: boolean;
  fairScheduling: boolean;
  queueTimeout: number;
}

/**
 * 并发管理器状态接口
 */
export interface ConcurrencyManagerStats {
  activeJobs: number;
  queuedJobs: number;
  completedJobs: number;
  failedJobs: number;
  totalWeight: number;
  activeWeight: number;
  averageWaitTime: number;
  averageProcessTime: number;
  throughput: number;
}

/**
 * 高级并发管理器
 * 提供精细的并发控制、队列优先级和速率限制功能
 */
export class ConcurrencyManager<T = any> {
  private config: ConcurrencyManagerConfig;
  private activeJobs: Map<string, Job<T>> = new Map();
  private jobQueue: Job<T>[] = [];
  private isProcessing = false;
  private requestTimestamps: number[] = [];
  private stats: ConcurrencyManagerStats;
  private paused = false;
  private abortController: AbortController = new AbortController();

  /**
   * 创建并发管理器实例
   */
  constructor(config?: Partial<ConcurrencyManagerConfig>) {
    // 默认配置
    this.config = {
      maxConcurrent: 5,
      maxWeight: 10,
      rateLimit: {
        requestsPerInterval: 10,
        intervalMs: 1000,
      },
      retryOptions: {
        maxRetries: 3,
        retryDelayMs: 1000,
        exponentialBackoff: true,
      },
      priorityBoost: true,
      fairScheduling: true,
      queueTimeout: 150000,
      ...config,
    };

    // 初始化统计信息
    this.stats = {
      activeJobs: 0,
      queuedJobs: 0,
      completedJobs: 0,
      failedJobs: 0,
      totalWeight: 0,
      activeWeight: 0,
      averageWaitTime: 0,
      averageProcessTime: 0,
      throughput: 0,
    };
  }

  /**
   * 添加任务到队列
   * @param job 任务对象或任务执行函数
   * @param options 任务选项
   * @returns 任务ID
   */
  async enqueue(
    job: Job<T> | (() => Promise<T>),
    options: {
      id?: string;
      priority?: JobPriority;
      weight?: number;
      metadata?: Record<string, any>;
      maxRetries?: number;
      onProgress?: (progress: number) => void;
      onComplete?: (result: T) => void;
      onError?: (error: Error) => void;
    } = {},
  ): Promise<string> {
    // 如果输入是函数，转换为Job对象
    const jobObj: Job<T> =
      typeof job === 'function'
        ? {
            id:
              options.id ||
              `job_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`,
            execute: job,
            priority: options.priority ?? JobPriority.NORMAL,
            weight: options.weight ?? 1,
            metadata: options.metadata,
            onProgress: options.onProgress,
            onComplete: options.onComplete,
            onError: options.onError,
            status: JobStatus.QUEUED,
            createdAt: Date.now(),
            retryCount: 0,
            maxRetries:
              options.maxRetries ?? this.config.retryOptions.maxRetries,
          }
        : job;

    // 添加到队列
    this.jobQueue.push(jobObj);
    this.stats.queuedJobs++;
    this.stats.totalWeight += jobObj.weight;

    // 如果没有正在处理队列，开始处理
    if (!this.isProcessing && !this.paused) {
      this.processQueue();
    }

    return jobObj.id;
  }

  /**
   * 批量添加任务到队列
   * @param jobs 任务数组
   * @returns 任务ID数组
   */
  async enqueueBatch(
    jobs: Array<Job<T> | (() => Promise<T>)>,
    options: {
      priority?: JobPriority;
      weight?: number;
      maxRetries?: number;
    } = {},
  ): Promise<string[]> {
    const jobIds: string[] = [];

    for (const job of jobs) {
      const id = await this.enqueue(job, options);
      jobIds.push(id);
    }

    return jobIds;
  }

  /**
   * 处理队列中的任务
   */
  private async processQueue(): Promise<void> {
    if (this.paused || this.isProcessing) {
      return;
    }

    this.isProcessing = true;

    try {
      while (this.jobQueue.length > 0 && !this.paused) {
        // 检查是否可以执行更多任务
        if (!this.canExecuteMore()) {
          break;
        }

        // 根据优先级和公平调度策略选择下一个任务
        const nextJob = this.selectNextJob();
        if (!nextJob) {
          break;
        }

        // 从队列中移除任务
        this.jobQueue = this.jobQueue.filter(job => job.id !== nextJob.id);
        this.stats.queuedJobs--;

        // 执行任务
        this.executeJob(nextJob);
      }
    } finally {
      this.isProcessing = false;

      // 如果队列不为空且有空闲槽位，继续处理
      if (this.jobQueue.length > 0 && this.canExecuteMore() && !this.paused) {
        setTimeout(() => this.processQueue(), 0);
      }
    }
  }

  /**
   * 检查是否可以执行更多任务
   */
  private canExecuteMore(): boolean {
    // 检查并发数限制
    if (this.activeJobs.size >= this.config.maxConcurrent) {
      return false;
    }

    // 检查权重限制
    const currentWeight = Array.from(this.activeJobs.values()).reduce(
      (sum, job) => sum + job.weight,
      0,
    );
    if (currentWeight >= this.config.maxWeight) {
      return false;
    }

    // 检查速率限制
    if (this.isRateLimited()) {
      return false;
    }

    return true;
  }

  /**
   * 检查是否超过速率限制
   */
  private isRateLimited(): boolean {
    const now = Date.now();
    const { requestsPerInterval, intervalMs } = this.config.rateLimit;

    // 清除过期的时间戳
    this.requestTimestamps = this.requestTimestamps.filter(
      timestamp => now - timestamp < intervalMs,
    );

    // 检查是否达到速率限制
    return this.requestTimestamps.length >= requestsPerInterval;
  }

  /**
   * 选择下一个要执行的任务
   */
  private selectNextJob(): Job<T> | undefined {
    if (this.jobQueue.length === 0) {
      return undefined;
    }

    // 如果启用优先级提升，按优先级排序
    if (this.config.priorityBoost) {
      this.jobQueue.sort((a, b) => {
        // 首先按优先级排序
        if (a.priority !== b.priority) {
          return b.priority - a.priority;
        }

        // 其次按创建时间排序（先进先出）
        return a.createdAt - b.createdAt;
      });

      return this.jobQueue[0];
    }

    // 如果启用公平调度，考虑任务权重和等待时间
    if (this.config.fairScheduling) {
      // 计算每个任务的调度分数：优先级 * 等待时间 / 权重
      const now = Date.now();
      const jobScores = this.jobQueue.map(job => ({
        job,
        score: ((job.priority + 1) * (now - job.createdAt)) / job.weight,
      }));

      // 选择分数最高的任务
      jobScores.sort((a, b) => b.score - a.score);
      return jobScores[0].job;
    }

    // 默认简单的FIFO策略
    return this.jobQueue[0];
  }

  /**
   * 执行单个任务
   */
  private async executeJob(job: Job<T>): Promise<void> {
    // 更新任务状态
    job.status = JobStatus.RUNNING;
    job.startedAt = Date.now();

    // 添加到活动任务列表
    this.activeJobs.set(job.id, job);
    this.stats.activeJobs++;
    this.stats.activeWeight += job.weight;

    // 记录请求时间戳（用于速率限制）
    this.requestTimestamps.push(Date.now());

    try {
      // 创建超时Promise
      const timeoutPromise = new Promise<never>((_, reject) => {
        const timeoutId = setTimeout(() => {
          reject(
            new Error(
              `Job ${job.id} timed out after ${this.config.queueTimeout}ms`,
            ),
          );
        }, this.config.queueTimeout);

        // 如果任务完成，清除超时
        this.abortController.signal.addEventListener('abort', () => {
          clearTimeout(timeoutId);
        });
      });

      // 竞争执行任务和超时
      const result = await Promise.race([job.execute(), timeoutPromise]);

      // 更新任务状态为完成
      job.status = JobStatus.COMPLETED;
      job.completedAt = Date.now();
      job.result = result;

      // 调用完成回调
      if (job.onComplete) {
        job.onComplete(result);
      }

      // 更新统计信息
      this.stats.completedJobs++;
      this.updatePerformanceStats(job);
    } catch (error) {
      // 处理错误
      const err = error instanceof Error ? error : new Error(String(error));
      job.error = err;

      // 检查是否需要重试
      if (job.retryCount < job.maxRetries) {
        // 准备重试
        job.retryCount++;
        job.status = JobStatus.QUEUED;

        // 计算重试延迟
        const delay = this.calculateRetryDelay(job.retryCount);

        // 延迟后重新入队
        setTimeout(() => {
          this.jobQueue.push(job);
          this.stats.queuedJobs++;
          this.processQueue();
        }, delay);
      } else {
        // 达到最大重试次数，标记为失败
        job.status = JobStatus.FAILED;
        job.completedAt = Date.now();

        // 调用错误回调
        if (job.onError) {
          job.onError(err);
        }

        // 更新统计信息
        this.stats.failedJobs++;
        this.updatePerformanceStats(job);
      }
    } finally {
      // 从活动任务列表中移除
      this.activeJobs.delete(job.id);
      this.stats.activeJobs--;
      this.stats.activeWeight -= job.weight;

      // 继续处理队列
      if (this.jobQueue.length > 0 && !this.paused) {
        this.processQueue();
      }
    }
  }

  /**
   * 计算重试延迟时间
   */
  private calculateRetryDelay(retryCount: number): number {
    const { retryDelayMs, exponentialBackoff } = this.config.retryOptions;

    if (exponentialBackoff) {
      // 指数退避策略：基础延迟 * 2^(重试次数-1) + 随机抖动
      return retryDelayMs * Math.pow(2, retryCount - 1) + Math.random() * 1000;
    }

    // 固定延迟策略
    return retryDelayMs;
  }

  /**
   * 更新性能统计信息
   */
  private updatePerformanceStats(job: Job<T>): void {
    if (!job.startedAt || !job.completedAt) {
      return;
    }

    // 计算等待时间和处理时间
    const waitTime = job.startedAt - job.createdAt;
    const processTime = job.completedAt - job.startedAt;

    // 更新平均等待时间
    this.stats.averageWaitTime = this.calculateMovingAverage(
      this.stats.averageWaitTime,
      waitTime,
      this.stats.completedJobs + this.stats.failedJobs,
    );

    // 更新平均处理时间
    this.stats.averageProcessTime = this.calculateMovingAverage(
      this.stats.averageProcessTime,
      processTime,
      this.stats.completedJobs + this.stats.failedJobs,
    );

    // 更新吞吐量（每秒完成的任务数）
    const now = Date.now();
    const recentCompletions = [...this.requestTimestamps].filter(
      timestamp => now - timestamp < 60000,
    ).length;

    this.stats.throughput = recentCompletions / 60;
  }

  /**
   * 计算移动平均值
   */
  private calculateMovingAverage(
    currentAvg: number,
    newValue: number,
    count: number,
  ): number {
    if (count <= 1) {
      return newValue;
    }
    return currentAvg + (newValue - currentAvg) / count;
  }

  /**
   * 取消指定任务
   * @param jobId 任务ID
   * @returns 是否成功取消
   */
  cancelJob(jobId: string): boolean {
    // 检查活动任务
    if (this.activeJobs.has(jobId)) {
      const job = this.activeJobs.get(jobId)!;
      job.status = JobStatus.CANCELLED;
      this.activeJobs.delete(jobId);
      this.stats.activeJobs--;
      this.stats.activeWeight -= job.weight;
      return true;
    }

    // 检查队列中的任务
    const index = this.jobQueue.findIndex(job => job.id === jobId);
    if (index !== -1) {
      const job = this.jobQueue[index];
      job.status = JobStatus.CANCELLED;
      this.jobQueue.splice(index, 1);
      this.stats.queuedJobs--;
      this.stats.totalWeight -= job.weight;
      return true;
    }

    return false;
  }

  /**
   * 批量取消任务
   * @param jobIds 任务ID数组
   * @returns 成功取消的任务数
   */
  cancelJobs(jobIds: string[]): number {
    let cancelledCount = 0;

    for (const jobId of jobIds) {
      if (this.cancelJob(jobId)) {
        cancelledCount++;
      }
    }

    return cancelledCount;
  }

  /**
   * 取消所有任务
   * @returns 取消的任务数
   */
  cancelAll(): number {
    const activeCount = this.activeJobs.size;
    const queuedCount = this.jobQueue.length;

    // 标记所有活动任务为取消
    for (const job of this.activeJobs.values()) {
      job.status = JobStatus.CANCELLED;
    }

    // 清空活动任务列表
    this.activeJobs.clear();

    // 标记所有队列中的任务为取消
    for (const job of this.jobQueue) {
      job.status = JobStatus.CANCELLED;
    }

    // 清空队列
    this.jobQueue = [];

    // 更新统计信息
    this.stats.activeJobs = 0;
    this.stats.queuedJobs = 0;
    this.stats.activeWeight = 0;
    this.stats.totalWeight = 0;

    // 中止所有正在进行的操作
    this.abortController.abort();
    this.abortController = new AbortController();

    return activeCount + queuedCount;
  }

  /**
   * 暂停队列处理
   */
  pause(): void {
    this.paused = true;
  }

  /**
   * 恢复队列处理
   */
  resume(): void {
    if (this.paused) {
      this.paused = false;
      this.processQueue();
    }
  }

  /**
   * 获取队列状态
   * @returns 队列统计信息
   */
  getStats(): ConcurrencyManagerStats {
    return { ...this.stats };
  }

  /**
   * 获取任务状态
   * @param jobId 任务ID
   * @returns 任务对象或undefined
   */
  getJobStatus(jobId: string): Job<T> | undefined {
    // 检查活动任务
    if (this.activeJobs.has(jobId)) {
      return { ...this.activeJobs.get(jobId)! };
    }

    // 检查队列中的任务
    const queuedJob = this.jobQueue.find(job => job.id === jobId);
    if (queuedJob) {
      return { ...queuedJob };
    }

    return undefined;
  }

  /**
   * 获取所有任务状态
   * @returns 所有任务的状态
   */
  getAllJobStatus(): Job<T>[] {
    return [...Array.from(this.activeJobs.values()), ...this.jobQueue].map(
      job => ({ ...job }),
    );
  }

  /**
   * 更新配置
   * @param config 新的配置选项
   */
  updateConfig(config: Partial<ConcurrencyManagerConfig>): void {
    this.config = {
      ...this.config,
      ...config,
    };

    // 如果队列有任务且有空闲槽位，尝试处理队列
    if (
      this.jobQueue.length > 0 &&
      this.canExecuteMore() &&
      !this.paused &&
      !this.isProcessing
    ) {
      this.processQueue();
    }
  }

  /**
   * 清理完成的任务历史
   * @param olderThanMs 清理多少毫秒前完成的任务
   */
  clearHistory(olderThanMs = 3600000): void {
    const now = Date.now();
    this.requestTimestamps = this.requestTimestamps.filter(
      timestamp => now - timestamp < olderThanMs,
    );
  }
}
