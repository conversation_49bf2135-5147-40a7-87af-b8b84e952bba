/**
 * Timeline 时间线组件
 * 
 * 用于展示按时间顺序排列的事件列表，支持自定义数据源、样式配置和交互效果。
 * 适用于展示新闻动态、发展历程等场景。
 */
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    /**
     * 时间线数据数组
     * @type {Array<TimelineItem>}
     */
    timelineData: {
      type: Array,
      value: [],
      observer(newVal) {
        if (Array.isArray(newVal) && newVal.length > 0) {
          // 初始化动画状态
          const data = newVal.map(function(item) {
            return Object.assign({}, item, {
              animated: false
            });
          });
          this.setData({ internalTimelineData: data });
          
          // 如果启用了动画，则在数据更新后执行动画
          if (this.data.showAnimation) {
            this.animateTimeline();
          }
        } else if (Array.isArray(newVal) && newVal.length === 0) {
          // 处理空数组情况
          this.setData({ internalTimelineData: [] });
        }
      }
    },
    
    /**
     * 是否展示进入动画效果
     * @type {Boolean}
     */
    showAnimation: {
      type: Boolean,
      value: true
    },
    
    /**
     * 主题色，影响时间点和连接线颜色
     * @type {String}
     */
    primaryColor: {
      type: String,
      value: "#4a90e2"
    },
    
    /**
     * 时间线标题
     * @type {String}
     */
    title: {
      type: String,
      value: "发展历程",
      observer(newVal) {
        if (newVal && newVal.length > 20) {
          console.warn('title 长度超过 20 个字符的限制');
        }
      }
    },
    
    /**
     * 时间线项目间距(rpx)
     * @type {Number}
     */
    itemSpacing: {
      type: Number,
      value: 50,
      observer(newVal) {
        if (newVal < 30 || newVal > 100) {
          console.warn('itemSpacing 应在 30-100 范围内');
        }
      }
    }
  },

  /**
   * 组件的内部数据
   */
  data: {
    internalTimelineData: [] // 内部使用的数据，包含动画状态
  },

  /**
   * 组件的生命周期函数
   */
  lifetimes: {
    /**
     * 组件在视图层布局完成后执行
     */
    attached() {
      // 组件初始化时，如果已有数据，则设置内部数据
      if (this.properties.timelineData && this.properties.timelineData.length > 0) {
        const data = this.properties.timelineData.map(function(item) {
          return Object.assign({}, item, {
            animated: false
          });
        });
        this.setData({ internalTimelineData: data });
      }
    },
    
    /**
     * 组件在视图层渲染完毕后执行
     */
    ready() {
      // 组件准备完成后，如果启用了动画，则执行动画
      if (this.data.showAnimation && this.data.internalTimelineData.length > 0) {
        this.animateTimeline();
      }
    }
  },

  /**
   * 组件的方法列表
   */

  methods: {
    /**
     * 执行时间线动画
     * 逐个显示时间线项目
     */
    animateTimeline() {
      const items = this.data.internalTimelineData || [];
      items.forEach(function(item, index) {
        setTimeout(function() {
          this.setData({
            ['internalTimelineData[' + index + '].animated']: true
          });
        }.bind(this), index * 200); // 每项延迟200ms显示
      }.bind(this));
    },

    /**
     * 时间线项目点击事件处理
     * @param {Object} event - 事件对象
     */
    onTimelineItemTap(event) {
      const { index } = event.currentTarget.dataset;
      const item = this.data.internalTimelineData[index];
      
      if (item) {
        // 触发自定义事件，传递点击的项目数据
        this.triggerEvent('itemtap', { item });
        
        // 显示模态框
        lynx.showModal({
          title: item.title,
          content: item.description,
          confirmText: '确定',
          success: function(res) {
            if (res.confirm) {
              console.log('用户点击确定');
            }
          }
        });
      }
    }
  }
});
