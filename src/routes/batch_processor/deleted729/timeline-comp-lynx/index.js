/**
 * Timeline 组件演示页面
 * 展示 timeline-comp-lynx 组件的各种使用方式和功能
 */
Card({
  data: {
    // 基础示例数据 - 简化版本
    basicTimelineData: [
      {
        id: 1,
        date: "2024年1月",
        title: "项目启动",
        description: "开始新项目开发"
      },
      {
        id: 2,
        date: "2024年3月",
        title: "原型完成",
        description: "完成产品原型设计"
      },
      {
        id: 3,
        date: "2024年6月",
        title: "测试发布",
        description: "发布测试版本"
      }
    ],

    // 高级示例数据 - 简化版本
    advancedTimelineData: [
      {
        id: 1,
        date: "2024年1月",
        title: "项目启动",
        description: "正式启动新产品开发项目"
      },
      {
        id: 2,
        date: "2024年3月",
        title: "原型完成",
        description: "完成产品原型设计"
      },
      {
        id: 3,
        date: "2024年6月",
        title: "Beta发布",
        description: "发布Beta测试版本"
      },
      {
        id: 4,
        date: "2024年8月",
        title: "正式上线",
        description: "产品正式版本上线"
      }
    ],

    // 当前显示的数据集
    currentDataSet: 'basic',

    // 组件配置
    showAnimation: true,
    primaryColor: '#3498db',
    timelineTitle: '发展历程'
  },

  onLoad: function() {
    console.log('Timeline 演示页面加载完成');
  },

  onReady: function() {
    console.log('Timeline 演示页面渲染完成');
  },

  // 切换数据集
  switchDataSet: function(event) {
    const dataSet = event.currentTarget.dataset.type;
    this.setData({
      currentDataSet: dataSet
    });

    console.log('切换到数据集:', dataSet);
  },

  // 切换动画效果
  toggleAnimation: function() {
    this.setData({
      showAnimation: !this.data.showAnimation
    });

    console.log('动画效果:', this.data.showAnimation ? '开启' : '关闭');
  },

  // 更改主题色
  changeThemeColor: function(event) {
    const color = event.currentTarget.dataset.color;
    this.setData({
      primaryColor: color
    });

    console.log('主题色更改为:', color);
  },

  // 处理时间线项目点击事件
  onTimelineItemTap: function(event) {
    const item = event.detail.item;
    console.log('点击了时间线项目:', item);

    // 可以在这里添加自定义的处理逻辑
    lynx.showToast({
      title: '点击了: ' + item.title,
      duration: 2000
    });
  },

  // 重置演示
  resetDemo: function() {
    this.setData({
      currentDataSet: 'basic',
      showAnimation: true,
      primaryColor: '#3498db',
      timelineTitle: '发展历程'
    });

    lynx.showToast({
      title: '演示已重置',
      duration: 1500
    });
  }
});