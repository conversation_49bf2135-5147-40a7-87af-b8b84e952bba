/**
 * Timeline 组件演示页面
 * 展示 timeline-comp-lynx 组件的各种使用方式和功能
 */
Card({
  data: {
    // 基础示例数据
    basicTimelineData: [
      {
        id: 1,
        date: "2022年11月8日",
        title: "拜登预防性赦免",
        description: "即任前赦免福奇、米利等关键人物及全家",
        isLast: false,
        animated: false
      },
      {
        id: 2,
        date: "2022年11月15日",
        title: "特朗普解雇行为",
        description: "上台后解雇超10000名拜登政府人员",
        isLast: false,
        animated: false
      },
      {
        id: 3,
        date: "2022年12月1日",
        title: "特朗普大规模赦免",
        description: "即任前赦免福奇、米利等关键人物及全家",
        isLast: false,
        animated: false
      },
      {
        id: 4,
        date: "2022年12月20日",
        title: "19州检察长起诉",
        description: "民主党人联合起诉特朗普行政令违宪",
        isLast: true,
        animated: false
      }
    ],

    // 高级示例数据 - 包含更多字段
    advancedTimelineData: [
      {
        id: 1,
        date: "2024年1月15日",
        title: "项目启动",
        description: "正式启动新产品开发项目，组建核心团队",
        type: "milestone",
        status: "completed",
        isLast: false,
        animated: false
      },
      {
        id: 2,
        date: "2024年3月20日",
        title: "原型设计完成",
        description: "完成产品原型设计，通过内部评审",
        type: "design",
        status: "completed",
        isLast: false,
        animated: false
      },
      {
        id: 3,
        date: "2024年6月10日",
        title: "Beta版本发布",
        description: "发布Beta测试版本，邀请用户参与测试",
        type: "release",
        status: "completed",
        isLast: false,
        animated: false
      },
      {
        id: 4,
        date: "2024年8月25日",
        title: "正式版上线",
        description: "产品正式版本上线，开始商业化运营",
        type: "launch",
        status: "ongoing",
        isLast: false,
        animated: false
      },
      {
        id: 5,
        date: "2024年12月31日",
        title: "年度总结",
        description: "回顾全年成果，制定下一年发展计划",
        type: "summary",
        status: "planned",
        isLast: true,
        animated: false
      }
    ],

    // 当前显示的数据集
    currentDataSet: 'basic',

    // 组件配置
    showAnimation: true,
    primaryColor: '#3498db',
    timelineTitle: '发展历程'
  },

  onLoad: function() {
    console.log('Timeline 演示页面加载完成');
  },

  onReady: function() {
    console.log('Timeline 演示页面渲染完成');
  },

  // 切换数据集
  switchDataSet: function(event) {
    const dataSet = event.currentTarget.dataset.type;
    this.setData({
      currentDataSet: dataSet
    });

    console.log('切换到数据集:', dataSet);
  },

  // 切换动画效果
  toggleAnimation: function() {
    this.setData({
      showAnimation: !this.data.showAnimation
    });

    console.log('动画效果:', this.data.showAnimation ? '开启' : '关闭');
  },

  // 更改主题色
  changeThemeColor: function(event) {
    const color = event.currentTarget.dataset.color;
    this.setData({
      primaryColor: color
    });

    console.log('主题色更改为:', color);
  },

  // 处理时间线项目点击事件
  onTimelineItemTap: function(event) {
    const item = event.detail.item;
    console.log('点击了时间线项目:', item);

    // 可以在这里添加自定义的处理逻辑
    lynx.showToast({
      title: '点击了: ' + item.title,
      duration: 2000
    });
  },

  // 重置演示
  resetDemo: function() {
    this.setData({
      currentDataSet: 'basic',
      showAnimation: true,
      primaryColor: '#3498db',
      timelineTitle: '发展历程'
    });

    lynx.showToast({
      title: '演示已重置',
      duration: 1500
    });
  }
});