Timeline Component 时间线组件

  概述

  展示时间线事件的组件，支持自定义数据源和样式配置。

  Props 参数

  timelineData

  - 类型: Array<TimelineItem>
  - 必填: 是
  - 默认值: []
  - 描述: 时间线数据数组

  TimelineItem 对象结构：

  interface TimelineItem {
    id: string | number;          // 唯一标识
    date: string;                 // 时间显示文本
    title: string;                // 事件标题
    description: string;          // 事件描述
    isLast?: boolean;            // 是否为最后一项
  }

  showAnimation

  - 类型: Boolean
  - 必填: 否
  - 默认值: true
  - 描述: 是否显示进入动画效果

  primaryColor

  - 类型: String
  - 必填: 否
  - 默认值: "#4a90e2"
  - 描述: 主题色，影响时间点颜色
  - 限制: 有效的CSS颜色值

  title

  - 类型: String
  - 必填: 否
  - 默认值: "发展历程"
  - 描述: 时间线标题
  - 限制: 最大长度 20 个字符

  itemSpacing

  - 类型: Number
  - 必填: 否
  - 默认值: 50
  - 描述: 时间线项目间距(rpx)
  - 限制: 范围 30-100

  使用示例

  // 在页面中使用
  Card({
    data: {
      timelineData: [
        {
          id: 1,
          date: "2022年11月8日",
          title: "拜登预防性赦免",
          description: "即任前赦免福奇、米利等关键人物及全家物及全物及全"
        },
        {
          id: 2,
          date: "2022年11月8日",
          title: "特朗普解雇行为",
          description: "上台台后解雇超10000名拜登政府人员"
        }
      ]
    }
  });

  事件回调

  onTimelineItemTap

  - 参数: event - 事件对象，包含点击项目的索引
  - 描述: 点击时间线项目时触发

  onShareTimeline

  - 参数: 无
  - 描述: 分享时间线时触发

  样式自定义

  组件支持通过CSS变量自定义样式：

  .timeline-container {
    --primary-color: #4a90e2;
    --text-color: #333333;
    --date-color: #999999;
    --description-color: #666666;
    --line-color: #e0e0e0;
  }

  兼容性

  - Lynx 版本: >= 3.6.0
  - 平台支持: iOS、Android、Web
  - 性能: 支持大数据量渲染(建议 < 100 项)