const { miniAppNodiffPlugin } = require("@byted-lynx/plugin-miniapp-nodiff");

/**
 * @type {import('@byted-lynx/lynx-speedy').UserConfig}
 */
module.exports = {
  dsl: "miniAppNodiff",
  input: {
    // 智能检测的入口文件路径: ./index.js
    main: "./index.js"
  },
  dslPlugin: miniAppNodiffPlugin({}),
  pageConfig: {
    enableJSRender: true,
  },
  encode: {
    useLepusNG: true,
    targetSdkVersion: "2.13",
    enableParallelElement: true,
  },
};