<scroll-view class="demo-page-container" scroll-y="true">
  <view class="demo-page">
    <!-- 页面标题 -->
    <view class="page-header">
      <text class="page-title">Timeline 组件演示</text>
      <text class="page-subtitle">展示时间轴组件的各种功能和配置</text>
    </view>

    <!-- 控制面板 -->
    <view class="control-panel">
      <text class="panel-title">演示控制</text>

      <!-- 数据集切换 -->
      <view class="control-group">
        <text class="control-label">数据集:</text>
        <view class="button-group">
          <view
            class="control-button {{currentDataSet === 'basic' ? 'active' : ''}}"
            bindtap="switchDataSet"
            data-type="basic"
          >
            <text class="{{currentDataSet === 'basic' ? 'button-text-active' : 'button-text'}}">基础数据</text>
          </view>
          <view
            class="control-button {{currentDataSet === 'advanced' ? 'active' : ''}}"
            bindtap="switchDataSet"
            data-type="advanced"
          >
            <text class="{{currentDataSet === 'advanced' ? 'button-text-active' : 'button-text'}}">高级数据</text>
          </view>
        </view>
      </view>

      <!-- 动画控制 -->
      <view class="control-group">
        <text class="control-label">动画效果:</text>
        <view class="control-button" bindtap="toggleAnimation">
          <text class="button-text">{{showAnimation ? '关闭动画' : '开启动画'}}</text>
        </view>
      </view>

      <!-- 主题色控制 -->
      <view class="control-group">
        <text class="control-label">主题色:</text>
        <view class="color-group">
          <view
            class="color-button"
            style="background-color: #3498db"
            bindtap="changeThemeColor"
            data-color="#3498db"
          ></view>
          <view
            class="color-button"
            style="background-color: #e74c3c"
            bindtap="changeThemeColor"
            data-color="#e74c3c"
          ></view>
          <view
            class="color-button"
            style="background-color: #27ae60"
            bindtap="changeThemeColor"
            data-color="#27ae60"
          ></view>
          <view
            class="color-button"
            style="background-color: #f39c12"
            bindtap="changeThemeColor"
            data-color="#f39c12"
          ></view>
        </view>
      </view>

      <!-- 重置按钮 -->
      <view class="control-group">
        <view class="control-button reset-button" bindtap="resetDemo">
          <text class="button-text-reset">重置演示</text>
        </view>
      </view>
    </view>

    <!-- Timeline 组件演示区域 -->
    <view class="timeline-demo-section">
      <text class="section-title">组件演示</text>

      <!-- 使用 timeline 组件 -->
      <timeline-comp
        timelineData="{{currentDataSet === 'basic' ? basicTimelineData : advancedTimelineData}}"
        showAnimation="{{showAnimation}}"
        primaryColor="{{primaryColor}}"
        title="{{timelineTitle}}"
        binditemtap="onTimelineItemTap"
      />
    </view>

    <!-- 代码示例 -->
    <view class="code-example-section">
      <text class="section-title">使用示例</text>
      <view class="code-block">
        <text class="code-text">timeline-comp 组件使用示例</text>
      </view>
    </view>

    <!-- 数据格式说明 -->
    <view class="data-format-section">
      <text class="section-title">数据格式</text>
      <view class="format-block">
        <text class="format-text">数据格式说明：包含 id、date、title、description、isLast 等字段</text>
      </view>
    </view>
  </view>
</scroll-view>
