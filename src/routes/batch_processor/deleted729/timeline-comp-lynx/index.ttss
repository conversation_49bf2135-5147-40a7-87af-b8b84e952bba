/**
 * 全局样式
 */

page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  background-color: #f5f5fa;
  color: #333333;
  font-size: 28rpx;
  line-height: 1.5;
}

.container {
  display: flex;
  flex-direction: column;
  padding: 30rpx;
}

.section {
  margin-bottom: 40rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eeeeee;
}

.section-content {
  padding: 10rpx 0;
}

.btn {
  display: inline-block;
  padding: 16rpx 32rpx;
  background-color: #4a90e2;
  color: #ffffff;
  font-size: 28rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
  margin-bottom: 20rpx;
}

.btn-primary {
  background-color: #4a90e2;
}

.btn-success {
  background-color: #52c41a;
}

.btn-warning {
  background-color: #faad14;
}

.btn-danger {
  background-color: #f5222d;
}

.custom-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx 0;
}

.custom-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
}

.custom-item {
  padding: 20rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
}

.custom-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60rpx 0;
}

.custom-footer {
  text-align: center;
  color: #999999;
  padding: 30rpx 0;
}

.item-image {
  width: 100%;
  height: 200rpx;
  margin-top: 20rpx;
  border-radius: 8rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.logo {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 10rpx;
}