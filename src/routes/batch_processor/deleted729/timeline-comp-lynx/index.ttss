/**
 * Timeline 组件演示页面样式
 */

/* 页面容器 */
.demo-page-container {
  height: 100vh;
  width: 100%;
  background-color: #f8f9fa;
}

.demo-page {
  padding: 40rpx;
  min-height: 100%;
}

/* 页面标题 */
.page-header {
  text-align: center;
  margin-bottom: 60rpx;
  padding: 40rpx 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
  color: #ffffff;
}

.page-title {
  font-size: 48rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  text-align: center;
  display: block;
}

.page-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  text-align: center;
  display: block;
}

/* 控制面板 */
.control-panel {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  border: 1rpx solid #e8e8e8;
}

.panel-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 30rpx;
  text-align: center;
  display: block;
}

/* 控制组 */
.control-group {
  margin-bottom: 30rpx;
  display: flex;
  flex-direction: column;
}

.control-label {
  font-size: 28rpx;
  color: #5a6c7d;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.button-group {
  display: flex;
  gap: 16rpx;
}

.control-button {
  padding: 16rpx 24rpx;
  background-color: #f8f9fa;
  border: 1rpx solid #dee2e6;
  border-radius: 8rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.control-button.active {
  background-color: #3498db;
  border-color: #3498db;
}

.button-text {
  font-size: 26rpx;
  color: #495057;
  text-align: center;
}

.button-text-active {
  color: #ffffff;
}

.reset-button {
  background-color: #e74c3c;
  border-color: #e74c3c;
}

.button-text-reset {
  color: #ffffff;
  font-size: 26rpx;
  text-align: center;
}

/* 颜色选择器 */
.color-group {
  display: flex;
  gap: 16rpx;
}

.color-button {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  border: 3rpx solid #ffffff;
}

/* 演示区域 */
.timeline-demo-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  border: 1rpx solid #e8e8e8;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 30rpx;
  text-align: center;
  display: block;
}

/* 代码示例区域 */
.code-example-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  border: 1rpx solid #e8e8e8;
}

.code-block {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}

.code-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 24rpx;
  color: #495057;
  line-height: 1.6;
}

/* 数据格式区域 */
.data-format-section {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 40rpx;
  border: 1rpx solid #e8e8e8;
}

.format-block {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 24rpx;
  border: 1rpx solid #e9ecef;
}

.format-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 24rpx;
  color: #495057;
  line-height: 1.6;
}