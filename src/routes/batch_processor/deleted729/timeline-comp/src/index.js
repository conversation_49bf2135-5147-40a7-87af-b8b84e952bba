class TimelineItem extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: 'open' });
  }

  connectedCallback() {
    const date = this.getAttribute('date');
    const title = this.getAttribute('title');
    const content = this.getAttribute('content');

    this.shadowRoot.innerHTML = `
      <style>
        :host {
          display: block;
          position: relative;
          margin-bottom: 16px;
        }
        .timeline-item {
          display: grid;
          grid-template-columns: 120px 24px 1fr;
          gap: 8px;
          opacity: 0;
        }
        .timeline-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: rgb(var(--timeline-color, 37 99 235));
          border: 2px solid white;
          box-shadow: 0 0 0 2px rgb(var(--timeline-color, 37 99 235) / 0.2);
          position: absolute;
          top: 6px;
          right: 6px;
          z-index: 2;
        }
        .dot-column {
          position: relative;
        }
        .timeline-content {
          background-color: rgb(var(--timeline-color, 37 99 235) / 0.05);
          border: 1px solid rgb(var(--timeline-color, 37 99 235) / 0.1);
          border-radius: 8px;
          padding: 12px;
          transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .date {
          color: #666;
          font-size: 14px;
          text-align: right;
          padding-top: 4px;
        }
        .title {
          font-weight: bold;
          font-size: 15px;
          margin-bottom: 4px;
          color: #333;
        }
        .description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
        }

        /* 基础动画 */
        .timeline-item {
          animation-duration: 0.5s;
          animation-fill-mode: forwards;
          animation-timing-function: ease-out;
          animation-delay: calc(var(--item-index, 0) * 0.1s);
        }

        /* 默认渐入 */
        .timeline-item.fade {
          animation-name: fadeIn;
        }

        /* 滑入 */
        .timeline-item.slide {
          animation-name: slideIn;
        }

        /* 弹跳 */
        .timeline-item.bounce {
          animation-name: bounceIn;
        }

        /* 缩放 */
        .timeline-item.scale {
          animation-name: scaleIn;
        }

        /* 翻转 */
        .timeline-item.flip {
          animation-name: flipIn;
        }

        /* 动画关键帧定义 */
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from {
            opacity: 0;
            transform: translateX(-30px);
          }
          to {
            opacity: 1;
            transform: translateX(0);
          }
        }

        @keyframes bounceIn {
          0% {
            opacity: 0;
            transform: scale(0.3);
          }
          50% {
            opacity: 0.9;
            transform: scale(1.1);
          }
          80% {
            opacity: 1;
            transform: scale(0.9);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }

        @keyframes scaleIn {
          from {
            opacity: 0;
            transform: scale(0.8);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }

        @keyframes flipIn {
          from {
            opacity: 0;
            transform: perspective(400px) rotateX(-90deg);
          }
          to {
            opacity: 1;
            transform: perspective(400px) rotateX(0);
          }
        }

        .timeline-content:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(var(--timeline-color), 0.15);
        }
      </style>
      <div class="timeline-item ${this.parentElement.getAttribute('animation') || 'fade'}">
        <div class="date">${date}</div>
        <div class="dot-column">
          <div class="timeline-dot"></div>
        </div>
        <div class="timeline-content">
          <div class="title">${title}</div>
          <div class="description">${content}</div>
        </div>
      </div>
    `;
  }
}

class Timeline extends HTMLElement {
  constructor() {
    super();
    this.attachShadow({ mode: 'open' });
    this._list = [];
  }

  static get observedAttributes() {
    return ['list'];
  }

  attributeChangedCallback(name, oldValue, newValue) {
    if (name === 'list' && oldValue !== newValue) {
      try {
        this._list = JSON.parse(newValue);
      } catch (e) {
        console.error('Failed to parse list attribute:', e);
        this._list = [];
      }
      this.render();
    }
  }

  connectedCallback() {
    if (this.hasAttribute('list')) {
      try {
        this._list = JSON.parse(this.getAttribute('list'));
      } catch (e) {
        console.error('Failed to parse list attribute:', e);
        this._list = [];
      }
    }
    this.render();
  }

  render() {
    this.shadowRoot.innerHTML = `
      <style>
        :host {
          display: block;
          position: relative;
          padding: 16px 0;
        }
        .timeline-container {
          position: relative;
        }
        .timeline-line {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 138px;
          width: 0;
          border-left: 2px dashed rgba(37, 99, 235, 0.3);
          animation: growLine 1s ease-out forwards;
          transform-origin: top;
        }
        @keyframes growLine {
          from { transform: scaleY(0); }
          to { transform: scaleY(1); }
        }
        .timeline-item {
          display: grid;
          grid-template-columns: 120px 24px 1fr;
          gap: 8px;
          margin-bottom: 16px;
          position: relative;
        }
        .timeline-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: rgb(37, 99, 235);
          border: 2px solid white;
          box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
          position: absolute;
          top: 6px;
          right: 6px;
          z-index: 2;
        }
        .dot-column {
          position: relative;
        }
        .timeline-content {
          background-color: rgba(37, 99, 235, 0.05);
          border: 1px solid rgba(37, 99, 235, 0.1);
          border-radius: 8px;
          padding: 12px;
          transition: transform 0.3s ease, box-shadow 0.3s ease;
          min-width: 0; /* Prevent content from stretching the container */
        }
        .title {
          font-weight: bold;
          font-size: 15px;
          margin-bottom: 4px;
          color: #333;
        }
        .description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .media-scroller {
          display: flex;
          gap: 8px;
          overflow-x: auto;
          margin-top: 12px;
          scrollbar-width: none; /* Firefox */
        }
        .media-scroller::-webkit-scrollbar {
          display: none; /* Safari, Chrome */
        }
        .media-item {
          display: block;
          text-decoration: none;
          width: 88px;
          height: 58px;
          border-radius: 4px;
          flex-shrink: 0;
          object-fit: cover;
          position: relative;
        }
        .video-thumbnail {
          width: 100%;
          height: 100%;
          border-radius: 4px;
          object-fit: cover;
        }
        .play-button {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 24px;
          height: 24px;
          background-color: rgba(0, 0, 0, 0.6);
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          transition: background-color: 0.3s;
        }
        .play-button:hover {
          background-color: rgba(0, 0, 0, 0.8);
        }
        .play-button::after {
          content: '';
          display: block;
          width: 0;
          height: 0;
          border-style: solid;
          border-width: 6px 0 6px 10px;
          border-color: transparent transparent transparent white;
          margin-left: 3px;
        }
        .date {
          color: #666;
          font-size: 14px;
          text-align: right;
          padding-top: 4px;
        }
        .title {
          font-weight: bold;
          font-size: 15px;
          margin-bottom: 4px;
          color: #333;
        }
        .description {
          font-size: 14px;
          color: #666;
          line-height: 1.5;
        }
        .timeline-content:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
        }
      </style>
      <div class="timeline-container">
        <div class="timeline-line"></div>
        ${(this._list || [])
          .map((item, index) => {
            let mediaHtml = '';
            if (item.media && Array.isArray(item.media) && item.media.length > 0) {
              mediaHtml = `
                <div class="media-scroller">
                  ${item.media
                    .map(mediaItem => {
                      if (mediaItem.type === 'image') {
                        return `<img src="${mediaItem.thumbnail}" alt="" class="media-item">`;
                      } else if (mediaItem.type === 'video') {
                        return `
                          <a href="${mediaItem.videoSrc || '#'}" target="_blank" class="media-item">
                            <img src="${mediaItem.thumbnail}" alt="" class="video-thumbnail">
                            <div class="play-button"></div>
                          </a>
                        `;
                      }
                      return '';
                    })
                    .join('')}
                </div>
              `;
            }
            return `
              <div class="timeline-item" style="--item-index: ${index};">
                <div class="date">${item.date}</div>
                <div class="dot-column">
                  <div class="timeline-dot"></div>
                </div>
                <div class="timeline-content">
                  <div class="title">${item.title}</div>
                  <div class="description">${item.content}</div>
                  ${mediaHtml}
                </div>
              </div>
            `;
          })
          .join('')}
      </div>
    `;
  }
}

customElements.define('timeline-comp', Timeline);
customElements.define('timeline-item', TimelineItem);
