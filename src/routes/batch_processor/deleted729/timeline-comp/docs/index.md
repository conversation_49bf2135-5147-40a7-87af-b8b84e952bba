# 时间线组件

## 组件名称

timeline-comp

## 使用场景

用于以垂直时间轴的形式展示一系列事件，支持图文、视频等多种媒体内容，常用于项目历程、新闻动态、活动记录等场景。

## 版本号

0.0.0-feat-ai-material-2025-07-06-20250706125703

## 属性列表

| 属性名 | 类型                       | 默认值 | 说明                                                                                                                                                                                                                                                                                                                                                                                                                             |
| ------ | -------------------------- | ------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| `list` | `string` (JSON 格式的数组) | `[]`   | 用于定义时间线的全部内容。数组中的每个对象代表一个时间事件，应包含以下 key：<br>- `date`: (必须) 事件日期<br>- `title`: (必须) 事件标题<br>- `content`: (必须) 事件描述，最多显示两行<br>- `media`: (可选) 媒体对象数组，每个对象包含：<br>&nbsp;&nbsp;- `type`: (必须) 媒体类型，'image' 或 'video'<br>&nbsp;&nbsp;- `thumbnail`: (必须) 预览图 URL<br>&nbsp;&nbsp;- `videoSrc`: (可选) 视频 URL，仅当 `type` 为 'video' 时有效 |

## 使用示例

```html
<timeline-comp
  list='[
    {
      "date": "2024-01-01",
      "title": "新年计划",
      "content": "制定全年发展计划，设定目标。"
    },
    {
      "date": "2024-03-15",
      "title": "产品发布",
      "content": "正式发布1.0版本，获得用户好评。",
      "media": [
        {"type": "image", "thumbnail": "https://path/to/release_image.jpg"}
      ]
    },
    {
      "date": "2024-06-20",
      "title": "用户突破",
      "content": "用户数突破10万大关，庆祝活动。",
      "media": [
        {"type": "image", "thumbnail": "https://path/to/celebration1.jpg"},
        {"type": "image", "thumbnail": "https://path/to/celebration2.jpg"}
      ]
    },
    {
      "date": "2024-12-31",
      "title": "年度总结",
      "content": "回顾全年成果，展望新的一年。"
    }
  ]'
>
</timeline-comp>
```
