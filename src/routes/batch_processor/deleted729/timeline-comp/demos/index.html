<!DOCTYPE html>
<html>
  <head>
    <meta name="viewport" content="width=device-width, initial-scale=1.0" charset="UTF-8" />
    <title>移动端时间线组件</title>
    <style>
      body {
        margin: 20px;
        font-family: system-ui, -apple-system, sans-serif;
      }
      .demo-section {
        margin-bottom: 40px;
      }
      h2 {
        color: #333;
        margin-bottom: 20px;
      }
    </style>
    <script src="../dist/index.js"></script>
  </head>
  <body>
    <div class="demo-section">
      <h2>基础时间线</h2>
      <timeline-comp
        list='[
          {"date": "2024-05-20", "title": "项目启动", "content": "项目初始化，完成基本设置，这是一个比较长的描述，目的是为了测试多行文本截断的效果是否生效。"},
          {"date": "2024-05-21", "title": "组件开发", "content": "完成核心组件的开发与测试...", "media": [
            {"type": "image", "thumbnail": "https://images.unsplash.com/photo-1618477388954-7852f32655ec?q=80&w=2864&auto=format&fit=crop"},
            {"type": "video", "thumbnail": "https://images.unsplash.com/photo-1618477388954-7852f32655ec?q=80&w=2864&auto=format&fit=crop", "videoSrc": "https://www.w3schools.com/html/mov_bbb.mp4"},
            {"type": "image", "thumbnail": "https://images.unsplash.com/photo-1542831371-29b0f74f9713?q=80&w=2940&auto=format&fit=crop"}
          ]},
          {"date": "2024-06-01", "title": "新功能上线", "content": "发布了用户期待已久的新功能..."},
          {"date": "2024-06-10", "title": "性能优化", "content": "对应用性能进行了全面优化...", "media": [
            {"type": "video", "thumbnail": "https://images.unsplash.com/photo-1618477388954-7852f32655ec?q=80&w=2864&auto=format&fit=crop", "videoSrc": "https://www.w3schools.com/html/mov_bbb.mp4"},
            {"type": "image", "thumbnail": "https://images.unsplash.com/photo-1587620962725-abab7fe55159?q=80&w=2831&auto=format&fit=crop"}
          ]},
          {"date": "2024-07-01", "title": "重大更新", "content": "发布了2.0版本重大更新..."}
        ]'
      >
      </timeline-comp>
    </div>
  </body>
</html>
