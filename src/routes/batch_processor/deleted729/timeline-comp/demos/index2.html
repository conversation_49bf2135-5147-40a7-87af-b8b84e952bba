<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>渐进式组件渲染方案</title>
    <!-- 使用CDN引入morphdom，避免模块定义错误 -->
    <!-- 引入 Himalaya 库 -->
    <script src="https://cdn.jsdelivr.net/npm/jsonrepair@3.13.0/lib/umd/jsonrepair.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/morphdom@2.6.1/dist/morphdom-umd.min.js"></script>
    <style>
      body {
        font-family: Arial, sans-serif;
        line-height: 1.6;
        max-width: 1000px;
        margin: 0 auto;
        padding: 20px;
      }

      .canvas {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        min-height: 400px;
        background-color: #f9f9f9;
      }

      .controls {
        margin: 20px 0;
        padding: 15px;
        background-color: #eee;
        border-radius: 8px;
      }

      button {
        padding: 8px 16px;
        background-color: #2196f3;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
      }

      button:hover {
        background-color: #0b7dda;
      }

      .stream-info {
        margin-top: 10px;
        font-family: monospace;
        background-color: #f0f0f0;
        padding: 10px;
        border-radius: 4px;
        white-space: pre-wrap;
        word-break: break-all;
        max-height: 100px;
        overflow-y: auto;
      }

      /* 时间线组件样式 */
      .timeline {
        position: relative;
        max-width: 1200px;
        margin: 0 auto;
      }

      .timeline::after {
        content: '';
        position: absolute;
        width: 6px;
        background-color: #2196f3;
        top: 0;
        bottom: 0;
        left: 50%;
        margin-left: -3px;
      }

      .timeline-container {
        padding: 10px 40px;
        position: relative;
        background-color: inherit;
        width: 50%;
        animation: fadeIn 0.5s ease-in-out forwards;
        opacity: 0;
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .timeline-container:nth-child(odd) {
        left: 0;
      }

      .timeline-container:nth-child(even) {
        left: 50%;
      }

      .timeline-content {
        padding: 20px 30px;
        background-color: white;
        position: relative;
        border-radius: 6px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
      }

      .timeline-container::after {
        content: '';
        position: absolute;
        width: 25px;
        height: 25px;
        right: -17px;
        background-color: white;
        border: 4px solid #2196f3;
        top: 15px;
        border-radius: 50%;
        z-index: 1;
      }

      .timeline-container:nth-child(even)::after {
        left: -16px;
      }

      .timeline-time {
        color: #777;
        font-style: italic;
      }

      .timeline-title {
        margin: 5px 0;
        color: #333;
        font-weight: bold;
      }
    </style>
  </head>
  <body>
    <h1>渐进式组件渲染方案演示</h1>

    <div class="controls">
      <button id="startBtn">开始流式渲染</button>
      <button id="resetBtn">重置演示</button>
      <div class="stream-info">当前HTML流: <span id="currentStream"></span></div>
    </div>

    <div id="canvas" class="canvas"></div>
    <script src="./incremental-renderer.js"></script>
    <script type="module">
      import { LitElement, html, css } from 'https://unpkg.com/lit@2.6.1/index.js?module';
      // 注册按钮组件
      class ButtonComp extends HTMLElement {
        static get observedAttributes() {
          return ['type', 'size', 'disabled'];
        }

        constructor() {
          super();
          this.attachShadow({ mode: 'open' });
        }

        connectedCallback() {
          this.render();
        }

        attributeChangedCallback(name, oldValue, newValue) {
          if (oldValue !== newValue) {
            this.render();
          }
        }

        render() {
          // 获取属性
          const type = this.getAttribute('type') || 'default';
          const size = this.getAttribute('size') || 'medium';
          const disabled = this.hasAttribute('disabled');

          // 样式映射
          const typeStyles = {
            primary: 'background-color: #1890ff; color: white;',
            danger: 'background-color: #ff4d4f; color: white;',
            success: 'background-color: #52c41a; color: white;',
            default: 'background-color: #f0f0f0; color: #333;',
          };

          const sizeStyles = {
            small: 'padding: 4px 8px; font-size: 12px;',
            medium: 'padding: 8px 16px; font-size: 14px;',
            large: 'padding: 12px 20px; font-size: 16px;',
          };

          // 渲染按钮
          this.shadowRoot.innerHTML = `
        <style>
          .button {
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: opacity 0.3s;
            ${typeStyles[type] || typeStyles.default}
            ${sizeStyles[size] || sizeStyles.medium}
            ${disabled ? 'opacity: 0.5; cursor: not-allowed;' : ''}
          }
          .button:hover {
            ${!disabled ? 'opacity: 0.8;' : ''}
          }
        </style>
        <button class="button" ${disabled ? 'disabled' : ''}>
          <slot>按钮</slot>
        </button>
      `;
        }
      }

      // 注册组件
      customElements.define('button-comp', ButtonComp);
      // 注册已知的自定义组件
      const CUSTOM_COMPONENTS = ['timeline-comp'];

      // 渐进式组件基类
      class ProgressiveComponent extends LitElement {
        // 解析不完整的JSON字符串
        parseIncompleteJSON(jsonStr) {
          try {
            return JSON.parse(jsonStr);
          } catch (e) {
            console.log('parseIncompleteJSON', e, jsonStr);
            try {
              // 处理缺少右括号的情况
              if (jsonStr.includes('[') && !jsonStr.includes(']')) {
                jsonStr += ']';
              }

              // 处理缺少右大括号的情况
              const openBraces = (jsonStr.match(/{/g) || []).length;
              const closeBraces = (jsonStr.match(/}/g) || []).length;
              if (openBraces > closeBraces) {
                jsonStr += '}'.repeat(openBraces - closeBraces);
              }

              // 处理属性名没有引号的情况
              jsonStr = jsonStr.replace(/([{,]\s*)(\w+)(\s*:)/g, '$1"$2"$3');

              // 处理值中未闭合的引号
              jsonStr = jsonStr.replace(/'([^']*$)/g, "'$1'");
              jsonStr = jsonStr.replace(/"([^"]*$)/g, '"$1"');

              return JSON.parse(jsonStr);
            } catch (e2) {
              console.warn('无法完全解析JSON，尝试提取部分数据', e2);

              // 提取数组的第一个元素
              const arrayMatch = jsonStr.match(/\[\s*{([^}]*)}/);
              if (arrayMatch) {
                try {
                  return JSON.parse(`[{${arrayMatch[1]}}]`);
                } catch (e3) {
                  console.warn('提取第一个元素失败', e3);
                }
              }

              // 提取单个键值对
              const propMatches = jsonStr.match(/['"](\w+)['"]:\s*['"]([^'"]*)['"]/g);

              if (propMatches) {
                const obj = {};
                propMatches.forEach(prop => {
                  const parts = prop.split(':');
                  if (parts.length >= 2) {
                    const key = parts[0].trim().replace(/['"]/g, '');
                    const value = parts[1].trim().replace(/['"]/g, '');
                    obj[key] = value;
                  }
                });
                return [obj];
              }

              // 如果还是解析失败，返回一个最小可用的对象
              if (jsonStr.includes('title')) {
                const titleMatch = jsonStr.match(/title['"]?\s*:\s*['"]([^'"]*)['"]/);
                if (titleMatch) {
                  return [{ title: titleMatch[1] }];
                }
              }

              return [];
            }
          }
        }

        updateFromAttribute() {
          this._hasRendered = true;
          this.requestUpdate();
        }

        firstUpdated() {
          this._hasRendered = true;
        }
      }

      // 时间线组件实现
      class TimelineComponent extends ProgressiveComponent {
        static properties = {
          list: { type: Array },
        };

        constructor() {
          super();
          this.list = [];
        }

        updateFromAttribute() {
          const listAttr = this.getAttribute('list');
          if (listAttr) {
            console.log('updateFromAttribute', listAttr);
            const parsedList = this.parseIncompleteJSON(listAttr);
            if (parsedList && Array.isArray(parsedList)) {
              this.list = parsedList;
              super.updateFromAttribute();
            }
          }
        }

        connectedCallback() {
          console.log('connectedCallback');
          super.connectedCallback();
          this.updateFromAttribute();
        }

        attributeChangedCallback(name, oldVal, newVal) {
          super.attributeChangedCallback(name, oldVal, newVal);
          if (name === 'list' && oldVal !== newVal) {
            this.updateFromAttribute();
          }
        }

        static styles = css`
          :host {
            display: block;
            margin: 20px 0;
            font-family: Arial, sans-serif;
          }
          .timeline {
            position: relative;
            max-width: 1200px;
            margin: 0 auto;
          }
          .timeline::after {
            content: '';
            position: absolute;
            width: 6px;
            background-color: #2196f3;
            top: 0;
            bottom: 0;
            left: 50%;
            margin-left: -3px;
          }
          .container {
            padding: 10px 40px;
            position: relative;
            background-color: inherit;
            width: 50%;
            animation: fadeIn 0.5s ease-in-out forwards;
            opacity: 0;
          }
          @keyframes fadeIn {
            from {
              opacity: 0;
              transform: translateY(20px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
          .container:nth-child(odd) {
            left: 0;
          }
          .container:nth-child(even) {
            left: 50%;
          }
          .content {
            padding: 20px 30px;
            background-color: white;
            position: relative;
            border-radius: 6px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
            // min-height: 120px; /* 保证占位时高度一致 */
          }
          .container::after {
            content: '';
            position: absolute;
            width: 25px;
            height: 25px;
            right: -17px;
            background-color: white;
            border: 4px solid #2196f3;
            top: 15px;
            border-radius: 50%;
            z-index: 1;
          }
          .container:nth-child(even)::after {
            left: -16px;
          }
          .time {
            color: #777;
            font-style: italic;
            min-height: 1.2em;
          }
          .title {
            margin: 5px 0;
            color: #333;
            font-weight: bold;
            min-height: 1.5em;
          }
          .content-text {
            min-height: 3em;
          }

          .skeleton {
            background-color: #e0e0e0;
            border-radius: 4px;
            animation: pulse 1.5s infinite ease-in-out;
            display: block;
          }

          .skeleton.time {
            max-width: 80px;
            width: 60%;
            height: 1.2em;
            margin-bottom: 8px;
          }

          .skeleton.title {
            max-width: 60%;
            width: 100%;
            height: 1.4em;
            margin-bottom: 8px;
          }

          .skeleton.content {
            max-width: 100%;
            height: 30px;
            margin-top: 4px;
            margin-bottom: 4px;
          }

          @keyframes pulse {
            0% {
              opacity: 1;
            }
            50% {
              opacity: 0.4;
            }
            100% {
              opacity: 1;
            }
          }
        `;

        render() {
          console.log('---render---');
          return html`
            <div class="timeline">
              ${(this.list && this.list.length ? this.list : [{}]).map((item, index) => {
                const hasTime = !!item.time;
                const hasTitle = !!item.title;
                const hasContent = !!item.content;
                return html`
                  <div class="container" style="animation-delay: ${index * 0.2}s">
                    <div class="content">
                      ${hasTime ? html`<div class="time">${item.time}</div>` : html`<div class="time skeleton"></div>`}
                      ${hasTitle
                        ? html`<h2 class="title">${item.title}</h2>`
                        : html`<div class="title skeleton"></div>`}
                      ${hasContent
                        ? html`<p class="content-text">${item.content}</p>`
                        : html`<div class="content skeleton"></div>`}
                    </div>
                  </div>
                `;
              })}
            </div>
          `;
        }
      }

      // 注册自定义组件
      customElements.define('timeline-comp', TimelineComponent);

      class RenderEngine {
        constructor(targetSelector) {
          this.targetElement = document.querySelector(targetSelector);
          this.currentHTML = '';
        }

        // 处理HTML流
        processHTMLStream(htmlStream) {
          if (!this.targetElement) return;

          // 预处理HTML流，确保自定义组件标签闭合
          const processedHtml = preprocessHtmlStream(htmlStream);

          // const processedHtml = htmlStream;
          this.currentHTML = processedHtml;

          // 创建临时容器
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = this.currentHTML;

          if (this.targetElement.firstChild) {
            // 使用morphdom进行增量DOM更新
            morphdom(this.targetElement.firstChild, tempDiv.firstChild, {
              onBeforeElUpdated: (fromEl, toEl) => {
                // 保留自定义组件的状态
                if (fromEl.tagName.includes('-') && fromEl._hasRendered) {
                  // 只更新属性，不完全替换元素
                  Array.from(toEl.attributes).forEach(attr => {
                    if (fromEl.getAttribute(attr.name) !== attr.value) {
                      fromEl.setAttribute(attr.name, attr.value);
                    }
                  });

                  // 手动触发自定义组件的更新
                  if (typeof fromEl.updateFromAttribute === 'function') {
                    fromEl.updateFromAttribute();
                  }

                  return false; // 阻止替换
                }
                return true;
              },
            });
          } else {
            // 首次渲染
            this.targetElement.appendChild(tempDiv.firstChild);
          }

          // 查找并处理所有自定义组件
          const customElements = this.targetElement.querySelectorAll('*[list]');
          customElements.forEach(el => {
            if (el.hasAttribute('list') && typeof el.updateFromAttribute === 'function') {
              el.updateFromAttribute();
            }
          });
        }
      }

      // 初始化渲染引擎
      const renderEngine = new RenderEngine('#canvas');

      // 模拟HTML流
      const htmlStreams = [
        '<div><h2>渐进式渲染演示</h2>',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="pr',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="med',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="medium">按钮',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="medium">按钮</button-comp>',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="medium">按钮</button-comp><timeline-comp',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="medium">按钮</button-comp><timeline-comp list=\'[{"title": "项目',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="medium">按钮</button-comp><timeline-comp list=\'[{"title": "项目启动", "time": "2025-01-15"',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="medium">按钮</button-comp><timeline-comp list=\'[{"title": "项目启动", "time": "2025-01-15", "content": "开始规划新的前端渲染方案"}, {"title": "方案设计"}',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="medium">按钮</button-comp><timeline-comp list=\'[{"title": "项目启动", "time": "2025-01-15", "content": "开始规划新的前端渲染方案"}, {"title": "方案设计", "time": "2025-02-01", "content": "完成基于Lit的组件设计"}, {"title": "开发实现"}',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="medium">按钮</button-comp><timeline-comp list=\'[{"title": "项目启动", "time": "2025-01-15", "content": "开始规划新的前端渲染方案"}, {"title": "方案设计", "time": "2025-02-01", "content": "完成基于Lit的组件设计"}, {"title": "开发实现", "time": "2025-03-10", "content": "实现渐进式渲染引擎"}, {"title": "测试部署"',
        '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary" disabled size="medium">按钮</button-comp><timeline-comp list=\'[{"title": "项目启动", "time": "2025-01-15", "content": "开始规划新的前端渲染方案"}, {"title": "方案设计", "time": "2025-02-01", "content": "完成基于Lit的组件设计"}, {"title": "开发实现", "time": "2025-03-10", "content": "实现渐进式渲染引擎"}, {"title": "测试部署", "time": "2025-04-15", "content": "完成所有测试并部署上线"}]\'></timeline-comp><p>渲染演示完成！</p></div>',
      ];
      // const htmlStreams = [
      //   '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary"',
      //   '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary">按钮</button-comp><timeline-comp',
      //   '<div><h2>渐进式渲染演示</h2><p>这是一个基于Lit和morphdom的渐进式渲染方案</p><button-comp type="primary">按钮</button-comp><timeline-comp list=\'[{"title": "项目启动"}]\'',
      // ];

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', () => {
        const startBtn = document.getElementById('startBtn');
        const resetBtn = document.getElementById('resetBtn');
        const currentStream = document.getElementById('currentStream');
        const canvas = document.getElementById('canvas');

        let streamIndex = 0;
        let streamInterval;

        // 开始流式渲染
        startBtn.addEventListener('click', () => {
          if (streamInterval) clearInterval(streamInterval);

          streamIndex = 0;
          streamInterval = setInterval(() => {
            if (streamIndex >= htmlStreams.length) {
              clearInterval(streamInterval);
              startBtn.textContent = '渲染完成';
              startBtn.disabled = true;
              return;
            }

            const htmlStream = htmlStreams[streamIndex];
            renderEngine.processHTMLStream(htmlStream);
            currentStream.textContent = htmlStream;
            streamIndex++;
          }, 1000);

          startBtn.textContent = '渲染中...';
          startBtn.disabled = true;
        });

        // 重置演示
        resetBtn.addEventListener('click', () => {
          if (streamInterval) clearInterval(streamInterval);
          canvas.innerHTML = '';
          currentStream.textContent = '';
          startBtn.textContent = '开始流式渲染';
          startBtn.disabled = false;
        });
      });
    </script>

    <!-- 添加一个非模块版本的回退脚本，以防浏览器不支持ES模块 -->
    <script nomodule>
      document.body.innerHTML =
        '<div style="padding: 20px; color: red; text-align: center;">您的浏览器不支持ES模块，请使用现代浏览器访问此页面。</div>';
    </script>
  </body>
</html>
