/**
 * 预处理HTML流，修复自定义组件属性中截断的JSON，保证标签闭合等
 * @param {string} htmlStream
 * @param {Object} options
 * @returns {string}
 */
function preprocessHtmlStream(htmlStream, options = {}) {
  const config = {
    customTags: ['button-comp', 'timeline-comp', 'chart-comp', 'data-table', 'custom-form'],
    booleanAttributes: {
      'button-comp': ['disabled'],
    },
    maxAttrLength: 300, // 属性值最大截取长度，防止过长影响性能
    ...options,
  };

  if (!htmlStream || htmlStream.trim() === '') {
    return htmlStream;
  }

  // 第一步：修复属性中的截断JSON
  const fixedJsonHtml = fixJsonAttributesInHtml(htmlStream, config);
  console.log('fixedJsonHtml:', fixedJsonHtml);
  // 第二步：尝试使用DOMParser解析修复后的HTML，处理自定义组件属性和闭合标签
  try {
    return processWithDOMParser(fixedJsonHtml, config);
  } catch (e) {
    console.warn('DOMParser 处理失败，回退正则处理', e);
    return processWithRegex(fixedJsonHtml, config);
  }
}

/**
 * 修复HTML流中末尾截断的JSON属性
 * @param {string} htmlStream - HTML流内容
 * @param {Object} config - 配置项
 * @returns {string} - 修复后的HTML
 */
function fixJsonAttributesInHtml(htmlStream, config = {}) {
  if (!htmlStream || htmlStream.trim() === '') {
    return htmlStream;
  }

  const mergedConfig = config;

  // 检查HTML是否在末尾有未闭合的标签
  const lastLtIndex = htmlStream.lastIndexOf('<');
  if (lastLtIndex === -1) {
    return htmlStream;
  }

  // 提取最后一个可能未闭合的标签
  const lastTagFragment = htmlStream.substring(lastLtIndex);

  // 检查这个标签是否是我们关心的自定义标签
  let matchedTag = null;
  for (const tag of mergedConfig.customTags) {
    if (lastTagFragment.startsWith(`<${tag}`) || lastTagFragment.startsWith(`</${tag}`)) {
      matchedTag = tag;
      break;
    }
  }

  // 如果不是自定义标签，或者是闭合标签，直接返回
  if (!matchedTag || lastTagFragment.startsWith(`</${matchedTag}`)) {
    return htmlStream;
  }

  // 找到最后一个属性（支持单/双引号，属性值可跨行）
  const attrRegex = /(\S+)=(['"])([\s\S]*?)(?:\2|$)/g;
  let lastAttrMatch = null;
  let matches;

  // 从最后一个标签开始位置查找所有属性
  const tagContent = htmlStream.substring(lastLtIndex);
  while ((matches = attrRegex.exec(tagContent)) !== null) {
    lastAttrMatch = matches;
  }

  // 如果没找到属性，直接返回
  if (!lastAttrMatch) {
    return htmlStream;
  }

  // 解析属性信息
  const [fullMatch, attrName, quoteChar, attrValue] = lastAttrMatch;

  // 计算属性值在原始HTML中的位置
  const attrValueStartIndex = lastLtIndex + lastAttrMatch.index + attrName.length + 2; // +2 for =' or ="

  // 检查属性值是否完整（有闭合引号）
  const hasClosingQuote = fullMatch.endsWith(quoteChar);

  // 如果属性值已经有闭合引号，不需要修复
  if (hasClosingQuote) {
    return htmlStream;
  }

  // 提取属性值（可能不完整）
  const valueFragment = htmlStream.substring(attrValueStartIndex);

  // 检查是否是JSON格式（以 [ 或 { 开头）
  const trimmedValue = valueFragment.trim();
  if (!(trimmedValue.startsWith('[') || trimmedValue.startsWith('{'))) {
    // 不是JSON格式，让DOMParser处理，直接返回
    return htmlStream;
  }

  // 如果是JSON且没有闭合引号，说明JSON属性被截断，尝试修复
  try {
    let repairedJson;
    if (typeof window !== 'undefined' && window.JSONRepair && typeof window.JSONRepair.jsonrepair === 'function') {
      repairedJson = window.JSONRepair.jsonrepair(valueFragment);
    } else {
      repairedJson = basicJsonRepair(valueFragment);
    }

    // 转义双引号，避免HTML属性解析问题
    const escapedJson = quoteChar === '"' ? repairedJson.replace(/"/g, '&quot;') : repairedJson;

    // 构建修复后的HTML
    return htmlStream.substring(0, attrValueStartIndex) + escapedJson + quoteChar;
  } catch (e) {
    console.warn('JSON修复失败:', e);
    // 修复失败，返回原始HTML
    return htmlStream;
  }
}

/**
 * 基本的JSON修复函数
 * @param {string} jsonStr - 可能被截断或无效的JSON字符串
 * @returns {string} - 修复后的JSON字符串
 */
function basicJsonRepair(jsonStr) {
  if (!jsonStr) {
    return '{}';
  }

  const isArray = jsonStr.trim().startsWith('[');
  const isObject = jsonStr.trim().startsWith('{');

  if (!isArray && !isObject) {
    return jsonStr;
  }

  let openBraces = 0;
  let openBrackets = 0;

  for (let i = 0; i < jsonStr.length; i++) {
    const char = jsonStr[i];
    if (char === '{') {
      openBraces++;
    } else if (char === '}') {
      openBraces--;
    } else if (char === '[') {
      openBrackets++;
    } else if (char === ']') {
      openBrackets--;
    }
  }

  let repairedJson = jsonStr;

  for (let i = 0; i < openBraces; i++) {
    repairedJson += '}';
  }

  for (let i = 0; i < openBrackets; i++) {
    repairedJson += ']';
  }

  if (repairedJson.trim() === '{') {
    repairedJson = '{}';
  }
  if (repairedJson.trim() === '[') {
    repairedJson = '[]';
  }

  try {
    JSON.parse(repairedJson);
    return repairedJson;
  } catch (e) {
    return isArray ? '[]' : '{}';
  }
}

/**
 * 使用DOMParser处理HTML流，自动检测和修复JSON属性
 * @param {string} htmlStream - HTML流字符串
 * @param {Object} config - 配置选项
 * @returns {string} - 处理后的HTML
 */
function processWithDOMParser(htmlStream, config) {
  const parser = new DOMParser();
  const wrappedHtml = `<div id="stream-wrapper">${htmlStream}</div>`;
  const doc = parser.parseFromString(wrappedHtml, 'text/html');
  const wrapper = doc.getElementById('stream-wrapper');
  if (!wrapper) {
    throw new Error('未找到包装元素');
  }

  for (const tagName of config.customTags) {
    const elements = wrapper.querySelectorAll(tagName);
    const booleanAttrs = config.booleanAttributes?.[tagName] || [];

    elements.forEach(element => {
      for (const attr of element.attributes) {
        const attrName = attr.name;
        const attrValue = attr.value;

        if (booleanAttrs.includes(attrName)) {
          continue;
        }

        if (attrValue.trim().startsWith('[') || attrValue.trim().startsWith('{')) {
          try {
            JSON.parse(attrValue);
          } catch (e) {
            try {
              let repairedJson;
              if (
                typeof window !== 'undefined' &&
                window.JSONRepair &&
                typeof window.JSONRepair.jsonrepair === 'function'
              ) {
                repairedJson = window.JSONRepair.jsonrepair(attrValue);
              } else {
                repairedJson = basicJsonRepair(attrValue);
              }

              if (repairedJson !== attrValue) {
                element.setAttribute(attrName, repairedJson);
              }
            } catch (repairError) {
              console.warn(`修复属性 ${attrName} 的JSON值失败:`, repairError);
            }
          }
        }
      }

      booleanAttrs.forEach(attrName => {
        if (element.hasAttribute(attrName) && element.getAttribute(attrName) === '') {
          element.setAttribute(attrName, '');
        }
      });

      if (!element.innerHTML && !element.childNodes.length) {
        element.innerHTML = '';
      }
    });
  }

  return wrapper.innerHTML;
}

/**
 * 失败回退：使用正则表达式处理标签闭合和布尔属性
 * @param {string} htmlStream
 * @param {Object} config
 * @returns {string}
 */
function processWithRegex(htmlStream, config) {
  let processedHtml = htmlStream;

  for (const tagName of config.customTags) {
    const booleanAttrs = config.booleanAttributes?.[tagName] || [];

    // 处理布尔属性
    for (const attrName of booleanAttrs) {
      const boolAttrRegex = new RegExp(`<${tagName}([^>]*)\\s+${attrName}(?=[\\s>])`, 'g');
      processedHtml = processedHtml.replace(boolAttrRegex, (match, prefix) => `<${tagName}${prefix} ${attrName}=""`);
    }

    // 添加缺失的闭合标签
    const tagRegex = new RegExp(`<${tagName}([^>]*)>([^<]*)?(?:</${tagName}>)?`, 'g');
    processedHtml = processedHtml.replace(tagRegex, (match, attrs, content) => {
      if (!match.includes(`</${tagName}>`)) {
        if (content) {
          return `<${tagName}${attrs}>${content}</${tagName}>`;
        }
        return `<${tagName}${attrs}></${tagName}>`;
      }
      return match;
    });
  }

  return processedHtml;
}
