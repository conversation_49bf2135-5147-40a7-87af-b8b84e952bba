import { defineConfig } from '@rslib/core';

export default defineConfig({
  // 源码入口配置
  source: {
    entry: {
      index: './src/index.js', // 主入口文件
    },
  },

  // 库构建配置
  lib: [
    {
      format: 'umd', // 输出格式
      autoExternal: false,
    },
    {
      format: 'esm',
      autoExternal: true,
    },
  ],
  // 4️⃣ 显式配置 Tree Shaking 和压缩
  output: {
    target: 'web',
    minify: true,
    sourceMap: false, // 生产环境关闭 SourceMap 减小体
  },
});
