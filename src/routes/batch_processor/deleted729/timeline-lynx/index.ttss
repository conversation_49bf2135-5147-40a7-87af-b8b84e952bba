.timeline-container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.timeline-header {
  margin-bottom: 60rpx;
  text-align: center;
}

.timeline-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
  text-align: center;
}

.timeline-content {
  position: relative;
  padding-left: 40rpx;
  height: 100vh;
}

.timeline-item {
  position: relative;
  margin-bottom: 60rpx;
  display: flex;
  align-items: flex-start;
}

.timeline-date-section {
  position: relative;
  margin-right: 40rpx;
  min-width: 200rpx;
}

.timeline-date {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
  line-height: 1.4;
}

.timeline-dot {
  position: absolute;
  right: -20rpx;
  top: 8rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3498db;
  border: 4rpx solid #ffffff;
  box-shadow: 0 0 0 4rpx #3498db;
}

.timeline-line {
  position: absolute;
  left: 219rpx;
  top: 40rpx;
  width: 2rpx;
  height: 80rpx;
  background-color: #bdc3c7;
}

.timeline-content-section {
  flex: 1;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-left: 20rpx;
}

.timeline-event-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.timeline-description {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
  margin-bottom: 16rpx;
}

/* 媒体内容样式 */
.timeline-media {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-top: 16rpx;
}

.timeline-media-item {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.timeline-media-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

/* 小屏幕适配样式 */
.timeline-container-small {
  padding: 20rpx;
}

.timeline-title-small {
  font-size: 42rpx;
}

.timeline-date-section-small {
  min-width: 160rpx;
}

.timeline-content-section-small {
  padding: 20rpx;
}

.timeline-event-title-small {
  font-size: 28rpx;
}

.timeline-description-small {
  font-size: 24rpx;
}
