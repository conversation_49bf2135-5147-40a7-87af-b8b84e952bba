/**
 * 生成时间轴组件 demo 的 FILES 格式内容
 * 可以直接复制到 batch processor 页面进行上传
 */

// 基础示例的 FILES 格式内容
export const BASIC_DEMO_FILES = `
FILES

FILE path="index.ttml"
<scroll-view class="card-container" scroll-y="true">
  <view class="demo-container">
    <view class="demo-header">
      <text class="demo-title">基础时间轴示例</text>
    </view>

    <view class="timeline-container">
      <view class="timeline-header">
        <text class="timeline-title">发展历程</text>
      </view>

      <view class="timeline-content">
        <view class="timeline-item" tt:for="{{demoTimelineData}}" tt:key="{{item?.id}}" bindtap="onTimelineItemTap" data-index="{{index}}">
        <view class="timeline-date-section">
          <text class="timeline-date">{{item?.date}}</text>
          <view class="timeline-dot"></view>
        </view>

        <view class="timeline-line" tt:if="{{!item?.isLast}}"></view>

        <view class="timeline-content-section">
          <text class="timeline-event-title">{{item?.title}}</text>
          <text class="timeline-description">{{item?.description}}</text>
        </view>
      </view>
    </view>
  </view>
</scroll-view>
FILE

FILE path="index.ttss"
.card-container {
  height: 800rpx;
  max-height: 800rpx;
  width: 100%;
}

.demo-container {
  padding: 20rpx;
  background-color: #ffffff;
}

.demo-header {
  margin-bottom: 40rpx;
  text-align: center;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
}

.demo-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
}

.timeline-container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.timeline-header {
  margin-bottom: 60rpx;
  text-align: center;
}

.timeline-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
  text-align: center;
}

.timeline-content {
  position: relative;
  padding-left: 40rpx;
}

.timeline-item {
  position: relative;
  margin-bottom: 60rpx;
  display: flex;
  align-items: flex-start;
}

.timeline-date-section {
  position: relative;
  margin-right: 40rpx;
  min-width: 200rpx;
}

.timeline-date {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
  line-height: 1.4;
}

.timeline-dot {
  position: absolute;
  right: -20rpx;
  top: 8rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3498db;
  border: 4rpx solid #ffffff;
}

.timeline-line {
  position: absolute;
  left: 219rpx;
  top: 40rpx;
  width: 2rpx;
  height: 80rpx;
  background-color: #bdc3c7;
}

.timeline-content-section {
  flex: 1;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e8e8e8;
  margin-left: 20rpx;
}

.timeline-event-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.timeline-description {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
}
FILE

FILE path="index.ts"
Card({
  data: {
    // 使用 list 属性，兼容 timeline-comp 接口
    list: '[
      {
        "date": "2022年11月8日",
        "title": "拜登预防性赦免",
        "content": "即任前赦免福奇、米利等关键人物及全家物及全物及全"
      },
      {
        "date": "2022年11月8日",
        "title": "特朗普解雇行为",
        "content": "上台台后解雇超10000名拜登政府人员"
      },
      {
        "date": "2022年11月8日",
        "title": "特朗普大规模赦免",
        "content": "即任前赦免福奇、米利等关键人物及全家米利等关键人物及全家"
      },
      {
        "date": "2022年11月8日",
        "title": "19州检察长起诉",
        "content": "民主党人联合起诉特朗普行政令违宪"
      }
    ]',
    demoTimelineData: [
      {
        id: 1,
        date: "2022年11月8日",
        title: "拜登预防性赦免",
        description: "即任前赦免福奇、米利等关键人物及全家物及全物及全",
        isLast: false,
        animated: false
      },
      {
        id: 2,
        date: "2022年11月8日", 
        title: "特朗普解雇行为",
        description: "上台台后解雇超10000名拜登政府人员",
        isLast: false,
        animated: false
      },
      {
        id: 3,
        date: "2022年11月8日",
        title: "特朗普大规模赦免", 
        description: "即任前赦免福奇、米利等关键人物及全家米利等关键人物及全家",
        isLast: false,
        animated: false
      },
      {
        id: 4,
        date: "2022年11月8日",
        title: "19州检察长起诉",
        description: "民主党人联合起诉特朗普行政令违宪",
        isLast: true,
        animated: false
      }
    ],
    showAnimation: true
  },

  onLoad() {
    console.log('Timeline demo loaded');
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  animateTimeline() {
    const items = this.data.demoTimelineData || [];
    items.forEach((item, index) => {
      setTimeout(() => {
        this.setData({
          ['demoTimelineData[' + index + '].animated']: true
        });
      }, index * 200);
    });
  },

  onTimelineItemTap(event) {
    const { index } = event.currentTarget.dataset;
    const item = this.data.demoTimelineData[index];
    
    if (item) {
      lynx.showModal({
        title: item.title,
        content: item.description,
        confirmText: '确定',
        success: function(res) {
          if (res.confirm) {
            console.log('用户点击确定');
          }
        }
      });
    }
  }
});
FILE

FILE path="index.json"
{
  "component": true,
  "usingComponents": {}
}
FILE

FILES
`;

// 高级示例的 FILES 格式内容
export const ADVANCED_DEMO_FILES = `
FILES

FILE path="index.ttml"
<view class="demo-container">
  <view class="demo-header">
    <text class="demo-title">高级时间轴示例</text>
    <text class="demo-subtitle">支持类型分类和自定义颜色</text>
  </view>

  <view class="timeline-container">
    <view class="timeline-header">
      <text class="timeline-title">产品发展历程</text>
    </view>

    <scroll-view class="timeline-content" scroll-y="true">
      <view class="timeline-item" tt:for="{{advancedTimelineData}}" tt:key="{{item.id}}" bindtap="onTimelineItemTap" data-index="{{index}}">
        <view class="timeline-date-section">
          <text class="timeline-date">{{item.date}}</text>
          <view class="timeline-dot timeline-dot-{{item.type}}"></view>
        </view>

        <view class="timeline-line" tt:if="{{!item.isLast}}"></view>

        <view class="timeline-content-section">
          <text class="timeline-event-title">{{item.title}}</text>
          <text class="timeline-description">{{item.description}}</text>
          <view class="timeline-type-badge timeline-type-{{item.type}}">
            <text class="timeline-type-text">{{item.type}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
FILE

FILE path="index.ttss"
.demo-container {
  padding: 20rpx;
  background-color: #ffffff;
}

.demo-header {
  margin-bottom: 40rpx;
  text-align: center;
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16rpx;
}

.demo-title {
  font-size: 40rpx;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
  margin-bottom: 10rpx;
}

.demo-subtitle {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
}

.timeline-container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.timeline-header {
  margin-bottom: 60rpx;
  text-align: center;
}

.timeline-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
  text-align: center;
}

.timeline-content {
  position: relative;
  padding-left: 40rpx;
  height: 100vh;
}

.timeline-item {
  position: relative;
  margin-bottom: 60rpx;
  display: flex;
  align-items: flex-start;
}

.timeline-date-section {
  position: relative;
  margin-right: 40rpx;
  min-width: 200rpx;
}

.timeline-date {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
  line-height: 1.4;
}

.timeline-dot {
  position: absolute;
  right: -20rpx;
  top: 8rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
}

.timeline-dot-milestone {
  background-color: #3498db;
  box-shadow: 0 0 0 4rpx #3498db;
}

.timeline-dot-design {
  background-color: #9b59b6;
  box-shadow: 0 0 0 4rpx #9b59b6;
}

.timeline-dot-release {
  background-color: #f39c12;
  box-shadow: 0 0 0 4rpx #f39c12;
}

.timeline-dot-launch {
  background-color: #27ae60;
  box-shadow: 0 0 0 4rpx #27ae60;
}

.timeline-dot-summary {
  background-color: #e74c3c;
  box-shadow: 0 0 0 4rpx #e74c3c;
}

.timeline-line {
  position: absolute;
  left: 219rpx;
  top: 40rpx;
  width: 2rpx;
  height: 80rpx;
  background-color: #bdc3c7;
}

.timeline-content-section {
  flex: 1;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-left: 20rpx;
  position: relative;
}

.timeline-event-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.timeline-description {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
  margin-bottom: 20rpx;
}

.timeline-type-badge {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
}

.timeline-type-milestone {
  background-color: rgba(52, 152, 219, 0.1);
}

.timeline-type-design {
  background-color: rgba(155, 89, 182, 0.1);
}

.timeline-type-release {
  background-color: rgba(243, 156, 18, 0.1);
}

.timeline-type-launch {
  background-color: rgba(39, 174, 96, 0.1);
}

.timeline-type-summary {
  background-color: rgba(231, 76, 60, 0.1);
}

.timeline-type-text {
  font-size: 20rpx;
  font-weight: 500;
  color: #666666;
}
FILE

FILE path="index.ts"
Page({
  data: {
    advancedTimelineData: [
      {
        id: 1,
        date: "2024年1月15日",
        title: "项目启动",
        description: "正式启动新产品开发项目，组建核心团队",
        isLast: false,
        animated: false,
        type: "milestone"
      },
      {
        id: 2,
        date: "2024年3月20日", 
        title: "原型设计完成",
        description: "完成产品原型设计，通过内部评审",
        isLast: false,
        animated: false,
        type: "design"
      },
      {
        id: 3,
        date: "2024年6月10日",
        title: "Beta版本发布", 
        description: "发布Beta测试版本，邀请用户参与测试",
        isLast: false,
        animated: false,
        type: "release"
      },
      {
        id: 4,
        date: "2024年8月25日",
        title: "正式版上线",
        description: "产品正式版本上线，开始商业化运营",
        isLast: false,
        animated: false,
        type: "launch"
      },
      {
        id: 5,
        date: "2024年12月31日",
        title: "年度总结",
        description: "回顾全年成果，制定下一年发展计划",
        isLast: true,
        animated: false,
        type: "summary"
      }
    ],
    showAnimation: true,
    primaryColor: "#e74c3c"
  },

  onLoad() {
    console.log('Advanced timeline demo loaded');
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  animateTimeline() {
    const items = this.data.advancedTimelineData || [];
    items.forEach((item, index) => {
      setTimeout(() => {
        this.setData({
          ['advancedTimelineData[' + index + '].animated']: true
        });
      }, index * 300);
    });
  },

  onTimelineItemTap(event) {
    const { index } = event.currentTarget.dataset;
    const item = this.data.advancedTimelineData[index];
    
    if (item) {
      const typeNames = {
        milestone: "里程碑",
        design: "设计阶段", 
        release: "版本发布",
        launch: "产品上线",
        summary: "总结回顾"
      };
      
      lynx.showModal({
        title: \`\${typeNames[item.type] || '事件'}: \${item.title}\`,
        content: \`日期: \${item.date}\\n\\n\${item.description}\`,
        confirmText: '确定',
        success: res => {
          if (res.confirm) {
            console.log('用户查看了事件详情:', item.title);
          }
        }
      });
    }
  },

  onShareTimeline() {
    lynx.showShareMenu({
      title: '产品发展历程',
      path: '/pages/timeline-advanced/index',
      success: res => {
        console.log('分享成功');
      },
      fail: err => {
        console.error('分享失败', err);
      }
    });
  }
});
FILE

FILE path="index.json"
{
  "usingComponents": {},
  "navigationBarTitleText": "时间轴组件 - 高级示例",
  "navigationBarBackgroundColor": "#ffffff",
  "navigationBarTextStyle": "black",
  "backgroundColor": "#f8f9fa",
  "backgroundTextStyle": "light",
  "enablePullDownRefresh": false,
  "onReachBottomDistance": 50
}
FILE

FILES
`;

// 导出函数，用于在控制台输出内容
export function printBasicDemo() {
  console.log('='.repeat(80));
  console.log('📱 基础时间轴示例 - FILES 格式');
  console.log('='.repeat(80));
  console.log(BASIC_DEMO_FILES);
  console.log('='.repeat(80));
}

export function printAdvancedDemo() {
  console.log('='.repeat(80));
  console.log('🚀 高级时间轴示例 - FILES 格式');
  console.log('='.repeat(80));
  console.log(ADVANCED_DEMO_FILES);
  console.log('='.repeat(80));
}

// 如果直接运行此文件，则输出所有示例
if (require.main === module) {
  printBasicDemo();
  printAdvancedDemo();
}
