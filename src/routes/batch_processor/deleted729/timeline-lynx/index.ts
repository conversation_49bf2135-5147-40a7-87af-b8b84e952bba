Page({
  data: {
    timelineData: [],
    showAnimation: true,
    primaryColor: '#3498db',
  },

  properties: {
    timelineData: {
      type: Array,
      value: [],
    },
    showAnimation: {
      type: Boolean,
      value: true,
    },
    primaryColor: {
      type: String,
      value: '#3498db',
    },
  },

  onLoad() {
    console.log('Timeline component loaded');
  },

  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  animateTimeline() {
    // 逐个显示时间线项目的动画效果
    const items = this.data.timelineData || [];
    items.forEach((item, index) => {
      setTimeout(() => {
        this.setData({
          ['timelineData[' + index + '].animated']: true,
        });
      }, index * 200);
    });
  },

  onTimelineItemTap(event) {
    const { index } = event.currentTarget.dataset;
    const item = this.data.timelineData[index];

    if (item) {
      lynx.showModal({
        title: item.title,
        content: item.description,
        confirmText: '确定',
        success: res => {
          if (res.confirm) {
            console.log('用户点击确定');
          }
        },
      });
    }
  },

  onShareTimeline() {
    lynx.showShareMenu({
      title: '发展历程',
      path: '/pages/timeline/index',
      success: res => {
        console.log('分享成功');
      },
      fail: err => {
        console.error('分享失败', err);
      },
    });
  },
});
