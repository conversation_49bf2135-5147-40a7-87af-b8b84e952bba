Page({
  data: {
    timelineData: [
      {
        id: 1,
        date: "2022年11月8日",
        title: "拜登预防性赦免",
        description: "即任前赦免福奇、米利等关键人物及全家物及全物及全",
        isLast: false,
        animated: false
      },
      {
        id: 2,
        date: "2022年11月8日", 
        title: "特朗普解雇行为",
        description: "上台台后解雇超10000名拜登政府人员",
        isLast: false,
        animated: false
      },
      {
        id: 3,
        date: "2022年11月8日",
        title: "特朗普大规模赦免", 
        description: "即任前赦免福奇、米利等关键人物及全家米利等关键人物及全家",
        isLast: false,
        animated: false
      },
      {
        id: 4,
        date: "2022年11月8日",
        title: "19州检察长起诉",
        description: "民主党人联合起诉特朗普行政令违宪",
        isLast: true,
        animated: false
      }
    ],
    showAnimation: true,
    primaryColor: "#3498db"
  },

  onLoad() {
    console.log('Timeline component loaded');
  },

  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  animateTimeline() {
    // 逐个显示时间线项目的动画效果
    const items = this.data.timelineData || [];
    items.forEach((item, index) => {
      setTimeout(() => {
        this.setData({
          ['timelineData[' + index + '].animated']: true
        });
      }, index * 200);
    });
  },

  onTimelineItemTap(event) {
    const { index } = event.currentTarget.dataset;
    const item = this.data.timelineData[index];
    
    if (item) {
      lynx.showModal({
        title: item.title,
        content: item.description,
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            console.log('用户点击确定');
          }
        }
      });
    }
  },

  onShareTimeline() {
    lynx.showShareMenu({
      title: '发展历程',
      path: '/pages/timeline/index',
      success: (res) => {
        console.log('分享成功');
      },
      fail: (err) => {
        console.error('分享失败', err);
      }
    });
  }
});
