Card({
  data: {
    timelineData: [],
    showAnimation: true,
    primaryColor: '#3498db',
  },

  properties: {
    // 兼容 timeline-comp 接口
    list: {
      type: String,
      value: '[]',
      observer: 'parseListData',
    },
    // 原有属性保持不变
    timelineData: {
      type: Array,
      value: [],
    },
    showAnimation: {
      type: Boolean,
      value: true,
    },
    primaryColor: {
      type: String,
      value: '#3498db',
    },
  },

  onLoad() {
    console.log('Timeline component loaded');
  },

  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  // 解析 list 属性数据，兼容 timeline-comp 接口
  parseListData() {
    try {
      const listStr = this.data.list || this.properties.list.value;
      let parsedData = [];

      if (typeof listStr === 'string' && listStr.trim()) {
        parsedData = JSON.parse(listStr);
      }

      // 转换为内部数据格式
      const timelineData = parsedData.map(function (item, index) {
        return {
          id: item.id || index + 1,
          date: item.date || '',
          title: item.title || '',
          description: item.content || item.description || '',
          media: item.media || [],
          isLast: index === parsedData.length - 1,
          animated: false,
        };
      });

      this.setData({
        timelineData: timelineData,
      });
    } catch (error) {
      console.error('Timeline list parsing error:', error);
      this.setData({
        timelineData: [],
      });
    }
  },

  animateTimeline() {
    // 逐个显示时间线项目的动画效果
    const items = this.data.timelineData || [];
    items.forEach((item, index) => {
      setTimeout(() => {
        this.setData({
          ['timelineData[' + index + '].animated']: true,
        });
      }, index * 200);
    });
  },

  onTimelineItemTap(event) {
    const { index } = event.currentTarget.dataset;
    const item = this.data.timelineData[index];

    if (item) {
      lynx.showModal({
        title: item.title,
        content: item.description,
        confirmText: '确定',
        success: res => {
          if (res.confirm) {
            console.log('用户点击确定');
          }
        },
      });
    }
  },

  onShareTimeline() {
    lynx.showShareMenu({
      title: '发展历程',
      path: '/pages/timeline/index',
      success: res => {
        console.log('分享成功');
      },
      fail: err => {
        console.error('分享失败', err);
      },
    });
  },
});
