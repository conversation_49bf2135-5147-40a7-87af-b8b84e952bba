/* 卡片容器 */
.timeline-card-container {
  height: 800rpx;
  max-height: 800rpx;
  width: 100%;
}

.timeline-container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100%;
}

.timeline-header {
  margin-bottom: 60rpx;
  text-align: center;
}

.timeline-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
  text-align: center;
}

.timeline-content {
  position: relative;
  padding-left: 40rpx;
}

/* 时间轴项目 */
.timeline-item {
  position: relative;
  margin-bottom: 60rpx;
  display: flex;
  align-items: flex-start;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s ease;
}

.timeline-item-animated {
  opacity: 1;
  transform: translateY(0);
}

/* 日期区域 */
.timeline-date-section {
  position: relative;
  margin-right: 40rpx;
  min-width: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.timeline-date {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
  line-height: 1.4;
  text-align: center;
  margin-bottom: 10rpx;
}

.timeline-dot {
  position: relative;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 4rpx solid #ffffff;
  margin-bottom: 10rpx;
}

.timeline-status-indicator {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
}

.timeline-status-indicator.completed {
  background-color: #27ae60;
}

.timeline-status-indicator.ongoing {
  background-color: #f39c12;
}

.timeline-status-indicator.planned {
  background-color: #95a5a6;
}

/* 连接线 */
.timeline-line {
  position: absolute;
  left: 219rpx;
  top: 40rpx;
  width: 2rpx;
  height: 80rpx;
  background-color: #bdc3c7;
}

/* 内容区域 */
.timeline-content-section {
  flex: 1;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  border: 1rpx solid #e8e8e8;
  margin-left: 20rpx;
  position: relative;
}

.timeline-header-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.timeline-event-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.3;
  flex: 1;
}

.timeline-type-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  margin-left: 16rpx;
}

.timeline-type-badge.milestone {
  background-color: rgba(52, 152, 219, 0.1);
}

.timeline-type-badge.release {
  background-color: rgba(39, 174, 96, 0.1);
}

.timeline-type-badge.achievement {
  background-color: rgba(243, 156, 18, 0.1);
}

.timeline-type-text {
  font-size: 20rpx;
  font-weight: 500;
  color: #666666;
}

.timeline-description {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
  margin-bottom: 20rpx;
}

/* 媒体内容 */
.timeline-media-container {
  margin-top: 20rpx;
}

.timeline-media-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.timeline-media-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
}

.timeline-media-thumbnail {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.timeline-media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

.timeline-media-play-icon {
  color: #ffffff;
  font-size: 24rpx;
  text-align: center;
}

.timeline-media-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  color: #ffffff;
  font-size: 18rpx;
  padding: 8rpx;
  text-align: center;
}

/* 状态区域 */
.timeline-status-section {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #ecf0f1;
}

.timeline-status-text {
  font-size: 24rpx;
  font-weight: 500;
}

.timeline-status-text.ongoing {
  color: #f39c12;
}

.timeline-status-text.planned {
  color: #95a5a6;
}

/* 空状态 */
.timeline-empty {
  text-align: center;
  padding: 100rpx 40rpx;
}

.timeline-empty-text {
  font-size: 28rpx;
  color: #95a5a6;
  text-align: center;
}

/* 主题样式 */
.timeline-theme-minimal .timeline-content-section {
  border: none;
  background-color: transparent;
  padding: 20rpx 0;
}

.timeline-theme-colorful .timeline-item.type-milestone .timeline-dot {
  background-color: #3498db;
}

.timeline-theme-colorful .timeline-item.type-release .timeline-dot {
  background-color: #27ae60;
}

.timeline-theme-colorful .timeline-item.type-achievement .timeline-dot {
  background-color: #f39c12;
}

/* 响应式设计 */
@media (max-width: 480rpx) {
  .timeline-container {
    padding: 20rpx;
  }
  
  .timeline-title {
    font-size: 42rpx;
  }
  
  .timeline-date-section {
    min-width: 160rpx;
  }
  
  .timeline-content-section {
    padding: 20rpx;
  }
  
  .timeline-event-title {
    font-size: 28rpx;
  }
  
  .timeline-description {
    font-size: 24rpx;
  }
  
  .timeline-media-item {
    width: 100rpx;
    height: 100rpx;
  }
}
