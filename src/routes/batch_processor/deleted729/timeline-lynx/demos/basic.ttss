.demo-container {
  padding: 20rpx;
  background-color: #ffffff;
}

.demo-header {
  margin-bottom: 40rpx;
  text-align: center;
  padding: 20rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
}

.demo-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333333;
  text-align: center;
}

/* 引入组件样式 */
.timeline-container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.timeline-header {
  margin-bottom: 60rpx;
  text-align: center;
}

.timeline-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
  text-align: center;
}

.timeline-content {
  position: relative;
  padding-left: 40rpx;
  height: 100vh;
}

.timeline-item {
  position: relative;
  margin-bottom: 60rpx;
  display: flex;
  align-items: flex-start;
}

.timeline-date-section {
  position: relative;
  margin-right: 40rpx;
  min-width: 200rpx;
}

.timeline-date {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
  line-height: 1.4;
}

.timeline-dot {
  position: absolute;
  right: -20rpx;
  top: 8rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3498db;
  border: 4rpx solid #ffffff;
  box-shadow: 0 0 0 4rpx #3498db;
}

.timeline-line {
  position: absolute;
  left: 219rpx;
  top: 40rpx;
  width: 2rpx;
  height: 80rpx;
  background-color: #bdc3c7;
}

.timeline-content-section {
  flex: 1;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-left: 20rpx;
}

.timeline-event-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.timeline-description {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
}
