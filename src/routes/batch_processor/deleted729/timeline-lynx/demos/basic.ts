Page({
  data: {
    demoTimelineData: [
      {
        id: 1,
        date: '2022年11月8日',
        title: '拜登预防性赦免',
        description: '即任前赦免福奇、米利等关键人物及全家物及全物及全',
        isLast: false,
        animated: false,
      },
      {
        id: 2,
        date: '2022年11月8日',
        title: '特朗普解雇行为',
        description: '上台台后解雇超10000名拜登政府人员',
        isLast: false,
        animated: false,
      },
      {
        id: 3,
        date: '2022年11月8日',
        title: '特朗普大规模赦免',
        description: '即任前赦免福奇、米利等关键人物及全家米利等关键人物及全家',
        isLast: false,
        animated: false,
      },
      {
        id: 4,
        date: '2022年11月8日',
        title: '19州检察长起诉',
        description: '民主党人联合起诉特朗普行政令违宪',
        isLast: true,
        animated: false,
      },
    ],
    showAnimation: true,
  },

  onLoad() {
    console.log('Timeline demo loaded');
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  animateTimeline() {
    // 逐个显示时间线项目的动画效果
    const items = this.data.demoTimelineData || [];
    items.forEach((item, index) => {
      setTimeout(() => {
        this.setData({
          ['demoTimelineData[' + index + '].animated']: true,
        });
      }, index * 200);
    });
  },

  onTimelineItemTap(event) {
    const { index } = event.currentTarget.dataset;
    const item = this.data.demoTimelineData[index];

    if (item) {
      lynx.showModal({
        title: item.title,
        content: item.description,
        confirmText: '确定',
        success: function (res) {
          if (res.confirm) {
            console.log('用户点击确定');
          }
        },
      });
    }
  },
});
