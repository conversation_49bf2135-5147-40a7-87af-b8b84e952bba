<view class="demo-container">
  <view class="demo-header">
    <text class="demo-title">基础时间轴示例</text>
  </view>

  <view class="timeline-container">
    <view class="timeline-header">
      <text class="timeline-title">发展历程</text>
    </view>

    <scroll-view class="timeline-content" scroll-y="true">
      <view class="timeline-item" tt:for="{{demoTimelineData}}" tt:key="{{item?.id}}" bindtap="onTimelineItemTap" data-index="{{index}}">
        <view class="timeline-date-section">
          <text class="timeline-date">{{item?.date}}</text>
          <view class="timeline-dot"></view>
        </view>

        <view class="timeline-line" tt:if="{{!item?.isLast}}"></view>

        <view class="timeline-content-section">
          <text class="timeline-event-title">{{item?.title}}</text>
          <text class="timeline-description">{{item?.description}}</text>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
