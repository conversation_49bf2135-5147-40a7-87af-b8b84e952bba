Card({
  data: {
    advancedTimelineData: [
      {
        id: 1,
        date: '2024年1月15日',
        title: '项目启动',
        description: '正式启动新产品开发项目，组建核心团队',
        isLast: false,
        animated: false,
        type: 'milestone',
      },
      {
        id: 2,
        date: '2024年3月20日',
        title: '原型设计完成',
        description: '完成产品原型设计，通过内部评审',
        isLast: false,
        animated: false,
        type: 'design',
      },
      {
        id: 3,
        date: '2024年6月10日',
        title: 'Beta版本发布',
        description: '发布Beta测试版本，邀请用户参与测试',
        isLast: false,
        animated: false,
        type: 'release',
      },
      {
        id: 4,
        date: '2024年8月25日',
        title: '正式版上线',
        description: '产品正式版本上线，开始商业化运营',
        isLast: false,
        animated: false,
        type: 'launch',
      },
      {
        id: 5,
        date: '2024年12月31日',
        title: '年度总结',
        description: '回顾全年成果，制定下一年发展计划',
        isLast: true,
        animated: false,
        type: 'summary',
      },
    ],
    showAnimation: true,
    primaryColor: '#e74c3c',
    customColors: {
      milestone: '#3498db',
      design: '#9b59b6',
      release: '#f39c12',
      launch: '#27ae60',
      summary: '#e74c3c',
    },
  },

  onLoad() {
    console.log('Advanced timeline demo loaded');
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  animateTimeline() {
    // 逐个显示时间线项目的动画效果，带有不同延迟
    const items = this.data.advancedTimelineData || [];
    items.forEach((item, index) => {
      setTimeout(() => {
        this.setData({
          ['advancedTimelineData[' + index + '].animated']: true,
        });
      }, index * 300); // 更长的延迟时间
    });
  },

  onTimelineItemTap(event) {
    const { index } = event.currentTarget.dataset;
    const item = this.data.advancedTimelineData[index];

    if (item) {
      const typeNames = {
        milestone: '里程碑',
        design: '设计阶段',
        release: '版本发布',
        launch: '产品上线',
        summary: '总结回顾',
      };

      lynx.showModal({
        title: (typeNames[item.type] || '事件') + ': ' + item.title,
        content: '日期: ' + item.date + '\n\n' + item.description,
        confirmText: '确定',
        success: function (res) {
          if (res.confirm) {
            console.log('用户查看了事件详情:', item.title);
          }
        },
      });
    }
  },

  onShareTimeline() {
    lynx.showShareMenu({
      title: '产品发展历程',
      path: '/pages/timeline-advanced/index',
      success: function (res) {
        console.log('分享成功');
      },
      fail: function (err) {
        console.error('分享失败', err);
      },
    });
  },
});
