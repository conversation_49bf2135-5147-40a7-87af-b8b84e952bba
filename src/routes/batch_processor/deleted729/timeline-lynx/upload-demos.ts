import { UploadService } from '../services/UploadService';
import { readFileSync } from 'fs';
import { join } from 'path';

/**
 * 上传时间轴组件 demo 到 playground 并生成 URL
 */

// 读取文件内容的辅助函数
function readFileContent(filePath: string): string {
  try {
    return readFileSync(filePath, 'utf-8');
  } catch (error) {
    console.error(`读取文件失败: ${filePath}`, error);
    return '';
  }
}

// 创建 FILES 格式的内容
function createFilesFormat(files: { [path: string]: string }): string {
  let content = 'FILES\n\n';
  
  for (const [path, fileContent] of Object.entries(files)) {
    content += `FILE path="${path}"\n`;
    content += fileContent;
    content += '\nFILE\n\n';
  }
  
  content += 'FILES';
  return content;
}

async function uploadBasicDemo() {
  console.log('🚀 开始上传基础示例...');
  
  const basePath = __dirname;
  
  // 读取基础示例文件
  const files = {
    'index.ttml': readFileContent(join(basePath, 'demos/basic.ttml')),
    'index.ttss': readFileContent(join(basePath, 'demos/basic.ttss')),
    'index.ts': readFileContent(join(basePath, 'demos/basic.ts')),
    'index.json': readFileContent(join(basePath, 'demos/basic.json'))
  };
  
  // 创建 FILES 格式内容
  const filesContent = createFilesFormat(files);
  
  // 创建上传服务
  const uploadService = new UploadService({
    enableLogging: true,
    mockMode: false
  });
  
  try {
    // 构建文件结构
    const fileStructure = {
      'index.ttml': files['index.ttml'],
      'index.ttss': files['index.ttss'], 
      'index.ts': files['index.ts'],
      'index.json': files['index.json']
    };
    
    // 上传到 CDN
    const uploadResult = await uploadService.uploadToCDN(fileStructure);
    
    if (!uploadResult.success) {
      throw new Error(uploadResult.error || '上传失败');
    }
    
    // 生成 playground URL
    const playgroundUrl = uploadService.buildPlaygroundUrl(
      uploadResult.cdnUrl,
      'ttml-nodiff',
      'preview',
      '3.6.0-beta.2'
    );
    
    console.log('✅ 基础示例上传成功！');
    console.log('📦 CDN URL:', uploadResult.cdnUrl);
    console.log('🎮 Playground URL:', playgroundUrl);
    
    return {
      success: true,
      cdnUrl: uploadResult.cdnUrl,
      playgroundUrl,
      demoType: 'basic'
    };
    
  } catch (error) {
    console.error('❌ 基础示例上传失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      demoType: 'basic'
    };
  }
}

async function uploadAdvancedDemo() {
  console.log('🚀 开始上传高级示例...');
  
  const basePath = __dirname;
  
  // 读取高级示例文件（复用基础示例的 UI 和样式，使用高级逻辑）
  const files = {
    'index.ttml': readFileContent(join(basePath, 'demos/basic.ttml')).replace(
      'demoTimelineData',
      'advancedTimelineData'
    ),
    'index.ttss': readFileContent(join(basePath, 'demos/basic.ttss')),
    'index.ts': readFileContent(join(basePath, 'demos/advanced.ts')),
    'index.json': JSON.stringify({
      "usingComponents": {},
      "navigationBarTitleText": "时间轴组件 - 高级示例",
      "navigationBarBackgroundColor": "#ffffff",
      "navigationBarTextStyle": "black",
      "backgroundColor": "#f8f9fa",
      "backgroundTextStyle": "light",
      "enablePullDownRefresh": false,
      "onReachBottomDistance": 50
    }, null, 2)
  };
  
  // 创建上传服务
  const uploadService = new UploadService({
    enableLogging: true,
    mockMode: false
  });
  
  try {
    // 构建文件结构
    const fileStructure = {
      'index.ttml': files['index.ttml'],
      'index.ttss': files['index.ttss'], 
      'index.ts': files['index.ts'],
      'index.json': files['index.json']
    };
    
    // 上传到 CDN
    const uploadResult = await uploadService.uploadToCDN(fileStructure);
    
    if (!uploadResult.success) {
      throw new Error(uploadResult.error || '上传失败');
    }
    
    // 生成 playground URL
    const playgroundUrl = uploadService.buildPlaygroundUrl(
      uploadResult.cdnUrl,
      'ttml-nodiff',
      'preview',
      '3.6.0-beta.2'
    );
    
    console.log('✅ 高级示例上传成功！');
    console.log('📦 CDN URL:', uploadResult.cdnUrl);
    console.log('🎮 Playground URL:', playgroundUrl);
    
    return {
      success: true,
      cdnUrl: uploadResult.cdnUrl,
      playgroundUrl,
      demoType: 'advanced'
    };
    
  } catch (error) {
    console.error('❌ 高级示例上传失败:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error),
      demoType: 'advanced'
    };
  }
}

// 主函数：上传所有示例
async function uploadAllDemos() {
  console.log('🎯 开始上传时间轴组件所有示例到 Playground...\n');
  
  const results = [];
  
  // 上传基础示例
  const basicResult = await uploadBasicDemo();
  results.push(basicResult);
  
  console.log('\n' + '='.repeat(60) + '\n');
  
  // 上传高级示例
  const advancedResult = await uploadAdvancedDemo();
  results.push(advancedResult);
  
  // 输出总结
  console.log('\n' + '='.repeat(60));
  console.log('📊 上传结果总结:');
  console.log('='.repeat(60));
  
  results.forEach((result, index) => {
    const status = result.success ? '✅ 成功' : '❌ 失败';
    console.log(`${index + 1}. ${result.demoType} 示例: ${status}`);
    
    if (result.success) {
      console.log(`   🎮 Playground: ${result.playgroundUrl}`);
    } else {
      console.log(`   ❌ 错误: ${result.error}`);
    }
    console.log('');
  });
  
  return results;
}

// 如果直接运行此文件，则执行上传
if (require.main === module) {
  uploadAllDemos().catch(console.error);
}

export { uploadBasicDemo, uploadAdvancedDemo, uploadAllDemos };
