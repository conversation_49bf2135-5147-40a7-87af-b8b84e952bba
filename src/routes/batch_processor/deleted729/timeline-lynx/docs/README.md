# Lynx 时间轴组件

## 组件名称

timeline-lynx

## 使用场景

用于以垂直时间轴的形式展示一系列事件，支持动画效果和交互功能，常用于项目历程、发展历程、新闻动态、活动记录等场景。

## 版本号

1.0.0

## 组件特性

- ✅ 符合 Lynx 框架 TTML/TTSS 语法规范
- ✅ 支持垂直时间轴布局
- ✅ 支持动画效果和渐进显示
- ✅ 支持点击交互和模态框展示
- ✅ 响应式设计，适配移动端
- ✅ 可自定义主题色和样式
- ✅ 支持滚动查看长列表

## 属性列表

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `list` | `String` (JSON 格式) | `"[]"` | 时间轴数据数组，每个对象包含：<br>- `id`: (可选) 唯一标识，自动生成<br>- `date`: (必须) 事件日期<br>- `title`: (必须) 事件标题<br>- `content`: (必须) 事件描述内容<br>- `media`: (可选) 媒体对象数组，支持图片和视频<br>- `type`: (可选) 事件类型，用于样式区分<br>- `status`: (可选) 事件状态：'completed', 'ongoing', 'planned' |
| `showAnimation` | `Boolean` | `true` | 是否显示动画效果 |
| `primaryColor` | `String` | `"#3498db"` | 主题色，用于时间点和连接线 |
| `animationDelay` | `Number` | `200` | 动画延迟时间（毫秒） |
| `enableInteraction` | `Boolean` | `true` | 是否启用点击交互 |
| `theme` | `String` | `"default"` | 主题样式：'default', 'minimal', 'colorful' |

## 数据格式示例

### 基础用法
```javascript
list: '[
  {
    "date": "2024-01-15",
    "title": "项目启动",
    "content": "正式启动新产品开发项目，组建核心团队"
  },
  {
    "date": "2024-03-20",
    "title": "原型设计完成",
    "content": "完成产品原型设计，通过内部评审"
  }
]'
```

### 高级用法（包含媒体和状态）
```javascript
list: '[
  {
    "date": "2024-01-15",
    "title": "项目启动",
    "content": "正式启动新产品开发项目，组建核心团队",
    "type": "milestone",
    "status": "completed"
  },
  {
    "date": "2024-03-20",
    "title": "产品发布",
    "content": "正式发布1.0版本，获得用户好评",
    "type": "release",
    "status": "completed",
    "media": [
      {
        "type": "image",
        "thumbnail": "https://example.com/release.jpg",
        "title": "发布现场"
      },
      {
        "type": "video",
        "thumbnail": "https://example.com/video-thumb.jpg",
        "videoSrc": "https://example.com/release-video.mp4",
        "title": "发布演示"
      }
    ]
  },
  {
    "date": "2024-06-20",
    "title": "用户突破",
    "content": "用户数突破10万大关，庆祝活动",
    "type": "achievement",
    "status": "ongoing"
  }
]'
```

## 使用方法

### 1. 引入组件文件

将以下文件复制到你的 Lynx 项目中：
- `index.ttml` - UI 结构文件
- `index.ttss` - 样式文件  
- `index.ts` - 逻辑文件
- `index.json` - 配置文件

### 2. 在页面中使用

#### 基础用法
```xml
<timeline-lynx
  list='[
    {
      "date": "2024-01-15",
      "title": "项目启动",
      "content": "正式启动新产品开发项目"
    }
  ]'
/>
```

#### 高级用法
```xml
<timeline-lynx
  list='[...]'
  showAnimation="{{true}}"
  primaryColor="#e74c3c"
  animationDelay="{{300}}"
  enableInteraction="{{true}}"
  theme="colorful"
/>
```

### 3. 配置数据

```javascript
// 在你的 TS 文件中
Page({
  data: {
    timelineData: [
      // 你的时间轴数据
    ],
    showAnimation: true,
    primaryColor: "#3498db"
  },
  
  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },
  
  // 其他方法...
})
```

## 样式自定义

你可以通过修改 TTSS 文件中的 CSS 变量来自定义样式：

```css
.timeline-dot {
  background-color: #your-color; /* 自定义时间点颜色 */
}

.timeline-line {
  background-color: #your-color; /* 自定义连接线颜色 */
}
```

## 注意事项

1. 确保 `timelineData` 数组中最后一项的 `isLast` 属性设置为 `true`
2. 每个时间轴项目都需要唯一的 `id` 用于 `tt:key`
3. 组件使用了 `scroll-view`，确保设置合适的高度
4. 动画效果依赖于 `animated` 属性的状态变化

## 浏览器兼容性

- 支持所有 Lynx 框架支持的平台
- iOS 12+
- Android 5.0+
- 微信小程序
- 字节跳动小程序
