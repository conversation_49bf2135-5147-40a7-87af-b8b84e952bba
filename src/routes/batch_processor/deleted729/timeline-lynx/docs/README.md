# Lynx 时间轴组件

## 组件名称

timeline-lynx

## 使用场景

用于以垂直时间轴的形式展示一系列事件，支持动画效果和交互功能，常用于项目历程、发展历程、新闻动态、活动记录等场景。

## 版本号

1.0.0

## 组件特性

- ✅ 符合 Lynx 框架 TTML/TTSS 语法规范
- ✅ 支持垂直时间轴布局
- ✅ 支持动画效果和渐进显示
- ✅ 支持点击交互和模态框展示
- ✅ 响应式设计，适配移动端
- ✅ 可自定义主题色和样式
- ✅ 支持滚动查看长列表

## 属性列表

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `timelineData` | `Array` | `[]` | 时间轴数据数组，每个对象包含：<br>- `id`: (必须) 唯一标识<br>- `date`: (必须) 事件日期<br>- `title`: (必须) 事件标题<br>- `description`: (必须) 事件描述<br>- `isLast`: (必须) 是否为最后一项<br>- `animated`: (可选) 动画状态 |
| `showAnimation` | `Boolean` | `true` | 是否显示动画效果 |
| `primaryColor` | `String` | `"#3498db"` | 主题色，用于时间点和连接线 |

## 数据格式示例

```javascript
timelineData: [
  {
    id: 1,
    date: "2024年1月15日",
    title: "项目启动",
    description: "正式启动新产品开发项目，组建核心团队",
    isLast: false,
    animated: false
  },
  {
    id: 2,
    date: "2024年3月20日",
    title: "原型设计完成", 
    description: "完成产品原型设计，通过内部评审",
    isLast: true,
    animated: false
  }
]
```

## 使用方法

### 1. 引入组件文件

将以下文件复制到你的 Lynx 项目中：
- `index.ttml` - UI 结构文件
- `index.ttss` - 样式文件  
- `index.ts` - 逻辑文件
- `index.json` - 配置文件

### 2. 在页面中使用

```xml
<!-- 在你的 TTML 文件中 -->
<view class="timeline-container">
  <view class="timeline-header">
    <text class="timeline-title">发展历程</text>
  </view>

  <scroll-view class="timeline-content" scroll-y="true">
    <view class="timeline-item" tt:for="{{timelineData}}" tt:key="{{item.id}}" bindtap="onTimelineItemTap" data-index="{{index}}">
      <view class="timeline-date-section">
        <text class="timeline-date">{{item.date}}</text>
        <view class="timeline-dot"></view>
      </view>

      <view class="timeline-line" tt:if="{{!item.isLast}}"></view>

      <view class="timeline-content-section">
        <text class="timeline-event-title">{{item.title}}</text>
        <text class="timeline-description">{{item.description}}</text>
      </view>
    </view>
  </scroll-view>
</view>
```

### 3. 配置数据

```javascript
// 在你的 TS 文件中
Page({
  data: {
    timelineData: [
      // 你的时间轴数据
    ],
    showAnimation: true,
    primaryColor: "#3498db"
  },
  
  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },
  
  // 其他方法...
})
```

## 样式自定义

你可以通过修改 TTSS 文件中的 CSS 变量来自定义样式：

```css
.timeline-dot {
  background-color: #your-color; /* 自定义时间点颜色 */
}

.timeline-line {
  background-color: #your-color; /* 自定义连接线颜色 */
}
```

## 注意事项

1. 确保 `timelineData` 数组中最后一项的 `isLast` 属性设置为 `true`
2. 每个时间轴项目都需要唯一的 `id` 用于 `tt:key`
3. 组件使用了 `scroll-view`，确保设置合适的高度
4. 动画效果依赖于 `animated` 属性的状态变化

## 浏览器兼容性

- 支持所有 Lynx 框架支持的平台
- iOS 12+
- Android 5.0+
- 微信小程序
- 字节跳动小程序
