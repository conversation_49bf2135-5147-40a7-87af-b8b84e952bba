# API 文档

## 组件属性 (Properties)

### timelineData
- **类型**: `Array`
- **默认值**: `[]`
- **描述**: 时间轴数据数组
- **必填**: 是

#### timelineData 数组项结构:
```typescript
interface TimelineItem {
  id: number | string;        // 唯一标识符
  date: string;              // 事件日期
  title: string;             // 事件标题
  description: string;       // 事件描述
  isLast: boolean;          // 是否为最后一项
  animated?: boolean;       // 动画状态（可选）
}
```

### showAnimation
- **类型**: `Boolean`
- **默认值**: `true`
- **描述**: 是否启用动画效果
- **必填**: 否

### primaryColor
- **类型**: `String`
- **默认值**: `"#3498db"`
- **描述**: 主题色，用于时间点和连接线
- **必填**: 否

## 组件方法 (Methods)

### animateTimeline()
- **描述**: 触发时间轴动画效果
- **参数**: 无
- **返回值**: 无
- **使用场景**: 在数据加载完成后调用，实现渐进显示效果

```javascript
this.animateTimeline();
```

### onTimelineItemTap(event)
- **描述**: 时间轴项目点击事件处理
- **参数**: 
  - `event`: 事件对象，包含 `currentTarget.dataset.index`
- **返回值**: 无
- **使用场景**: 用户点击时间轴项目时触发

```javascript
onTimelineItemTap(event) {
  const { index } = event.currentTarget.dataset;
  const item = this.data.timelineData[index];
  // 处理点击逻辑
}
```

### onShareTimeline()
- **描述**: 分享时间轴功能
- **参数**: 无
- **返回值**: 无
- **使用场景**: 用户触发分享操作时调用

## 组件事件 (Events)

### bindtap
- **触发时机**: 用户点击时间轴项目时
- **事件对象**: 包含 `currentTarget.dataset.index` 属性
- **使用方式**: 在 TTML 中绑定 `bindtap="onTimelineItemTap"`

## 样式类名 (CSS Classes)

### 容器类
- `.timeline-container`: 时间轴主容器
- `.timeline-header`: 时间轴头部
- `.timeline-content`: 时间轴内容区域（scroll-view）

### 项目类
- `.timeline-item`: 单个时间轴项目
- `.timeline-date-section`: 日期区域
- `.timeline-content-section`: 内容区域

### 元素类
- `.timeline-title`: 时间轴标题
- `.timeline-date`: 事件日期
- `.timeline-dot`: 时间点圆圈
- `.timeline-line`: 连接线
- `.timeline-event-title`: 事件标题
- `.timeline-description`: 事件描述

## 生命周期

### onLoad()
- **触发时机**: 页面加载时
- **用途**: 初始化组件，输出日志

### onReady()
- **触发时机**: 页面渲染完成后
- **用途**: 启动动画效果（如果 `showAnimation` 为 `true`）

## 数据更新

使用 `this.setData()` 方法更新组件数据：

```javascript
// 更新整个时间轴数据
this.setData({
  timelineData: newTimelineData
});

// 更新单个项目的动画状态
this.setData({
  [`timelineData[${index}].animated`]: true
});

// 更新主题色
this.setData({
  primaryColor: "#e74c3c"
});
```

## 错误处理

组件内置了基本的错误处理：

1. **数据为空**: 当 `timelineData` 为空数组时，不会渲染任何内容
2. **动画异常**: 如果动画过程中出现异常，不会影响基本显示功能
3. **点击事件异常**: 点击事件包含了数据存在性检查

## 性能优化建议

1. **大数据量**: 对于超过 50 项的时间轴，建议使用分页加载
2. **动画性能**: 可以通过设置 `showAnimation: false` 关闭动画以提升性能
3. **内存管理**: 及时清理不需要的数据，避免内存泄漏

## 调试信息

组件在关键节点会输出调试信息：

```javascript
console.log('Timeline component loaded');        // 组件加载
console.log('用户点击确定');                     // 用户交互
console.log('分享成功');                        // 分享成功
console.error('分享失败', err);                 // 分享失败
```
