# Lynx 时间轴组件

一个符合 Lynx 框架规范的垂直时间轴组件，支持动画效果和交互功能。

## 📁 项目结构

```
timeline-lynx/
├── README.md                 # 项目说明文档
├── index.ttml               # 组件 UI 结构文件
├── index.ttss               # 组件样式文件
├── index.ts                 # 组件逻辑文件
├── index.json               # 组件配置文件
├── demos/                   # 示例文件夹
│   ├── basic.ttml          # 基础示例 - UI
│   ├── basic.ttss          # 基础示例 - 样式
│   ├── basic.ts            # 基础示例 - 逻辑
│   ├── basic.json          # 基础示例 - 配置
│   └── advanced.ts         # 高级示例 - 逻辑
└── docs/                    # 文档文件夹
    ├── README.md           # 详细使用文档
    └── API.md              # API 参考文档
```

## 🚀 快速开始

### 1. 复制组件文件

将以下核心文件复制到你的 Lynx 项目中：
- `index.ttml`
- `index.ttss` 
- `index.ts`
- `index.json`

### 2. 配置数据

```javascript
Page({
  data: {
    timelineData: [
      {
        id: 1,
        date: "2024年1月15日",
        title: "项目启动",
        description: "正式启动新产品开发项目",
        isLast: false,
        animated: false
      }
      // 更多数据...
    ]
  }
})
```

### 3. 在页面中使用

直接使用组件提供的 TTML 结构，或参考 `demos/` 文件夹中的示例。

## 📖 文档

- [详细使用文档](./docs/README.md) - 完整的使用指南和配置说明
- [API 参考文档](./docs/API.md) - 详细的 API 文档和方法说明

## 🎯 示例

### 基础示例
查看 `demos/basic.*` 文件，展示了基本的时间轴功能。

### 高级示例
查看 `demos/advanced.ts` 文件，展示了带有自定义类型和颜色的高级用法。

## 🎮 Playground 演示

### 方法一：直接复制 FILES 格式内容

1. 运行 `generate-demo-files.ts` 获取 FILES 格式的内容：
   ```bash
   node generate-demo-files.ts
   ```

2. 复制输出的 FILES 格式内容到 [Batch Processor 页面](../../+page.svelte)

3. 点击"处理"按钮，系统会自动：
   - 解析 FILES 格式
   - 构建文件结构
   - 上传到 CDN
   - 生成 Playground URL

### 方法二：使用上传脚本

运行 `upload-demos.ts` 脚本自动上传所有示例：
```bash
node upload-demos.ts
```

脚本会输出：
- ✅ 基础示例 Playground URL
- 🚀 高级示例 Playground URL

### 预期的 Playground URL 格式

```
https://playground.cn.goofy.app/?useSpeedy=true&exampleType=ttml-nodiff&layout=preview&project=https://lf3-static.bytedance.com/so-web-code/[文件ID].zip&sdkVersion=3.6.0-beta.2
```

## ✨ 特性

- ✅ **Lynx 规范**: 完全符合 Lynx 框架的 TTML/TTSS 语法规范
- ✅ **响应式设计**: 适配不同屏幕尺寸的移动设备
- ✅ **动画效果**: 支持渐进显示动画，提升用户体验
- ✅ **交互功能**: 支持点击事件和模态框展示详情
- ✅ **可定制**: 支持自定义主题色和样式
- ✅ **滚动支持**: 使用 scroll-view 支持长列表滚动
- ✅ **无 Demo 数据**: 组件本身不包含示例数据，保持纯净

## 🔧 技术规范

### 符合 Lynx 约束
- 使用 `view`、`text`、`scroll-view` 等 Lynx 组件
- 使用 `tt:for`、`tt:if` 等 Lynx 指令语法
- 使用 `bindtap` 等 Lynx 事件绑定语法
- 使用 `rpx` 单位确保跨端适配
- 所有文本都用 `text` 标签包裹

### 样式特性
- 使用 TTSS 样式系统
- 支持响应式媒体查询
- 避免使用 Lynx 不支持的 CSS 属性
- 优化的移动端交互体验

## 🎨 自定义

你可以通过修改以下方式来自定义组件：

1. **主题色**: 修改 `primaryColor` 属性
2. **样式**: 编辑 `index.ttss` 文件中的样式
3. **布局**: 调整 `index.ttml` 文件中的结构
4. **交互**: 扩展 `index.ts` 文件中的方法

## 📱 兼容性

- Lynx 框架 3.6.0+
- iOS 12+
- Android 5.0+
- 微信小程序
- 字节跳动小程序

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个组件。

## 📄 许可证

MIT License
