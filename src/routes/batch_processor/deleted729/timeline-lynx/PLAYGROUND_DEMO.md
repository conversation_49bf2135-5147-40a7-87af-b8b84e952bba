# 🎮 时间轴组件 Playground 演示

## 📋 使用步骤

### 1. 获取 FILES 格式内容

运行以下命令获取可直接使用的 FILES 格式内容：

```bash
cd src/routes/batch_processor/deleted729/timeline-lynx
node generate-demo-files.ts
```

### 2. 复制内容到 Batch Processor

1. 打开 [Batch Processor 页面](../../+page.svelte)
2. 将生成的 FILES 格式内容复制到输入框
3. 点击"处理"按钮

### 3. 获取 Playground URL

系统会自动：
- ✅ 解析 FILES 格式
- ✅ 构建 lynx 文件结构  
- ✅ 上传到 CDN
- ✅ 生成 Playground URL

## 📱 可用的示例

### 基础示例
- **功能**: 展示基本的时间轴功能
- **特性**: 垂直布局、动画效果、点击交互
- **数据**: 包含 4 个时间节点的示例数据

### 高级示例  
- **功能**: 展示高级时间轴功能
- **特性**: 类型分类、自定义颜色、类型徽章
- **数据**: 包含 5 个不同类型的时间节点

## 🔗 预期的 Playground URL 格式

```
https://playground.cn.goofy.app/?useSpeedy=true&exampleType=ttml-nodiff&layout=preview&project=https://lf3-static.bytedance.com/so-web-code/[文件ID].zip&sdkVersion=3.6.0-beta.2
```

## 📝 FILES 格式示例

基础示例的 FILES 格式内容：

```
FILES

FILE path="index.ttml"
<view class="timeline-container">
  <view class="timeline-header">
    <text class="timeline-title">发展历程</text>
  </view>
  <!-- 更多内容... -->
</view>
FILE

FILE path="index.ttss"
.timeline-container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}
/* 更多样式... */
FILE

FILE path="index.ts"
Page({
  data: {
    demoTimelineData: [
      // 示例数据...
    ]
  },
  // 更多逻辑...
});
FILE

FILE path="index.json"
{
  "usingComponents": {},
  "navigationBarTitleText": "时间轴组件 - 基础示例"
}
FILE

FILES
```

## 🚀 自动上传脚本

如果你想要自动化上传过程，可以运行：

```bash
node upload-demos.ts
```

这个脚本会：
1. 读取所有 demo 文件
2. 构建文件结构
3. 上传到 CDN
4. 生成 Playground URL
5. 输出结果摘要

## 🔧 技术细节

### 文件结构要求
- `index.ttml`: UI 结构文件
- `index.ttss`: 样式文件
- `index.ts`: 逻辑文件  
- `index.json`: 配置文件

### 上传流程
1. **解析**: 从 FILES 格式提取各个文件
2. **压缩**: 将文件打包成 ZIP
3. **上传**: 上传到字节跳动 CDN
4. **生成**: 构建 Playground URL

### Playground 参数
- `useSpeedy=true`: 启用加速模式
- `exampleType=ttml-nodiff`: TTML 无差异模式
- `layout=preview`: 预览布局
- `sdkVersion=3.6.0-beta.2`: SDK 版本

## 📞 问题反馈

如果在使用过程中遇到问题，请检查：

1. ✅ FILES 格式是否正确
2. ✅ 文件路径是否匹配
3. ✅ TTML/TTSS 语法是否符合 lynx 规范
4. ✅ 网络连接是否正常

## 🎯 下一步

成功生成 Playground URL 后，你可以：

1. 📱 在浏览器中预览效果
2. 📋 分享给其他开发者
3. 🔧 基于示例进行二次开发
4. 📚 参考代码学习 lynx 开发
