<scroll-view class="timeline-card-container {{getThemeClass('timeline-theme')}}" scroll-y="true">
  <view class="timeline-container">
    <view class="timeline-header" tt:if="{{timelineData.length > 0}}">
      <text class="timeline-title">发展历程</text>
    </view>

    <view class="timeline-content">
      <view 
        class="timeline-item {{getStatusClass(item.status)}} {{getTypeClass(item.type)}} {{item.animated ? 'timeline-item-animated' : ''}}" 
        tt:for="{{timelineData}}" 
        tt:key="{{item.id}}" 
        bindtap="onTimelineItemTap" 
        data-index="{{index}}"
      >
        <!-- 时间和状态指示器 -->
        <view class="timeline-date-section">
          <text class="timeline-date">{{item.date}}</text>
          <view class="timeline-dot" style="background-color: {{primaryColor}}"></view>
          <view class="timeline-status-indicator {{item.status}}"></view>
        </view>

        <!-- 连接线 -->
        <view class="timeline-line" tt:if="{{!item.isLast}}" style="background-color: {{primaryColor}}"></view>

        <!-- 内容区域 -->
        <view class="timeline-content-section">
          <!-- 标题和类型标签 -->
          <view class="timeline-header-section">
            <text class="timeline-event-title">{{item.title}}</text>
            <view class="timeline-type-badge {{item.type}}" tt:if="{{item.type !== 'default'}}">
              <text class="timeline-type-text">{{item.type}}</text>
            </view>
          </view>

          <!-- 描述内容 -->
          <text class="timeline-description">{{item.content}}</text>

          <!-- 媒体内容 -->
          <view class="timeline-media-container" tt:if="{{item.media && item.media.length > 0}}">
            <view class="timeline-media-grid">
              <view 
                class="timeline-media-item {{mediaItem.type}}"
                tt:for="{{item.media}}" 
                tt:for-item="mediaItem"
                tt:for-index="mediaIndex"
                tt:key="{{mediaItem.thumbnail}}"
                bindtap="onMediaTap"
                data-item-index="{{index}}"
                data-media-index="{{mediaIndex}}"
              >
                <image 
                  class="timeline-media-thumbnail" 
                  src="{{mediaItem.thumbnail}}" 
                  mode="aspectFill"
                />
                <view class="timeline-media-overlay" tt:if="{{mediaItem.type === 'video'}}">
                  <text class="timeline-media-play-icon">▶</text>
                </view>
                <text class="timeline-media-title" tt:if="{{mediaItem.title}}">{{mediaItem.title}}</text>
              </view>
            </view>
          </view>

          <!-- 状态指示文本 -->
          <view class="timeline-status-section" tt:if="{{item.status !== 'completed'}}">
            <text class="timeline-status-text {{item.status}}">
              {{item.status === 'ongoing' ? '进行中' : '计划中'}}
            </text>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="timeline-empty" tt:if="{{timelineData.length === 0}}">
      <text class="timeline-empty-text">暂无时间轴数据</text>
    </view>
  </view>
</scroll-view>
