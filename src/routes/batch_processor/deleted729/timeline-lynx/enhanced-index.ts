Card({
  data: {
    timelineData: [],
    showAnimation: true,
    primaryColor: '#3498db',
    animationDelay: 200,
    enableInteraction: true,
    theme: 'default'
  },

  properties: {
    // 主要数据属性 - 兼容 timeline-comp 接口
    list: {
      type: String,
      value: '[]',
      observer: 'parseTimelineData'
    },
    
    // 动画控制
    showAnimation: {
      type: Boolean,
      value: true
    },
    animationDelay: {
      type: Number,
      value: 200
    },
    
    // 样式控制
    primaryColor: {
      type: String,
      value: '#3498db'
    },
    theme: {
      type: String,
      value: 'default' // 'default', 'minimal', 'colorful'
    },
    
    // 交互控制
    enableInteraction: {
      type: Boolean,
      value: true
    }
  },

  onLoad: function() {
    console.log('Enhanced Timeline component loaded');
    this.parseTimelineData();
  },

  onReady: function() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  // 解析 JSON 格式的 list 数据
  parseTimelineData: function() {
    try {
      const listStr = this.data.list || this.properties.list.value;
      let parsedData = [];
      
      if (typeof listStr === 'string') {
        parsedData = JSON.parse(listStr);
      } else if (Array.isArray(listStr)) {
        parsedData = listStr;
      }
      
      // 数据处理和增强
      const enhancedData = parsedData.map(function(item, index) {
        return {
          id: item.id || index + 1,
          date: item.date || '',
          title: item.title || '',
          content: item.content || item.description || '',
          media: item.media || [],
          type: item.type || 'default',
          status: item.status || 'completed',
          isLast: index === parsedData.length - 1,
          animated: false
        };
      });
      
      this.setData({
        timelineData: enhancedData
      });
      
    } catch (error) {
      console.error('Timeline data parsing error:', error);
      this.setData({
        timelineData: []
      });
    }
  },

  // 动画效果
  animateTimeline: function() {
    const items = this.data.timelineData || [];
    const delay = this.data.animationDelay || 200;
    
    items.forEach(function(item, index) {
      setTimeout(function() {
        this.setData({
          ['timelineData[' + index + '].animated']: true
        });
      }.bind(this), index * delay);
    }.bind(this));
  },

  // 点击事件处理
  onTimelineItemTap: function(event) {
    if (!this.data.enableInteraction) {
      return;
    }
    
    const index = event.currentTarget.dataset.index;
    const item = this.data.timelineData[index];
    
    if (item) {
      // 触发自定义事件
      this.triggerEvent('itemtap', {
        item: item,
        index: index
      });
      
      // 默认行为：显示详情
      this.showItemDetail(item);
    }
  },

  // 显示项目详情
  showItemDetail: function(item) {
    let content = '日期: ' + item.date + '\n\n' + item.content;
    
    // 如果有媒体内容，添加媒体信息
    if (item.media && item.media.length > 0) {
      content += '\n\n包含 ' + item.media.length + ' 个媒体文件';
    }
    
    lynx.showModal({
      title: item.title,
      content: content,
      confirmText: '确定',
      success: function(res) {
        if (res.confirm) {
          console.log('用户查看了详情:', item.title);
        }
      }
    });
  },

  // 媒体点击事件
  onMediaTap: function(event) {
    const itemIndex = event.currentTarget.dataset.itemIndex;
    const mediaIndex = event.currentTarget.dataset.mediaIndex;
    const item = this.data.timelineData[itemIndex];
    const media = item.media[mediaIndex];
    
    if (media) {
      this.triggerEvent('mediatap', {
        media: media,
        item: item,
        itemIndex: itemIndex,
        mediaIndex: mediaIndex
      });
      
      // 默认行为：预览媒体
      this.previewMedia(media);
    }
  },

  // 预览媒体
  previewMedia: function(media) {
    if (media.type === 'image') {
      lynx.previewImage({
        urls: [media.thumbnail],
        current: media.thumbnail
      });
    } else if (media.type === 'video') {
      // 视频预览逻辑
      console.log('Preview video:', media.videoSrc);
    }
  },

  // 分享功能
  onShareTimeline: function() {
    lynx.showShareMenu({
      title: '时间轴分享',
      path: '/pages/timeline/index',
      success: function(res) {
        console.log('分享成功');
      },
      fail: function(err) {
        console.error('分享失败', err);
      }
    });
  },

  // 获取主题样式类
  getThemeClass: function(baseClass) {
    const theme = this.data.theme;
    if (theme === 'default') {
      return baseClass;
    }
    return baseClass + ' ' + baseClass + '-' + theme;
  },

  // 获取状态样式类
  getStatusClass: function(status) {
    const statusMap = {
      'completed': 'status-completed',
      'ongoing': 'status-ongoing', 
      'planned': 'status-planned'
    };
    return statusMap[status] || 'status-completed';
  },

  // 获取类型样式类
  getTypeClass: function(type) {
    const typeMap = {
      'milestone': 'type-milestone',
      'release': 'type-release',
      'achievement': 'type-achievement',
      'default': 'type-default'
    };
    return typeMap[type] || 'type-default';
  }
});
