export const TIMELINE_COMPONENT = `
FILES

FILE path="index.ttml"
<view class="timeline-container">
  <view class="timeline-header">
    <text class="timeline-title">发展历程</text>
  </view>

  <scroll-view class="timeline-content" scroll-y="true">
    <view class="timeline-item" tt:for="{{timelineData}}" tt:key="{{item.id}}" bindtap="onTimelineItemTap" data-index="{{index}}">
      <view class="timeline-date-section">
        <text class="timeline-date">{{item.date}}</text>
        <view class="timeline-dot"></view>
      </view>

      <view class="timeline-line" tt:if="{{!item.isLast}}"></view>

      <view class="timeline-content-section">
        <text class="timeline-event-title">{{item.title}}</text>
        <text class="timeline-description">{{item.description}}</text>
      </view>
    </view>
  </scroll-view>
</view>
FILE

FILE path="index.ttss"
.timeline-container {
  padding: 40rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.timeline-header {
  margin-bottom: 60rpx;
  text-align: center;
}

.timeline-title {
  font-size: 48rpx;
  font-weight: 600;
  color: #2c3e50;
  line-height: 1.2;
}

.timeline-content {
  position: relative;
  padding-left: 40rpx;
  height: 100vh;
}

.timeline-item {
  position: relative;
  margin-bottom: 60rpx;
  display: flex;
  align-items: flex-start;
}

.timeline-date-section {
  position: relative;
  margin-right: 40rpx;
  min-width: 200rpx;
}

.timeline-date {
  font-size: 28rpx;
  color: #7f8c8d;
  font-weight: 500;
  line-height: 1.4;
}

.timeline-dot {
  position: absolute;
  right: -20rpx;
  top: 8rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #3498db;
  border: 4rpx solid #ffffff;
  box-shadow: 0 0 0 4rpx #3498db;
}

.timeline-line {
  position: absolute;
  left: 219rpx;
  top: 40rpx;
  width: 2rpx;
  height: 80rpx;
  background-color: #bdc3c7;
}

.timeline-content-section {
  flex: 1;
  background-color: #ffffff;
  padding: 30rpx;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
  margin-left: 20rpx;
}

.timeline-event-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16rpx;
  line-height: 1.3;
}

.timeline-description {
  font-size: 26rpx;
  color: #5a6c7d;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
}

/* 响应式设计 */
@media (max-width: 480rpx) {
  .timeline-container {
    padding: 20rpx;
  }
  
  .timeline-title {
    font-size: 42rpx;
  }
  
  .timeline-date-section {
    min-width: 160rpx;
  }
  
  .timeline-content-section {
    padding: 20rpx;
  }
  
  .timeline-event-title {
    font-size: 28rpx;
  }
  
  .timeline-description {
    font-size: 24rpx;
  }
}
FILE

FILE path="index.js"
Page({
  data: {
    timelineData: [
      {
        id: 1,
        date: "2022年11月8日",
        title: "拜登预防性赦免",
        description: "即任前赦免福奇、米利等关键人物及全家物及全物及全",
        isLast: false,
        animated: false
      },
      {
        id: 2,
        date: "2022年11月8日",
        title: "特朗普解雇行为",
        description: "上台台后解雇超10000名拜登政府人员",
        isLast: false,
        animated: false
      },
      {
        id: 3,
        date: "2022年11月8日",
        title: "特朗普大规模赦免",
        description: "即任前赦免福奇、米利等关键人物及全家米利等关键人物及全家",
        isLast: false,
        animated: false
      },
      {
        id: 4,
        date: "2022年11月8日",
        title: "19州检察长起诉",
        description: "民主党人联合起诉特朗普行政令&quot;违宪&quot;",
        isLast: true,
        animated: false
      }
    ],
    showAnimation: true,
    primaryColor: "#3498db"
  },

  onLoad() {
    console.log('Timeline component loaded');
  },

  onReady() {
    if (this.data.showAnimation) {
      this.animateTimeline();
    }
  },

  animateTimeline() {
    // 逐个显示时间线项目的动画效果
    const items = this.data.timelineData || [];
    items.forEach((item, index) => {
      setTimeout(() => {
        this.setData({
          [`timelineData[${index}].animated`]: true
        });
      }, index * 200);
    });
  },

  onTimelineItemTap(event) {
    const { index } = event.currentTarget.dataset;
    const item = this.data.timelineData[index];

    if (item) {
      lynx.showModal({
        title: item.title,
        content: item.description,
        confirmText: '确定',
        success: (res) => {
          if (res.confirm) {
            console.log('用户点击确定');
          }
        }
      });
    }
  },

  onShareTimeline() {
    lynx.showShareMenu({
      title: '发展历程',
      path: '/pages/timeline/index',
      success: (res) => {
        console.log('分享成功');
      },
      fail: (err) => {
        console.error('分享失败', err);
      }
    });
  }
});
FILE

FILE path="index.json"
{
  "usingComponents": {},
  "navigationBarTitleText": "发展历程",
  "navigationBarBackgroundColor": "#ffffff",
  "navigationBarTextStyle": "black",
  "backgroundColor": "#f8f9fa",
  "backgroundTextStyle": "light",
  "enablePullDownRefresh": false,
  "onReachBottomDistance": 50
}
FILE



FILES
`;
