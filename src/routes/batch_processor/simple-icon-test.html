<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 简单图标测试</title>
    <link rel="stylesheet" href="styles/index.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 40px;
            background: #f8fafc;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        .button-test {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        
        .debug-info {
            margin-top: 16px;
            padding: 12px;
            background: #fffbeb;
            border: 1px solid #fde68a;
            border-radius: 6px;
            font-size: 12px;
            font-family: monospace;
        }
        
        .icon-debug {
            display: inline-block;
            margin: 8px;
            padding: 8px;
            border: 2px solid #ddd;
            border-radius: 4px;
            background: white;
        }
        
        .icon-debug svg {
            display: block;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 简单图标测试</h1>
        <p>测试重试按钮图标是否正确显示</p>
        
        <div class="button-test">
            <h3>原始按钮（来自你的HTML）</h3>
            <button class="btn btn-sm btn--primary-gold retry-failed-btn">
                <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span class="retry-text">重试失败项</span>
                <span class="retry-failed-badge">4</span>
            </button>
            <div class="debug-info" id="button-debug"></div>
        </div>
        
        <div class="button-test">
            <h3>独立图标测试</h3>
            <div class="icon-debug">
                <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="24" height="24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <div>带 retry-icon 类</div>
            </div>
            
            <div class="icon-debug">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="#92400e" width="24" height="24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <div>直接设置 stroke 颜色</div>
            </div>
            
            <div class="icon-debug">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="24" height="24" style="color: #92400e;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <div>内联样式设置颜色</div>
            </div>
        </div>
        
        <div class="button-test">
            <h3>强制样式测试</h3>
            <button class="btn btn-sm btn--primary-gold retry-failed-btn" style="background: white; border: 2px solid #92400e;">
                <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor" style="color: #92400e !important; stroke: #92400e !important; width: 20px !important; height: 20px !important;">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                </svg>
                <span class="retry-text">强制样式</span>
                <span class="retry-failed-badge">4</span>
            </button>
        </div>
        
        <div class="button-test">
            <h3>实时检测结果</h3>
            <div id="live-debug"></div>
        </div>
    </div>
    
    <script>
        function debugIcon() {
            const button = document.querySelector('.retry-failed-btn');
            const icon = button.querySelector('.retry-icon');
            const rect = icon.getBoundingClientRect();
            const computedStyle = window.getComputedStyle(icon);
            
            const debugInfo = {
                '尺寸': `${rect.width.toFixed(1)} x ${rect.height.toFixed(1)}px`,
                '位置': `left: ${rect.left.toFixed(1)}, top: ${rect.top.toFixed(1)}`,
                '颜色': computedStyle.color,
                'Stroke': computedStyle.stroke,
                'Fill': computedStyle.fill,
                '可见性': computedStyle.visibility,
                '透明度': computedStyle.opacity,
                'Display': computedStyle.display,
                'Z-Index': computedStyle.zIndex,
                '父元素背景': window.getComputedStyle(button).background.substring(0, 50) + '...'
            };
            
            document.getElementById('button-debug').innerHTML = Object.entries(debugInfo)
                .map(([key, value]) => `<div><strong>${key}:</strong> ${value}</div>`)
                .join('');
                
            // 实时检测
            const isVisible = rect.width > 0 && rect.height > 0 && 
                             computedStyle.visibility === 'visible' && 
                             parseFloat(computedStyle.opacity) > 0 &&
                             computedStyle.display !== 'none';
                             
            document.getElementById('live-debug').innerHTML = `
                <div style="padding: 12px; border-radius: 6px; background: ${isVisible ? '#f0fdf4' : '#fef2f2'}; border: 1px solid ${isVisible ? '#bbf7d0' : '#fecaca'};">
                    <strong>${isVisible ? '✅ 图标可见' : '❌ 图标不可见'}</strong>
                    <div style="margin-top: 8px; font-size: 12px;">
                        检测时间: ${new Date().toLocaleTimeString()}
                    </div>
                </div>
            `;
        }
        
        // 页面加载后立即检测
        window.addEventListener('load', debugIcon);
        
        // 每秒检测一次
        setInterval(debugIcon, 1000);
        
        // 鼠标悬停时也检测
        document.addEventListener('mouseover', function(e) {
            if (e.target.closest('.retry-failed-btn')) {
                setTimeout(debugIcon, 100);
            }
        });
    </script>
</body>
</html>
