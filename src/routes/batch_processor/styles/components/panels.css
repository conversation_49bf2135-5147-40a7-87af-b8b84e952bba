/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📋 UNIFIED PANELS SYSTEM - 统一面板系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了modules/panels/history.css + modules/panels/query-input.css + modules/panels/results.css
 * 提供完整的面板组件解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces modules/panels/history.css + modules/panels/query-input.css + modules/panels/results.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏛️ BASE PANEL STYLES - 基础面板样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--color-white);
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.panel-header {
  flex-shrink: 0;
  padding: var(--space-6);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.98) 0%, 
    rgba(59, 130, 246, 0.02) 100%);
}

.panel-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.panel-scrollable {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📋 HISTORY PANEL - 历史面板
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 历史面板容器 */
.history-panel-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 历史面板头部 */
.history-panel-header {
  flex-shrink: 0;
  padding: var(--space-6);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.05) 0%, 
    rgba(168, 85, 247, 0.05) 50%, 
    rgba(59, 130, 246, 0.05) 100%);
  border-radius: 16px 16px 0 0;
  box-shadow: 0 4px 20px rgba(99, 102, 241, 0.08), 0 1px 3px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: none;
}

/* 历史记录列表容器 */
.history-list-container {
  flex-grow: 1;
  overflow-y: auto;
  padding: var(--space-2);
  background: linear-gradient(135deg, 
    rgba(248, 250, 252, 0.8) 0%, 
    rgba(241, 245, 249, 0.8) 100%);
}

/* 历史记录图标容器 */
.history-icon-container {
  padding: var(--space-3);
  border-radius: 12px;
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.1) 0%, 
    rgba(168, 85, 247, 0.1) 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.15), 0 1px 3px rgba(0, 0, 0, 0.04);
}

/* 历史记录统计文本 */
.history-stats-text {
  background: linear-gradient(135deg, #4338ca, #c026d3, #2563eb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
  letter-spacing: 0.5px;
  font-weight: 600;
  font-size: 15px;
  padding: 2px 0;
  animation: historyPulse 2s infinite ease-in-out;
}

/* 历史记录条目 */
.history-record-item {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  animation: slideInFromBottom 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  margin-bottom: var(--space-2);
}

.history-record-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1), 0 3px 10px rgba(0, 0, 0, 0.08);
  border-color: rgba(99, 102, 241, 0.3);
  background: rgba(255, 255, 255, 1);
}

.history-record-item-modern {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(99, 102, 241, 0.1);
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.08);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  animation: slideInFromBottom 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) both;
  margin-bottom: var(--space-2);
}

.history-record-item-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1), 0 3px 10px rgba(0, 0, 0, 0.08);
  border-color: rgba(99, 102, 241, 0.3);
  background: rgba(255, 255, 255, 1);
}

.history-record-content {
  color: var(--color-gray-800);
  line-height: 1.5;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 历史记录操作按钮 */
.history-action-btn {
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid;
}

.history-action-btn.primary {
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.08) 0%, 
    rgba(168, 85, 247, 0.08) 100%);
  border-color: rgba(99, 102, 241, 0.2);
  color: var(--color-primary-600);
}

.history-action-btn.primary:hover {
  background: linear-gradient(135deg, 
    rgba(99, 102, 241, 0.12) 0%, 
    rgba(168, 85, 247, 0.12) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(99, 102, 241, 0.15);
}

.history-action-btn.secondary {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.08) 0%, 
    rgba(37, 99, 235, 0.08) 100%);
  border-color: rgba(59, 130, 246, 0.2);
  color: var(--color-blue-600);
}

.history-action-btn.secondary:hover {
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.12) 0%, 
    rgba(37, 99, 235, 0.12) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.15);
}

.history-action-btn.neutral {
  background: linear-gradient(135deg, 
    rgba(107, 114, 128, 0.08) 0%, 
    rgba(75, 85, 99, 0.08) 100%);
  border-color: rgba(107, 114, 128, 0.2);
  color: var(--color-gray-600);
}

.history-action-btn.neutral:hover {
  background: linear-gradient(135deg, 
    rgba(107, 114, 128, 0.12) 0%, 
    rgba(75, 85, 99, 0.12) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(107, 114, 128, 0.15);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📝 QUERY INPUT PANEL - 查询输入面板
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 查询输入头部 */
.query-input-header-icon {
  background-color: var(--color-primary-500);
  color: var(--color-white);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-2);
}

.query-input-header-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-800);
}

/* 查询输入操作按钮 */
.query-input-btn-sample {
  background: linear-gradient(135deg,
    var(--color-primary-500) 0%,
    var(--color-primary-600) 100%);
  color: var(--color-white);
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.query-input-btn-sample:hover {
  background: linear-gradient(135deg,
    var(--color-primary-600) 0%,
    var(--color-primary-700) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.query-input-btn-sample:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.query-input-btn-clear {
  background: linear-gradient(135deg,
    rgba(107, 114, 128, 0.9) 0%,
    rgba(75, 85, 99, 0.9) 100%);
  color: var(--color-white);
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.75rem;
  display: flex;
  align-items: center;
  gap: 6px;
  border: none;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(107, 114, 128, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.query-input-btn-clear:hover:not(:disabled) {
  background: linear-gradient(135deg,
    rgba(75, 85, 99, 0.9) 0%,
    rgba(55, 65, 81, 0.9) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.query-input-btn-clear:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: 0 1px 2px rgba(107, 114, 128, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

/* 查询输入状态指示器 */
.query-input-status-dot-active {
  background-color: var(--color-primary-500);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--space-2);
}

.query-input-status-dot-inactive {
  background-color: var(--color-gray-400);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: var(--space-2);
}

.query-input-status-text {
  color: var(--color-gray-600);
}

.query-input-character-count {
  color: var(--color-gray-500);
}

/* 查询输入文本区域 */
.query-input-container-header {
  background-color: var(--color-primary-50);
  border-color: var(--color-primary-200);
  padding: var(--space-2) var(--space-3);
  border-bottom: 1px solid;
}

.query-input-container-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-600);
}

.query-input-container-meta {
  font-size: 0.75rem;
  color: var(--color-gray-500);
  display: flex;
  align-items: center;
}

.query-input-focus-dot-active {
  background-color: var(--color-primary-500);
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.query-input-focus-dot-inactive {
  background-color: var(--color-gray-400);
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.query-input-textarea {
  width: 100%;
  height: 160px;
  padding: var(--space-3);
  resize: none;
  border: 0;
  min-height: 160px;
  color: var(--color-gray-800);
  transition: all 0.2s ease;
  background: transparent;
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
}

.query-input-textarea:focus {
  outline: none;
  background: transparent;
}

.query-input-textarea::placeholder {
  color: var(--input-placeholder);
  font-weight: 400;
}

.query-input-textarea-normal {
  background-color: var(--color-white);
}

.query-input-textarea-processing {
  background-color: var(--color-primary-50);
}

/* 查询输入卡片 */
.query-input-card {
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.98) 0%,
    rgba(255, 255, 255, 0.95) 100%);
  border: 1px solid rgba(59, 130, 246, 0.12);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.06),
    0 1px 3px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(8px);
  position: relative;
}

.query-input-card:hover {
  background: linear-gradient(135deg,
    rgba(239, 246, 255, 0.98) 0%,
    rgba(255, 255, 255, 1) 100%);
  border-color: rgba(59, 130, 246, 0.25);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.12),
    0 2px 8px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.query-input-card.optimized-card {
  background: rgba(255, 255, 255, 0.98);
  border: 1px solid rgba(148, 163, 184, 0.15);
  border-radius: 12px;
}

.query-input-card.optimized-card:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(35, 146, 239, 0.25);
}

.query-input-card > * {
  position: relative;
  z-index: 1;
}

.query-input-card .border-b {
  border-bottom: 1px solid rgba(59, 130, 246, 0.08);
  transition: border-color 0.3s ease;
}

.query-input-card:hover .border-b {
  border-bottom-color: rgba(59, 130, 246, 0.15);
}

.query-input-card .rounded-lg,
.query-input-card .rounded {
  border-radius: 8px;
}

/* 查询输入容器 */
.batch-processor-layout .query-input-textarea-container {
  position: relative;
  background: var(--color-white);
  transition: background-color 0.2s ease;
  isolation: isolate;
  z-index: 3;
  border-radius: 0 0 12px 12px;
  min-height: 120px;
}

.batch-processor-layout .query-input-card:hover .query-input-textarea-container,
.batch-processor-layout .glass-card:hover .query-input-textarea-container {
  background: rgba(255, 255, 255, 1);
}

/* 查询输入处理覆盖层 */
.query-input-processing-overlay {
  background-color: rgba(255, 255, 255, 0.9);
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.query-input-processing-badge {
  background-color: var(--color-blue-100);
  border: 1px solid var(--color-blue-300);
  border-radius: 8px;
  padding: var(--space-2) var(--space-3);
  display: flex;
  align-items: center;
}

.query-input-processing-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-blue-500);
  border-top-color: transparent;
  border-radius: 50%;
  margin-right: var(--space-2);
  animation: spin 1s linear infinite;
}

.query-input-processing-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-blue-700);
}

/* 查询预览区域 */
.query-preview-header-icon {
  background-color: var(--color-primary-500);
  color: var(--color-white);
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-2);
  flex-shrink: 0;
}

.batch-processor-layout .query-preview-header-icon .icon,
.batch-processor-layout .query-preview-header-icon svg {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  display: block;
}

.batch-processor-layout .query-preview-header-icon,
.batch-processor-layout .w-6.h-6.rounded-lg,
.batch-processor-layout .w-8.h-8.rounded-full {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.batch-processor-layout .query-preview-header-icon .icon,
.batch-processor-layout .w-6.h-6.rounded-lg .icon,
.batch-processor-layout .w-8.h-8.rounded-full .icon {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
  max-width: 16px;
  max-height: 16px;
}

.query-preview-header-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-800);
}

.query-preview-count-badge {
  background-color: var(--color-primary-50);
  border: 1px solid var(--color-primary-200);
  border-radius: 8px;
  padding: var(--space-1) var(--space-2);
}

.query-preview-count-number {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-primary-700);
}

.query-preview-count-label {
  font-size: 0.75rem;
  margin-left: var(--space-1);
  color: var(--color-gray-600);
}

/* 查询预览容器 */
.batch-processor-layout .query-preview-container,
.batch-processor-layout .query-preview-scroll {
  background: linear-gradient(135deg,
    rgba(239, 246, 255, 0.8) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  border: 1px solid rgba(59, 130, 246, 0.15);
  border-radius: 8px;
  padding: 12px;
  max-height: 180px;
  overflow-y: auto;
  overflow-x: hidden;
  backdrop-filter: blur(4px);
  box-shadow: inset 0 1px 3px rgba(59, 130, 246, 0.08);
  position: relative;
  z-index: 1;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
  flex-shrink: 0;
  display: block;
}

.batch-processor-layout .query-preview-item {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(59, 130, 246, 0.08);
  border-radius: 6px;
  padding: 8px 10px;
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.04);
  flex-shrink: 0;
}

.batch-processor-layout .query-preview-item:hover {
  background: rgba(255, 255, 255, 1);
  border-color: rgba(59, 130, 246, 0.15);
  transform: translateX(2px);
  box-shadow: 0 2px 6px rgba(59, 130, 246, 0.08);
}

.batch-processor-layout .query-preview-item:last-child {
  margin-bottom: 0;
}

.batch-processor-layout .query-preview-item-number {
  background: linear-gradient(135deg,
    var(--color-primary-400) 0%,
    var(--color-primary-600) 100%);
  color: #fff;
  font-size: 0.7rem;
  font-weight: 600;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.batch-processor-layout .query-preview-item-text {
  font-size: 0.75rem;
  color: var(--color-gray-700);
  word-break: break-words;
  flex: 1;
  line-height: 1.4;
}

.query-preview-more {
  background-color: var(--color-primary-100);
  color: var(--color-primary-600);
  font-size: 0.75rem;
  padding: var(--space-2);
  border-radius: 4px;
  text-align: center;
}

/* 查询预览滚动条 */
.query-preview-container::-webkit-scrollbar,
.query-preview-scroll::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.query-preview-container::-webkit-scrollbar-track,
.query-preview-scroll::-webkit-scrollbar-track {
  background: rgba(59, 130, 246, 0.05);
  border-radius: 3px;
}

.query-preview-container::-webkit-scrollbar-thumb,
.query-preview-scroll::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.query-preview-container::-webkit-scrollbar-thumb:hover,
.query-preview-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

.query-preview-container .space-y-1,
.query-preview-scroll .space-y-1 {
  min-height: fit-content;
  padding-bottom: 4px;
}

.query-preview-container,
.query-preview-scroll {
  contain: layout;
  word-wrap: break-word;
  flex-shrink: 1;
  min-height: 0;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📊 RESULTS PANEL - 结果面板
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 结果面板容器 */
.results-panel-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  flex: 1;
}

/* 结果列表容器 */
.results-list-container {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  margin-right: -8px;
  max-height: 100%;
}

.results-list-content {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding-bottom: 12px;
}

.results-list-container::-webkit-scrollbar {
  width: 8px;
  display: block;
}

.results-list-container::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
}

.results-list-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 结果面板空状态 */
.results-empty-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.results-empty-content {
  text-align: center;
  padding: 4rem 0;
}

.results-empty-icon {
  width: 5rem;
  height: 5rem;
  margin: 0 auto 1.5rem;
  border-radius: 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 12px rgba(35, 146, 239, 0.15);
}

.results-empty-icon-gradient {
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-blue-50) 100%);
}

.results-empty-icon-svg {
  height: 2.5rem;
  width: 2.5rem;
  color: var(--color-primary-500);
}

.results-empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--color-gray-800);
  margin-bottom: 0.75rem;
}

.results-empty-description {
  color: var(--color-gray-500);
  max-width: 24rem;
  margin: 0 auto;
  line-height: 1.6;
}

.results-empty-tip {
  margin-top: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  color: var(--color-gray-400);
}

/* 结果面板头部 */
.results-header {
  backdrop-filter: blur(12px);
  border-radius: 12px;
  padding: var(--space-6);
  border: 1px solid var(--color-gray-200);
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.15);
  background: rgba(255, 255, 255, 0.95);
}

.results-header-icon {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(35, 146, 239, 0.15);
}

.results-header-icon-gradient {
  background: linear-gradient(135deg, var(--color-primary-100) 0%, var(--color-blue-100) 100%);
}

.results-header-icon-svg {
  height: 1.25rem;
  width: 1.25rem;
  color: var(--color-primary-600);
}

.results-header-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--color-gray-800);
}

.results-header-subtitle {
  font-size: 0.875rem;
  color: var(--color-gray-500);
}

/* 状态筛选按钮 - 优化版 */
.status-filter-btn {
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: inline-flex;
  align-items: center;
  gap: 6px;
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

/* 覆盖 Tailwind 的默认样式 */
.status-filter-btn.px-3 {
  padding-left: 16px;
  padding-right: 16px;
}

.status-filter-btn.py-1\.5 {
  padding-top: 8px;
  padding-bottom: 8px;
}

.status-filter-btn.rounded-lg {
  border-radius: 20px;
}

/* 全部按钮 - 选中状态 - 深蓝色渐变主题 */
.status-filter-btn.selected.all {
  background: linear-gradient(135deg, #39aae7 0%, #66bceb 25%, #60a5fa 75%, #93c5fd 100%);
  color: #ffffff;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4), 0 0 20px rgba(147, 197, 253, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
  border: 1px solid rgba(59, 130, 246, 0.6);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

/* 全部按钮的星光特效 */
.status-filter-btn.selected.all::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 15% 25%, rgba(255, 255, 255, 0.9) 2px, rgba(255, 215, 0, 0.5) 4px, transparent 6px),
    radial-gradient(circle at 75% 25%, rgba(255, 215, 0, 0.8) 2px, rgba(255, 248, 220, 0.4) 4px, transparent 6px),
    radial-gradient(circle at 85% 75%, rgba(255, 255, 255, 0.8) 2px, rgba(255, 215, 0, 0.4) 4px, transparent 6px);
  background-size: 100% 100%;
  animation: gentleStarTwinkle 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
  border-radius: inherit;
}

.status-filter-btn.selected.all:hover {
  background: linear-gradient(135deg, #3f91d4 0%, #459dd8 25%, #60a5fa 50%, #93c5fd 75%, #dbeafe 100%);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.6), 0 0 40px rgba(255, 255, 255, 0.5), 0 0 60px rgba(59, 130, 246, 0.3), inset 0 2px 0 rgba(255, 255, 255, 0.4);
  transform: translateY(-2px) scale(1.02);
  border-color: rgba(31, 113, 185, 0.8);
}

/* 确保按钮内容在星光特效之上 */
.status-filter-btn.selected.all > * {
  position: relative;
  z-index: 2;
}

/* 成功按钮 - 选中状态 - 浅绿色渐变 */
.status-filter-btn.selected.success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.25) 100%);
  color: #047857;
  box-shadow: 0 4px 16px rgba(16, 185, 129, 0.12), 0 2px 8px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.status-filter-btn.selected.success:hover {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.2) 0%, rgba(5, 150, 105, 0.3) 100%);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.15), 0 3px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* 失败按钮 - 选中状态 - 浅橙色渐变 */
.status-filter-btn.selected.error {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.25) 100%);
  color: #b45309;
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.12), 0 2px 8px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.status-filter-btn.selected.error:hover {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.2) 0%, rgba(217, 119, 6, 0.3) 100%);
  box-shadow: 0 6px 20px rgba(245, 158, 11, 0.15), 0 3px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* 处理中按钮 - 选中状态 - 浅紫色渐变 */
.status-filter-btn.selected.processing {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.15) 0%, rgba(124, 58, 237, 0.25) 100%);
  color: #6d28d9;
  box-shadow: 0 4px 16px rgba(139, 92, 246, 0.12), 0 2px 8px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

.status-filter-btn.selected.processing:hover {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2) 0%, rgba(124, 58, 237, 0.3) 100%);
  box-shadow: 0 6px 20px rgba(139, 92, 246, 0.15), 0 3px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
}

/* 未选中状态 - 统一样式 */
.status-filter-btn.unselected {
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.95) 100%);
  color: #64748b;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
}

.status-filter-btn.unselected:hover {
  background: linear-gradient(135deg, rgba(241, 245, 249, 0.95) 0%, rgba(226, 232, 240, 1) 100%);
  color: #475569;
  border-color: rgba(59, 130, 246, 0.2);
  box-shadow: 0 4px 12px rgba(100, 116, 139, 0.12), 0 2px 6px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

/* 数字徽章优化 */
.status-filter-btn .count-badge {
  background: rgba(255, 255, 255, 0.25);
  color: inherit;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 700;
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  min-width: 20px;
  text-align: center;
}

.status-filter-btn.unselected .count-badge {
  background: rgba(100, 116, 139, 0.1);
  color: #64748b;
  border-color: rgba(100, 116, 139, 0.2);
}

/* 视图模式切换器 - Switch样式 */
.view-mode-switcher {
  display: flex;
  background: rgba(241, 245, 249, 0.8);
  border-radius: 12px;
  padding: 4px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 8px rgba(100, 116, 139, 0.08);
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

.view-mode-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  background: transparent;
  color: #64748b;
}

.view-mode-btn svg {
  width: 14px;
  height: 14px;
  stroke-width: 2;
}

.view-mode-btn:hover {
  color: #475569;
  background: rgba(255, 255, 255, 0.5);
}

.view-mode-btn.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(37, 99, 235, 0.25) 100%);
  color: #1d4ed8;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.12);
  transform: translateY(-1px);
}

.view-mode-btn.active:hover {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2) 0%, rgba(37, 99, 235, 0.3) 100%);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* 状态按钮微妙的星光特效 */
.status-filter-btn.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.4) 1px, transparent 2px),
    radial-gradient(circle at 75% 25%, rgba(255, 255, 255, 0.3) 1px, transparent 2px),
    radial-gradient(circle at 25% 75%, rgba(255, 255, 255, 0.4) 1px, transparent 2px),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.3) 1px, transparent 2px);
  animation: statusStarTwinkle 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
  border-radius: inherit;
}

@keyframes statusStarTwinkle {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

/* 状态按钮激活时的脉冲效果 */
.status-filter-btn.selected:active {
  transform: translateY(0) scale(0.98);
  transition: transform 0.1s ease;
}

/* 确保按钮内容在特效之上 */
.status-filter-btn > * {
  position: relative;
  z-index: 2;
}

/* 状态按钮的微妙呼吸效果 */
.status-filter-btn.selected.all {
  animation: blueBreathing 4s ease-in-out infinite;
}

.status-filter-btn.selected.success {
  animation: greenBreathing 4s ease-in-out infinite;
}

.status-filter-btn.selected.error {
  animation: orangeBreathing 4s ease-in-out infinite;
}

.status-filter-btn.selected.processing {
  animation: purpleBreathing 4s ease-in-out infinite;
}

@keyframes blueBreathing {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4), 0 3px 12px rgba(0, 0, 0, 0.15);
  }
}

@keyframes greenBreathing {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(16, 185, 129, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4), 0 3px 12px rgba(0, 0, 0, 0.15);
  }
}

@keyframes orangeBreathing {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(245, 158, 11, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 6px 20px rgba(245, 158, 11, 0.4), 0 3px 12px rgba(0, 0, 0, 0.15);
  }
}

@keyframes purpleBreathing {
  0%, 100% {
    box-shadow: 0 4px 16px rgba(139, 92, 246, 0.3), 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  50% {
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4), 0 3px 12px rgba(0, 0, 0, 0.15);
  }
}

.results-filter-btn-success-active {
  background: var(--color-success-gradient);
  color: var(--color-white);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(100, 181, 246, 0.25);
}

.results-filter-btn-success-inactive {
  background: var(--color-success-light);
  color: var(--color-success);
  transform: scale(1);
  box-shadow: 0 1px 3px rgba(100, 181, 246, 0.12);
}

.results-filter-btn-error-active {
  background: var(--color-error-gradient);
  color: var(--color-gray-700);
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.20);
  opacity: 1;
}

.results-filter-btn-error-inactive {
  background: var(--color-error-light);
  color: var(--color-gray-600);
  transform: scale(1);
  box-shadow: 0 1px 3px rgba(148, 163, 184, 0.12);
  opacity: 0.85;
}

.results-filter-btn-processing-active {
  background: var(--color-warning-main);
  color: var(--color-white);
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.25);
}

.results-filter-btn-processing-inactive {
  background: var(--color-warning-light);
  color: var(--color-warning-main);
  transform: scale(1);
  box-shadow: 0 1px 3px rgba(255, 193, 7, 0.12);
}

.results-filter-badge {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 9999px;
  padding: 0.125rem var(--space-2);
  font-size: 0.75rem;
  font-family: 'Courier New', monospace;
}

/* 结果复制按钮 */
.results-copy-btn {
  padding: var(--space-2) var(--space-4);
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--space-2);
  border: none;
  cursor: pointer;
}

.results-copy-btn-normal {
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  color: var(--color-white);
  box-shadow: 0 2px 8px rgba(35, 146, 239, 0.20);
}

.results-copy-btn-success {
  background: var(--color-success-gradient);
  color: var(--color-primary-800);
  box-shadow: 0 4px 12px rgba(35, 146, 239, 0.25);
}

.results-copy-btn-normal:hover {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
}

.results-copy-icon {
  height: 1rem;
  width: 1rem;
}

/* 结果项卡片 */
.results-item-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(100, 116, 139, 0.12);
  padding: var(--space-4);
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.results-item-card:hover {
  transform: translateY(-1px);
}

.results-item-card-success {
  background: var(--color-success-highlight);
  border-color: rgba(100, 181, 246, 0.25);
  box-shadow: 0 2px 8px rgba(100, 181, 246, 0.15);
}

.results-item-card-success:hover {
  border-color: var(--color-primary-400);
  box-shadow: 0 4px 16px rgba(100, 181, 246, 0.25);
}

.results-item-card-error {
  background: var(--color-error-disabled);
  border-color: rgba(203, 213, 225, 0.6);
  box-shadow: 0 1px 3px rgba(148, 163, 184, 0.08);
  opacity: 0.9;
}

.results-item-card-error:hover {
  border-color: #F0E68C;
  box-shadow: 0 2px 8px rgba(240, 230, 140, 0.35);
}

.results-item-card-processing {
  background: linear-gradient(135deg, var(--color-warning-light) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(255, 213, 79, 0.4);
  box-shadow: 0 2px 8px rgba(255, 213, 79, 0.15);
}

.results-item-card-processing:hover {
  border-color: var(--color-warning-main);
  box-shadow: 0 4px 16px rgba(255, 213, 79, 0.25);
}

.results-item-card-pending {
  background: linear-gradient(135deg, var(--color-gray-50) 0%, rgba(255, 255, 255, 0.95) 100%);
  border-color: rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.10);
}

.results-item-card-pending:hover {
  border-color: var(--color-gray-400);
  box-shadow: 0 4px 16px rgba(148, 163, 184, 0.20);
}

/* 结果状态图标 */
.results-status-icon {
  flex-shrink: 0;
  padding: var(--space-2);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(100, 116, 139, 0.12);
}

.results-status-icon-success {
  background: var(--color-success-highlight);
  color: var(--color-success);
  border: 1px solid rgba(100, 181, 246, 0.4);
  box-shadow: 0 2px 8px rgba(100, 181, 246, 0.25);
}

.results-status-icon-error {
  background: var(--color-error-disabled);
  color: var(--color-gray-500);
  border: 1px solid rgba(203, 213, 225, 0.4);
  box-shadow: 0 1px 3px rgba(148, 163, 184, 0.15);
  opacity: 0.8;
}

.results-status-icon-processing {
  background: linear-gradient(135deg, var(--color-warning-light) 0%, rgba(255, 255, 255, 0.9) 100%);
  color: var(--color-warning-dark);
  border: 1px solid rgba(255, 213, 79, 0.4);
  box-shadow: 0 2px 8px rgba(255, 213, 79, 0.25);
}

.results-status-icon-pending {
  background: linear-gradient(135deg, var(--color-gray-50) 0%, rgba(255, 255, 255, 0.9) 100%);
  color: var(--color-gray-500);
  border: 1px solid rgba(226, 232, 240, 0.4);
  box-shadow: 0 2px 8px rgba(148, 163, 184, 0.15);
}

.results-query-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-gray-900);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.results-process-time {
  font-size: 0.75rem;
  padding: var(--space-1) var(--space-2);
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  flex-shrink: 0;
  background: var(--color-gray-100);
  color: var(--color-gray-600);
}

/* 结果预览按钮 */
.results-preview-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  border-radius: 8px;
  border: 1px solid;
  transition: all 0.3s ease;
  font-size: 0.875rem;
  font-weight: 500;
  transform: scale(1);
  backdrop-filter: blur(8px);
  text-decoration: none;
}

.results-preview-btn-normal {
  background: var(--color-success-gradient);
  color: var(--color-success);
  border-color: rgba(100, 181, 246, 0.6);
  box-shadow: 0 4px 12px rgba(100, 181, 246, 0.25);
}

.results-preview-btn:hover {
  background: linear-gradient(135deg, var(--color-primary-100) 0%, var(--color-primary-200) 50%, var(--color-primary-300) 100%);
  box-shadow: 0 6px 20px rgba(100, 181, 246, 0.35);
  transform: scale(1.05) translateY(-2px);
}

.results-preview-icon {
  height: 1rem;
  width: 1rem;
}

.results-preview-indicator {
  width: 8px;
  height: 8px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
}

.results-error-btn {
  padding: var(--space-2);
  border-radius: 8px;
  border: 1px solid;
  cursor: not-allowed;
  background: var(--color-error-disabled);
  color: var(--color-gray-500);
  border-color: rgba(203, 213, 225, 0.6);
  box-shadow: 0 1px 3px rgba(148, 163, 184, 0.15);
  opacity: 0.8;
}

.results-error-icon {
  height: 1rem;
  width: 1rem;
}

/* 结果过滤器空状态 */
.results-filter-empty {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.results-filter-empty-content {
  text-align: center;
  padding: 3rem 0;
}

.results-filter-empty-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.results-filter-empty-icon-gradient {
  background: linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
}

.results-filter-empty-icon-svg {
  height: 2rem;
  width: 2rem;
  color: var(--color-gray-400);
}

.results-filter-empty-title {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--color-gray-700);
  margin-bottom: var(--space-2);
}

.results-filter-empty-description {
  font-size: 0.875rem;
  color: var(--color-gray-500);
  margin-bottom: var(--space-4);
}

.results-filter-empty-btn {
  padding: var(--space-2) var(--space-4);
  border-radius: 8px;
  transition: background-color 0.2s;
  background-color: var(--color-primary-500);
  color: var(--color-white);
  border: none;
  cursor: pointer;
}

.results-filter-empty-btn:hover {
  background-color: var(--color-primary-600);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎬 ANIMATIONS - 动画
 * ═══════════════════════════════════════════════════════════════════════════ */

@keyframes historyPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 1024px) {
  .panel-header {
    padding: var(--space-4);
  }
  
  .panel-scrollable {
    padding: var(--space-3);
  }
  
  .history-panel-header {
    padding: var(--space-4);
  }
  
  .results-header {
    padding: var(--space-4);
  }
}

@media (max-width: 768px) {
  .panel-header {
    padding: var(--space-3);
  }
  
  .panel-scrollable {
    padding: var(--space-2);
  }
  
  .history-panel-header {
    padding: var(--space-3);
  }
  
  .results-header {
    padding: var(--space-3);
  }
  
  .query-input-card {
    border-radius: 10px;
  }
  
  .query-input-card .rounded-lg,
  .query-input-card .rounded {
    border-radius: 6px;
  }
  
  .results-item-card {
    padding: var(--space-3);
  }
  
  .results-filter-btn {
    padding: var(--space-2) var(--space-3);
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .panel-header {
    padding: var(--space-2);
  }
  
  .panel-scrollable {
    padding: var(--space-1);
  }
  
  .history-record-item,
  .history-record-item-modern {
    padding: 10px 12px;
  }
  
  .query-input-textarea {
    min-height: 120px;
  }
  
  .results-item-card {
    padding: var(--space-2);
  }
  
  .results-empty-icon {
    width: 4rem;
    height: 4rem;
  }
  
  .results-empty-title {
    font-size: 1.125rem;
  }
}

/* 小屏幕hover效果调整 */
@media (hover: none) {
  .query-input-card:hover {
    transform: none;
  }
  
  .history-record-item:hover,
  .history-record-item-modern:hover {
    transform: none;
  }
  
  .results-item-card:hover {
    transform: none;
  }
  
  .results-preview-btn:hover {
    transform: none;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY - 无障碍
 * ═══════════════════════════════════════════════════════════════════════════ */

.panel:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.query-input-textarea:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.results-filter-btn:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.results-preview-btn:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.history-action-btn:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .panel,
  .query-input-card,
  .history-record-item,
  .results-item-card,
  .results-filter-btn,
  .results-preview-btn,
  .history-action-btn {
    transition: none;
  }
  
  .query-input-processing-spinner {
    animation: none;
  }
  
  .history-stats-text {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .panel,
  .query-input-card,
  .history-record-item,
  .results-item-card {
    border-width: 2px;
  }
  
  .results-filter-btn,
  .results-preview-btn,
  .history-action-btn {
    border-width: 2px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .results-filter-btn,
  .results-preview-btn,
  .history-action-btn,
  .query-input-btn-sample,
  .query-input-btn-clear {
    min-height: 44px;
    min-width: 44px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌟 STAR TWINKLE ANIMATIONS - 星光闪烁动画
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 温和的星光闪烁动画 */
@keyframes gentleStarTwinkle {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}