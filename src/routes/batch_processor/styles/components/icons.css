/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🎨 ICON SYSTEM - 图标系统
 * ═══════════════════════════════════════════════════════════════════════════════
 * 
 * 统一的图标颜色和样式系统
 * 提供语义化的图标颜色类
 * 
 * @version 3.0 - 重构版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces design-system/icons.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 BASE ICON STYLES - 基础图标样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  stroke-width: 2;
  fill: none;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎬 ICON ANIMATIONS - 图标动画
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 图标专用旋转动画 - 避免与其他组件动画冲突 */
@keyframes iconSpin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* 增强优先级确保动画正常工作 */
.icon-spin,
.icon.icon-spin,
svg.icon.icon-spin {
  animation: iconSpin 1s linear infinite !important;
  transform-origin: center center !important;
  will-change: transform !important;
}
/* ═══════════════════════════════════════════════════════════════════════════
 * 📏 ICON SIZES - 图标尺寸
 * ═══════════════════════════════════════════════════════════════════════════ */

.icon-xs {
  width: 12px;
  height: 12px;
}

.icon-sm {
  width: 16px;
  height: 16px;
}

.icon-md {
  width: 20px;
  height: 20px;
}

.icon-lg {
  width: 24px;
  height: 24px;
}

.icon-xl {
  width: 28px;
  height: 28px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 ICON COLORS - 图标颜色
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 主要颜色 */
.icon-primary {
  color: var(--color-primary-600);
}

.icon-primary:hover {
  color: var(--color-primary-700);
  transform: scale(1.05);
}

/* 标题专用深蓝色 - 用于"状态概览"、"处理统计"等标题 */
.icon-title {
  color: var(--color-primary-700) !important;
}

.icon-title:hover {
  color: var(--color-primary-800) !important;
  transform: scale(1.05);
}

/* 次要颜色 */
.icon-secondary {
  color: var(--color-blue-600);
}

.icon-secondary:hover {
  color: var(--color-blue-700);
  transform: scale(1.05);
}

/* 状态颜色 */
.icon-success {
  color: var(--color-success);
}

.icon-success:hover {
  color: var(--color-success-light);
  transform: scale(1.05);
}

.icon-warning {
  color: var(--color-warning);
}

.icon-warning:hover {
  color: var(--color-warning-light);
  transform: scale(1.05);
}

.icon-error {
  color: var(--color-error);
}

.icon-error:hover {
  color: var(--color-error-light);
  transform: scale(1.05);
}

.icon-processing {
  color: var(--color-processing);
}

.icon-processing:hover {
  color: var(--color-processing-light);
  transform: scale(1.05);
}

/* 中性颜色 */
.icon-neutral {
  color: var(--color-gray-500);
}

.icon-neutral:hover {
  color: var(--color-gray-600);
  transform: scale(1.05);
}

.icon-gray {
  color: var(--color-gray-400);
}

.icon-gray:hover {
  color: var(--color-gray-500);
  transform: scale(1.05);
}

/* 特殊颜色 */
.icon-white {
  color: var(--color-white);
}

.icon-white:hover {
  color: var(--color-gray-100);
  transform: scale(1.05);
}

.icon-blue {
  color: var(--color-blue-500);
}

.icon-blue:hover {
  color: var(--color-blue-600);
  transform: scale(1.05);
}

.icon-green {
  color: var(--color-success);
}

.icon-green:hover {
  color: var(--color-success-light);
  transform: scale(1.05);
}

.icon-red {
  color: var(--color-error);
}

.icon-red:hover {
  color: var(--color-error-light);
  transform: scale(1.05);
}

.icon-purple {
  color: var(--color-blue-600);
}

.icon-purple:hover {
  color: var(--color-blue-700);
  transform: scale(1.05);
}

.icon-accent {
  color: var(--color-warning);
}

.icon-accent:hover {
  color: var(--color-warning-light);
  transform: scale(1.05);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 SPECIAL EFFECTS - 特殊效果
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 发光效果 */
.icon-glow {
  filter: drop-shadow(0 0 4px currentColor);
}

.icon-glow:hover {
  filter: drop-shadow(0 0 8px currentColor);
}

/* 阴影效果 */
.icon-shadow {
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.icon-shadow:hover {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.15));
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 14寸笔记本屏幕优化 - 与标题容器尺寸保持一致 */
@media (min-width: 1366px) and (max-width: 1919px) {
  .icon-xs {
    width: 10px;
    height: 10px;
  }

  .icon-sm {
    width: 14px;
    height: 14px;
  }

  .icon-md {
    width: 16px;
    height: 16px;
  }

  .icon-lg {
    width: 20px;
    height: 20px;
  }

  .icon-xl {
    width: 24px;
    height: 24px;
  }
}

@media (max-width: 768px) {
  .icon-xs {
    width: 10px;
    height: 10px;
  }

  .icon-sm {
    width: 14px;
    height: 14px;
  }

  .icon-md {
    width: 18px;
    height: 18px;
  }

  .icon-lg {
    width: 22px;
    height: 22px;
  }

  .icon-xl {
    width: 26px;
    height: 26px;
  }
}