/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📊 UNIFIED INDICATORS SYSTEM - 统一指示器系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了modules/status-indicators.css + status-cards-optimization.css
 * 提供完整的状态指示器和卡片解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces modules/status-indicators.css + status-cards-optimization.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔵 BASE STATUS INDICATORS - 基础状态指示器
 * ═══════════════════════════════════════════════════════════════════════════ */

.status-indicator {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-indicator::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: inherit;
  opacity: 0.2;
  transform: scale(0.8);
  transition: all 0.3s ease;
}

.status-indicator:hover::before {
  transform: scale(1.2);
  opacity: 0.3;
}

/* 成功状态指示器 */
.status-indicator--success {
  background: linear-gradient(135deg, #87ceeb 0%, #059669 50%, #047857 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4),
    0 2px 6px rgba(16, 185, 129, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: successPulse 2s ease-in-out infinite;
}

.status-indicator--success::before {
  background: linear-gradient(135deg, #87ceeb, #059669);
}

.status-indicator--success:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(16, 185, 129, 0.5),
    0 3px 10px rgba(16, 185, 129, 0.4);
}

/* 错误状态指示器 */
.status-indicator--error {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 50%, #b91c1c 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.4),
    0 2px 6px rgba(239, 68, 68, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: errorShake 0.5s ease-in-out;
}

.status-indicator--error::before {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.status-indicator--error:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(239, 68, 68, 0.5),
    0 3px 10px rgba(239, 68, 68, 0.4);
}

/* 处理中状态指示器 */
.status-indicator--processing {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 50%, #b45309 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(245, 158, 11, 0.4),
    0 2px 6px rgba(245, 158, 11, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  animation: processingRotate 1.5s linear infinite;
}

.status-indicator--processing::before {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.status-indicator--processing::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 120%;
  height: 120%;
  border: 2px solid transparent;
  border-top: 2px solid rgba(245, 158, 11, 0.6);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: processingSpinner 1s linear infinite;
}

/* 等待状态指示器 */
.status-indicator--pending {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 50%, #374151 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(107, 114, 128, 0.3),
    0 1px 4px rgba(107, 114, 128, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  animation: pendingBreathe 3s ease-in-out infinite;
}

.status-indicator--pending::before {
  background: linear-gradient(135deg, #6b7280, #4b5563);
}

/* 指示器尺寸变体 */
.status-indicator--sm {
  width: 16px;
  height: 16px;
  font-size: 8px;
}

.status-indicator--md {
  width: 24px;
  height: 24px;
  font-size: 12px;
}

.status-indicator--lg {
  width: 32px;
  height: 32px;
  font-size: 16px;
}

.status-indicator--xl {
  width: 40px;
  height: 40px;
  font-size: 20px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📊 STATUS BADGES - 状态徽章
 * ═══════════════════════════════════════════════════════════════════════════ */

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.status-badge--success {
  background: linear-gradient(135deg, rgba(16, 185, 129, 0.15) 0%, rgba(5, 150, 105, 0.2) 100%);
  color: #047857;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-badge--error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.15) 0%, rgba(220, 38, 38, 0.2) 100%);
  color: #b91c1c;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.status-badge--processing {
  background: linear-gradient(135deg, rgba(245, 158, 11, 0.15) 0%, rgba(217, 119, 6, 0.2) 100%);
  color: #b45309;
  border: 1px solid rgba(245, 158, 11, 0.3);
  animation: processingGlow 2s ease-in-out infinite;
}

.status-badge--pending {
  background: linear-gradient(135deg, rgba(107, 114, 128, 0.15) 0%, rgba(75, 85, 99, 0.2) 100%);
  color: #374151;
  border: 1px solid rgba(107, 114, 128, 0.3);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🃏 STATUS CARDS - 状态卡片
 * ═══════════════════════════════════════════════════════════════════════════ */

.status-cards-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 16px;
  padding: 0;
}

.status-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 12px 8px;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  min-height: 64px;
}

.status-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 0, 0, 0.12);
}

/* 状态卡片变体 */
.status-card-total {
  --status-card-accent: var(--color-blue-600, #2563eb);
  background: linear-gradient(135deg, 
    var(--color-blue-50, #eff6ff) 0%, 
    var(--color-blue-100, #dbeafe) 100%);
  border-color: var(--color-blue-200, #bfdbfe);
}

.status-card-total:hover {
  background: linear-gradient(135deg, 
    var(--color-blue-100, #dbeafe) 0%, 
    var(--color-blue-200, #bfdbfe) 100%);
  border-color: var(--color-blue-300, #93c5fd);
}

.status-card-success {
  --status-card-accent: var(--color-blue-600, #2563eb);
  background: linear-gradient(135deg, 
    var(--color-blue-50, #eff6ff) 0%, 
    rgba(59, 130, 246, 0.1) 100%);
  border-color: var(--color-blue-200, #bfdbfe);
}

.status-card-success:hover {
  background: linear-gradient(135deg, 
    var(--color-blue-100, #dbeafe) 0%, 
    rgba(59, 130, 246, 0.15) 100%);
  border-color: var(--color-blue-300, #93c5fd);
}

.status-card-error {
  --status-card-accent: var(--color-error, #ef4444);
  background: linear-gradient(135deg, 
    var(--color-blue-50, #eff6ff) 0%, 
    rgba(239, 68, 68, 0.08) 100%);
  border-color: var(--color-blue-200, #bfdbfe);
}

.status-card-error:hover {
  background: linear-gradient(135deg, 
    var(--color-blue-100, #dbeafe) 0%, 
    rgba(239, 68, 68, 0.12) 100%);
  border-color: var(--color-blue-300, #93c5fd);
}

.status-card-waiting {
  --status-card-accent: var(--color-warning, #f59e0b);
  background: linear-gradient(135deg, 
    var(--color-blue-50, #eff6ff) 0%, 
    rgba(245, 158, 11, 0.08) 100%);
  border-color: var(--color-blue-200, #bfdbfe);
}

.status-card-waiting:hover {
  background: linear-gradient(135deg, 
    var(--color-blue-100, #dbeafe) 0%, 
    rgba(245, 158, 11, 0.12) 100%);
  border-color: var(--color-blue-300, #93c5fd);
}

/* 状态卡片内容 */
.status-card-value {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
  color: var(--status-card-accent);
  transition: all 0.3s ease;
}

.status-card-total .status-card-value {
  color: var(--color-blue-600, #2563eb);
}

.status-card-success .status-card-value {
  color: var(--color-blue-600, #2563eb);
}

.status-card-error .status-card-value {
  color: var(--color-error, #ef4444);
}

.status-card-waiting .status-card-value {
  color: var(--color-warning, #f59e0b);
}

.status-card-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  margin-top: 2px;
  text-align: center;
  transition: all 0.3s ease;
}

.status-card:hover .status-card-label {
  color: #4b5563;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📈 PROGRESS BARS - 进度条
 * ═══════════════════════════════════════════════════════════════════════════ */

.progress-bar-container {
  width: 100%;
  height: 8px;
  background: rgba(229, 231, 235, 0.6);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
  margin-top: 12px;
}

.progress-bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 4px;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-width: 2px;
}

.progress-bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%);
  animation: progressShine 2s infinite;
}

.progress-bar-success .progress-bar-fill {
  background: linear-gradient(90deg, #10b981 0%, #059669 100%);
}

.progress-bar-error .progress-bar-fill {
  background: linear-gradient(90deg, #ef4444 0%, #dc2626 100%);
}

.progress-bar-mixed .progress-bar-fill {
  background: linear-gradient(90deg, #10b981 0%, #f59e0b 50%, #ef4444 100%);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 PASTEL PROGRESS BARS - 柔和进度条 (ProgressDisplay组件专用)
 * ═══════════════════════════════════════════════════════════════════════════ */

.pastel-progress-container {
  width: 100% !important;
  height: 6px !important;
  background: rgba(229, 231, 235, 0.4) !important;
  border-radius: 3px !important;
  overflow: hidden !important;
  position: relative !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
}

.pastel-progress-container .pastel-progress-bar {
  height: 100% !important;
  border-radius: 3px !important;
  transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  min-width: 2px !important;
}

/* 默认进度条 - 蓝色渐变 */
.batch-processor-layout .pastel-progress-container .pastel-progress-bar.progress-default,
.progress-display .pastel-progress-container .pastel-progress-bar.progress-default,
.pastel-progress-container .pastel-progress-bar.progress-default {
  background: linear-gradient(90deg, #6090dc 0%, #55b6d9 100%) !important;
  box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3) !important;
}

/* 成功进度条 - 绿色渐变 */
.batch-processor-layout .pastel-progress-container .pastel-progress-bar.progress-success,
.progress-display .pastel-progress-container .pastel-progress-bar.progress-success,
.pastel-progress-container .pastel-progress-bar.progress-success {
  background: linear-gradient(90deg, #42b7c4 0%, #1cd8db 100%) !important;
  box-shadow: 0 1px 3px rgba(16, 185, 129, 0.3) !important;
}

/* 错误进度条 - 红色渐变 */
.batch-processor-layout .pastel-progress-container .pastel-progress-bar.progress-error,
.progress-display .pastel-progress-container .pastel-progress-bar.progress-error,
.pastel-progress-container .pastel-progress-bar.progress-error {
  background: linear-gradient(90deg, #d2ce5d 0%, #f8ce5a 100%) !important;
  box-shadow: 0 1px 3px rgba(239, 68, 68, 0.3) !important;
}

/* 混合进度条 - 多色渐变 */
.batch-processor-layout .pastel-progress-container .pastel-progress-bar.progress-mixed,
.progress-display .pastel-progress-container .pastel-progress-bar.progress-mixed,
.pastel-progress-container .pastel-progress-bar.progress-mixed {
  background: linear-gradient(90deg, #5c97ca 0%, #1d88df 50%, #258bbe 100%) !important;
  box-shadow: 0 1px 3px rgba(245, 158, 11, 0.3) !important;
}

/* 进度条光泽效果 */
.pastel-progress-container .pastel-progress-bar::after {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%) !important;
  animation: progressShine 2s infinite !important;
  border-radius: 3px !important;
}

/* 活跃状态的进度条 */
.pastel-progress-container .pastel-progress-bar.progress-active {
  animation: progressGlow 2s ease-in-out infinite !important;
}

/* 进度条动画 */
@keyframes progressGlow {
  0%, 100% {
    box-shadow: 0 1px 3px rgba(59, 130, 246, 0.3);
  }
  50% {
    box-shadow: 0 1px 6px rgba(59, 130, 246, 0.5), 0 0 12px rgba(59, 130, 246, 0.2);
  }
}

/* 进度显示标签 */
.progress-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--color-gray-700, #374151);
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label-success {
  color: var(--color-blue-600, #2563eb);
}

.progress-label-primary {
  color: var(--color-blue-600, #2563eb);
}

.progress-percentage {
  font-size: 0.75rem;
  font-weight: 700;
  color: var(--color-gray-600, #6b7280);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 ANIMATIONS - 动画
 * ═══════════════════════════════════════════════════════════════════════════ */

@keyframes successPulse {
  0%, 100% { 
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.4),
      0 2px 6px rgba(16, 185, 129, 0.3);
  }
  50% { 
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6),
      0 3px 10px rgba(16, 185, 129, 0.5);
  }
}

@keyframes errorShake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

@keyframes processingRotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes processingSpinner {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

@keyframes processingGlow {
  0%, 100% { 
    box-shadow: 0 0 5px rgba(245, 158, 11, 0.3);
  }
  50% { 
    box-shadow: 0 0 15px rgba(245, 158, 11, 0.6);
  }
}

@keyframes pendingBreathe {
  0%, 100% { 
    opacity: 0.7;
    transform: scale(1);
  }
  50% { 
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes progressShine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 LEGACY COMPATIBILITY - 兼容现有样式
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 兼容现有的网格卡片样式 */
.batch-processor-layout .glass-card .grid.grid-cols-2 > div {
  background: transparent;
  border-radius: 8px;
  padding: 12px 8px;
  text-align: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
  min-height: 64px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
  border-color: rgba(0, 0, 0, 0.12);
}

/* 总数卡片 */
.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(1) {
  background: linear-gradient(135deg, 
    var(--color-blue-50, #eff6ff) 0%, 
    var(--color-blue-100, #dbeafe) 100%);
  border-color: var(--color-blue-200, #bfdbfe);
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(1):hover {
  background: linear-gradient(135deg, 
    var(--color-blue-100, #dbeafe) 0%, 
    var(--color-blue-200, #bfdbfe) 100%);
  border-color: var(--color-blue-300, #93c5fd);
}

/* 成功卡片 */
.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(2) {
  background: linear-gradient(135deg, 
    var(--color-blue-50, #eff6ff) 0%, 
    rgba(59, 130, 246, 0.1) 100%);
  border-color: var(--color-blue-200, #bfdbfe);
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(2):hover {
  background: linear-gradient(135deg, 
    var(--color-blue-100, #dbeafe) 0%, 
    rgba(59, 130, 246, 0.15) 100%);
  border-color: var(--color-blue-300, #93c5fd);
}

/* 失败卡片 */
.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(3) {
  background: linear-gradient(135deg, 
    var(--color-blue-50, #eff6ff) 0%, 
    rgba(239, 68, 68, 0.08) 100%);
  border-color: var(--color-blue-200, #bfdbfe);
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(3):hover {
  background: linear-gradient(135deg, 
    var(--color-blue-100, #dbeafe) 0%, 
    rgba(239, 68, 68, 0.12) 100%);
  border-color: var(--color-blue-300, #93c5fd);
}

/* 等待卡片 */
.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(4) {
  background: linear-gradient(135deg, 
    var(--color-blue-50, #eff6ff) 0%, 
    rgba(245, 158, 11, 0.08) 100%);
  border-color: var(--color-blue-200, #bfdbfe);
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(4):hover {
  background: linear-gradient(135deg, 
    var(--color-blue-100, #dbeafe) 0%, 
    rgba(245, 158, 11, 0.12) 100%);
  border-color: var(--color-blue-300, #93c5fd);
}

/* 卡片数值样式 */
.batch-processor-layout .glass-card .grid.grid-cols-2 > div .text-lg {
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
  transition: all 0.3s ease;
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(1) .text-lg {
  color: var(--color-blue-600, #2563eb);
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(2) .text-lg {
  color: var(--color-blue-600, #2563eb);
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(3) .text-lg {
  color: var(--color-error, #ef4444);
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:nth-child(4) .text-lg {
  color: var(--color-warning, #f59e0b);
}

/* 卡片标签样式 */
.batch-processor-layout .glass-card .grid.grid-cols-2 > div .text-xs {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--color-gray-600, #6b7280);
  margin-top: 2px;
  transition: all 0.3s ease;
}

.batch-processor-layout .glass-card .grid.grid-cols-2 > div:hover .text-xs {
  color: var(--color-gray-700, #374151);
}

/* ProgressDisplay组件优化 */
.progress-display .flex.justify-between.text-xs {
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: var(--color-gray-700, #374151);
}

.progress-display .flex.justify-between.text-xs span:first-child {
  font-weight: 600;
}

.progress-display .flex.justify-between.text-xs span:last-child {
  font-weight: 700;
  color: var(--color-gray-600, #6b7280);
}

/* 主内容布局修复 */
#main-content .grid.grid-cols-1.md\:grid-cols-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

#main-content .grid.grid-cols-1.md\:grid-cols-3 h1,
#main-content .grid.grid-cols-1.md\:grid-cols-3 h2,
#main-content .grid.grid-cols-1.md\:grid-cols-3 h3,
#main-content .grid.grid-cols-1.md\:grid-cols-3 h4,
#main-content .grid.grid-cols-1.md\:grid-cols-3 h5,
#main-content .grid.grid-cols-1.md\:grid-cols-3 h6 {
  font-size: 0.9375rem;
  font-weight: 500;
  color: var(--color-gray-600, #4b5563);
}



#main-content .grid.grid-cols-1.md\:grid-cols-3 a,
#main-content .grid.grid-cols-1.md\:grid-cols-3 button {
  font-size: 0.8125rem;
  font-weight: 400;
  color: var(--color-gray-400, #9ca3af);
  text-decoration: none;
  border: none;
  background: transparent;
  transition: color 0.2s ease;
}

#main-content .grid.grid-cols-1.md\:grid-cols-3 a:hover,
#main-content .grid.grid-cols-1.md\:grid-cols-3 button:hover {
  color: var(--color-gray-500, #6b7280);
}

#main-content .grid.grid-cols-1.md\:grid-cols-3 * {
  text-shadow: 0 0 1px rgba(107, 114, 128, 0.1);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌙 DARK MODE SUPPORT - 深色模式支持
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (prefers-color-scheme: dark) {
  .status-badge--success {
    background: linear-gradient(135deg, rgba(16, 185, 129, 0.25) 0%, rgba(5, 150, 105, 0.3) 100%);
    color: #87ceeb;
  }
  
  .status-badge--error {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.25) 0%, rgba(220, 38, 38, 0.3) 100%);
    color: #ef4444;
  }
  
  .status-badge--processing {
    background: linear-gradient(135deg, rgba(245, 158, 11, 0.25) 0%, rgba(217, 119, 6, 0.3) 100%);
    color: #f59e0b;
  }
  
  .status-badge--pending {
    background: linear-gradient(135deg, rgba(107, 114, 128, 0.25) 0%, rgba(75, 85, 99, 0.3) 100%);
    color: #9ca3af;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 1024px) {
  #main-content .grid.grid-cols-1.md\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .status-indicator {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
  
  .status-badge {
    font-size: 10px;
    padding: 3px 6px;
  }
  
  .status-cards-container {
    gap: 6px;
  }
  
  .status-card {
    padding: 10px 6px;
    min-height: 56px;
  }
  
  .status-card-value {
    font-size: 1.125rem;
  }
  
  .status-card-label {
    font-size: 0.6875rem;
  }
  
  .progress-bar-container {
    height: 6px;
    margin-top: 10px;
  }
/*   
  #main-content .grid.grid-cols-1.md\:grid-cols-3 {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
  }
  
  #main-content .grid.grid-cols-1.md\:grid-cols-3 h1,
  #main-content .grid.grid-cols-1.md\:grid-cols-3 h2,
  #main-content .grid.grid-cols-1.md\:grid-cols-3 h3,
  #main-content .grid.grid-cols-1.md\:grid-cols-3 h4,
  #main-content .grid.grid-cols-1.md\:grid-cols-3 h5,
  #main-content .grid.grid-cols-1.md\:grid-cols-3 h6 {
    font-size: 0.875rem;
    color: var(--color-gray-600, #4b5563);
  }
  
  #main-content .grid.grid-cols-1.md\:grid-cols-3 p,
  #main-content .grid.grid-cols-1.md\:grid-cols-3 span,
  #main-content .grid.grid-cols-1.md\:grid-cols-3 div {
    font-size: 0.75rem;
    color: var(--color-gray-500, #6b7280);
  */
}

@media (max-width: 480px) {
  .status-indicator {
    width: 18px;
    height: 18px;
    font-size: 9px;
  }
  
  .status-badge {
    font-size: 9px;
    padding: 2px 5px;
  }
  
  .status-cards-container {
    gap: 4px;
  }
  
  .status-card {
    padding: 8px 4px;
    min-height: 48px;
  }
  
  .status-card-value {
    font-size: 1rem;
  }
  
  .status-card-label {
    font-size: 0.625rem;
  }
  
  .progress-bar-container {
    height: 4px;
    margin-top: 8px;
  }

}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY - 无障碍
 * ═══════════════════════════════════════════════════════════════════════════ */

.status-indicator:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.status-card:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.status-badge:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .status-card {
    border-width: 2px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  }
  
  .status-card-value {
    font-weight: 800;
  }
  
  .status-indicator {
    border: 2px solid currentColor;
  }
  
  .status-badge {
    border-width: 2px;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .status-indicator,
  .status-card,
  .status-badge,
  .progress-bar-fill,
  .status-card-value,
  .status-card-label {
    animation: none;
    transition: none;
  }
  
  .status-indicator--success,
  .status-indicator--processing,
  .status-indicator--pending {
    animation: none;
  }
  
  .status-badge--processing {
    animation: none;
  }
  
  .progress-bar-fill::after {
    animation: none;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .status-indicator {
    min-width: 44px;
    min-height: 44px;
  }
  
  .status-card {
    min-height: 54px;
  }
  
  .status-badge {
    min-height: 32px;
    padding: 8px 12px;
  }
}