/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🚪 UNIFIED DRAWER SYSTEM - 统一抽屉系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了modules/drawers/base.css + modules/drawers/themes.css
 * 提供完整的抽屉组件解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces modules/drawers/base.css + modules/drawers/themes.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏗️ BASE DRAWER STRUCTURE - 基础抽屉结构
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 抽屉主容器 */
.enhanced-drawer-container {
  height: 100vh;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
  position: absolute;
  left: 0;
  top: 0;
  z-index: 1000;
  
  /* 默认背景 */
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 50%,
    rgba(227, 242, 253, 0.92) 100%);
  
  backdrop-filter: blur(20px);
  border: none;
  
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.08),
    1px 0 10px rgba(59, 130, 246, 0.06),
    0 0 4px rgba(139, 92, 246, 0.04);
  
  /* 更流畅的动画 */
  transition: transform 0.4s cubic-bezier(0.23, 1, 0.32, 1),
    opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    box-shadow 0.25s ease-out;
  
  /* 硬件加速 */
  will-change: transform, opacity;
  transform: translateX(-100%) translateZ(0);
  opacity: 0; /* 初始状态隐藏，防止闪烁 */

  /* 动画性能优化 */
  backface-visibility: hidden;
  perspective: 1000px;
  transform-style: preserve-3d;
}

/* 抽屉打开状态 */
.enhanced-drawer-container.open {
  transform: translateX(0) translateZ(0);
  opacity: 1;
}

/* 抽屉动画状态 */
.drawer-animating {
  pointer-events: none;
}

/* 抽屉头部 - 紧凑优化的间距 */
.enhanced-drawer-header {
  flex-shrink: 0;
  padding: 12px 16px;
  
  background: linear-gradient(135deg,
    rgba(252, 228, 236, 0.4) 0%,
    rgba(255, 255, 255, 0.95) 40%,
    rgba(255, 255, 255, 0.95) 60%,
    rgba(227, 242, 253, 0.4) 100%);
  
  backdrop-filter: blur(15px);
  
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: none;
  
  /* 硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 抽屉标题 - 紧凑优化 */
.enhanced-drawer-title {
  font-size: 1.2rem;
  font-weight: 700;
  margin: 0;
  padding: 6px 10px;
  
  text-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #3b82f6, #8b5cf6, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.015em;
  
  display: flex;
  align-items: center;
  gap: 0.875rem;
  line-height: 1.25;
  border-radius: 9px;
  position: relative;
  
  /* 标题高光动画 */
  animation: titleHighlight 3s ease-in-out infinite;
}

/* 抽屉内容区域 - 紧凑优化的间距 */
.enhanced-drawer-content {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 12px 16px;
  gap: 12px;
  
  /* 内容区域微妙背景 */
  background: radial-gradient(circle at 10% 20%, rgba(255, 255, 255, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(248, 250, 252, 0.3) 0%, transparent 50%);
  
  /* 硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 标题容器 */
.title-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* 操作按钮容器 */
.action-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 14px;
  justify-content: flex-end;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎬 DRAWER ANIMATIONS - 抽屉动画
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 左侧抽屉滑入动画 (从左边滑入) */
@keyframes drawerSlideIn {
  0% {
    transform: translateX(-100%) translateZ(0);
    opacity: 0;
  }
  100% {
    transform: translateX(0) translateZ(0);
    opacity: 1;
  }
}

/* 左侧抽屉滑出动画 (滑出到左边) */
@keyframes drawerSlideOut {
  0% {
    transform: translateX(0) translateZ(0);
    opacity: 1;
  }
  100% {
    transform: translateX(-100%) translateZ(0);
    opacity: 0;
  }
}

/* 右侧抽屉滑入动画 (从右边滑入) */
@keyframes rightDrawerSlideIn {
  0% {
    transform: translateX(100%) translateZ(0);
    opacity: 0;
  }
  100% {
    transform: translateX(0) translateZ(0);
    opacity: 1;
  }
}

/* 右侧抽屉滑出动画 (滑出到右边) */
@keyframes rightDrawerSlideOut {
  0% {
    transform: translateX(0) translateZ(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateZ(0);
    opacity: 0;
  }
}

/* 左侧抽屉滑入动画类 */
.enhanced-drawer-container.slide-in {
  animation: drawerSlideIn 0.4s ease-out forwards;
  animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
}

/* 左侧抽屉滑出动画类 */
.enhanced-drawer-container.slide-out {
  animation: drawerSlideOut 0.3s ease-in forwards;
  animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

/* 右侧抽屉滑入动画类 */
.enhanced-drawer-container.right-slide-in {
  animation: rightDrawerSlideIn 0.4s ease-out forwards;
  animation-timing-function: cubic-bezier(0.23, 1, 0.32, 1);
}

/* 右侧抽屉滑出动画类 */
.enhanced-drawer-container.right-slide-out {
  animation: rightDrawerSlideOut 0.3s ease-in forwards;
  animation-timing-function: cubic-bezier(0.55, 0.085, 0.68, 0.53);
}

/* 右侧定位的抽屉容器 - 使用左侧滑入动画但定位在右侧 */
/* 通过更高的选择器特异性来覆盖基础定位，避免使用 !important */
.enhanced-drawer-container.enhanced-drawer-container.right-positioned {
  left: auto;
  right: 0;
}

/* 标题高光动画 */
@keyframes titleHighlight {
  0%, 100% {
    filter: brightness(1) saturate(1);
    transform: scale(1);
  }
  50% {
    filter: brightness(1.05) saturate(1.1);
    transform: scale(1.002);
  }
}

/* 标题悬停动画 */
@keyframes titleHighlightHover {
  0% {
    filter: brightness(1) saturate(1);
  }
  50% {
    filter: brightness(1.1) saturate(1.2);
  }
  100% {
    filter: brightness(1.05) saturate(1.1);
  }
}

.enhanced-drawer-title:hover {
  animation: titleHighlightHover 0.6s ease-out;
  transform: scale(1.005);
}

/* 滑动指示器 - 位于右边缘 */
.slide-indicator {
  position: absolute;
  right: -6px;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 60px;
  
  background: linear-gradient(180deg,
    rgba(59, 130, 246, 0.9) 0%,
    rgba(37, 99, 235, 0.8) 50%,
    rgba(59, 130, 246, 0.9) 100%);
  
  border-radius: 0 6px 6px 0;
  box-shadow: 2px 0 12px rgba(59, 130, 246, 0.4),
    inset -1px 0 2px rgba(255, 255, 255, 0.3);
  z-index: 10;
}

@keyframes slideIndicatorPulse {
  0% {
    transform: translateY(-50%) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-50%) scale(1.2);
    opacity: 1;
  }
  100% {
    transform: translateY(-50%) scale(1);
    opacity: 0.8;
  }
}

@keyframes slideIndicatorBreath {
  0%, 100% {
    transform: translateY(-50%) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-50%) scale(1.08);
    opacity: 0.9;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 DRAWER OVERLAY - 抽屉遮罩
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 左侧抽屉遮罩 */
.left-drawer-overlay {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(4px);
  transition: opacity 0.35s ease-out, backdrop-filter 0.35s ease-out;
  cursor: pointer;
}

/* 平滑遮罩过渡 */
.smooth-drawer-overlay {
  transition: opacity 0.35s ease-out, backdrop-filter 0.35s ease-out;
  will-change: opacity, backdrop-filter;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 DRAWER BUTTONS - 抽屉按钮
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 快捷工具按钮专用样式 - 轻量浅蓝色渐变 */
.quick-tool-btn,
button.quick-tool-btn,
.btn.quick-tool-btn {
  /* 轻量浅蓝色渐变背景 */
  background: linear-gradient(135deg, #f8fbff 0%, #f0f9ff 50%, #e0f2fe 100%);
  color: #1e40af;
  border: 1px solid rgba(30, 64, 175, 0.15);
  backdrop-filter: blur(8px);
  border-radius: 8px;

  /* 合理的padding - 紧凑但舒适 */
  padding: 8px 12px;
  min-height: 36px;

  /* 轻微的阴影效果 */
  box-shadow: 0 2px 8px rgba(30, 64, 175, 0.08),
    0 1px 3px rgba(30, 64, 175, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);

  /* 文字效果 */
  text-shadow: none;
  font-weight: 500;
  font-size: 13px;

  /* 动画和交互 */
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease-out;
  z-index: 1;

  /* 确保图标和文字对齐 - 与中栏按钮保持一致 */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 确保图标颜色与文字一致 */
.quick-tool-btn .semi-icon,
.quick-tool-btn svg,
button.quick-tool-btn .semi-icon,
button.quick-tool-btn svg,
.btn.quick-tool-btn .semi-icon,
.btn.quick-tool-btn svg {
  color: #1e40af;
  fill: #1e40af;
}

/* 快捷工具按钮悬停效果 */
.quick-tool-btn:hover:not(:disabled),
button.quick-tool-btn:hover:not(:disabled),
.btn.quick-tool-btn:hover:not(:disabled) {
  /* 轻微加深的渐变 */
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
  color: #1d4ed8;
  border-color: rgba(29, 78, 216, 0.2);

  /* 轻微增强的阴影 */
  box-shadow: 0 3px 12px rgba(29, 78, 216, 0.12),
    0 2px 6px rgba(29, 78, 216, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.7);

  /* 轻微上移效果 */
  transform: translateY(-1px);
}

/* 快捷工具按钮悬停时图标颜色 */
.quick-tool-btn:hover:not(:disabled) .semi-icon,
.quick-tool-btn:hover:not(:disabled) svg,
button.quick-tool-btn:hover:not(:disabled) .semi-icon,
button.quick-tool-btn:hover:not(:disabled) svg,
.btn.quick-tool-btn:hover:not(:disabled) .semi-icon,
.btn.quick-tool-btn:hover:not(:disabled) svg {
  color: #1d4ed8;
  fill: #1d4ed8;
}

/* 快捷工具按钮激活效果 */
.quick-tool-btn:active:not(:disabled) {
  /* 按下时的渐变 */
  background: linear-gradient(135deg, #e0f2fe 0%, #bae6fd 50%, #7dd3fc 100%);
  color: #1e40af;

  /* 按下时的阴影效果 */
  box-shadow: 0 1px 4px rgba(30, 64, 175, 0.15),
    inset 0 1px 2px rgba(30, 64, 175, 0.1);

  /* 按下效果 */
  transform: translateY(0);
}

/* 快捷工具按钮禁用状态 */
.quick-tool-btn:disabled {
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  color: #9ca3af;
  border-color: rgba(156, 163, 175, 0.2);
  box-shadow: none;
  cursor: not-allowed;
  transform: none;
}

.quick-tool-btn:disabled .semi-icon,
.quick-tool-btn:disabled svg {
  color: #9ca3af;
  fill: #9ca3af;
}

.quick-tool-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.25) 50%, 
    transparent 100%);
  transition: left 0.5s cubic-bezier(0.25, 1, 0.5, 1);
  z-index: 0;
}





.quick-tool-btn > * {
  position: relative;
  z-index: 2;
}

/* 优化按钮尺寸 - 区分主要和次要操作 */
.enhanced-drawer-container .action-buttons .btn-authority,
.enhanced-drawer-container .action-buttons button,
.action-buttons .btn-authority,
.action-buttons button {
  /* 次要按钮基础尺寸 */
  width: 88px;
  height: 36px;
  min-width: 88px;
  max-width: 88px;
  min-height: 36px;
  max-height: 36px;
  /* 统一内边距和字体 */
  padding: 7px 10px;
    font-size: 12px;
    font-weight: 500;
    line-height: 1.3;
    /* 确保正确的布局 - 这是关键！ */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
    flex: 0 0 88px;
    /* 改善边框圆角 */
    border-radius: 8px;
    /* 平滑过渡 */
    transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 主要按钮（保存）- 更大更突出 */
.enhanced-drawer-container .action-buttons .btn-authority.btn-primary-gold,
.enhanced-drawer-container .action-buttons button.btn-primary-gold,
.action-buttons .btn-authority.btn-primary-gold,
.action-buttons button.btn-primary-gold {
  /* 确保金色按钮也有正确的布局 */
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 100px;
    height: 40px;
    min-width: 100px;
    max-width: 100px;
    min-height: 40px;
    max-height: 40px;
    flex: 0 0 100px;
    padding: 9px 12px;
    font-size: 13px;
    font-weight: 600;
    border-radius: 10px;
    gap: 5px;
}

/* 移除冲突的金色按钮样式，让buttons.css中的统一样式生效 */
/* 主要按钮悬停效果 */
.btn-primary-gold:hover,
.btn--primary-gold:hover,
.enhanced-drawer-container .action-buttons .btn-primary-gold:hover,
.action-buttons .btn-primary-gold:hover {
  background: linear-gradient(135deg, #fde68a 0%, #fcd34d 25%, #fbbf24 50%, #f59e0b 75%, #d97706 100%);
  transform: translateY(-2px) scale(1.05);
  
  box-shadow: 0 8px 25px rgba(251, 191, 36, 0.5),
    0 4px 12px rgba(253, 230, 138, 0.4),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    inset 0 -2px 0 rgba(251, 191, 36, 0.3),
    0 0 30px rgba(251, 191, 36, 0.3);
}

/* 主要按钮高光效果 */
.btn-primary-gold::before,
.btn--primary-gold::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.6) 50%, 
    transparent 100%);
  transition: left 0.6s ease;
  z-index: 1;
}

.btn-primary-gold:hover::before,
.btn--primary-gold:hover::before {
  left: 100%;
}

.btn-primary-gold > *,
.btn--primary-gold > * {
  position: relative;
  z-index: 2;
}

/* 次要按钮（蓝色渐变） */
.btn-secondary-glass,
.btn--secondary-glass,
.enhanced-drawer-container .action-buttons .btn-secondary-glass,
.enhanced-drawer-container .action-buttons button.btn-secondary-glass,
.action-buttons .btn-secondary-glass,
.action-buttons button.btn-secondary-glass {
  background: linear-gradient(135deg, #7dd3fc, #38bdf8 50%, #87ceeb);
  color: white;
  border: 1px solid rgba(14, 165, 233, 0.6);
    -webkit-backdrop-filter: blur(12px);
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3),
      0 2px 6px rgba(56, 189, 248, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(14, 165, 233, 0.2);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

/* 次要按钮悬停效果 */
.btn-secondary-glass:hover,
.btn--secondary-glass:hover,
.enhanced-drawer-container .action-buttons .btn-secondary-glass:hover,
.action-buttons .btn-secondary-glass:hover {
  background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 25%, #2563eb 50%, #1d4ed8 75%, #1e40af 100%);
  border-color: rgba(96, 165, 250, 0.8);
  color: white;
  transform: translateY(-2px) scale(1.03);

  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4),
    0 3px 10px rgba(37, 99, 235, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.5),
    inset 0 -2px 0 rgba(30, 58, 138, 0.3),
    0 0 25px rgba(59, 130, 246, 0.2);
}

/* 次要按钮高光效果 */
.btn-secondary-glass::before,
.btn--secondary-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.4) 50%, 
    transparent 100%);
  transition: left 0.5s ease;
  z-index: 1;
}

.btn-secondary-glass:hover::before,
.btn--secondary-glass:hover::before {
  left: 100%;
}

.btn-secondary-glass > *,
.btn--secondary-glass > * {
  position: relative;
  z-index: 2;
}

/* 增强按钮样式 - 紧凑优化 */
.enhanced-primary-button {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  padding: 8px 14px;
  border-radius: 7px;
  transition: all 0.3s ease-out;
  transform: translateZ(0);
}

.enhanced-primary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.4) 50%, 
    transparent 100%);
  transition: left 0.5s ease-out;
  z-index: 1;
}

.enhanced-primary-button:hover {
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15),
    0 2px 4px rgba(0, 0, 0, 0.1);
}

.enhanced-primary-button:hover::before {
  left: 100%;
}

.enhanced-primary-button:active {
  transform: translateY(0) scale(0.98);
  transition: transform 0.1s ease;
}

.enhanced-secondary-button {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  padding: 6px 12px;
  border-radius: 6px;
  transition: all 0.25s ease-out;
  transform: translateZ(0);
}

.enhanced-secondary-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(139, 92, 246, 0.2) 50%, 
    transparent 100%);
  transition: left 0.4s ease-out;
  z-index: 1;
}

.enhanced-secondary-button:hover {
  transform: translateY(-0.5px) scale(1.01);
  box-shadow: 0 3px 8px rgba(139, 92, 246, 0.2),
    0 1px 3px rgba(0, 0, 0, 0.08);
}

.enhanced-secondary-button:hover::before {
  left: 100%;
}

.enhanced-secondary-button:active {
  transform: translateY(0) scale(0.99);
  transition: transform 0.08s ease;
}

.enhanced-toggle-button {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  padding: 6px 10px;
  border-radius: 5px;
  transition: all 0.2s ease-out;
  transform: translateZ(0);
}

.enhanced-toggle-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255, 255, 255, 0.3) 50%, 
    transparent 100%);
  transition: left 0.35s ease-out;
  z-index: 1;
}

.enhanced-toggle-button:hover {
  transform: translateY(-0.5px) scale(1.008);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1),
    0 1px 2px rgba(0, 0, 0, 0.06);
}

.enhanced-toggle-button:hover::before {
  left: 100%;
}

.enhanced-toggle-button:active {
  transform: translateY(0) scale(0.995);
  transition: transform 0.06s ease;
}

/* 按钮文字和图标层级 */
.enhanced-primary-button > *,
.enhanced-secondary-button > *,
.enhanced-toggle-button > * {
  position: relative;
  z-index: 2;
}

/* 统一按钮图标 */
.action-buttons .btn-authority svg,
.action-buttons .btn-authority .icon,
.action-buttons .btn-authority i {
  width: 14px;
  height: 14px;
  flex-shrink: 0;
}

/* 统一按钮文字 */
.action-buttons .btn-authority span {
  font-size: 11px;
  line-height: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 抽屉关闭按钮 */
.drawer-close-button {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(233, 30, 99, 0.08);

  box-shadow: 0 3px 12px rgba(233, 30, 99, 0.08),
    0 1px 4px rgba(0, 0, 0, 0.04);

  border-radius: 50%;
  width: 36px !important;
  height: 36px !important;
  min-width: 36px !important;
  min-height: 36px !important;
  max-width: 36px !important;
  max-height: 36px !important;

  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.25s ease-out;
  margin-left: 16px;
  position: relative;
  overflow: hidden;

  /* 确保按钮本身不会被任何样式变形 */
  flex-shrink: 0 !important;
  aspect-ratio: 1 !important;
}

.drawer-close-button::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 1;
}

.drawer-close-button:hover::before {
  opacity: 1;
}

.drawer-close-button:active {
  transform: translateY(0) !important;
  transition: transform 0.1s ease !important;
}

.drawer-close-button > * {
  position: relative;
  z-index: 2;
}

/* 确保关闭按钮图标不受任何变换影响 */
.drawer-close-button .icon,
.drawer-close-button svg,
.drawer-close-button .icon-primary,
.drawer-close-button .icon-sm {
  transform: none !important;
  width: 16px !important;
  height: 16px !important;
  aspect-ratio: 1 !important;
}

/* 覆盖所有可能的悬停变换 */
.drawer-close-button:hover .icon,
.drawer-close-button:hover svg,
.drawer-close-button:hover .icon-primary,
.drawer-close-button:hover .icon-sm,
.drawer-close-button .icon:hover,
.drawer-close-button svg:hover,
.drawer-close-button .icon-primary:hover,
.drawer-close-button .icon-sm:hover {
  transform: none !important;
  width: 16px !important;
  height: 16px !important;
}



/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 DRAWER THEMES - 抽屉主题
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 设置抽屉主题（蓝色科技） */
.settings-drawer .enhanced-drawer-container {
  background: linear-gradient(135deg,
    rgba(30, 58, 138, 0.08) 0%,
    rgba(59, 130, 246, 0.05) 25%,
    rgba(255, 255, 255, 0.98) 45%,
    rgba(147, 197, 253, 0.04) 75%,
    rgba(37, 99, 235, 0.06) 100%);
  
  /* 科技网格纹理 */
  background-image: radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(147, 197, 253, 0.08) 0%, transparent 50%),
    linear-gradient(45deg, rgba(59, 130, 246, 0.08) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(59, 130, 246, 0.08) 25%, transparent 25%);
  
  background-size: 200px 200px, 150px 150px, 20px 20px, 20px 20px;
  background-position: 0 0, 100px 100px, 0 0, 0 0;
  
  backdrop-filter: blur(25px);
  
  box-shadow: -18px 0 60px rgba(30, 58, 138, 0.1),
    -10px 0 30px rgba(59, 130, 246, 0.06),
    -4px 0 12px rgba(147, 197, 253, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.4);
}

.settings-drawer .enhanced-drawer-content {
  background: linear-gradient(135deg,
    rgba(30, 58, 138, 0.03) 0%,
    rgba(59, 130, 246, 0.02) 25%,
    rgba(255, 255, 255, 0.96) 45%,
    rgba(147, 197, 253, 0.02) 75%,
    rgba(37, 99, 235, 0.04) 100%);
  padding: 12px 16px; /* 紧凑的padding */
}

.settings-drawer .enhanced-drawer-header {
  background: linear-gradient(135deg,
    rgba(30, 58, 138, 0.06) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(147, 197, 253, 0.05) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.1);
}

/* 历史抽屉主题（琥珀金色） */
.history-drawer .enhanced-drawer-container {
  background: linear-gradient(135deg,
    rgba(146, 64, 14, 0.08) 0%,
    rgba(217, 119, 6, 0.05) 25%,
    rgba(255, 255, 255, 0.98) 45%,
    rgba(252, 211, 77, 0.04) 75%,
    rgba(180, 83, 9, 0.06) 100%);
  
  /* 温暖琥珀纹理 */
  background-image: radial-gradient(circle at 30% 20%, rgba(245, 158, 11, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 70% 80%, rgba(251, 191, 36, 0.06) 0%, transparent 60%),
    linear-gradient(45deg, rgba(59, 130, 246, 0.08) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(59, 130, 246, 0.08) 25%, transparent 25%);
  
  background-size: 180px 180px, 120px 120px, 40px 40px, 40px 40px;
  background-position: 0 0, 90px 90px, 0 0, 20px 20px;
  
  backdrop-filter: blur(25px);
  
  box-shadow: -18px 0 60px rgba(146, 64, 14, 0.1),
    -10px 0 30px rgba(217, 119, 6, 0.06),
    -4px 0 12px rgba(252, 211, 77, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.4);
}

.history-drawer .enhanced-drawer-content {
  background: linear-gradient(135deg,
    rgba(146, 64, 14, 0.03) 0%,
    rgba(217, 119, 6, 0.02) 25%,
    rgba(255, 255, 255, 0.96) 45%,
    rgba(252, 211, 77, 0.02) 75%,
    rgba(180, 83, 9, 0.04) 100%);
  padding: 12px 16px; /* 紧凑的padding */
}

.history-drawer .enhanced-drawer-header {
  background: linear-gradient(135deg,
    rgba(146, 64, 14, 0.06) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(252, 211, 77, 0.05) 100%);
  border-bottom: 1px solid rgba(217, 119, 6, 0.1);
}

/* 提示词抽屉主题（紫色魔法） */
.prompt-drawer .enhanced-drawer-container {
  background: linear-gradient(135deg,
    rgba(88, 28, 135, 0.08) 0%,
    rgba(139, 92, 246, 0.05) 25%,
    rgba(255, 255, 255, 0.98) 45%,
    rgba(196, 181, 253, 0.04) 75%,
    rgba(67, 56, 202, 0.06) 100%);
  
  /* 魔法多层纹理 */
  background-image: radial-gradient(circle at 40% 30%, rgba(139, 92, 246, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 60% 70%, rgba(167, 139, 250, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 20% 80%, rgba(196, 181, 253, 0.04) 0%, transparent 40%),
    linear-gradient(60deg, rgba(59, 130, 246, 0.08) 50%, transparent 50%),
    linear-gradient(-60deg, rgba(59, 130, 246, 0.08) 50%, transparent 50%);
  
  background-size: 160px 160px, 100px 100px, 80px 80px, 30px 30px, 30px 30px;
  background-position: 0 0, 80px 80px, 40px 40px, 0 0, 15px 15px;
  
  backdrop-filter: blur(25px);
  
  box-shadow: -18px 0 60px rgba(88, 28, 135, 0.1),
    -10px 0 30px rgba(139, 92, 246, 0.06),
    -4px 0 12px rgba(196, 181, 253, 0.08),
    0 0 0 1px rgba(255, 255, 255, 0.4);
}

.prompt-drawer .enhanced-drawer-content {
  background: linear-gradient(135deg,
    rgba(88, 28, 135, 0.03) 0%,
    rgba(139, 92, 246, 0.02) 25%,
    rgba(255, 255, 255, 0.96) 45%,
    rgba(196, 181, 253, 0.02) 75%,
    rgba(67, 56, 202, 0.04) 100%);
  padding: 12px 16px; /* 紧凑的padding */
}

.prompt-drawer .enhanced-drawer-header {
  background: linear-gradient(135deg,
    rgba(88, 28, 135, 0.06) 0%,
    rgba(255, 255, 255, 0.9) 50%,
    rgba(196, 181, 253, 0.05) 100%);
  border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 THEMED FORM ELEMENTS - 主题化表单元素
 * ═══════════════════════════════════════════════════════════════════════════ */

/* ═══════════════════════════════════════════════════════════════════════════
 * 📋 DRAWER CONTENT LAYOUTS - 抽屉内容布局
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 设置抽屉内容容器 - 优化滚动和间距 */
.settings-drawer-content-compact {
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;

  /* 优化滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgba(59, 130, 246, 0.3) transparent;
}

.settings-drawer-content-compact::-webkit-scrollbar {
  width: 6px;
}

.settings-drawer-content-compact::-webkit-scrollbar-track {
  background: transparent;
}

.settings-drawer-content-compact::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.settings-drawer-content-compact::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* 设置抽屉底部操作区 - 优化版 */
.settings-drawer-footer {
  position: sticky;
  bottom: 0;
  background: linear-gradient(to top,
      rgba(255, 255, 255, 0.98) 0%,
      rgba(255, 255, 255, 0.96) 30%,
      rgba(255, 255, 255, 0.92) 70%,
      rgba(255, 255, 255, 0.88) 100%);
  -webkit-backdrop-filter: blur(16px);
  backdrop-filter: blur(16px);
  border-top: 1px solid rgba(59, 130, 246, 0.12);
  padding: 16px 20px 20px 20px;
  margin-top: 20px;
  z-index: 15;

  /* 添加微妙的阴影增强层次感 */
  box-shadow:
    0 -4px 20px rgba(0, 0, 0, 0.02),
    0 -2px 8px rgba(59, 130, 246, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);

  /* 平滑过渡 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  align-items: center;
  flex-wrap: wrap;

  /* 改善按钮组的视觉分组 */
  position: relative;
}

/* 按钮组分隔线 */
.footer-actions::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 1px;
  height: 24px;
  background: linear-gradient(to bottom,
      transparent 0%,
      rgba(59, 130, 246, 0.15) 20%,
      rgba(59, 130, 246, 0.25) 50%,
      rgba(59, 130, 246, 0.15) 80%,
      transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 当有多个按钮时显示分隔线 */
.footer-actions:has(.action-button:nth-child(2))::before {
  opacity: 1;
}

/* 按钮悬停增强效果 */
.footer-actions .btn-authority:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow:
    0 4px 12px rgba(59, 130, 246, 0.2),
    0 2px 6px rgba(0, 0, 0, 0.1);
}

.footer-actions .btn-authority.btn-primary-gold:hover:not(:disabled) {
  transform: translateY(-2px) scale(1.02);
  box-shadow:
    0 8px 25px rgba(251, 191, 36, 0.4),
    0 4px 12px rgba(253, 230, 138, 0.3),
    inset 0 2px 0 rgba(255, 255, 255, 0.8),
    inset 0 -2px 0 rgba(251, 191, 36, 0.3);
}

/* 按钮激活状态 */
.footer-actions .btn-authority:active:not(:disabled) {
  transform: translateY(0) scale(0.98);
  transition-duration: 0.1s;
}

/* 禁用状态优化 */
.footer-actions .btn-authority:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* 按钮焦点状态 - 改善键盘导航 */
.footer-actions .btn-authority:focus-visible {
  outline: 2px solid rgba(59, 130, 246, 0.6);
  outline-offset: 2px;
  z-index: 1;
}

.footer-actions .btn-authority.btn-primary-gold:focus-visible {
  outline-color: rgba(251, 191, 36, 0.8);
}

/* 按钮加载状态 */
.footer-actions .btn-authority.loading {
  position: relative;
  color: transparent !important;
  pointer-events: none;
}

.footer-actions .btn-authority.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  border: 2px solid currentColor;
  border-top-color: transparent;
  border-radius: 50%;
  animation: button-spin 0.8s linear infinite;
}

@keyframes button-spin {
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 响应式按钮优化 */
@media (max-width: 768px) {
  .settings-drawer-footer {
    padding: 12px 16px 16px 16px;
  }

  .footer-actions {
    gap: 8px;
    flex-wrap: wrap;
  }

  /* 小屏幕上按钮尺寸调整 */
  .footer-actions .btn-authority {
    width: 80px;
    height: 34px;
    min-width: 80px;
    max-width: 80px;
    min-height: 34px;
    max-height: 34px;
    flex: 0 0 80px;
    font-size: 11px;
    padding: 6px 8px;
  }

  .footer-actions .btn-authority.btn-primary-gold {
    width: 90px;
    height: 36px;
    min-width: 90px;
    max-width: 90px;
    min-height: 36px;
    max-height: 36px;
    flex: 0 0 90px;
    font-size: 12px;
    padding: 7px 10px;
  }
}

@media (max-width: 480px) {
  .footer-actions {
    justify-content: center;
    gap: 6px;
  }

  /* 超小屏幕上进一步缩小 */
  .footer-actions .btn-authority {
    width: 72px;
    height: 32px;
    min-width: 72px;
    max-width: 72px;
    min-height: 32px;
    max-height: 32px;
    flex: 0 0 72px;
    font-size: 10px;
    padding: 5px 6px;
    gap: 3px;
  }

  .footer-actions .btn-authority.btn-primary-gold {
    width: 80px;
    height: 34px;
    min-width: 80px;
    max-width: 80px;
    min-height: 34px;
    max-height: 34px;
    flex: 0 0 80px;
    font-size: 11px;
    padding: 6px 8px;
  }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
  .footer-actions {
    gap: 16px;
  }

  .footer-actions .btn-authority {
    width: 96px;
    height: 38px;
    min-width: 96px;
    max-width: 96px;
    min-height: 38px;
    max-height: 38px;
    flex: 0 0 96px;
    font-size: 13px;
    padding: 8px 12px;
  }

  .footer-actions .btn-authority.btn-primary-gold {
    width: 108px;
    height: 42px;
    min-width: 108px;
    max-width: 108px;
    min-height: 42px;
    max-height: 42px;
    flex: 0 0 108px;
    font-size: 14px;
    padding: 10px 14px;
  }
}

/* 按钮进入动画 */
.footer-actions .btn-authority {
  animation: buttonSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  opacity: 0;
  transform: translateY(20px);
}

.footer-actions .btn-authority:nth-child(1) {
  animation-delay: 0.1s;
}

.footer-actions .btn-authority:nth-child(2) {
  animation-delay: 0.15s;
}

.footer-actions .btn-authority:nth-child(3) {
  animation-delay: 0.2s;
}

.footer-actions .btn-authority:nth-child(4) {
  animation-delay: 0.25s;
}

@keyframes buttonSlideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 按钮图标旋转效果 */
.footer-actions .btn-authority:hover .icon,
.footer-actions .btn-authority:hover svg {
  transform: rotate(5deg) scale(1.1);
  transition: transform 0.2s ease;
}

.footer-actions .btn-authority[aria-label="重置设置"]:hover .icon,
.footer-actions .btn-authority[aria-label="重置设置"]:hover svg {
  transform: rotate(180deg) scale(1.1);
}

/* 按钮文字效果 */
.footer-actions .btn-authority span {
  position: relative;
  overflow: hidden;
}

.footer-actions .btn-authority span::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -100%;
  width: 100%;
  height: 1px;
  background: currentColor;
  transition: left 0.3s ease;
}

.footer-actions .btn-authority:hover span::after {
  left: 0;
}

/* 保存按钮特殊效果 */
.footer-actions .btn-authority.btn-primary-gold:not(:disabled) {
  position: relative;
  overflow: hidden;
}

.footer-actions .btn-authority.btn-primary-gold:not(:disabled)::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
  transform: rotate(45deg);
  transition: transform 0.6s ease;
  z-index: 1;
}

.footer-actions .btn-authority.btn-primary-gold:hover:not(:disabled)::before {
  transform: rotate(45deg) translateX(100%);
}
/* 设置抽屉表单元素 - 紧凑优化 */
.settings-drawer .form-input-modern {
  border: 1px solid rgba(59, 130, 246, 0.2);
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 6px;
}

.settings-drawer .form-input-modern:focus {
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.settings-drawer .glass-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(59, 130, 246, 0.1);
  box-shadow: 0 4px 20px rgba(59, 130, 246, 0.05);
  padding: 14px 16px;
  border-radius: 10px;
  margin-bottom: 12px;
}

/* 历史抽屉表单元素 - 紧凑优化 */
.history-drawer .form-input-modern {
  border: 1px solid rgba(217, 119, 6, 0.2);
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 6px;
}

.history-drawer .form-input-modern:focus {
  border-color: rgba(217, 119, 6, 0.4);
  box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.1);
}

.history-drawer .glass-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(217, 119, 6, 0.1);
  box-shadow: 0 4px 20px rgba(217, 119, 6, 0.05);
  padding: 14px 16px;
  border-radius: 10px;
  margin-bottom: 12px;
}

/* 提示词抽屉表单元素 - 紧凑优化 */
.prompt-drawer .form-input-modern {
  border: 1px solid rgba(139, 92, 246, 0.2);
  background: rgba(255, 255, 255, 0.95);
  padding: 8px 12px;
  border-radius: 6px;
}

.prompt-drawer .form-input-modern:focus {
  border-color: rgba(139, 92, 246, 0.4);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.prompt-drawer .glass-card {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(139, 92, 246, 0.1);
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.05);
  padding: 14px 16px;
  border-radius: 10px;
  margin-bottom: 12px;
}

/* 通用文本域优化 - 紧凑版 */
.settings-drawer textarea.form-input-modern,
.history-drawer textarea.form-input-modern,
.prompt-drawer textarea.form-input-modern {
  padding: 10px 14px;
  line-height: 1.5;
  min-height: 100px;
  resize: vertical;
}

/* 表单标签优化 - 紧凑版 */
.settings-drawer .form-label,
.history-drawer .form-label,
.prompt-drawer .form-label {
  margin-bottom: 6px;
  padding: 0 2px;
  font-weight: 600;
  font-size: 0.875rem;
}

/* 表单复选框优化 */
.settings-drawer .form-checkbox,
.history-drawer .form-checkbox,
.prompt-drawer .form-checkbox {
  margin-right: 8px;
  transform: scale(1.1);
}

/* 金色主题卡片 - 紧凑优化 */
.prompt-drawer .glass-card-gold {
  background: rgba(255, 255, 255, 0.92);
  border: 1px solid rgba(139, 92, 246, 0.08);
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.04),
    0 1px 3px rgba(0, 0, 0, 0.02);
  padding: 12px 14px;
  border-radius: 8px;
  margin-bottom: 10px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌊 BACKGROUND ANIMATIONS - 背景动画
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 设置抽屉背景流动动画 */
@keyframes settingsBackgroundFlow {
  0%, 100% {
    background-position: 0 0, 100px 100px, 0 0, 0 0;
  }
  25% {
    background-position: 50px 25px, 150px 125px, 5px 5px, 5px 5px;
  }
  50% {
    background-position: 100px 50px, 200px 150px, 10px 10px, 10px 10px;
  }
  75% {
    background-position: 50px 75px, 150px 175px, 5px 15px, 5px 15px;
  }
}

/* 历史抽屉背景流动动画 */
@keyframes historyBackgroundFlow {
  0%, 100% {
    background-position: 0 0, 90px 90px, 0 0, 20px 20px;
  }
  33% {
    background-position: 30px 20px, 120px 110px, 10px 10px, 30px 30px;
  }
  66% {
    background-position: 60px 40px, 150px 130px, 20px 20px, 40px 40px;
  }
}

/* 提示词抽屉背景流动动画 */
@keyframes promptBackgroundFlow {
  0%, 100% {
    background-position: 0 0, 80px 80px, 40px 40px, 0 0, 15px 15px;
  }
  20% {
    background-position: 20px 15px, 100px 95px, 60px 55px, 5px 5px, 20px 20px;
  }
  40% {
    background-position: 40px 30px, 120px 110px, 80px 70px, 10px 10px, 25px 25px;
  }
  60% {
    background-position: 60px 45px, 140px 125px, 100px 85px, 15px 15px, 30px 30px;
  }
  80% {
    background-position: 40px 60px, 120px 140px, 80px 100px, 10px 20px, 25px 35px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 强制统一尺寸 */
@media screen {
  .enhanced-drawer-container .action-buttons .btn-authority,
  .enhanced-drawer-container .action-buttons button,
  .action-buttons .btn-authority,
  .action-buttons button {
    width: 92px;
    height: 38px;
    min-width: 92px;
    max-width: 92px;
    min-height: 38px;
    max-height: 38px;
    padding: 8px 10px;
    font-size: 13px;
    flex: 0 0 92px;
  }
}

/* 移动端优化 - 紧凑的间距 */
@media (max-width: 768px) {
  .enhanced-drawer-header {
    padding: 10px 14px;
  }

  .enhanced-drawer-content {
    padding: 10px 14px;
    gap: 10px;
  }

  .enhanced-drawer-title {
    font-size: 1.1rem;
    padding: 4px 8px;
  }
  
  .enhanced-drawer-title {
    font-size: 1.15rem;
  }
  
  .settings-drawer .enhanced-drawer-container,
  .history-drawer .enhanced-drawer-container,
  .prompt-drawer .enhanced-drawer-container {
    background-size: 100px 100px, 75px 75px, 40px 40px, 15px 15px, 15px 15px;
  }
}

@media (max-width: 480px) {
  .enhanced-drawer-header {
    padding: 8px 12px;
  }

  .enhanced-drawer-content {
    padding: 8px 12px;
    gap: 8px;
  }

  .enhanced-drawer-title {
    font-size: 1rem;
    padding: 3px 6px;
  }
  
  .enhanced-drawer-title {
    font-size: 1.05rem;
  }
  
  .action-buttons {
    gap: 8px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY - 无障碍
 * ═══════════════════════════════════════════════════════════════════════════ */

.enhanced-drawer-container:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.drawer-close-button:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.enhanced-primary-button:focus-visible,
.enhanced-secondary-button:focus-visible,
.enhanced-toggle-button:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .enhanced-drawer-container,
  .enhanced-drawer-title,
  .enhanced-primary-button,
  .enhanced-secondary-button,
  .enhanced-toggle-button,
  .drawer-close-button {
    animation: none;
    transition: none;
  }
  
  .enhanced-drawer-container.slide-in,
  .enhanced-drawer-container.slide-out {
    animation: none;
  }
  
  .settings-drawer .enhanced-drawer-container,
  .history-drawer .enhanced-drawer-container,
  .prompt-drawer .enhanced-drawer-container {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .enhanced-drawer-container {
    border: 2px solid var(--color-gray-600);
  }
  
  .enhanced-drawer-header {
    border-bottom: 2px solid var(--color-gray-600);
  }
  
  .enhanced-primary-button,
  .enhanced-secondary-button,
  .enhanced-toggle-button,
  .drawer-close-button {
    border: 2px solid currentColor;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .enhanced-primary-button,
  .enhanced-secondary-button,
  .enhanced-toggle-button,
  .drawer-close-button {
    min-height: 44px;
    min-width: 44px;
  }
}