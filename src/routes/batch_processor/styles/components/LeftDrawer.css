/* LeftDrawer.css */

.drawer-container-left {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  background-color: white;
  box-shadow: 2px 0 5px rgba(0,0,0,0.1);
  transform: translateX(-100%);
  transition: transform 0.3s ease-in-out;
  z-index: 1001;
}

.drawer-container-left.slide-in-left {
  transform: translateX(0);
}

.drawer-container-left.slide-out-left {
  transform: translateX(-100%);
}

.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  z-index: 1000;
  pointer-events: none;
}

.drawer-overlay.open {
  opacity: 1;
  pointer-events: auto;
}