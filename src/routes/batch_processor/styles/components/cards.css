/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🃏 UNIFIED CARD SYSTEM - 统一卡片系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了design-system/cards.css + components/result-card.css + glass-card相关样式
 * 提供完整的卡片组件解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces design-system/cards.css + components/result-card.css + glass-card相关样式
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 BASE CARD STYLES - 基础卡片样式
 * ═══════════════════════════════════════════════════════════════════════════ */

.card {
  background: var(--color-card-bg);
  border: none;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.card:hover {
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.12), 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-2px);
  background: var(--color-card-bg-hover);
}

.card:active {
  transform: translateY(0);
  box-shadow: var(--shadow-card-active);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📏 CARD SIZES - 卡片尺寸
 * ═══════════════════════════════════════════════════════════════════════════ */

.card-sm {
  border-radius: 8px;
  padding: var(--card-padding-sm);
}

.card-md {
  border-radius: 12px;
  padding: var(--card-padding-md);
}

.card-lg {
  border-radius: 16px;
  padding: var(--card-padding-lg);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎆 GLASS CARD SYSTEM - 玻璃卡片系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 玻璃卡片基础样式 - 无边框设计 */
.batch-processor-layout .glass-card {
  /* 背景和边框 */
  background: rgba(255, 255, 255, 0.95);
  border: none;
  border-radius: 12px;
  
  /* 玻璃效果 */
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  
  /* 阴影和过渡 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
  
  /* 布局和层级 */
  position: relative;
  z-index: 2;
  isolation: isolate;
  padding: 16px;
  
  /* 防止内容溢出 */
  contain: layout;
  flex-shrink: 0;
  margin-bottom: 12px;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: visible;
}
.glass-card.animate-fade-in-up{
  box-shadow: none;
}
/* 玻璃卡片悬停效果 - 仅用阴影无边框 */
.batch-processor-layout .glass-card.animate-fade-in-up:hover {
  box-shadow: none;
}
/* 玻璃卡片悬停效果 - 仅用阴影无边框 */
.batch-processor-layout .glass-card:hover {
  background: rgba(255, 255, 255, 0.98);
  border: none;
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

/* 玻璃卡片主题变体 - 无边框设计 */
.batch-processor-layout .glass-card-blue {
  border: none;
  background: linear-gradient(135deg,
      rgba(227, 242, 253, 0.3) 0%,
      rgba(255, 255, 255, 0.8) 100%);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.12), 0 2px 6px rgba(0, 0, 0, 0.06);
}

.batch-processor-layout .glass-card-blue:hover {
  border: none;
  background: linear-gradient(135deg,
      rgba(227, 242, 253, 0.4) 0%,
      rgba(255, 255, 255, 0.9) 100%);
  box-shadow: 0 8px 24px rgba(33, 150, 243, 0.18), 0 4px 12px rgba(0, 0, 0, 0.08);
}

.batch-processor-layout .glass-card-gold {
  border: none;
  background: linear-gradient(135deg,
      rgba(254, 243, 199, 0.3) 0%,
      rgba(255, 255, 255, 0.8) 100%);
  box-shadow: 0 4px 12px rgba(234, 179, 8, 0.12), 0 2px 6px rgba(0, 0, 0, 0.06);
}

.batch-processor-layout .glass-card-gold:hover {
  border: none;
  background: linear-gradient(135deg,
      rgba(254, 243, 199, 0.4) 0%,
      rgba(255, 255, 255, 0.9) 100%);
  box-shadow: 0 8px 24px rgba(234, 179, 8, 0.18), 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 玻璃卡片内部元素样式 */
.batch-processor-layout .glass-card .border-b {
  border-bottom: 1px solid rgba(148, 163, 184, 0.12);
  transition: border-color 0.3s ease;
}

.batch-processor-layout .glass-card:hover .border-b {
  border-bottom-color: rgba(35, 146, 239, 0.15);
}

/* 圆角统一 */
.batch-processor-layout .glass-card .rounded-lg,
.batch-processor-layout .glass-card .rounded,
.batch-processor-layout .glass-card .rounded-md {
  border-radius: 8px;
}

.batch-processor-layout .glass-card button {
  border-radius: 8px;
}

.batch-processor-layout .glass-card .rounded-full {
  border-radius: 50%;
}

/* 增强卡片 */
.enhanced-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.98) 0%, 
    rgba(59, 130, 246, 0.01) 100%);
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.enhanced-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌟 SPECIAL CARD VARIANTS - 特殊卡片变体
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 设置卡片 */
.settings-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.98) 0%, 
    rgba(59, 130, 246, 0.01) 100%);
  border: none;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04), inset 4px 0 0 var(--theme-settings-primary);
  transition: all 0.3s ease;
}

.enhanced-settings-card {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(59, 130, 246, 0.01) 100%);
  border: 1px solid rgba(59, 130, 246, 0.08);
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.06), 0 1px 3px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  margin-bottom: 16px;
}

.enhanced-settings-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--color-primary-500), var(--color-secondary-500), var(--color-success-500));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.enhanced-settings-card:hover {
  transform: translateY(-1px);
  border-color: rgba(59, 130, 246, 0.15);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.1), 0 2px 6px rgba(0, 0, 0, 0.06);
}

.enhanced-settings-card:hover::before {
  opacity: 1;
}

.enhanced-settings-card.group {
  border-color: rgba(59, 130, 246, 0.12);
  box-shadow: 0 3px 12px rgba(59, 130, 246, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
}

/* 金色卡片 */
.glass-card-gold {
  background: rgba(255, 255, 255, 0.92);
  border: none;
  box-shadow: 0 4px 12px rgba(234, 179, 8, 0.12), 0 2px 6px rgba(0, 0, 0, 0.06);
  border-radius: 12px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 CARD COMPONENTS - 卡片组件
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 卡片头部 */
/* 卡片头部 - 优化间距和视觉层次 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 14px 18px;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.6) 0%,
    rgba(255, 255, 255, 0.95) 100%);
  border-bottom: 1px solid rgba(59, 130, 246, 0.06);
  border-radius: 12px 12px 0 0;
  position: relative;
}

.card-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.card-title span {
  font-weight: 600;
  letter-spacing: -0.01em;
}

.card-subtitle {
  font-size: 0.8rem;
  font-weight: 400;
  color: var(--text-secondary);
  margin: 4px 0 0 0;
  opacity: 0.8;
}

/* 卡片内容 - 优化内边距 */
.card-content {
  padding: 16px 18px 18px 18px;
}

.card-content.no-padding {
  padding: 0;
}

/* 卡片底部 */
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-4) var(--space-6);
  background: var(--color-gray-50);
  box-shadow: 0 -1px 3px rgba(0, 0, 0, 0.04);
  margin: var(--space-4) calc(-1 * var(--card-padding-md)) calc(-1 * var(--card-padding-md)) calc(-1 * var(--card-padding-md));
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🃏 RESULT CARD - 结果卡片（批处理器特定）
 * ═══════════════════════════════════════════════════════════════════════════ */

.result-card {
  /* 基础布局 */
  width: 280px;
  height: 600px;
  position: relative;
  cursor: pointer;
  border-radius: 12px;
  overflow: hidden;
  
  /* 基础样式 */
  background: var(--color-white);
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04);
  
  /* 过渡动画 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 结果卡片状态样式 */
.result-card.success,
.result-card--success {
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
}

.result-card.success.focused,
.result-card--success.result-card--focused {
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08), 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.result-card.error,
.result-card--error {
  opacity: 0.9;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04);
}

.result-card.error.focused,
.result-card--error.result-card--focused {
  box-shadow: 0 8px 24px rgba(239, 68, 68, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08), 0 0 0 3px rgba(239, 68, 68, 0.3);
}

.result-card.processing,
.result-card--processing {
  box-shadow: 0 4px 12px rgba(234, 179, 8, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
}

.result-card.processing.focused,
.result-card--processing.result-card--focused {
  box-shadow: 0 8px 24px rgba(234, 179, 8, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08), 0 0 0 3px rgba(234, 179, 8, 0.3);
}

/* 结果卡片交互状态 */
.result-card:hover:not(.focused):not(.result-card--focused) {
  transform: translateY(-4px);
  box-shadow: var(--shadow-hover-lift);
}

.result-card.selected,
.result-card--selected {
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.2), 0 4px 12px rgba(0, 0, 0, 0.08), 0 0 0 2px rgba(59, 130, 246, 0.5);
}

.result-card.dragging,
.result-card--dragging {
  opacity: 0.8;
  transform: rotate(3deg);
  box-shadow: var(--shadow-dragging);
}

/* 拖拽手柄 */
.drag-handle,
.result-card__drag-handle {
  height: 20px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px 12px 0 0;
  cursor: grab;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.drag-handle:active,
.result-card__drag-handle:active {
  cursor: grabbing;
}

.drag-handle::before,
.result-card__drag-handle::before {
  content: '';
  width: 24px;
  height: 4px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 2px;
}

/* 选择框 */
.result-card__selection-checkbox {
  position: absolute;
  left: var(--space-2);
  top: 50%;
  transform: translateY(-50%);
  width: 14px;
  height: 14px;
}

/* iframe容器 */
.iframe-container,
.result-card__iframe-container {
  height: 500px;
  padding: 0;
  overflow: visible;
  border-radius: 0 0 8px 8px;
  position: relative;
}

/* 查询区域 */
.result-card__query-section {
  height: 30px;
  padding: var(--space-1) var(--space-3);
  display: flex;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.result-card__query-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--color-gray-900);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 元数据区域 */
.result-card__metadata-section {
  height: 50px;
  padding: var(--space-2) var(--space-3);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.result-card__tags-row {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.result-card__actions-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 状态标签 */
.result-card__status-tag {
  padding: 2px 6px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 2px;
  font-size: 10px;
  font-weight: 500;
}

.result-card__status-tag--success {
  background-color: var(--color-success-100);
  color: var(--color-success-700);
}

.result-card__status-tag--error {
  background-color: var(--color-error-100);
  color: var(--color-error-700);
}

.result-card__status-tag--processing {
  background-color: var(--color-warning-100);
  color: var(--color-warning-700);
}

.result-card__status-tag--pending {
  background-color: var(--color-gray-100);
  color: var(--color-gray-700);
}

/* 时间标签 */
.result-card__time-tag {
  font-size: 10px;
  color: var(--color-gray-500);
  background-color: var(--color-gray-100);
  padding: 2px 4px;
  border-radius: 2px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
}

/* 时间戳 */
.result-card__timestamp {
  font-size: 10px;
  color: var(--color-gray-400);
  margin-left: auto;
  flex-shrink: 0;
}

/* 操作连接 */
.result-card__playground-link {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  border-radius: 6px;
  background-color: var(--color-primary-100);
  color: var(--color-primary-700);
  text-decoration: none;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.result-card__playground-link:hover {
  background-color: var(--color-primary-200);
  color: var(--color-primary-800);
}

.result-card__playground-link-icon {
  width: 12px;
  height: 12px;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* 错误指示器 */
.result-card__error-indicator {
  color: var(--color-error-600);
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.result-card__error-icon {
  width: 12px;
  height: 12px;
  stroke: currentColor;
  fill: none;
  stroke-width: 2;
  stroke-linecap: round;
  stroke-linejoin: round;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📊 OVERVIEW CARD - 概览卡片
 * ═══════════════════════════════════════════════════════════════════════════ */

.overview-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(59, 130, 246, 0.02) 100%);
  border: none;
  border-radius: 16px;
  padding: var(--space-6);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08);
}

.settings-overview-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(59, 130, 246, 0.02) 100%);
  border: none;
  border-radius: 16px;
  padding: var(--space-6);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.settings-overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.08);
}

/* 概览头部 */
.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.overview-title {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.overview-title h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--color-primary-800);
  line-height: 1.2;
}

.overview-title p {
  margin: var(--space-1) 0 0 0;
  font-size: 0.875rem;
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 配置状态 */
.config-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--color-success-100);
  border: none;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--color-success-700);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.08);
}

/* 状态指示器 */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--color-success-500);
  animation: pulse 2s infinite;
}

.card-status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: var(--color-success-500);
  animation: pulse 2s infinite;
}

.card-status-indicator.api-status {
  background: var(--color-primary-500);
}

.card-status-indicator.processing-status {
  background: var(--color-secondary-500);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔢 NUMERIC CARD - 数字卡片
 * ═══════════════════════════════════════════════════════════════════════════ */

.numeric-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(59, 130, 246, 0.01) 100%);
  border: none;
  border-radius: 12px;
  padding: var(--space-5);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.06), 0 1px 4px rgba(0, 0, 0, 0.04);
}

.numeric-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
}

.form-group.numeric-card {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(59, 130, 246, 0.01) 100%);
  border: none;
  border-radius: 12px;
  padding: var(--space-5);
  transition: all 0.3s ease;
  margin-bottom: 0;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.06), 0 1px 4px rgba(0, 0, 0, 0.04);
}

.form-group.numeric-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
}

.form-group.toggle {
  background: linear-gradient(135deg, 
    rgba(255, 255, 255, 0.95) 0%, 
    rgba(59, 130, 246, 0.01) 100%);
  border: none;
  border-radius: 12px;
  padding: var(--space-5);
  transition: all 0.3s ease;
  margin-bottom: 0;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.06), 0 1px 4px rgba(0, 0, 0, 0.04);
}

.form-group.toggle:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.08), 0 2px 6px rgba(0, 0, 0, 0.04);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎮 INTERACTIVE CARDS - 交互式卡片
 * ═══════════════════════════════════════════════════════════════════════════ */

.card-clickable {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.card-clickable:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-card-hover);
}

.card-clickable:active {
  transform: translateY(0);
  box-shadow: var(--shadow-card-active);
}

/* 可选择卡片 */
.card-selectable {
  position: relative;
}

.card-selectable::before {
  content: '';
  position: absolute;
  top: 8px;
  left: 8px;
  width: 16px;
  height: 16px;
  border: none;
  border-radius: 4px;
  background: var(--color-white);
  opacity: 0;
  transition: opacity 0.2s ease;
  box-shadow: 0 0 0 2px var(--color-gray-300);
}

.card-selectable:hover::before {
  opacity: 1;
}

.card-selectable.selected::before {
  opacity: 1;
  background: var(--color-primary-500);
  box-shadow: 0 0 0 2px var(--color-primary-500);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 CARD GRIDS - 卡片网格
 * ═══════════════════════════════════════════════════════════════════════════ */

.cards-grid {
  display: grid;
  gap: var(--card-gap);
}

.cards-grid-1 {
  display: grid;
  gap: var(--card-gap);
  grid-template-columns: 1fr;
}

.cards-grid-2 {
  display: grid;
  gap: var(--card-gap);
  grid-template-columns: repeat(2, 1fr);
}

.cards-grid-3 {
  display: grid;
  gap: var(--card-gap);
  grid-template-columns: repeat(3, 1fr);
}

.cards-grid-4 {
  display: grid;
  gap: var(--card-gap);
  grid-template-columns: repeat(4, 1fr);
}

.cards-grid-5 {
  display: grid;
  gap: var(--card-gap);
  grid-template-columns: repeat(5, 1fr);
}

/* 自适应网格 */
.cards-grid-auto {
  display: grid;
  gap: var(--card-gap);
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
}

/* 结果卡片网格 */
.result-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--batch-card-gap);
  padding: var(--space-4);
  justify-items: center;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 1600px) {
  .result-cards-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 1280px) {
  .result-cards-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .cards-grid-5 {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .cards-grid-4 {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 1024px) {
  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-3);
  }
  
  .card-header {
    padding: var(--space-4) var(--space-5);
  }
  
  .card-content {
    padding: var(--space-5);
  }
  
  .cards-grid-3,
  .cards-grid-4,
  .cards-grid-5 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .result-cards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
    padding: var(--space-3);
  }
  
  .overview-card,
  .settings-overview-card {
    padding: var(--space-4);
  }
  
  .overview-title h2 {
    font-size: 1.25rem;
  }
  
  .card-header {
    padding: var(--space-4) var(--space-5);
  }
  
  .card-content {
    padding: var(--space-5);
  }
  
  .cards-grid-2,
  .cards-grid-3,
  .cards-grid-4,
  .cards-grid-5 {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .result-cards-grid {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .result-card {
    width: 100%;
    max-width: 320px;
    height: 500px;
  }
  
  .result-card__iframe-container {
    height: 400px;
  }
  
  .result-card__query-section {
    padding: var(--space-1) var(--space-2);
  }
  
  .result-card__metadata-section {
    padding: var(--space-1) var(--space-2);
  }
  
  .overview-card,
  .settings-overview-card {
    padding: var(--space-3);
  }
  
  .overview-title h2 {
    font-size: 1.125rem;
  }
  
  .card-header {
    padding: var(--space-3) var(--space-4);
  }
  
  .card-content {
    padding: var(--space-4);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY - 无障碍
 * ═══════════════════════════════════════════════════════════════════════════ */

.card:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.card-clickable:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.result-card:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.result-card__selection-checkbox:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 1px;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .card,
  .enhanced-card,
  .overview-card,
  .result-card,
  .numeric-card,
  .glass-card {
    transition: none;
  }
  
  .card:hover,
  .result-card:hover,
  .card-clickable:hover {
    transform: none;
  }
  
  .result-card--dragging {
    transform: none;
  }
  
  .status-indicator,
  .card-status-indicator {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .card,
  .enhanced-card,
  .overview-card,
  .result-card {
    border-width: 2px;
  }
  
  .result-card__status-tag {
    border: 1px solid currentColor;
  }
  
  .glass-card {
    border-width: 2px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .card-clickable,
  .result-card {
    min-height: 44px;
    min-width: 44px;
  }
}