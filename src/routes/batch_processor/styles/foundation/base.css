@tailwind base;
@tailwind components;
@tailwind utilities;

/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🌍 BASE STYLES - 基础样式
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了globals.css和layout-fix.css的核心功能
 * 提供全局重置、基础样式和布局修复
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces globals.css + layout-fix.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔄 GLOBAL RESETS - 全局重置
 * ═══════════════════════════════════════════════════════════════════════════ */

* {
  box-sizing: border-box;
}

html {
  font-family: var(--font-family);
  font-feature-settings: 'kern' 1, 'liga' 1;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
  overflow: auto; /* 允许页面滚动 */
  height: 100%;
}

body {
  margin: 0;
  padding: 0;
  background: var(--gradient-main);
  color: var(--text-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  min-height: 100vh; /* 最小高度填充视口，允许超出 */
  overflow: auto; /* 允许body滚动 */
  position: static; /* 允许自然滚动 */
  width: 100%;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏗️ LAYOUT SYSTEM - 布局系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 主布局容器 - 三栏网格布局 */
.batch-processor-layout {
  display: grid !important;
  min-height: 100vh; /* 最小高度填充视口，允许超出 */
  width: 100%;
  margin: 0;
  padding: var(--layout-gap);
  gap: var(--layout-gap);
  box-sizing: border-box;
  overflow: visible; /* 允许主容器内容溢出和滚动 */
  position: static; /* 静态定位允许自然滚动 */

  /* 网格布局 - 强制三栏布局 */
  grid-template-columns: var(--sidebar-width) 1fr var(--console-width) !important;
  grid-template-rows: 1fr; /* 单行填充全部高度 */

  /* 确保最小可用空间 */
  min-width: 1200px;

  /* 主容器背景 - 商务蓝灰渐变 */
  background: linear-gradient(135deg,
    #f8fafc 0%,   /* 极浅灰蓝 */
    #f1f5f9 35%,  /* 浅灰蓝 */
    #e2e8f0 65%,  /* 中浅灰蓝 */
    #f0f8ff 100%  /* 浅天蓝 */
  );

  /* z-index层级 */
  z-index: 0;
}

/* 栏容器基础样式 */
.layout-sidebar,
.layout-main,
.layout-console {
  border-radius: 12px;
  padding: var(--layout-padding);
  box-sizing: border-box;
  min-height: calc(100vh - 24px); /* 最小高度，允许内容超出 */
  overflow: visible; /* 允许容器内容溢出 */
  display: flex !important;
  flex-direction: column;
  transition: all 0.3s ease;
  
  /* 确保所有栏都可见 */
  opacity: 1 !important;
  visibility: visible !important;

  /* 背景和边框 */
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(195, 226, 251, 0.15);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);

  /* z-index层级 */
  position: relative;
  z-index: 2;
}

/* 左侧边栏特定样式 */
.layout-sidebar {
  position: relative;
  z-index: 10;
  background: transparent;
}

/* 主内容区域 */
.layout-main {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 右侧控制台 */
.layout-console {
  background: var(--gradient-business-blue);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE LAYOUT - 响应式布局
 * ═══════════════════════════════════════════════════════════════════════════ */

@media (max-width: 1200px) {
  .batch-processor-layout {
    grid-template-columns: var(--sidebar-width) 1fr var(--console-width) !important;
    min-width: 1000px;
  }
}

@media (max-width: 768px) {
  .batch-processor-layout {
    grid-template-columns: 1fr !important;
    min-width: auto;
    padding: var(--space-2);
    gap: var(--space-2);
  }
  
  .layout-sidebar,
  .layout-main,
  .layout-console {
    min-height: auto;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 TAILWIND COMPONENTS - Tailwind组件层
 * ═══════════════════════════════════════════════════════════════════════════ */

@layer components {
  /* 基础按钮样式 - 与组件系统配合 */
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-300 focus:outline-none relative;
  }

  /* 基础输入框样式 - 与组件系统配合 */
  .input {
    @apply block w-full rounded-lg px-4 py-3 focus:outline-none transition-all duration-300;
  }

  /* 基础徽章样式 - 与组件系统配合 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-all duration-200;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 UTILITY CLASSES - 工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 动画工具类 */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动优化 */
.scroll-smooth {
  scroll-behavior: smooth;
}

.scroll-optimized {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: thin;
  scrollbar-color: var(--color-gray-300) transparent;
}

.scroll-optimized::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scroll-optimized::-webkit-scrollbar-track {
  background: transparent;
}

.scroll-optimized::-webkit-scrollbar-thumb {
  background: var(--color-gray-300);
  border-radius: 3px;
}

.scroll-optimized::-webkit-scrollbar-thumb:hover {
  background: var(--color-gray-400);
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY - 无障碍访问
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .layout-sidebar,
  .layout-main,
  .layout-console {
    border-color: var(--color-gray-600);
  }
}

/* 焦点可见性增强 */
.focus-visible-enhanced:focus-visible {
  outline: 3px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: 4px;
}