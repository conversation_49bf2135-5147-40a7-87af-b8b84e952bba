/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * ⚡ UNIFIED ANIMATION SYSTEM - 统一动画系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了modules/animations/keyframes.css + modules/animations/utilities.css
 * 提供完整的动画解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces modules/animations/keyframes.css + modules/animations/utilities.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎭 KEYFRAMES DEFINITIONS - 动画关键帧定义
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 🌟 页面进入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 🔄 旋转动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 💓 脉冲动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes historyPulse {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.02);
  }
}

/* ✨ 按钮光晕动画 */
@keyframes buttonGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(35, 146, 239, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(35, 146, 239, 0.6), 0 0 30px rgba(35, 146, 239, 0.4);
  }
}

/* 🚀 火箭摇摆动画 */
@keyframes rocketShake {
  0%, 100% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(-2deg);
  }
  75% {
    transform: rotate(2deg);
  }
}

/* 🚀 火箭启动动画 - 约束在按钮内的版本 */
@keyframes rocketLaunch {
  0% {
    transform: translateY(0) scale(1) rotate(0deg);
    opacity: 1;
  }
  20% {
    transform: translateY(-3px) scale(1.1) rotate(-2deg);
    opacity: 1;
  }
  40% {
    transform: translateY(-6px) scale(1.2) rotate(2deg);
    opacity: 0.9;
  }
  60% {
    transform: translateY(-8px) scale(1.3) rotate(3deg);
    opacity: 0.8;
  }
  80% {
    transform: translateY(-10px) scale(1.4) rotate(5deg);
    opacity: 0.7;
  }
  100% {
    transform: translateY(-12px) scale(1.5) rotate(8deg);
    opacity: 0.6;
  }
}

/* ☁️ 云朵悬浮动画 */
@keyframes cloudFloat {
  0%, 100% {
    transform: translateX(-10px) translateY(0px);
    opacity: 0.7;
  }
  25% {
    transform: translateX(5px) translateY(-5px);
    opacity: 0.8;
  }
  50% {
    transform: translateX(10px) translateY(0px);
    opacity: 0.9;
  }
  75% {
    transform: translateX(-5px) translateY(5px);
    opacity: 0.8;
  }
}

/* ☁️ 云朵消失动画 */
@keyframes cloudFadeOut {
  0% {
    transform: translateX(0) translateY(0) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateX(20px) translateY(-10px) scale(1.1);
    opacity: 0.4;
  }
  100% {
    transform: translateX(50px) translateY(-30px) scale(1.3);
    opacity: 0;
  }
}

/* 🔥 火箭尾焰动画 */
@keyframes rocketFlame {
  0%, 100% {
    transform: scaleY(0.8) scaleX(1);
    opacity: 0.8;
  }
  50% {
    transform: scaleY(1.2) scaleX(0.9);
    opacity: 1;
  }
}

/* 🔥 火箭尾焰动画 - 变体2 */
@keyframes rocketFlame-2 {
  0%, 100% {
    transform: scaleY(0.6) scaleX(1.1) rotate(-2deg);
    opacity: 0.6;
  }
  33% {
    transform: scaleY(1.1) scaleX(0.8) rotate(1deg);
    opacity: 0.9;
  }
  66% {
    transform: scaleY(0.9) scaleX(1.2) rotate(-1deg);
    opacity: 0.7;
  }
}

/* 🚀 按钮发射背景动画 - 修复版：只改变阴影，保持金色背景 */
@keyframes buttonLaunchGlow {
  0% {
    box-shadow: 0 8px 25px rgba(251, 191, 36, 0.4), 0 4px 12px rgba(217, 119, 6, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }
  25% {
    box-shadow: 0 12px 30px rgba(255, 215, 0, 0.6), 0 6px 18px rgba(251, 191, 36, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }
  50% {
    box-shadow: 0 16px 35px rgba(255, 215, 0, 0.8), 0 8px 20px rgba(251, 191, 36, 0.6), inset 0 1px 0 rgba(255, 255, 255, 0.7);
  }
  75% {
    box-shadow: 0 14px 32px rgba(255, 215, 0, 0.7), 0 7px 18px rgba(251, 191, 36, 0.5), inset 0 1px 0 rgba(255, 255, 255, 0.6);
  }
  100% {
    box-shadow: 0 10px 28px rgba(251, 191, 36, 0.5), 0 5px 15px rgba(217, 119, 6, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.5);
  }
}

/* 🌊 波浪动画 */
@keyframes wave {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 💫 闪烁动画 */
@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

/* 🎯 弹跳动画 */
@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translateY(0);
  }
  40%, 43% {
    transform: translateY(-10px);
  }
  70% {
    transform: translateY(-5px);
  }
  90% {
    transform: translateY(-2px);
  }
}

/* 📈 缩放动画 */
@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 🌈 渐变移动动画 */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 💎 闪光动画 */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 🎪 摆动动画 */
@keyframes swing {
  20% {
    transform: rotate(15deg);
  }
  40% {
    transform: rotate(-10deg);
  }
  60% {
    transform: rotate(5deg);
  }
  80% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

/* 🎯 淡入动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 🎯 淡出动画 */
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

/* 🎯 滑入动画 */
@keyframes slideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 🎯 滑出动画 */
@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* ❌ 错误状态脉冲动画 */
@keyframes errorPulse {
  0%, 100% {
    border-color: var(--color-error-300);
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  50% {
    border-color: var(--color-error-500);
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.2);
  }
}

/* ✅ 成功状态闪光动画 */
@keyframes successFlash {
  0% {
    background-color: rgba(34, 197, 94, 0.1);
    border-color: var(--color-success-300);
  }
  50% {
    background-color: rgba(34, 197, 94, 0.3);
    border-color: var(--color-success-500);
  }
  100% {
    background-color: rgba(34, 197, 94, 0.1);
    border-color: var(--color-success-300);
  }
}

/* ⚠️ 警告状态闪烁动画 */
@keyframes warningBlink {
  0%, 100% {
    background-color: rgba(245, 158, 11, 0.1);
    border-color: var(--color-warning-300);
  }
  50% {
    background-color: rgba(245, 158, 11, 0.2);
    border-color: var(--color-warning-500);
  }
}

/* 文字发光动画 */
@keyframes textGlow {
  0% {
    text-shadow: 
      0 0 5px rgba(255, 255, 255, 0.8),
      0 0 10px rgba(100, 181, 246, 0.6),
      0 0 15px rgba(100, 181, 246, 0.4),
      0 0 20px rgba(100, 181, 246, 0.2);
  }
  100% {
    text-shadow: 
      0 0 8px rgba(255, 255, 255, 1),
      0 0 15px rgba(100, 181, 246, 0.8),
      0 0 25px rgba(100, 181, 246, 0.6),
      0 0 35px rgba(100, 181, 246, 0.4);
  }
}

/* 星光移动动画 */
@keyframes sparkleMove {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(0deg);
  }
  50% {
    transform: translateX(0%) translateY(0%) rotate(180deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(360deg);
  }
}

/* 星光浮动动画 */
@keyframes sparkleFloat {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0.8) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translate(-50%, -50%) scale(1.2) rotate(180deg);
    opacity: 1;
  }
}

/* 按钮星光背景动画 */
@keyframes starfieldMove {
  0% {
    transform: translateX(-100%) translateY(-100%);
    opacity: 0;
  }

  10% {
    opacity: 1;
  }

  90% {
    opacity: 1;
  }

  100% {
    transform: translateX(100%) translateY(100%);
    opacity: 0;
  }
}

/* 星光闪烁动画 */
@keyframes starTwinkle {
  0% {
    opacity: 0.8;
    transform: scale(1.0);
  }

  25% {
    opacity: 1;
    transform: scale(1.4);
  }

  50% {
    opacity: 1;
    transform: scale(2.0);
  }

  75% {
    opacity: 1;
    transform: scale(1.4);
  }

  100% {
    opacity: 0.8;
    transform: scale(1.0);
  }
}

/* 星光扫过动画 */
@keyframes starSweep {
  0% {
    transform: translateX(-250%) translateY(-250%) skewX(-15deg);
    opacity: 0;
  }

  10% {
    opacity: 0.5;
  }

  30% {
    opacity: 1;
  }

  50% {
    opacity: 1;
    transform: translateX(0%) translateY(0%) skewX(-15deg);
  }

  70% {
    opacity: 1;
  }

  90% {
    opacity: 0.5;
  }

  100% {
    transform: translateX(250%) translateY(250%) skewX(-15deg);
    opacity: 0;
  }
}

/* 图标脉冲动画 */
@keyframes iconPulse {

  0%,
  100% {
    box-shadow:
      0 4px 12px rgba(59, 130, 246, 0.4),
      0 0 30px rgba(255, 255, 255, 0.3),
      0 0 50px rgba(59, 130, 246, 0.2);
  }

  50% {
    box-shadow:
      0 6px 20px rgba(59, 130, 246, 0.6),
      0 0 50px rgba(255, 255, 255, 0.5),
      0 0 80px rgba(59, 130, 246, 0.4);
  }
}

/* 温柔的图标呼吸动画 */
@keyframes gentleIconBreathe {

  0%,
  100% {
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.3),
      0 0 40px rgba(147, 197, 253, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.3);
    transform: scale(1);
  }

  50% {
    box-shadow:
      0 0 30px rgba(59, 130, 246, 0.4),
      0 0 60px rgba(147, 197, 253, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.4);
    transform: scale(1.02);
  }
}

/* 超柔和图标呼吸动画 - 增强幅度版本 */
@keyframes ultraGentleIconBreathe {
  0%, 100% {
    transform: scale(1);
    box-shadow:
      0 0 20px rgba(59, 130, 246, 0.25),
      0 0 40px rgba(147, 197, 253, 0.2),
      inset 0 2px 0 rgba(255, 255, 255, 0.3);
  }

  25% {
    transform: scale(1.015);
    box-shadow:
      0 0 25px rgba(59, 130, 246, 0.35),
      0 0 50px rgba(147, 197, 253, 0.25),
      inset 0 2px 0 rgba(255, 255, 255, 0.35);
  }

  50% {
    transform: scale(1.03);
    box-shadow:
      0 0 30px rgba(59, 130, 246, 0.4),
      0 0 60px rgba(147, 197, 253, 0.3),
      inset 0 2px 0 rgba(255, 255, 255, 0.4);
  }

  75% {
    transform: scale(1.015);
    box-shadow:
      0 0 25px rgba(59, 130, 246, 0.35),
      0 0 50px rgba(147, 197, 253, 0.25),
      inset 0 2px 0 rgba(255, 255, 255, 0.35);
  }
}

/* 温柔的星光闪烁动画 */
@keyframes gentleStarTwinkle {

  0%,
  100% {
    opacity: 0.6;
    transform: scale(0.9);
  }

  33% {
    opacity: 0.8;
    transform: scale(1.0);
  }

  66% {
    opacity: 1.0;
    transform: scale(1.1);
  }
}

/* 超柔和星光闪烁动画 - 增强版本 */
@keyframes ultraGentleStarTwinkle {
  0%, 100% {
    opacity: 0.5;
    transform: scale(0.9);
  }

  20% {
    opacity: 0.7;
    transform: scale(0.95);
  }

  40% {
    opacity: 0.8;
    transform: scale(1);
  }

  60% {
    opacity: 0.9;
    transform: scale(1.05);
  }

  80% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* 白色星光闪烁动画 - 惊喜感 */
@keyframes whiteStarTwinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8) rotate(0deg);
    filter: brightness(1);
  }

  25% {
    opacity: 0.7;
    transform: scale(1.2) rotate(90deg);
    filter: brightness(1.3);
  }

  50% {
    opacity: 1;
    transform: scale(1.5) rotate(180deg);
    filter: brightness(1.6);
  }

  75% {
    opacity: 0.8;
    transform: scale(1.1) rotate(270deg);
    filter: brightness(1.2);
  }
}

/* 白色光芒扫过动画 */
@keyframes whiteStarSweep {
  0% {
    transform: translateX(-200%) translateY(-200%) rotate(-45deg);
    opacity: 0;
  }

  20% {
    opacity: 0.6;
  }

  50% {
    opacity: 1;
    transform: translateX(0%) translateY(0%) rotate(-45deg);
  }

  80% {
    opacity: 0.6;
  }

  100% {
    transform: translateX(200%) translateY(200%) rotate(-45deg);
    opacity: 0;
  }
}

/* 白色星点闪烁动画 */
@keyframes whiteSparkle {
  0%, 100% {
    opacity: 0.2;
    transform: scale(0.5);
  }

  10% {
    opacity: 0.8;
    transform: scale(1.2);
  }

  20% {
    opacity: 0.3;
    transform: scale(0.7);
  }

  30% {
    opacity: 1;
    transform: scale(1.5);
  }

  40% {
    opacity: 0.4;
    transform: scale(0.8);
  }

  50% {
    opacity: 0.9;
    transform: scale(1.3);
  }

  60% {
    opacity: 0.5;
    transform: scale(0.9);
  }

  70% {
    opacity: 1;
    transform: scale(1.4);
  }

  80% {
    opacity: 0.3;
    transform: scale(0.6);
  }

  90% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 惊喜星光爆发动画 */
@keyframes surpriseStarBurst {
  0% {
    opacity: 0;
    transform: scale(0) rotate(0deg);
  }

  20% {
    opacity: 0.8;
    transform: scale(1.5) rotate(72deg);
  }

  40% {
    opacity: 1;
    transform: scale(2.2) rotate(144deg);
  }

  60% {
    opacity: 0.9;
    transform: scale(1.8) rotate(216deg);
  }

  80% {
    opacity: 0.6;
    transform: scale(1.2) rotate(288deg);
  }

  100% {
    opacity: 0;
    transform: scale(0.5) rotate(360deg);
  }
}

/* 随机闪烁动画 - 第一组 */
@keyframes randomTwinkle1 {
  0%, 100% { opacity: 0.3; transform: scale(0.8); }
  15% { opacity: 1; transform: scale(1.4); }
  30% { opacity: 0.2; transform: scale(0.6); }
  45% { opacity: 0.9; transform: scale(1.2); }
  60% { opacity: 0.4; transform: scale(0.9); }
  75% { opacity: 0.8; transform: scale(1.1); }
  90% { opacity: 0.5; transform: scale(0.7); }
}

/* 随机闪烁动画 - 第二组 */
@keyframes randomTwinkle2 {
  0%, 100% { opacity: 0.4; transform: scale(0.9); }
  20% { opacity: 0.8; transform: scale(1.3); }
  40% { opacity: 0.3; transform: scale(0.7); }
  60% { opacity: 1; transform: scale(1.5); }
  80% { opacity: 0.6; transform: scale(1.0); }
}

/* 随机闪烁动画 - 第三组 */
@keyframes randomTwinkle3 {
  0%, 100% { opacity: 0.2; transform: scale(0.6); }
  25% { opacity: 0.9; transform: scale(1.2); }
  50% { opacity: 0.5; transform: scale(0.8); }
  75% { opacity: 1; transform: scale(1.4); }
}

/* 星光闪烁动画 - 用于新的金色按钮 */
@keyframes starlightTwinkle {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.05);
  }
}

/* 增强版星光闪烁动画 - 更快更明显 */
@keyframes enhancedStarlightTwinkle {
  0%, 100% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.08);
  }
}

/* 闪光效果动画 - 用于额外的光芒层 */
@keyframes sparkleGlow {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
}

/* 超强版星光闪烁动画 - 最快最明显 */
@keyframes superStarlightTwinkle {
  0%, 100% {
    opacity: 0.8;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.12);
  }
}

/* Hover状态的强化星光闪烁动画 */
@keyframes hoverStarTwinkle {
  0%, 100% {
    opacity: 0.7;
    transform: scale(0.95);
    filter: drop-shadow(0 0 6px rgba(255, 255, 255, 0.6));
  }

  25% {
    opacity: 0.9;
    transform: scale(1);
    filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.8));
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
    filter: drop-shadow(0 0 12px rgba(255, 255, 255, 1));
  }

  75% {
    opacity: 0.9;
    transform: scale(1.03);
    filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.9));
  }
}

/* 图标本身的呼吸动画 */
@keyframes iconBreathe {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1) drop-shadow(0 0 2px rgba(251, 205, 44, 0.3));
  }

  25% {
    transform: scale(1.02);
    filter: brightness(1.05) drop-shadow(0 0 4px rgba(251, 205, 44, 0.4));
  }

  50% {
    transform: scale(1.05);
    filter: brightness(0.98) drop-shadow(0 0 6px rgba(251, 205, 44, 0.5));
  }

  75% {
    transform: scale(1.02);
    filter: brightness(1.05) drop-shadow(0 0 4px rgba(251, 205, 44, 0.4));
  }
}

/* Hover时图标的增强呼吸动画 */
@keyframes iconHoverBreathe {
  0%, 100% {
    transform: scale(1);
    filter: brightness(1) drop-shadow(0 0 4px rgba(251, 205, 44, 0.5));
  }

  25% {
    transform: scale(1.03);
    filter: brightness(1.05) drop-shadow(0 0 6px rgba(251, 205, 44, 0.6));
  }

  50% {
    transform: scale(1.08);
    filter: brightness(1.1) drop-shadow(0 0 8px rgba(251, 205, 44, 0.7));
  }

  75% {
    transform: scale(1.03);
    filter: brightness(1.05) drop-shadow(0 0 6px rgba(251, 205, 44, 0.6));
  }
}
/* 呼吸动画 */
@keyframes breathe {
  0%, 100% {
    transform: scale(1) rotate(0deg);
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.3), 0 0 30px rgba(59, 130, 246, 0.2);
  }
  50% {
    transform: scale(1.1) rotate(1deg);
    box-shadow: 0 0 25px rgba(59, 130, 246, 0.5), 0 0 50px rgba(59, 130, 246, 0.3);
  }
}

/* 星光闪烁动画 */
@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(0.8) rotate(0deg);
  }
  25% {
    opacity: 1;
    transform: scale(1.2) rotate(90deg);
  }
  50% {
    opacity: 0.6;
    transform: scale(1) rotate(180deg);
  }
  75% {
    opacity: 0.9;
    transform: scale(1.1) rotate(270deg);
  }
}

/* 抽屉滑入动画 */
@keyframes drawerSlideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 抽屉滑出动画 */
@keyframes drawerSlideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 左侧抽屉滑入动画 */
@keyframes leftDrawerSlideIn {
  from {
    transform: translateX(-100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 右侧抽屉滑入动画 */
@keyframes rightDrawerSlideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 右侧抽屉滑出动画 */
@keyframes rightDrawerSlideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 滑动指示器脉冲动画 */
@keyframes slideIndicatorPulse {
  0%, 100% {
    opacity: 0.8;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
  }
}

/* 滑动指示器呼吸动画 */
@keyframes slideIndicatorBreath {
  0%, 100% {
    opacity: 0.6;
    box-shadow: 2px 0 8px rgba(139, 92, 246, 0.3);
  }
  50% {
    opacity: 0.9;
    box-shadow: 2px 0 12px rgba(139, 92, 246, 0.5);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 UTILITY CLASSES - 动画工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

.animate-spin {
  animation: spin 1s linear infinite;
  will-change: transform;
  backface-visibility: hidden;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-rocket-launch {
  animation: rocketLaunch 3s ease-out forwards !important;
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
  transform-origin: center center;
}

.animate-cloud-float {
  animation: cloudFloat 3s ease-in-out infinite;
  will-change: transform, opacity;
}

.animate-cloud-fade-out {
  animation: cloudFadeOut 1.5s ease-out forwards;
  will-change: transform, opacity;
}

.animate-rocket-flame {
  animation: rocketFlame 0.3s ease-in-out infinite;
  will-change: transform, opacity;
}

.animate-rocket-flame-2 {
  animation: rocketFlame-2 0.4s ease-in-out infinite;
  will-change: transform, opacity;
}

.animate-shimmer {
  animation: shimmer 2s linear infinite;
  will-change: background-position;
}

.animate-button-launch-glow {
  animation: buttonLaunchGlow 3s ease-in-out forwards;
  will-change: box-shadow, background;
}

/* 基础动画工具类 */
.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
  will-change: opacity;
}

.animate-fade-out {
  animation: fadeOut 0.3s ease-out;
  will-change: opacity;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
  will-change: transform, opacity;
}

.animate-slide-out {
  animation: slideOut 0.3s ease-out;
  will-change: transform, opacity;
}

.animate-bounce {
  animation: bounce 1s ease-in-out;
  will-change: transform;
}

.animate-swing {
  animation: swing 1s ease-in-out;
  will-change: transform;
}

/* 状态动画工具类 */
.animate-error-pulse {
  animation: errorPulse 1s ease-in-out 3;
  will-change: border-color, box-shadow;
}

.animate-success-flash {
  animation: successFlash 0.6s ease-in-out 2;
  will-change: background-color, border-color;
}

.animate-warning-blink {
  animation: warningBlink 0.8s ease-in-out 3;
  will-change: background-color, border-color;
}

/* 高光效果动画工具类 */
.animate-sparkle-move {
  animation: sparkleMove 3s ease-in-out infinite;
  will-change: transform, opacity;
}

.animate-sparkle-float {
  animation: sparkleFloat 2s ease-in-out infinite;
  will-change: transform, opacity;
}

.animate-text-glow {
  animation: textGlow 2s ease-in-out infinite;
  will-change: text-shadow;
}

.animate-button-glow {
  animation: buttonGlow 2s ease-in-out infinite;
  will-change: box-shadow;
}

.animate-breathe {
  animation: breathe 3s ease-in-out infinite;
  will-change: transform, box-shadow;
}

.animate-twinkle {
  animation: twinkle 2s ease-in-out infinite;
  will-change: transform, opacity;
}

/* 星光效果工具类 */
.animate-star-twinkle {
  animation: starTwinkle 3s ease-in-out infinite;
  will-change: transform, opacity;
}

.animate-star-sweep {
  animation: starSweep 4s ease-in-out infinite;
  will-change: transform, opacity;
}

.animate-starfield-move {
  animation: starfieldMove 6s ease-in-out infinite;
  will-change: transform, opacity;
}
/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 PERFORMANCE OPTIMIZATIONS - 性能优化
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 硬件加速优化 */
.animate-fade-in-up,
.animate-slide-in,
.animate-slide-out,
.animate-bounce,
.animate-swing {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE DESIGN - 响应式设计
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 移动端优化 - 减少动画复杂度 */
@media (max-width: 768px) {
  .animate-fade-in-up {
    animation-duration: 0.3s;
  }
  
  /* 在移动端禁用复杂动画 */
  .animate-button-glow,
  .animate-sparkle-move,
  .animate-sparkle-float,
  .animate-text-glow,
  .animate-shimmer {
    animation: none;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY - 无障碍
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 🎆 DOM-based particle animations removed - now using SVG-based GoldenParticleEffect component */
/* See: components/GoldenParticleEffect.tsx for SVG implementation */

/* 减少动画偏好设置 */
@media (prefers-reduced-motion: reduce) {
  .animate-fade-in-up,
  .animate-pulse,
  .animate-rocket-launch,
  .animate-rocket-flame,
  .animate-rocket-flame-2,
  .animate-button-launch-glow,
  .animate-shimmer,
  .animate-fade-in,
  .animate-fade-out,
  .animate-slide-in,
  .animate-slide-out,
  .animate-bounce,
  .animate-swing,
  .animate-error-pulse,
  .animate-success-flash,
  .animate-warning-blink,
  .animate-sparkle-move,
  .animate-sparkle-float,
  .animate-text-glow,
  .animate-button-glow,
  .animate-breathe,
  .animate-twinkle,
  .animate-cloud-float,
  .animate-cloud-fade-out {
    animation: none;
  }
  
  /* 保留关键的处理状态动画，但减慢速度 */
  .animate-spin {
    animation: spin 2s linear infinite !important;
  }

  /* SVG-based particle effects respect reduced motion preferences automatically */
}