/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🧩 UNIFIED UTILITIES SYSTEM - 统一工具类系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了所有工具类和辅助样式：
 * - component-utilities.css + components/tooltip.css + border-optimization.css
 * - design-system/responsive.css + modules/responsive-layout.css
 * - design-system/layout.css + layout-fix.css + layout-fixes.css
 * 提供完整的工具类和辅助样式解决方案
 *
 * @version 5.0 - 完整整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces 所有工具类和布局辅助文件
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 📐 RESPONSIVE DESIGN SYSTEM - 响应式设计系统
 * ═══════════════════════════════════════════════════════════════════════════ */

:root {
  /* 响应式断点 */
  --breakpoint-xs: 480px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
  --breakpoint-3xl: 1920px;
  
  /* 侧边栏和控制台宽度 */
  --sidebar-width: 380px;
  --console-width: 350px;
  --sidebar-width-collapsed: 280px;
  --console-width-collapsed: 300px;
}

/* 批处理器主布局 - 响应式网格 */
.batch-processor-layout {
  display: grid;
  grid-template-columns: var(--sidebar-width) 1fr var(--console-width);
  gap: 16px;
  height: 100vh;
  overflow: hidden;
  padding: 16px;
  background: var(--color-bg-primary);
  contain: layout style;
}

/* 响应式断点调整 */
@media (max-width: 1536px) {
  .batch-processor-layout {
    grid-template-columns: 360px 1fr 320px;
  }
}

@media (max-width: 1366px) {
  .batch-processor-layout {
    grid-template-columns: 340px 1fr 300px;
  }
}

@media (max-width: 1200px) {
  .batch-processor-layout {
    grid-template-columns: 320px 1fr 280px;
    gap: 12px;
    padding: 12px;
  }
}

@media (max-width: 1024px) {
  .batch-processor-layout {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 100vh;
    overflow: hidden;
  }
  
  .layout-sidebar {
    order: 1;
    height: auto;
    max-height: 50vh;
    min-height: 300px;
  }
  
  .layout-main {
    order: 2;
    flex: 1;
    min-height: 0;
  }
  
  .layout-console {
    order: 3;
    height: auto;
    max-height: 25vh;
    min-height: 200px;
  }
}

@media (max-width: 768px) {
  .batch-processor-layout {
    padding: 8px;
    gap: 8px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏗️ LAYOUT SYSTEM - 布局系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* Grid 系统 */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

/* Flexbox 系统 */
.flex { display: flex; }
.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }
.flex-grow { flex-grow: 1; }
.flex-shrink { flex-shrink: 1; }
.flex-shrink-0 { flex-shrink: 0; }

/* 对齐 */
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* 间距 */
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-5 { gap: 1.25rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }
.gap-10 { gap: 2.5rem; }
.gap-12 { gap: 3rem; }

/* 位置 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }
.static { position: static; }

/* 尺寸 */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-1\/2 { width: 50%; }
.w-1\/3 { width: 33.333333%; }
.w-2\/3 { width: 66.666667%; }
.w-1\/4 { width: 25%; }
.w-3\/4 { width: 75%; }
.w-fit { width: fit-content; }
.w-max { width: max-content; }
.w-min { width: min-content; }

.h-full { height: 100%; }
.h-auto { height: auto; }
.h-screen { height: 100vh; }
.h-fit { height: fit-content; }
.h-max { height: max-content; }
.h-min { height: min-content; }

/* 最小/最大尺寸 */
.min-h-0 { min-height: 0; }
.min-h-full { min-height: 100%; }
.min-h-screen { min-height: 100vh; }
.max-h-full { max-height: 100%; }
.max-h-screen { max-height: 100vh; }
.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }
.max-w-full { max-width: 100%; }

/* 布局修复 */
.batch-processor-layout {
  display: grid !important;
  min-height: 100vh;
  overflow: visible;
  contain: layout style;
}

/* 侧边栏滚动修复 */
.layout-sidebar {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100%;
  max-height: calc(100vh - 32px);
  contain: layout style;
}

/* 按钮可见性修复 */
.layout-sidebar .btn-authority {
  position: relative !important;
  z-index: 60 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  visibility: visible !important;
  opacity: 1 !important;
  pointer-events: auto !important;
  overflow: visible !important;
  contain: none !important;
}

/* 主布局容器修复 */
.layout-main {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100%;
  max-height: calc(100vh - 32px);
  contain: layout style;
}

/* 控制台布局修复 */
.layout-console {
  position: relative;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  height: 100%;
  max-height: calc(100vh - 32px);
  contain: layout style;
}

/* 防止内容溢出 */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-visible { overflow: visible; }
.overflow-x-hidden { overflow-x: hidden; }
.overflow-y-hidden { overflow-y: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-y-auto { overflow-y: auto; }
.overflow-x-scroll { overflow-x: scroll; }
.overflow-y-scroll { overflow-y: scroll; }

/* 层级管理 */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }

/* 可见性 */
.visible { visibility: visible; }
.invisible { visibility: hidden; }
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 指针事件 */
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

/* 用户选择 */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-auto { user-select: auto; }

/* 对象适应 */
.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

/* 变换 */
.transform { transform: translateZ(0); }
.transform-none { transform: none; }
.rotate-0 { transform: rotate(0deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }
.rotate-270 { transform: rotate(270deg); }
.scale-0 { transform: scale(0); }
.scale-50 { transform: scale(0.5); }
.scale-75 { transform: scale(0.75); }
.scale-90 { transform: scale(0.9); }
.scale-95 { transform: scale(0.95); }
.scale-100 { transform: scale(1); }
.scale-105 { transform: scale(1.05); }
.scale-110 { transform: scale(1.1); }
.scale-125 { transform: scale(1.25); }
.scale-150 { transform: scale(1.5); }

/* 过渡 */
.transition-none { transition: none; }
.transition-all { transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1); }
.transition-colors { transition: color 150ms cubic-bezier(0.4, 0, 0.2, 1), background-color 150ms cubic-bezier(0.4, 0, 0.2, 1), border-color 150ms cubic-bezier(0.4, 0, 0.2, 1); }
.transition-opacity { transition: opacity 150ms cubic-bezier(0.4, 0, 0.2, 1); }
.transition-shadow { transition: box-shadow 150ms cubic-bezier(0.4, 0, 0.2, 1); }
.transition-transform { transition: transform 150ms cubic-bezier(0.4, 0, 0.2, 1); }

/* 时长 */
.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }
.duration-700 { transition-duration: 700ms; }
.duration-1000 { transition-duration: 1000ms; }

/* 缓动 */
.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* 边距 */
.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-5 { margin: 1.25rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }
.m-10 { margin: 2.5rem; }
.m-12 { margin: 3rem; }
.m-auto { margin: auto; }

/* 单向边距 */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mt-5 { margin-top: 1.25rem; }
.mt-6 { margin-top: 1.5rem; }
.mt-8 { margin-top: 2rem; }
.mt-10 { margin-top: 2.5rem; }
.mt-12 { margin-top: 3rem; }
.mt-auto { margin-top: auto; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.mr-3 { margin-right: 0.75rem; }
.mr-4 { margin-right: 1rem; }
.mr-5 { margin-right: 1.25rem; }
.mr-6 { margin-right: 1.5rem; }
.mr-8 { margin-right: 2rem; }
.mr-10 { margin-right: 2.5rem; }
.mr-12 { margin-right: 3rem; }
.mr-auto { margin-right: auto; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-5 { margin-bottom: 1.25rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-auto { margin-bottom: auto; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }
.ml-3 { margin-left: 0.75rem; }
.ml-4 { margin-left: 1rem; }
.ml-5 { margin-left: 1.25rem; }
.ml-6 { margin-left: 1.5rem; }
.ml-8 { margin-left: 2rem; }
.ml-10 { margin-left: 2.5rem; }
.ml-12 { margin-left: 3rem; }
.ml-auto { margin-left: auto; }

/* 水平垂直边距 */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-1 { margin-left: 0.25rem; margin-right: 0.25rem; }
.mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }
.mx-3 { margin-left: 0.75rem; margin-right: 0.75rem; }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }
.mx-5 { margin-left: 1.25rem; margin-right: 1.25rem; }
.mx-6 { margin-left: 1.5rem; margin-right: 1.5rem; }
.mx-8 { margin-left: 2rem; margin-right: 2rem; }
.mx-auto { margin-left: auto; margin-right: auto; }

.my-0 { margin-top: 0; margin-bottom: 0; }
.my-1 { margin-top: 0.25rem; margin-bottom: 0.25rem; }
.my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }
.my-3 { margin-top: 0.75rem; margin-bottom: 0.75rem; }
.my-4 { margin-top: 1rem; margin-bottom: 1rem; }
.my-5 { margin-top: 1.25rem; margin-bottom: 1.25rem; }
.my-6 { margin-top: 1.5rem; margin-bottom: 1.5rem; }
.my-8 { margin-top: 2rem; margin-bottom: 2rem; }
.my-auto { margin-top: auto; margin-bottom: auto; }

/* 内边距 */
.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-5 { padding: 1.25rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.p-10 { padding: 2.5rem; }
.p-12 { padding: 3rem; }

/* 单向内边距 */
.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 0.25rem; }
.pt-2 { padding-top: 0.5rem; }
.pt-3 { padding-top: 0.75rem; }
.pt-4 { padding-top: 1rem; }
.pt-5 { padding-top: 1.25rem; }
.pt-6 { padding-top: 1.5rem; }
.pt-8 { padding-top: 2rem; }
.pt-10 { padding-top: 2.5rem; }
.pt-12 { padding-top: 3rem; }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: 0.25rem; }
.pr-2 { padding-right: 0.5rem; }
.pr-3 { padding-right: 0.75rem; }
.pr-4 { padding-right: 1rem; }
.pr-5 { padding-right: 1.25rem; }
.pr-6 { padding-right: 1.5rem; }
.pr-8 { padding-right: 2rem; }
.pr-10 { padding-right: 2.5rem; }
.pr-12 { padding-right: 3rem; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 0.25rem; }
.pb-2 { padding-bottom: 0.5rem; }
.pb-3 { padding-bottom: 0.75rem; }
.pb-4 { padding-bottom: 1rem; }
.pb-5 { padding-bottom: 1.25rem; }
.pb-6 { padding-bottom: 1.5rem; }
.pb-8 { padding-bottom: 2rem; }
.pb-10 { padding-bottom: 2.5rem; }
.pb-12 { padding-bottom: 3rem; }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: 0.25rem; }
.pl-2 { padding-left: 0.5rem; }
.pl-3 { padding-left: 0.75rem; }
.pl-4 { padding-left: 1rem; }
.pl-5 { padding-left: 1.25rem; }
.pl-6 { padding-left: 1.5rem; }
.pl-8 { padding-left: 2rem; }
.pl-10 { padding-left: 2.5rem; }
.pl-12 { padding-left: 3rem; }

/* 水平垂直内边距 */
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: 0.25rem; padding-right: 0.25rem; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-5 { padding-left: 1.25rem; padding-right: 1.25rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.py-5 { padding-top: 1.25rem; padding-bottom: 1.25rem; }
.py-6 { padding-top: 1.5rem; padding-bottom: 1.5rem; }
.py-8 { padding-top: 2rem; padding-bottom: 2rem; }

/* 文本对齐 */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* 字体大小 */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-5xl { font-size: 3rem; line-height: 1; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

/* 字体粗细 */
.font-thin { font-weight: 100; }
.font-extralight { font-weight: 200; }
.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }
.font-black { font-weight: 900; }

/* 行高 */
.leading-none { line-height: 1; }
.leading-tight { line-height: 1.25; }
.leading-snug { line-height: 1.375; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }
.leading-loose { line-height: 2; }

/* 文本装饰 */
.underline { text-decoration: underline; }
.overline { text-decoration: overline; }
.line-through { text-decoration: line-through; }
.no-underline { text-decoration: none; }

/* 文本变换 */
.uppercase { text-transform: uppercase; }
.lowercase { text-transform: lowercase; }
.capitalize { text-transform: capitalize; }
.normal-case { text-transform: none; }

/* 字符间距 */
.tracking-tighter { letter-spacing: -0.05em; }
.tracking-tight { letter-spacing: -0.025em; }
.tracking-normal { letter-spacing: 0em; }
.tracking-wide { letter-spacing: 0.025em; }
.tracking-wider { letter-spacing: 0.05em; }
.tracking-widest { letter-spacing: 0.1em; }

/* 文本颜色 - 蓝色主题系统 */
.text-transparent { color: transparent; }
.text-current { color: currentColor; }
.text-black { color: var(--text-blue-900); } /* 替换为最深蓝色 */
.text-white { color: #ffffff; }

/* 蓝色文字系统 - 增强深度层次 */
.text-blue-50 { color: var(--text-blue-50); }
.text-blue-100 { color: var(--text-blue-100); }
.text-blue-200 { color: var(--text-blue-200); }
.text-blue-300 { color: var(--text-blue-300); }
.text-blue-400 { color: var(--text-blue-400); }
.text-blue-500 { color: var(--text-blue-500); }
.text-blue-600 { color: var(--text-blue-600); }
.text-blue-700 { color: var(--text-blue-700); }
.text-blue-800 { color: var(--text-blue-800); }
.text-blue-900 { color: var(--text-blue-900); }
.text-blue-950 { color: var(--text-blue-950); }
.text-blue-975 { color: var(--text-blue-975); }

/* 白色文字系统 - 深色背景用 */
.text-white { color: var(--text-white); }
.text-white-90 { color: var(--text-white-90); }
.text-white-80 { color: var(--text-white-80); }
.text-white-70 { color: var(--text-white-70); }
.text-white-60 { color: var(--text-white-60); }
.text-white-50 { color: var(--text-white-50); }

/* 灰色文字映射到更深的蓝色 - 增强对比度 */
.text-gray-50 { color: var(--text-blue-50); }
.text-gray-100 { color: var(--text-blue-100); }
.text-gray-200 { color: var(--text-blue-200); }
.text-gray-300 { color: var(--text-blue-400); }  /* 跳过300，使用更深的400 */
.text-gray-400 { color: var(--text-blue-500); }  /* 使用更深的蓝色 */
.text-gray-500 { color: var(--text-blue-600); }  /* 使用更深的蓝色 */
.text-gray-600 { color: var(--text-blue-700); }  /* 使用更深的蓝色 */
.text-gray-700 { color: var(--text-blue-800); }  /* 使用更深的蓝色 */
.text-gray-800 { color: var(--text-blue-900); }  /* 使用更深的蓝色 */
.text-gray-900 { color: var(--text-blue-950); }  /* 使用最深的蓝色 */

/* 背景颜色 */
.bg-transparent { background-color: transparent; }
.bg-current { background-color: currentColor; }
.bg-black { background-color: #000000; }
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-gray-400 { background-color: #9ca3af; }
.bg-gray-500 { background-color: #6b7280; }
.bg-gray-600 { background-color: #4b5563; }
.bg-gray-700 { background-color: #374151; }
.bg-gray-800 { background-color: #1f2937; }
.bg-gray-900 { background-color: #111827; }

/* 边框 */
.border-0 { border-width: 0px; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-4 { border-width: 4px; }
.border-8 { border-width: 8px; }
.border-t-0 { border-top-width: 0px; }
.border-t { border-top-width: 1px; }
.border-t-2 { border-top-width: 2px; }
.border-t-4 { border-top-width: 4px; }
.border-t-8 { border-top-width: 8px; }
.border-r-0 { border-right-width: 0px; }
.border-r { border-right-width: 1px; }
.border-r-2 { border-right-width: 2px; }
.border-r-4 { border-right-width: 4px; }
.border-r-8 { border-right-width: 8px; }
.border-b-0 { border-bottom-width: 0px; }
.border-b { border-bottom-width: 1px; }
.border-b-2 { border-bottom-width: 2px; }
.border-b-4 { border-bottom-width: 4px; }
.border-b-8 { border-bottom-width: 8px; }
.border-l-0 { border-left-width: 0px; }
.border-l { border-left-width: 1px; }
.border-l-2 { border-left-width: 2px; }
.border-l-4 { border-left-width: 4px; }
.border-l-8 { border-left-width: 8px; }

/* 边框样式 */
.border-solid { border-style: solid; }
.border-dashed { border-style: dashed; }
.border-dotted { border-style: dotted; }
.border-double { border-style: double; }
.border-none { border-style: none; }

/* 边框圆角 */
.rounded-none { border-radius: 0px; }
.rounded-sm { border-radius: 0.125rem; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-3xl { border-radius: 1.5rem; }
.rounded-full { border-radius: 9999px; }

/* 阴影 */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }
.shadow-inner { box-shadow: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06); }

/* 光标 */
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-help { cursor: help; }

/* 轮廓 */
.outline-none { outline: 2px solid transparent; outline-offset: 2px; }
.outline-white { outline: 2px solid #ffffff; outline-offset: 2px; }
.outline-black { outline: 2px solid #000000; outline-offset: 2px; }

/* 焦点 */
.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:outline-white:focus { outline: 2px solid #ffffff; outline-offset: 2px; }
.focus\:outline-black:focus { outline: 2px solid #000000; outline-offset: 2px; }

/* 悬停 */
.hover\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\:bg-gray-100:hover { background-color: #f3f4f6; }
.hover\:bg-gray-200:hover { background-color: #e5e7eb; }
.hover\:bg-gray-300:hover { background-color: #d1d5db; }
.hover\:bg-gray-400:hover { background-color: #9ca3af; }
.hover\:bg-gray-500:hover { background-color: #6b7280; }
.hover\:bg-gray-600:hover { background-color: #4b5563; }
.hover\:bg-gray-700:hover { background-color: #374151; }
.hover\:bg-gray-800:hover { background-color: #1f2937; }
.hover\:bg-gray-900:hover { background-color: #111827; }

/* Hover状态文字颜色 - 蓝色主题增强 */
.hover\:text-blue-50:hover { color: var(--text-blue-50); }
.hover\:text-blue-100:hover { color: var(--text-blue-100); }
.hover\:text-blue-200:hover { color: var(--text-blue-200); }
.hover\:text-blue-300:hover { color: var(--text-blue-300); }
.hover\:text-blue-400:hover { color: var(--text-blue-400); }
.hover\:text-blue-500:hover { color: var(--text-blue-500); }
.hover\:text-blue-600:hover { color: var(--text-blue-600); }
.hover\:text-blue-700:hover { color: var(--text-blue-700); }
.hover\:text-blue-800:hover { color: var(--text-blue-800); }
.hover\:text-blue-900:hover { color: var(--text-blue-900); }
.hover\:text-blue-950:hover { color: var(--text-blue-950); }
.hover\:text-blue-975:hover { color: var(--text-blue-975); }

/* 白色文字hover状态 */
.hover\:text-white:hover { color: var(--text-white); }
.hover\:text-white-90:hover { color: var(--text-white-90); }
.hover\:text-white-80:hover { color: var(--text-white-80); }

/* 灰色hover映射到更深蓝色 - 增强对比度 */
.hover\:text-gray-50:hover { color: var(--text-blue-50); }
.hover\:text-gray-100:hover { color: var(--text-blue-100); }
.hover\:text-gray-200:hover { color: var(--text-blue-200); }
.hover\:text-gray-300:hover { color: var(--text-blue-400); }  /* 使用更深的蓝色 */
.hover\:text-gray-400:hover { color: var(--text-blue-500); }  /* 使用更深的蓝色 */
.hover\:text-gray-500:hover { color: var(--text-blue-600); }  /* 使用更深的蓝色 */
.hover\:text-gray-600:hover { color: var(--text-blue-700); }  /* 使用更深的蓝色 */
.hover\:text-gray-700:hover { color: var(--text-blue-800); }  /* 使用更深的蓝色 */
.hover\:text-gray-800:hover { color: var(--text-blue-900); }  /* 使用更深的蓝色 */
.hover\:text-gray-900:hover { color: var(--text-blue-950); }  /* 使用最深的蓝色 */

.hover\:shadow-sm:hover { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05); }
.hover\:shadow:hover { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.hover\:shadow-md:hover { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); }
.hover\:shadow-lg:hover { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.hover\:shadow-2xl:hover { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); }

.hover\:scale-105:hover { transform: scale(1.05); }
.hover\:scale-110:hover { transform: scale(1.1); }
.hover\:scale-125:hover { transform: scale(1.25); }
.hover\:-translate-y-1:hover { transform: translateY(-0.25rem); }
.hover\:-translate-y-2:hover { transform: translateY(-0.5rem); }
.hover\:-translate-y-3:hover { transform: translateY(-0.75rem); }

/* 活动状态 */
.active\:bg-gray-50:active { background-color: #f9fafb; }
.active\:bg-gray-100:active { background-color: #f3f4f6; }
.active\:bg-gray-200:active { background-color: #e5e7eb; }
.active\:bg-gray-300:active { background-color: #d1d5db; }
.active\:bg-gray-400:active { background-color: #9ca3af; }
.active\:bg-gray-500:active { background-color: #6b7280; }
.active\:bg-gray-600:active { background-color: #4b5563; }
.active\:bg-gray-700:active { background-color: #374151; }
.active\:bg-gray-800:active { background-color: #1f2937; }
.active\:bg-gray-900:active { background-color: #111827; }

.active\:scale-95:active { transform: scale(0.95); }
.active\:scale-100:active { transform: scale(1); }

/* 禁用状态 */
.disabled\:opacity-50:disabled { opacity: 0.5; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }
.disabled\:bg-gray-50:disabled { background-color: #f9fafb; }
.disabled\:bg-gray-100:disabled { background-color: #f3f4f6; }
.disabled\:bg-gray-200:disabled { background-color: #e5e7eb; }
.disabled\:bg-gray-300:disabled { background-color: #d1d5db; }
.disabled\:text-gray-400:disabled { color: var(--text-blue-400); }
.disabled\:text-gray-500:disabled { color: var(--text-blue-500); }
.disabled\:text-blue-400:disabled { color: var(--text-blue-400); }
.disabled\:text-blue-500:disabled { color: var(--text-blue-500); }
.disabled\:text-blue-600:disabled { color: var(--text-blue-600); }
.disabled\:border-gray-200:disabled { border-color: #e5e7eb; }
.disabled\:border-gray-300:disabled { border-color: #d1d5db; }

/* 响应式前缀 */
@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:inline-block { display: inline-block; }
  .sm\:inline { display: inline; }
  .sm\:flex { display: flex; }
  .sm\:inline-flex { display: inline-flex; }
  .sm\:table { display: table; }
  .sm\:table-caption { display: table-caption; }
  .sm\:table-cell { display: table-cell; }
  .sm\:table-column { display: table-column; }
  .sm\:table-column-group { display: table-column-group; }
  .sm\:table-footer-group { display: table-footer-group; }
  .sm\:table-header-group { display: table-header-group; }
  .sm\:table-row-group { display: table-row-group; }
  .sm\:table-row { display: table-row; }
  .sm\:flow-root { display: flow-root; }
  .sm\:grid { display: grid; }
  .sm\:inline-grid { display: inline-grid; }
  .sm\:contents { display: contents; }
  .sm\:hidden { display: none; }
}

@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:inline-block { display: inline-block; }
  .md\:inline { display: inline; }
  .md\:flex { display: flex; }
  .md\:inline-flex { display: inline-flex; }
  .md\:table { display: table; }
  .md\:table-caption { display: table-caption; }
  .md\:table-cell { display: table-cell; }
  .md\:table-column { display: table-column; }
  .md\:table-column-group { display: table-column-group; }
  .md\:table-footer-group { display: table-footer-group; }
  .md\:table-header-group { display: table-header-group; }
  .md\:table-row-group { display: table-row-group; }
  .md\:table-row { display: table-row; }
  .md\:flow-root { display: flow-root; }
  .md\:grid { display: grid; }
  .md\:inline-grid { display: inline-grid; }
  .md\:contents { display: contents; }
  .md\:hidden { display: none; }
}

@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:inline-block { display: inline-block; }
  .lg\:inline { display: inline; }
  .lg\:flex { display: flex; }
  .lg\:inline-flex { display: inline-flex; }
  .lg\:table { display: table; }
  .lg\:table-caption { display: table-caption; }
  .lg\:table-cell { display: table-cell; }
  .lg\:table-column { display: table-column; }
  .lg\:table-column-group { display: table-column-group; }
  .lg\:table-footer-group { display: table-footer-group; }
  .lg\:table-header-group { display: table-header-group; }
  .lg\:table-row-group { display: table-row-group; }
  .lg\:table-row { display: table-row; }
  .lg\:flow-root { display: flow-root; }
  .lg\:grid { display: grid; }
  .lg\:inline-grid { display: inline-grid; }
  .lg\:contents { display: contents; }
  .lg\:hidden { display: none; }
}

@media (min-width: 1280px) {
  .xl\:block { display: block; }
  .xl\:inline-block { display: inline-block; }
  .xl\:inline { display: inline; }
  .xl\:flex { display: flex; }
  .xl\:inline-flex { display: inline-flex; }
  .xl\:table { display: table; }
  .xl\:table-caption { display: table-caption; }
  .xl\:table-cell { display: table-cell; }
  .xl\:table-column { display: table-column; }
  .xl\:table-column-group { display: table-column-group; }
  .xl\:table-footer-group { display: table-footer-group; }
  .xl\:table-header-group { display: table-header-group; }
  .xl\:table-row-group { display: table-row-group; }
  .xl\:table-row { display: table-row; }
  .xl\:flow-root { display: flow-root; }
  .xl\:grid { display: grid; }
  .xl\:inline-grid { display: inline-grid; }
  .xl\:contents { display: contents; }
  .xl\:hidden { display: none; }
}

@media (min-width: 1536px) {
  .\32 xl\:block { display: block; }
  .\32 xl\:inline-block { display: inline-block; }
  .\32 xl\:inline { display: inline; }
  .\32 xl\:flex { display: flex; }
  .\32 xl\:inline-flex { display: inline-flex; }
  .\32 xl\:table { display: table; }
  .\32 xl\:table-caption { display: table-caption; }
  .\32 xl\:table-cell { display: table-cell; }
  .\32 xl\:table-column { display: table-column; }
  .\32 xl\:table-column-group { display: table-column-group; }
  .\32 xl\:table-footer-group { display: table-footer-group; }
  .\32 xl\:table-header-group { display: table-header-group; }
  .\32 xl\:table-row-group { display: table-row-group; }
  .\32 xl\:table-row { display: table-row; }
  .\32 xl\:flow-root { display: flow-root; }
  .\32 xl\:grid { display: grid; }
  .\32 xl\:inline-grid { display: inline-grid; }
  .\32 xl\:contents { display: contents; }
  .\32 xl\:hidden { display: none; }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 💬 TOOLTIP UTILITIES - 提示框工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

.tooltip-content {
  /* 基础样式 */
  font-size: var(--font-size-xs);
  line-height: var(--line-height-tight);
  max-width: 200px;
  word-wrap: break-word;
  z-index: var(--z-tooltip);
  
  /* 动画过渡 */
  transition: opacity var(--duration-fast) var(--ease-out),
              visibility var(--duration-fast) var(--ease-out),
              transform var(--duration-fast) var(--ease-out);
  
  /* 防止文本选择 */
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  
  /* 性能优化 */
  contain: layout style;
  will-change: opacity, visibility, transform;
  position: absolute;
}

/* 深色主题样式 */
.tooltip-dark {
  background-color: var(--color-gray-800) !important;
  color: var(--color-white) !important;
  border: 1px solid var(--color-gray-700) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
}

.tooltip-arrow-dark {
  background-color: var(--color-gray-800) !important;
  border-color: var(--color-gray-700) !important;
}

/* 位置特定样式 */
.tooltip-top .tooltip-arrow-dark {
  border-top: none !important;
  border-left: none !important;
}

.tooltip-bottom .tooltip-arrow-dark {
  border-bottom: none !important;
  border-right: none !important;
}

.tooltip-left .tooltip-arrow-dark {
  border-left: none !important;
  border-bottom: none !important;
}

.tooltip-right .tooltip-arrow-dark {
  border-right: none !important;
  border-top: none !important;
}

/* 确保tooltip在悬停时可见 */
.tooltip-content[data-visible="true"] {
  opacity: 1 !important;
  visibility: visible !important;
  pointer-events: auto !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 BORDER OPTIMIZATION UTILITIES - 边框优化工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

:root {
  /* 边框颜色 - 统一调色板 */
  --border-subtle: rgba(98, 164, 223, 0.607);
  --border-light: rgba(85, 145, 229, 0.721);
  --border-medium: rgba(83, 131, 198, 0.679);
  --border-hover: rgba(55, 143, 215, 0.653);
  --border-focus: rgba(35, 146, 239, 0.4);

  /* 边框半径 - 统一规格 */
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  
  /* 柔和渐变色系统 */
  --gradient-sky-fresh: linear-gradient(135deg, #7dd3fc 0%, #38bdf8 50%, #87ceeb 100%);
  --gradient-sky-light: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%);
  --gradient-sky-hover: linear-gradient(135deg, #38bdf8 0%, #87ceeb 50%, #0284c7 100%);
  
  --gradient-amber-fresh: linear-gradient(135deg, #fde68a 0%, #fbbf24 50%, #f59e0b 100%);
  --gradient-amber-light: linear-gradient(135deg, #fffbeb 0%, #fef3c7 50%, #fde68a 100%);
  --gradient-amber-hover: linear-gradient(135deg, #fbbf24 0%, #f59e0b 50%, #d97706 100%);
  
  --gradient-background: linear-gradient(135deg, #fafafa 0%, #f8fafc 30%, #f9fafb 70%, #f7f8f9 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255,255,255,0.85) 0%, rgba(252,252,253,0.8) 100%);
  
  /* 柔和阴影系统 */
  --shadow-soft: 0 2px 8px rgba(0, 0, 0, 0.04), 0 1px 3px rgba(0, 0, 0, 0.02);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.06), 0 2px 8px rgba(0, 0, 0, 0.03);
  --shadow-elevated: 0 8px 24px rgba(0, 0, 0, 0.08), 0 4px 12px rgba(0, 0, 0, 0.04);
  --shadow-floating: 0 12px 32px rgba(0, 0, 0, 0.12), 0 6px 16px rgba(0, 0, 0, 0.06);
}

/* 布局优化工具类 */
.layout-soft-shadow {
  border: none !important;
  box-shadow: var(--shadow-soft) !important;
  background: rgba(252, 252, 253, 0.9) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.layout-medium-shadow {
  box-shadow: var(--shadow-medium) !important;
  background: rgba(254, 254, 255, 0.95) !important;
  transform: translateZ(0) !important;
}

.layout-hover-lift {
  box-shadow: var(--shadow-medium) !important;
  transform: translateY(-1px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
}

/* 玻璃效果工具类 */
.glass-effect {
  background: var(--gradient-glass) !important;
  box-shadow: var(--shadow-soft) !important;
  backdrop-filter: blur(16px) !important;
  -webkit-backdrop-filter: blur(16px) !important;
  border-radius: 12px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.glass-effect:hover {
  background: rgba(255, 255, 255, 0.92) !important;
  box-shadow: var(--shadow-medium) !important;
  transform: translateY(-2px) scale(1.005) !important;
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}

/* 按钮工具类 */
.btn-sky-fresh {
  background: var(--gradient-sky-fresh) !important;
  border: none !important;
  box-shadow: var(--shadow-medium) !important;
  color: white !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.btn-sky-fresh:hover {
  background: var(--gradient-sky-hover) !important;
  box-shadow: var(--shadow-elevated) !important;
  transform: translateY(-2px) scale(1.01) !important;
}

.btn-amber-fresh {
  background: var(--gradient-amber-fresh) !important;
  border: none !important;
  box-shadow: var(--shadow-medium) !important;
  color: white !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  position: relative !important;
  overflow: hidden !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.btn-amber-fresh:hover {
  background: var(--gradient-amber-hover) !important;
  box-shadow: var(--shadow-elevated) !important;
  transform: translateY(-2px) scale(1.01) !important;
}

.btn-glass {
  background: var(--gradient-glass) !important;
  border: 1px solid rgba(56, 189, 248, 0.2) !important;
  color: #0369a1 !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.btn-glass:hover {
  background: var(--gradient-sky-light) !important;
  border-color: rgba(56, 189, 248, 0.4) !important;
  color: #0284c7 !important;
  box-shadow: var(--shadow-medium) !important;
  transform: translateY(-0.5px) !important;
}

/* 状态指示器工具类 */
.status-success {
  background: linear-gradient(135deg, #34d399 0%, #10b981 50%, #059669 100%) !important;
  color: white !important;
  box-shadow: 0 0 8px rgba(56, 189, 248, 0.5) !important;
  animation: pulse-success 2s ease-in-out infinite !important;
}

.status-warning {
  background: var(--gradient-amber-fresh) !important;
  color: white !important;
}

.status-error {
  background: linear-gradient(135deg, #fb7185 0%, #f43f5e 50%, #e11d48 100%) !important;
  color: white !important;
}

.status-pending {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%) !important;
  color: #475569 !important;
}

/* 标签和徽章工具类 */
.tag-comfortable {
  padding: 4px 12px !important;
  min-height: 26px !important;
  border-radius: 13px !important;
  font-weight: 500 !important;
  font-size: 12px !important;
  line-height: 1.3 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: var(--shadow-soft) !important;
  backdrop-filter: blur(4px) !important;
  -webkit-backdrop-filter: blur(4px) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.tag-comfortable:hover {
  transform: translateY(-0.5px) scale(1.01) !important;
  box-shadow: var(--shadow-medium) !important;
}

.tag-small {
  padding: 2px 8px !important;
  min-height: 20px !important;
  border-radius: 10px !important;
  font-size: 11px !important;
}

.tag-large {
  padding: 6px 16px !important;
  min-height: 32px !important;
  border-radius: 16px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
}

/* 间距工具类 */
.space-comfortable > * + * {
  margin-left: 12px !important;
}

.space-loose > * + * {
  margin-left: 16px !important;
}

.space-tight > * + * {
  margin-left: 8px !important;
}

.gap-comfortable {
  gap: 12px !important;
}

.gap-loose {
  gap: 16px !important;
}

.gap-tight {
  gap: 8px !important;
}

/* 排版工具类 */
.text-comfortable {
  letter-spacing: 0.01em !important;
  line-height: 1.6 !important;
}

.text-heading {
  letter-spacing: 0.02em !important;
  line-height: 1.4 !important;
}

.text-small {
  letter-spacing: 0.025em !important;
  line-height: 1.5 !important;
}

.text-mono {
  letter-spacing: 0.05em !important;
  font-variant-numeric: tabular-nums !important;
}

/* 动画工具类 */
@keyframes pulse-success {
  0%, 100% { 
    box-shadow: 0 0 8px rgba(56, 189, 248, 0.5);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 16px rgba(56, 189, 248, 0.8);
    transform: scale(1.05);
  }
}

@keyframes pulse-amber {
  0%, 100% { 
    box-shadow: 0 0 8px rgba(251, 191, 36, 0.5);
    transform: scale(1);
  }
  50% { 
    box-shadow: 0 0 16px rgba(251, 191, 36, 0.8);
    transform: scale(1.05);
  }
}

@keyframes gentleBreathe {
  0%, 100% { 
    filter: brightness(1) saturate(0.98); 
    backdrop-filter: blur(0px);
  }
  50% { 
    filter: brightness(1.01) saturate(1.02); 
    backdrop-filter: blur(0.5px);
  }
}

/* 呼吸动画工具类 */
.breathe-gentle {
  animation: gentleBreathe 12s ease-in-out infinite !important;
}

.breathe-success {
  animation: pulse-success 2s ease-in-out infinite !important;
}

.breathe-amber {
  animation: pulse-amber 2s ease-in-out infinite !important;
}

/* 焦点和交互工具类 */
.focus-visible {
  outline: 2px solid var(--border-focus) !important;
  outline-offset: 2px !important;
}

.input-error {
  border-color: #448bef7e !important;
}

.input-success {
  border-color: #168bba !important;
}

.input-focus {
  border-color: var(--border-focus) !important;
  box-shadow: 0 0 0 2px rgba(35, 146, 239, 0.1) !important;
}

/* 卡片变体工具类 */
.card-focused {
  box-shadow: var(--shadow-medium), 0 0 0 2px rgba(125, 211, 252, 0.3) !important;
  transform: translateY(-2px) !important;
  background: rgba(248, 252, 255, 0.95) !important;
}

.card-selected {
  box-shadow: var(--shadow-elevated), 0 0 0 2px rgba(125, 211, 252, 0.4) !important;
  background: rgba(240, 249, 255, 0.95) !important;
  transform: translateY(-2px) scale(1.005) !important;
}

.card-hover {
  box-shadow: var(--shadow-medium) !important;
  transform: translateY(-3px) scale(1.01) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(16px) !important;
  -webkit-backdrop-filter: blur(16px) !important;
}

/* 边框移除工具类 */
.no-border {
  border: none !important;
}

.no-border-decorative {
  border: none !important;
}

.no-border-inner {
  border: none !important;
}

.no-border-container {
  border: none !important;
}

/* 功能性边框保留工具类 */
.border-functional {
  border-bottom: 1px solid var(--border-subtle) !important;
}

.border-validation-error {
  border-color: #448bef7e !important;
}

.border-validation-success {
  border-color: #168bba !important;
}

/* 颜色替换工具类 */
.color-sky-fresh {
  background: var(--gradient-sky-fresh) !important;
  border-color: rgba(56, 189, 248, 0.3) !important;
  color: white !important;
}

.color-amber-fresh {
  background: var(--gradient-amber-fresh) !important;
  border-color: rgba(251, 191, 36, 0.3) !important;
  color: white !important;
}

/* 图标容器工具类 */
.icon-container {
  border: 1px solid rgba(56, 189, 248, 0.2) !important;
  transition: all 0.3s ease !important;
}

/* 图标容器 - 深蓝色背景变体 */
.icon-container--lg {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%) !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.icon-container:hover {
  background: var(--gradient-sky-light) !important;
  border-color: rgba(56, 189, 248, 0.4) !important;
  transform: translateY(-0.5px) scale(1.025) !important;
}

.icon-container--lg:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%) !important;
  transform: translateY(-1px) scale(1.05) !important;
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4) !important;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE UTILITIES - 响应式工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 14寸屏幕优化 */
@media (min-width: 1366px) and (max-width: 1919px) {
  .tooltip-content {
    font-size: 0.65rem;
    padding: 4px 6px;
    max-width: 180px;
  }
  
  .tooltip-arrow {
    width: 6px;
    height: 6px;
  }
  
  .glass-effect {
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05) !important;
  }
  
  .tag-comfortable {
    padding: 3px 10px !important;
    min-height: 24px !important;
    font-size: 11px !important;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .tooltip-content {
    font-size: 0.7rem;
    padding: 6px 8px;
    max-width: 160px;
  }
  
  .glass-effect {
    border: none !important;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04) !important;
  }
  
  .tag-comfortable {
    padding: 2px 8px !important;
    min-height: 20px !important;
    font-size: 10px !important;
  }
  
  .gap-comfortable {
    gap: 8px !important;
  }
  
  .space-comfortable > * + * {
    margin-left: 8px !important;
  }
}

/* 大屏幕优化 (1920px+) */
@media (min-width: 1920px) {
  .tag-comfortable {
    padding: 6px 16px !important;
    min-height: 32px !important;
    font-size: 14px !important;
  }
  
  .btn-sky-fresh,
  .btn-amber-fresh {
    padding: 12px 18px !important;
    font-size: 15px !important;
    min-height: 44px !important;
  }
  
  .gap-comfortable {
    gap: 16px !important;
  }
  
  .space-comfortable > * + * {
    margin-left: 16px !important;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * ♿ ACCESSIBILITY UTILITIES - 无障碍工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .tooltip-dark {
    background-color: #000000 !important;
    color: #ffffff !important;
    border-color: #ffffff !important;
  }
  
  .tooltip-arrow-dark {
    background-color: #000000 !important;
    border-color: #ffffff !important;
  }
  
  .glass-effect {
    border-width: 2px !important;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15) !important;
  }
  
  .tag-comfortable {
    border-width: 2px !important;
    font-weight: 800 !important;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  .tooltip-content {
    transition: none;
  }
  
  .glass-effect,
  .btn-sky-fresh,
  .btn-amber-fresh,
  .btn-glass,
  .tag-comfortable,
  .card-hover,
  .icon-container {
    animation: none !important;
    transition: none !important;
  }
  
  .breathe-gentle,
  .breathe-success,
  .breathe-amber {
    animation: none !important;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .tooltip-content {
    min-width: 44px !important;
    min-height: 44px !important;
  }
  
  .tag-comfortable {
    min-height: 44px !important;
    padding: 8px 16px !important;
  }
  
  .btn-sky-fresh,
  .btn-amber-fresh,
  .btn-glass {
    min-height: 44px !important;
    padding: 12px 16px !important;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 LEGACY COMPATIBILITY - 兼容性保持
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 保持与现有batch processor layout的兼容性 */
.batch-processor-layout .glass-effect {
  border: none !important;
  background: var(--gradient-glass) !important;
  box-shadow: var(--shadow-soft) !important;
  backdrop-filter: blur(16px) !important;
  -webkit-backdrop-filter: blur(16px) !important;
  border-radius: 12px !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.batch-processor-layout .btn-sky-fresh,
.batch-processor-layout .btn-amber-fresh {
  border: none !important;
  font-weight: 500 !important;
  letter-spacing: 0.25px !important;
}

.batch-processor-layout .tag-comfortable {
  backdrop-filter: blur(20px) !important;
  -webkit-backdrop-filter: blur(20px) !important;
}

/* 移除所有不必要的边框 */
.batch-processor-layout .border,
.batch-processor-layout .border-t,
.batch-processor-layout .border-r,
.batch-processor-layout .border-b,
.batch-processor-layout .border-l {
  border: none !important;
}

/* 统一文本颜色系统 - 蓝色主题增强对比度 */
.batch-processor-layout {
  --text-primary: var(--text-blue-950);
  --text-secondary: var(--text-blue-800);
  --text-muted: var(--text-blue-700);
}