/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 🏗️ GRID LAYOUT SYSTEM - 网格布局系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了layout-fixes.css和部分responsive-14inch.css
 * 提供完整的网格布局解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces layout-fixes.css + responsive-14inch.css (部分)
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 🏗️ SIDEBAR LAYOUT FIXES - 侧边栏布局修复
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 左侧栏整体容器优化 */
.layout-sidebar {
  position: relative;
  z-index: 10;
  background: transparent;
}

/* 修复组件间距和层级 */
.layout-sidebar .glass-card {
  position: relative;
  z-index: 1;
  margin-bottom: 0;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 确保按钮不会变形 */
.layout-sidebar .btn {
  min-width: auto;
  width: auto;
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* 图标防止变形 */
.layout-sidebar .icon {
  flex-shrink: 0;
  width: 1rem;
  height: 1rem;
  display: inline-block;
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📐 GRID UTILITIES - 网格工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

/* Grid基础类 */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }

/* Grid间距 */
.gap-1 { gap: var(--space-1); }
.gap-2 { gap: var(--space-2); }
.gap-3 { gap: var(--space-3); }
.gap-4 { gap: var(--space-4); }
.gap-6 { gap: var(--space-6); }
.gap-8 { gap: var(--space-8); }

/* Flexbox工具 */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* Flex扩展 */
.flex-1 { flex: 1 1 0%; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-grow { flex-grow: 1; }

/* ═══════════════════════════════════════════════════════════════════════════
 * 📏 POSITIONING - 定位系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 定位工具 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* 层级管理 */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* ═══════════════════════════════════════════════════════════════════════════
 * 📐 SPACING UTILITIES - 间距工具
 * ═══════════════════════════════════════════════════════════════════════════ */

/* Margin工具 */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-1); }
.m-2 { margin: var(--space-2); }
.m-3 { margin: var(--space-3); }
.m-4 { margin: var(--space-4); }
.m-6 { margin: var(--space-6); }
.m-8 { margin: var(--space-8); }

/* Margin方向 */
.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--space-1); }
.mt-2 { margin-top: var(--space-2); }
.mt-3 { margin-top: var(--space-3); }
.mt-4 { margin-top: var(--space-4); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--space-1); }
.mb-2 { margin-bottom: var(--space-2); }
.mb-3 { margin-bottom: var(--space-3); }
.mb-4 { margin-bottom: var(--space-4); }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: var(--space-1); }
.ml-2 { margin-left: var(--space-2); }
.ml-3 { margin-left: var(--space-3); }
.ml-4 { margin-left: var(--space-4); }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: var(--space-1); }
.mr-2 { margin-right: var(--space-2); }
.mr-3 { margin-right: var(--space-3); }
.mr-4 { margin-right: var(--space-4); }

/* Padding工具 */
.p-0 { padding: 0; }
.p-1 { padding: var(--space-1); }
.p-2 { padding: var(--space-2); }
.p-3 { padding: var(--space-3); }
.p-4 { padding: var(--space-4); }
.p-6 { padding: var(--space-6); }
.p-8 { padding: var(--space-8); }

/* Padding方向 */
.pt-0 { padding-top: 0; }
.pt-1 { padding-top: var(--space-1); }
.pt-2 { padding-top: var(--space-2); }
.pt-3 { padding-top: var(--space-3); }
.pt-4 { padding-top: var(--space-4); }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: var(--space-1); }
.pb-2 { padding-bottom: var(--space-2); }
.pb-3 { padding-bottom: var(--space-3); }
.pb-4 { padding-bottom: var(--space-4); }

.pl-0 { padding-left: 0; }
.pl-1 { padding-left: var(--space-1); }
.pl-2 { padding-left: var(--space-2); }
.pl-3 { padding-left: var(--space-3); }
.pl-4 { padding-left: var(--space-4); }

.pr-0 { padding-right: 0; }
.pr-1 { padding-right: var(--space-1); }
.pr-2 { padding-right: var(--space-2); }
.pr-3 { padding-right: var(--space-3); }
.pr-4 { padding-right: var(--space-4); }

/* 轴向padding */
.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }
.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }
.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }
.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }
.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }
.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }
.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }

/* ═══════════════════════════════════════════════════════════════════════════
 * 📏 SIZE UTILITIES - 尺寸工具
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 宽度 */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-1 { width: var(--space-1); }
.w-2 { width: var(--space-2); }
.w-3 { width: var(--space-3); }
.w-4 { width: var(--space-4); }
.w-8 { width: var(--space-8); }
.w-10 { width: var(--space-10); }
.w-12 { width: var(--space-12); }
.w-16 { width: var(--space-16); }
.w-20 { width: var(--space-20); }

/* 高度 */
.h-full { height: 100%; }
.h-auto { height: auto; }
.h-1 { height: var(--space-1); }
.h-2 { height: var(--space-2); }
.h-3 { height: var(--space-3); }
.h-4 { height: var(--space-4); }
.h-8 { height: var(--space-8); }
.h-10 { height: var(--space-10); }
.h-12 { height: var(--space-12); }
.h-16 { height: var(--space-16); }
.h-20 { height: var(--space-20); }

/* 最小尺寸 */
.min-h-screen { min-height: 100vh; }
.min-h-full { min-height: 100%; }
.min-w-0 { min-width: 0; }
.min-w-full { min-width: 100%; }

/* 最大尺寸 */
.max-w-full { max-width: 100%; }
.max-w-screen { max-width: 100vw; }
.max-h-full { max-height: 100%; }
.max-h-screen { max-height: 100vh; }

/* ═══════════════════════════════════════════════════════════════════════════
 * 🌊 OVERFLOW UTILITIES - 溢出工具
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 溢出控制 */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-visible { overflow: visible; }

/* 溢出方向 */
.overflow-x-hidden { overflow-x: hidden; }
.overflow-x-auto { overflow-x: auto; }
.overflow-x-scroll { overflow-x: scroll; }

.overflow-y-hidden { overflow-y: hidden; }
.overflow-y-auto { overflow-y: auto; }
.overflow-y-scroll { overflow-y: scroll; }

/* ═══════════════════════════════════════════════════════════════════════════
 * 👁️ VISIBILITY UTILITIES - 可见性工具
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 显示/隐藏 */
.hidden { display: none !important; }
.block { display: block; }
.inline { display: inline; }
.inline-block { display: inline-block; }

/* 可见性 */
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* 透明度 */
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 屏幕阅读器专用 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}