/**
 * ═══════════════════════════════════════════════════════════════════════════════
 * 📱 RESPONSIVE LAYOUT SYSTEM - 响应式布局系统
 * ═══════════════════════════════════════════════════════════════════════════════
 *
 * 合并了responsive-14inch.css和modules/responsive-layout.css
 * 提供完整的响应式布局解决方案
 *
 * @version 4.0 - 整合版本
 * <AUTHOR> Agent
 * @updated 2025-07-17
 * @replaces responsive-14inch.css + modules/responsive-layout.css
 */

/* ═══════════════════════════════════════════════════════════════════════════
 * 📐 RESPONSIVE GRID SYSTEM - 响应式网格系统
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 14寸屏幕优化 (1440px) */
@media (max-width: 1440px) {
  .batch-processor-layout {
    min-width: 1200px;
    grid-template-columns: 280px 1fr 300px !important;
  }
  
  .layout-sidebar {
    padding: 12px;
  }
  
  .layout-console {
    padding: 12px;
  }
}

/* 中等屏幕优化 (1200px) */
@media (max-width: 1200px) {
  .batch-processor-layout {
    min-width: 1000px;
    grid-template-columns: 260px 1fr 280px !important;
    gap: 8px;
  }
  
  .layout-sidebar,
  .layout-main,
  .layout-console {
    padding: 12px;
  }
}

/* 小屏幕优化 (768px) */
@media (max-width: 768px) {
  .batch-processor-layout {
    grid-template-columns: 1fr !important;
    grid-template-rows: auto auto auto;
    min-width: auto;
    gap: 4px;
    padding: 8px;
  }
  
  .layout-sidebar {
    order: 1;
    min-height: auto;
  }
  
  .layout-main {
    order: 2;
    min-height: 60vh;
  }
  
  .layout-console {
    order: 3;
    min-height: auto;
  }
}

/* 超小屏幕优化 (480px) */
@media (max-width: 480px) {
  .batch-processor-layout {
    padding: 4px;
    gap: 2px;
  }
  
  .layout-sidebar,
  .layout-main,
  .layout-console {
    padding: 8px;
    border-radius: 8px;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📊 RESPONSIVE UTILITIES - 响应式工具类
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 响应式显示 */
@media (max-width: 768px) {
  .hidden-mobile { display: none !important; }
  .block-mobile { display: block !important; }
  .flex-mobile { display: flex !important; }
  .grid-mobile { display: grid !important; }
}

@media (min-width: 769px) {
  .hidden-desktop { display: none !important; }
  .block-desktop { display: block !important; }
  .flex-desktop { display: flex !important; }
  .grid-desktop { display: grid !important; }
}

/* 响应式文本大小 */
@media (max-width: 768px) {
  .text-sm-mobile { font-size: var(--font-size-sm) !important; }
  .text-base-mobile { font-size: var(--font-size-base) !important; }
  .text-lg-mobile { font-size: var(--font-size-lg) !important; }
}

/* 响应式间距 */
@media (max-width: 768px) {
  .p-2-mobile { padding: var(--space-2) !important; }
  .p-3-mobile { padding: var(--space-3) !important; }
  .p-4-mobile { padding: var(--space-4) !important; }
  
  .m-2-mobile { margin: var(--space-2) !important; }
  .m-3-mobile { margin: var(--space-3) !important; }
  .m-4-mobile { margin: var(--space-4) !important; }
  
  .gap-2-mobile { gap: var(--space-2) !important; }
  .gap-3-mobile { gap: var(--space-3) !important; }
  .gap-4-mobile { gap: var(--space-4) !important; }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎴 RESPONSIVE CARDS - 响应式卡片
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 卡片网格响应式 */
.cards-grid {
  display: grid;
  gap: var(--space-4);
}

/* 桌面端 - 5列 */
@media (min-width: 1440px) {
  .cards-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

/* 大屏 - 4列 */
@media (min-width: 1200px) and (max-width: 1439px) {
  .cards-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 中屏 - 3列 */
@media (min-width: 900px) and (max-width: 1199px) {
  .cards-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* 小屏 - 2列 */
@media (min-width: 600px) and (max-width: 899px) {
  .cards-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }
}

/* 手机 - 1列 */
@media (max-width: 599px) {
  .cards-grid {
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 RESPONSIVE COMPONENTS - 响应式组件
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 响应式按钮 */
@media (max-width: 768px) {
  .btn-responsive {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
  }
}

/* 响应式输入框 */
@media (max-width: 768px) {
  .input-responsive {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
  }
}

/* 响应式模态框 */
@media (max-width: 768px) {
  .modal-responsive {
    width: 95vw;
    max-width: none;
    margin: var(--space-2);
  }
}

/* 响应式抽屉 */
@media (max-width: 768px) {
  .drawer-responsive {
    width: 100vw;
    height: 100vh;
    border-radius: 0;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎨 RESPONSIVE ANIMATIONS - 响应式动画
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 移动端减少动画 */
@media (max-width: 768px) {
  .animate-responsive {
    animation-duration: 0.2s !important;
  }
  
  .transition-responsive {
    transition-duration: 0.2s !important;
  }
}

/* 低功耗设备减少动画 */
@media (prefers-reduced-motion: reduce) {
  .animate-responsive {
    animation: none !important;
  }
  
  .transition-responsive {
    transition: none !important;
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🔧 RESPONSIVE FIXES - 响应式修复
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .touch-optimized {
    min-height: 44px; /* 最小触摸目标 */
    min-width: 44px;
  }
  
  .hover-disabled:hover {
    /* 禁用hover效果 */
  }
}

/* 高密度屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 2dppx) {
  .retina-optimized {
    /* 高密度屏幕优化 */
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  .landscape-optimized {
    /* 横屏模式优化 */
  }
}

/* 竖屏模式优化 */
@media (orientation: portrait) {
  .portrait-optimized {
    /* 竖屏模式优化 */
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 📱 MOBILE NAVIGATION - 移动端导航
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 移动端导航栏 */
@media (max-width: 768px) {
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--color-white);
    border-top: 1px solid var(--color-gray-200);
    padding: var(--space-2);
    z-index: var(--z-sticky);
  }
  
  .mobile-nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--space-2);
    text-align: center;
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    transition: color var(--duration-fast);
  }
  
  .mobile-nav-item:hover,
  .mobile-nav-item.active {
    color: var(--color-primary-600);
  }
}

/* ═══════════════════════════════════════════════════════════════════════════
 * 🎯 RESPONSIVE BREAKPOINTS - 响应式断点
 * ═══════════════════════════════════════════════════════════════════════════ */

/* 
 * 断点定义:
 * - xs: 0px - 479px    (超小屏幕)
 * - sm: 480px - 767px  (小屏幕)
 * - md: 768px - 1023px (中等屏幕)
 * - lg: 1024px - 1439px (大屏幕)
 * - xl: 1440px+        (超大屏幕)
 */

/* 容器最大宽度 */
@media (min-width: 480px) {
  .container { max-width: 480px; }
}

@media (min-width: 768px) {
  .container { max-width: 768px; }
}

@media (min-width: 1024px) {
  .container { max-width: 1024px; }
}

@media (min-width: 1440px) {
  .container { max-width: 1440px; }
}

/* 居中容器 */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}