/**
 * Prompt策略管理器 - 统一管理单一prompt和两阶段方案
 * 提供灵活的策略选择和切换机制
 */

import { TwoPhaseGenerationService } from './TwoPhaseGenerationService';
import { OptimizedSinglePrompt } from '../prompts/OptimizedSinglePrompt';

export type PromptStrategy = 'single_optimized' | 'two_phase' | 'auto_select';

export interface StrategyConfig {
  strategy: PromptStrategy;
  enableFallback: boolean;
  performanceThreshold: number; // ms
  qualityThreshold: number; // 0-1
  costBudget: number; // API调用成本预算
}

export interface GenerationResult {
  strategy: PromptStrategy;
  success: boolean;
  responseTime: number;
  qualityScore: number;
  cost: number;
  content: {
    lynxCode?: {
      ttml: string;
      ttss: string;
      js: string;
      json: string;
      config: string;
    };
    designSpec?: any;
    metadata: {
      tokensUsed: number;
      apiCalls: number;
      constraints_compliance: number;
      creativity_score: number;
    };
  };
}

export class PromptStrategyManager {
  private static instance: PromptStrategyManager;
  private currentConfig: StrategyConfig;
  private performance_stats: Map<
    PromptStrategy,
    {
      avgResponseTime: number;
      successRate: number;
      avgQualityScore: number;
      avgCost: number;
    }
  >;

  private constructor() {
    this.currentConfig = {
      strategy: 'auto_select',
      enableFallback: true,
      performanceThreshold: 10000, // 10秒
      qualityThreshold: 0.8,
      costBudget: 100, // 假设单位
    };

    this.performance_stats = new Map();
    this.initializeStats();
  }

  public static getInstance(): PromptStrategyManager {
    if (!PromptStrategyManager.instance) {
      PromptStrategyManager.instance = new PromptStrategyManager();
    }
    return PromptStrategyManager.instance;
  }

  /**
   * 核心方法：智能选择策略并执行生成
   */
  public async generateEducationalCard(
    query: string,
    config?: Partial<StrategyConfig>,
  ): Promise<GenerationResult> {
    const finalConfig = { ...this.currentConfig, ...config };
    const strategy =
      finalConfig.strategy === 'auto_select'
        ? this.selectOptimalStrategy(query)
        : finalConfig.strategy;

    console.log(`🎯 执行策略: ${strategy}`);

    try {
      const result = await this.executeStrategy(strategy, query);
      this.updatePerformanceStats(strategy, result);
      return result;
    } catch (error) {
      console.error(`策略 ${strategy} 执行失败:`, error);

      if (finalConfig.enableFallback && strategy !== 'single_optimized') {
        console.log('🔄 启用降级策略: single_optimized');
        return await this.executeStrategy('single_optimized', query);
      }

      throw error;
    }
  }

  /**
   * 智能策略选择算法
   */
  private selectOptimalStrategy(query: string): PromptStrategy {
    const queryAnalysis = this.analyzeQueryComplexity(query);
    const currentStats = this.getPerformanceStats();

    // 决策因子
    const factors = {
      complexity: queryAnalysis.complexity_score,
      creative_requirement: queryAnalysis.creative_requirement,
      technical_precision_need: queryAnalysis.technical_precision_need,
      time_constraint: this.currentConfig.performanceThreshold < 8000,
      cost_constraint: this.currentConfig.costBudget < 50,
      quality_requirement: this.currentConfig.qualityThreshold > 0.85,
    };

    // 决策逻辑
    if (factors.cost_constraint && factors.time_constraint) {
      return 'single_optimized'; // 严格成本/时间优先
    }

    if (factors.quality_requirement && factors.complexity > 0.7) {
      return 'two_phase'; // 质量优先，复杂任务
    }

    if (
      factors.creative_requirement > 0.8 &&
      factors.technical_precision_need > 0.8
    ) {
      return 'two_phase'; // 创意和技术都要求高
    }

    // 基于历史性能选择
    const singleStats = currentStats.get('single_optimized');
    const twoPhaseStats = currentStats.get('two_phase');

    if (singleStats && twoPhaseStats) {
      const singleScore = this.calculateStrategyScore(singleStats, factors);
      const twoPhaseScore = this.calculateStrategyScore(twoPhaseStats, factors);

      return twoPhaseScore > singleScore ? 'two_phase' : 'single_optimized';
    }

    return 'single_optimized'; // 默认选择
  }

  /**
   * 执行指定策略
   */
  private async executeStrategy(
    strategy: PromptStrategy,
    query: string,
  ): Promise<GenerationResult> {
    const startTime = Date.now();

    switch (strategy) {
      case 'two_phase':
        return await this.executeTwoPhaseStrategy(query, startTime);

      case 'single_optimized':
        return await this.executeSingleOptimizedStrategy(query, startTime);

      default:
        throw new Error(`未知策略: ${strategy}`);
    }
  }

  /**
   * 执行两阶段策略
   */
  private async executeTwoPhaseStrategy(
    query: string,
    startTime: number,
  ): Promise<GenerationResult> {
    const twoPhaseService = TwoPhaseGenerationService.getInstance();
    const result = await twoPhaseService.generateEducationalCard(query);

    const responseTime = Date.now() - startTime;
    const qualityScore = this.assessQuality(
      result.phase2.lynxCode,
      result.phase1,
    );

    return {
      strategy: 'two_phase',
      success: true,
      responseTime,
      qualityScore,
      cost: this.calculateCost(1, responseTime), // 1次API调用（包含两阶段prompt）
      content: {
        lynxCode: result.phase2.lynxCode,
        designSpec: result.phase1.designSpec,
        metadata: {
          tokensUsed: this.estimateTokenUsage(result),
          apiCalls: 1,
          constraints_compliance: this.assessConstraintsCompliance(
            result.phase2.lynxCode,
          ),
          creativity_score: this.assessCreativity(result.phase1),
        },
      },
    };
  }

  /**
   * 执行单一优化策略
   */
  private async executeSingleOptimizedStrategy(
    query: string,
    startTime: number,
  ): Promise<GenerationResult> {
    const singlePrompt = OptimizedSinglePrompt.getInstance();
    const prompt = singlePrompt.generateOptimizedPrompt(query);

    // 调用AI API (这里需要实际的API调用)
    const apiResponse = await this.callAIAPI(prompt);
    const lynxCode = this.parseLynxCode(apiResponse);

    const responseTime = Date.now() - startTime;
    const qualityScore = this.assessQuality(lynxCode);

    return {
      strategy: 'single_optimized',
      success: true,
      responseTime,
      qualityScore,
      cost: this.calculateCost(1, responseTime), // 1次API调用
      content: {
        lynxCode,
        metadata: {
          tokensUsed: Math.ceil(prompt.length / 4),
          apiCalls: 1,
          constraints_compliance: this.assessConstraintsCompliance(lynxCode),
          creativity_score: this.assessCreativityFromCode(lynxCode),
        },
      },
    };
  }

  /**
   * 分析查询复杂度
   */
  private analyzeQueryComplexity(query: string): {
    complexity_score: number;
    creative_requirement: number;
    technical_precision_need: number;
  } {
    const queryLower = query.toLowerCase();

    // 复杂度指标
    const complexityKeywords = ['系统', '架构', '完整', '详细', '深入', '高级'];
    const creativityKeywords = ['创新', '设计', '美观', '吸引', '独特', '创意'];
    const precisionKeywords = ['准确', '严格', '规范', '标准', '精确', '正确'];

    const complexityScore =
      complexityKeywords.filter(k => queryLower.includes(k)).length /
      complexityKeywords.length;
    const creativeRequirement =
      creativityKeywords.filter(k => queryLower.includes(k)).length /
      creativityKeywords.length;
    const technicalPrecisionNeed =
      precisionKeywords.filter(k => queryLower.includes(k)).length /
      precisionKeywords.length;

    return {
      complexity_score: Math.min(
        complexityScore + (query.length / 100) * 0.1,
        1,
      ),
      creative_requirement: creativeRequirement,
      technical_precision_need: technicalPrecisionNeed,
    };
  }

  /**
   * 计算策略综合得分
   */
  private calculateStrategyScore(stats: any, factors: any): number {
    // 权重配置
    const weights = {
      success_rate: 0.3,
      quality: 0.25,
      performance: 0.2,
      cost: 0.15,
      suitability: 0.1,
    };

    const normalizedPerformance = Math.max(
      0,
      1 - stats.avgResponseTime / this.currentConfig.performanceThreshold,
    );
    const normalizedCost = Math.max(
      0,
      1 - stats.avgCost / this.currentConfig.costBudget,
    );

    return (
      stats.successRate * weights.success_rate +
      stats.avgQualityScore * weights.quality +
      normalizedPerformance * weights.performance +
      normalizedCost * weights.cost +
      this.calculateSuitability(factors) * weights.suitability
    );
  }

  private calculateSuitability(factors: any): number {
    // 根据任务特点计算适配度
    if (
      factors.creative_requirement > 0.7 &&
      factors.technical_precision_need > 0.7
    ) {
      return 0.9; // 两阶段更适合
    }
    if (factors.time_constraint || factors.cost_constraint) {
      return 0.1; // 单一策略更适合
    }
    return 0.5; // 中性
  }

  /**
   * 质量评估
   */
  private assessQuality(lynxCode: any, designSpec?: any): number {
    let score = 0.5; // 基础分

    // 代码完整性检查
    if (lynxCode.ttml && lynxCode.ttss && lynxCode.js) score += 0.2;

    // 语法正确性检查 (简化版)
    if (lynxCode.ttml?.includes('<view') && !lynxCode.ttml?.includes('<div'))
      score += 0.1;
    if (lynxCode.js?.includes('bindtap') && !lynxCode.js?.includes('bindtap'))
      score += 0.1;
    if (
      lynxCode.ttml?.includes('scroll-view') &&
      lynxCode.ttss?.includes('height')
    )
      score += 0.1;

    // 设计质量 (如果有设计规范)
    if (designSpec) score += 0.1;

    return Math.min(score, 1);
  }

  /**
   * 约束遵守度评估
   */
  private assessConstraintsCompliance(lynxCode: any): number {
    let compliance = 1.0;

    // 检查禁用标签
    if (lynxCode.ttml?.includes('<div') || lynxCode.ttml?.includes('<span'))
      compliance -= 0.3;

    // 检查事件绑定语法
    if (lynxCode.ttml?.includes('bind')) compliance -= 0.2;

    // 检查scroll-view高度
    if (
      lynxCode.ttml?.includes('scroll-view') &&
      !lynxCode.ttss?.includes('height')
    )
      compliance -= 0.2;

    return Math.max(compliance, 0);
  }

  private assessCreativity(designSpec: any): number {
    // 基于设计规范评估创意度
    if (!designSpec) return 0.3;

    let creativity = 0.5;
    if (designSpec.creativeConcepts?.length > 0) creativity += 0.2;
    if (designSpec.userInsights?.length > 50) creativity += 0.2;
    if (designSpec.designSpec?.visualDesign?.length > 100) creativity += 0.1;

    return Math.min(creativity, 1);
  }

  private assessCreativityFromCode(lynxCode: any): number {
    // 基于代码评估创意度 (简化版)
    let creativity = 0.3;

    if (
      lynxCode.ttss?.includes('animation') ||
      lynxCode.ttss?.includes('transform')
    )
      creativity += 0.2;
    if (lynxCode.ttml?.includes('swiper') || lynxCode.ttml?.includes('canvas'))
      creativity += 0.2;
    if (
      lynxCode.js?.includes('handleAnimation') ||
      lynxCode.js?.includes('handleInteraction')
    )
      creativity += 0.3;

    return Math.min(creativity, 1);
  }

  /**
   * 模拟API调用 (实际使用时需要替换)
   */
  private async callAIAPI(prompt: string): Promise<string> {
    // 这里应该是实际的API调用
    await new Promise(resolve => setTimeout(resolve, 2000)); // 模拟延迟

    return `
<FILES>
<FILE path="index.ttml">
<scroll-view style="height: 100vh;">
  <view class="container">
    <text>Generated content based on: ${prompt.substring(0, 50)}...</text>
  </view>
</scroll-view>
</FILE>
<FILE path="index.ttss">
.container { padding: 20rpx; }
</FILE>
<FILE path="index.js">
Card({ data() { return {}; } });
</FILE>
</FILES>
`;
  }

  /**
   * 解析Lynx代码
   */
  private parseLynxCode(response: string): any {
    const filesMatch = response.match(/<FILES>([\s\S]*?)<\/FILES>/);
    if (!filesMatch) return {};

    const files: any = {};
    const filePattern = /<FILE path="([^"]+)">([\s\S]*?)<\/FILE>/g;

    let match;
    while ((match = filePattern.exec(filesMatch[1])) !== null) {
      const [, path, content] = match;
      const fileName = path.split('.').pop();
      files[fileName] = content.trim();
    }

    return files;
  }

  private calculateCost(apiCalls: number, responseTime: number): number {
    // 简化的成本计算
    return apiCalls * 10 + Math.ceil(responseTime / 1000) * 2;
  }

  private estimateTokenUsage(result: any): number {
    // 简化的token估算
    return 1500; // 固定估算，实际应该基于内容长度
  }

  /**
   * 初始化性能统计
   */
  private initializeStats(): void {
    this.performance_stats.set('single_optimized', {
      avgResponseTime: 3000,
      successRate: 0.92,
      avgQualityScore: 0.75,
      avgCost: 15,
    });

    this.performance_stats.set('two_phase', {
      avgResponseTime: 4500, // 单次API调用，响应时间减少
      successRate: 0.96,
      avgQualityScore: 0.88,
      avgCost: 20, // 单次API调用，成本降低
    });
  }

  /**
   * 更新性能统计
   */
  private updatePerformanceStats(
    strategy: PromptStrategy,
    result: GenerationResult,
  ): void {
    const stats = this.performance_stats.get(strategy);
    if (stats) {
      // 简化的滑动平均更新
      stats.avgResponseTime =
        stats.avgResponseTime * 0.9 + result.responseTime * 0.1;
      stats.avgQualityScore =
        stats.avgQualityScore * 0.9 + result.qualityScore * 0.1;
      stats.avgCost = stats.avgCost * 0.9 + result.cost * 0.1;
      stats.successRate = result.success
        ? Math.min(stats.successRate + 0.01, 1)
        : Math.max(stats.successRate - 0.05, 0);
    }
  }

  /**
   * 获取性能统计
   */
  public getPerformanceStats(): Map<PromptStrategy, any> {
    return this.performance_stats;
  }

  /**
   * 更新配置
   */
  public updateConfig(config: Partial<StrategyConfig>): void {
    this.currentConfig = { ...this.currentConfig, ...config };
  }

  /**
   * 获取策略推荐
   */
  public getStrategyRecommendation(query: string): {
    recommended: PromptStrategy;
    confidence: number;
    reasoning: string;
  } {
    const analysis = this.analyzeQueryComplexity(query);
    const recommended = this.selectOptimalStrategy(query);

    let reasoning = '';
    let confidence = 0.7;

    if (recommended === 'two_phase') {
      reasoning = '查询复杂度高，需要创意设计和技术精度并重';
      confidence = analysis.complexity_score > 0.7 ? 0.9 : 0.75;
    } else {
      reasoning = '查询相对简单，单一优化策略更高效';
      confidence = analysis.complexity_score < 0.3 ? 0.9 : 0.7;
    }

    return { recommended, confidence, reasoning };
  }
}

// 便捷导出函数
export async function generateWithOptimalStrategy(
  query: string,
  config?: Partial<StrategyConfig>,
) {
  const manager = PromptStrategyManager.getInstance();
  return await manager.generateEducationalCard(query, config);
}

export function getStrategyRecommendation(query: string) {
  const manager = PromptStrategyManager.getInstance();
  return manager.getStrategyRecommendation(query);
}

export function getPerformanceStats() {
  const manager = PromptStrategyManager.getInstance();
  return manager.getPerformanceStats();
}

export default PromptStrategyManager.getInstance();
