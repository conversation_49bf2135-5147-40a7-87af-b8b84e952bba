/**
 * UploadService.ts
 * 上传服务，实现文件压缩和CDN上传功能
 * 基于realUploadTestUI.html中的实现
 */

import { FileStructure, UploadResult } from '../types';
import JSZip from 'jszip';
import { v4 as uuidv4 } from 'uuid';

/**
 * 上传服务配置
 */
interface UploadServiceConfig {
  /** CDN上传地址 */
  cdnEndpoint: string;
  /** 上传超时时间(毫秒) */
  timeout: number;
  /** 是否启用压缩 */
  enableCompression: boolean;
  /** 压缩级别 (1-9) */
  compressionLevel: number;
  /** 是否记录上传日志 */
  enableLogging: boolean;
  /** 重试次数 */
  maxRetries: number;
  /** 重试间隔(毫秒) */
  retryInterval: number;
  /** Playground基础URL */
  playgroundBaseUrl: string;
  /** 是否模拟上传模式(用于测试) */
  mockMode: boolean;
  /** CDN域名 */
  cdnDomain?: string;
  /** 目录路径 */
  directoryPath?: string;
  /** 联系邮箱 */
  contactEmail?: string;
}

/**
 * 文件元数据
 */
interface FileMetadata {
  /** 文件名 */
  fileName: string;
  /** 大小(字节) */
  size: number;
  /** 创建时间 */
  createdAt: number;
  /** MIME类型 */
  mimeType: string;
  /** 文件类型标签 */
  typeTag: string;
}

/**
 * 上传成功响应
 */
interface UploadSuccessResponse {
  /** 上传状态 */
  status: 'success';
  /** CDN URL */
  url: string;
  /** CDN URL (alternative) */
  cdnUrl?: string;
  /** 文件ID */
  fileId: string;
  /** 元数据 */
  metadata?: Record<string, any>;
  /** CDN域名 */
  domain?: string;
  /** CDN路径 */
  path?: string;
  /** TOS密钥 */
  tosKey?: string;
  /** 响应代码 */
  code?: number;
}

/**
 * 上传进度回调函数类型
 */
export type UploadProgressCallback = (progress: {
  /** 上传的百分比 (0-100) */
  percentage: number;
  /** 已上传的字节数 */
  loaded: number;
  /** 总字节数 */
  total: number;
  /** 上传速度 (字节/秒) */
  speed?: number;
  /** 预计剩余时间 (毫秒) */
  estimatedTimeRemaining?: number;
}) => void;

/**
 * 上传服务
 * 实现文件压缩和CDN上传功能
 */
export class UploadService {
  private config: UploadServiceConfig;
  private abortControllers: Map<string, AbortController> = new Map();
  private progressCallback?: UploadProgressCallback;

  /**
   * 构造函数
   * @param config 上传服务配置
   */
  constructor(config?: Partial<UploadServiceConfig>) {
    // 默认配置 - 与 realUploadProcessor.js 保持一致
    this.config = {
      cdnEndpoint: 'https://ife.bytedance.net/cdn/upload', // 修正为正确的上传端点
      timeout: 180000, // 使用3分钟超时
      enableCompression: true,
      compressionLevel: 6,
      enableLogging: false,
      maxRetries: 3,
      retryInterval: 2000,
      playgroundBaseUrl: 'https://playground.cn.goofy.app/', // 修正为正确的Playground URL
      mockMode: false,
      cdnDomain: 'lf3-static.bytedance.com',
      directoryPath: 'so-web-code',
      contactEmail: '<EMAIL>',
      ...config,
    };

    if (this.config.enableLogging) {
      console.log('[UploadService] 初始化上传服务');
      console.log(`[UploadService] CDN端点: ${this.config.cdnEndpoint}`);
      console.log(
        `[UploadService] 压缩: ${this.config.enableCompression ? '启用' : '禁用'}`,
      );
      console.log(`[UploadService] CDN域名: ${this.config.cdnDomain}`);

      if (this.config.mockMode) {
        console.warn('[UploadService] 当前为模拟上传模式，不会发送实际请求');
      }
    }
  }

  /**
   * 设置上传进度回调
   * @param callback 进度回调函数
   */
  setProgressCallback(callback: UploadProgressCallback) {
    this.progressCallback = callback;
    if (this.config.enableLogging) {
      console.log('[UploadService] 设置上传进度回调函数');
    }
  }

  /**
   * 智能检测main入口路径
   * 增强的组件名称匹配检测，优先检测组件模式文件
   */
  private detectMainEntryPath(files: FileStructure): string {
    const fileNames = Object.keys(files);

    // 优先级1: 检查src目录下的入口文件
    const srcEntryFiles = [
      'src/index.js',
      'src/index.jsx',
      'src/index.ts',
      'src/index.tsx',
      'src/main.js',
      'src/main.jsx',
      'src/main.ts',
      'src/main.tsx',
      'src/App.js',
      'src/App.jsx',
      'src/App.ts',
      'src/App.tsx',
    ];

    for (const srcFile of srcEntryFiles) {
      if (fileNames.includes(srcFile)) {
        // 返回相对于项目根目录的路径，去掉src/前缀
        return `./${srcFile.replace('src/', '')}`;
      }
    }

    // 优先级2: 检查根目录下的入口文件
    const rootEntryFiles = [
      'index.js',
      'index.jsx',
      'index.ts',
      'index.tsx',
      'main.js',
      'main.jsx',
      'main.ts',
      'main.tsx',
      'App.js',
      'App.jsx',
      'App.ts',
      'App.tsx',
    ];

    for (const rootFile of rootEntryFiles) {
      if (fileNames.includes(rootFile)) {
        return `./${rootFile}`;
      }
    }

    // 优先级3: 智能检测组件名称匹配的文件
    // 检查 pages/[componentName]/[componentName].js 模式
    const componentFiles = fileNames.filter(name => {
      const validExtensions = ['.js', '.jsx', '.ts', '.tsx'];
      if (!validExtensions.some(ext => name.endsWith(ext))) {
        return false;
      }
      
      // 检查组件名模式: pages/componentName/componentName.js
      const pathParts = name.split('/');
      if (pathParts.length >= 3) {
        const fileName = pathParts[pathParts.length - 1]; // 最后一部分（文件名）
        const dirName = pathParts[pathParts.length - 2]; // 倒数第二部分（目录名）
        const fileNameWithoutExt = fileName.replace(/\.(js|jsx|ts|tsx)$/, '');
        
        // 检查文件名是否与目录名匹配
        if (fileNameWithoutExt === dirName) {
          return true;
        }
      }
      
      return false;
    });

    // 按路径深度排序，优先选择较浅的路径
    if (componentFiles.length > 0) {
      const sortedComponentFiles = componentFiles.sort((a, b) => {
        const aDepth = a.split('/').length;
        const bDepth = b.split('/').length;
        return aDepth - bDepth;
      });
      
      const selectedFile = sortedComponentFiles[0];
      
      // 如果是src/下的文件，去掉src/前缀
      if (selectedFile.startsWith('src/')) {
        return `./${selectedFile.replace('src/', '')}`;
      }
      return `./${selectedFile}`;
    }

    // 优先级4: 检查是否有任何以index开头的JavaScript/TypeScript文件
    const indexFile = fileNames.find(name => {
      // 检查文件是否以有效扩展名结尾
      const validExtensions = ['.js', '.jsx', '.ts', '.tsx'];
      if (!validExtensions.some(ext => name.endsWith(ext))) {
        return false;
      }
      
      // 检查文件名模式
      return (
        name.startsWith('index.') ||
        name.startsWith('src/index.') ||
        name.includes('/index.')
      );
    });

    if (indexFile) {
      // 如果是src/下的文件，去掉src/前缀
      if (indexFile.startsWith('src/')) {
        return `./${indexFile.replace('src/', '')}`;
      }
      return `./${indexFile}`;
    }

    // 优先级5: 检查是否有任何JavaScript/TypeScript文件
    const jsFile = fileNames.find(
      name =>
        name.endsWith('.js') ||
        name.endsWith('.jsx') ||
        name.endsWith('.ts') ||
        name.endsWith('.tsx'),
    );

    if (jsFile) {
      // 如果是src/下的文件，去掉src/前缀
      if (jsFile.startsWith('src/')) {
        return `./${jsFile.replace('src/', '')}`;
      }
      return `./${jsFile}`;
    }

    // 默认返回index
    return './index';
  }

  /**
   * 获取配置文件模板
   * 与 realUploadTestUI.html 的实现完全一致
   */
  private getConfigTemplates(
    fileStructure: FileStructure,
  ): Record<string, string> {
    const mainEntry = this.detectMainEntryPath(fileStructure);

    return {
      'lynx.config.js': `const { miniAppNodiffPlugin } = require("@byted-lynx/plugin-miniapp-nodiff");

/**
 * @type {import('@byted-lynx/lynx-speedy').UserConfig}
 */
module.exports = {
  dsl: "miniAppNodiff",
  input: {
    // 智能检测的入口文件路径: ${mainEntry}
    main: "${mainEntry}"
  },
  dslPlugin: miniAppNodiffPlugin({}),
  pageConfig: {
    enableJSRender: true,
  },
  encode: {
    useLepusNG: true,
    targetSdkVersion: "2.13",
    enableParallelElement: true,
  },
};`,
      'package.json': `{
  "dependencies": {
    "@byted-lynx/lepus-runtime": "3.0.0",
    "@byted-lynx/plugin-miniapp-nodiff": "3.0.0",
    "@byted/lynx-lightcharts": "0.9.4"

  }
}`,
      'app.json': `{
  "pages": [
    "pages/index/index"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#667eea",
    "navigationBarTitleText": "Lynx应用",
    "navigationBarTextStyle": "white",
    "backgroundColor": "#667eea"
  },
  "sitemapLocation": "sitemap.json"
}`,
    };
  }

  /**
   * 压缩文件
   * 将文件结构压缩为ZIP格式，包含配置模板
   * 与 realUploadTestUI.html 的实现保持一致
   *
   * @param files 文件结构对象
   * @returns Promise<Blob> 压缩后的ZIP文件
   */
  async compressFiles(files: FileStructure): Promise<Blob> {
    if (this.config.enableLogging) {
      console.log(
        `[UploadService] 开始压缩文件，文件数: ${Object.keys(files).length}`,
      );
    }

    const startTime = performance.now();
    const zip = new JSZip();

    // 添加解析出的文件到ZIP
    Object.entries(files).forEach(([path, content]) => {
      // 处理路径，确保采用正常的斜杠
      const normalizedPath = path.replace(/\\/g, '/');

      zip.file(normalizedPath, content);
      if (this.config.enableLogging) {
        console.log(
          `[UploadService] 添加文件到ZIP: ${normalizedPath} (${content.length} 字节)`,
        );
      }
    });

    // 添加必要的配置文件模板（如果不存在）
    // 获取配置模板，与 realUploadTestUI.html 保持一致
    const configTemplates = this.getConfigTemplates(files);
    const detectedMainEntry = this.detectMainEntryPath(files);

    if (this.config.enableLogging) {
      console.log(
        `[UploadService] 🎯 智能检测到主入口文件: ${detectedMainEntry}`,
      );
      console.log(
        `[UploadService] 📋 可用配置模板 (${Object.keys(configTemplates).length}个):`,
        Object.keys(configTemplates),
      );
      console.log(
        `[UploadService] 📁 现有文件 (${Object.keys(files).length}个):`,
        Object.keys(files),
      );
      console.log('[UploadService] 🔧 配置模板来源: realUploadTestUI.html');
    }

    let addedConfigFiles = 0;
    const addedFilesList: string[] = [];
    const skippedFilesList: string[] = [];

    Object.entries(configTemplates).forEach(([filePath, content]) => {
      if (!files[filePath]) {
        zip.file(filePath, content);
        addedConfigFiles++;
        addedFilesList.push(filePath);
        if (this.config.enableLogging) {
          console.log(
            `[UploadService] ✅ 添加配置文件: ${filePath} (${content.length} 字符)`,
          );
          // 显示配置文件的完整内容以验证格式
          console.log(`[UploadService] 📝 ${filePath} 完整内容:`);
          console.log(content);
          console.log(`[UploadService] 📝 ${filePath} 内容结束`);
        }
      } else {
        skippedFilesList.push(filePath);
        if (this.config.enableLogging) {
          console.log(`[UploadService] ⏭️ 跳过已存在的配置文件: ${filePath}`);
        }
      }
    });

    if (this.config.enableLogging) {
      console.log('[UploadService] 📊 配置文件统计:');
      console.log(`  - 总配置模板数: ${Object.keys(configTemplates).length}`);
      console.log(`  - 新添加文件数: ${addedConfigFiles}`);
      console.log(`  - 跳过文件数: ${skippedFilesList.length}`);
      console.log(`  - 添加的文件: [${addedFilesList.join(', ')}]`);
      console.log(`  - 跳过的文件: [${skippedFilesList.join(', ')}]`);
      console.log(
        '[UploadService] ✅ 配置模板处理完成，与 realUploadTestUI.html 完全一致',
      );
    }

    // 添加元数据文件
    const addedConfigFilesList = Object.keys(configTemplates).filter(
      key => !files[key],
    );
    const metadata = {
      generated: new Date().toISOString(),
      fileCount: Object.keys(files).length,
      configFilesAdded: addedConfigFilesList.length,
      configFilesList: addedConfigFilesList,
      totalConfigTemplates: Object.keys(configTemplates).length,
      mainEntry: this.detectMainEntryPath(files),
      generator: 'BatchProcessorService',
      version: '1.0.0',
      configTemplateSource: 'realUploadTestUI.html',
    };

    zip.file('meta.json', JSON.stringify(metadata, null, 2));

    // 生成ZIP文件 - 使用最高压缩级别，与realUploadTestUI.html保持一致
    const zipOptions = {
      type: 'blob' as const,
      compression: 'DEFLATE' as const,
      compressionOptions: {
        level: 9, // 最高压缩级别，与realUploadTestUI.html保持一致
      },
    };

    const zipBlob = await zip.generateAsync(zipOptions);
    const endTime = performance.now();

    if (this.config.enableLogging) {
      console.log(
        `[UploadService] 压缩完成，大小: ${zipBlob.size} 字节，耗时: ${(endTime - startTime).toFixed(2)}ms`,
      );
      console.log(
        `[UploadService] 主入口文件: ${this.detectMainEntryPath(files)}`,
      );
    }

    return zipBlob;
  }

  /**
   * 生成文件元数据
   * @param blob 文件Blob
   * @param fileName 文件名
   * @returns 文件元数据
   */
  private createFileMetadata(blob: Blob, fileName: string): FileMetadata {
    return {
      fileName,
      size: blob.size,
      createdAt: Date.now(),
      mimeType: blob.type || 'application/zip',
      typeTag: 'lynx',
    };
  }

  /**
   * 创建上传FormData
   * @param blob 文件Blob
   * @param fileName 文件名
   * @returns FormData对象
   */
  private createUploadFormData(blob: Blob, fileName: string): FormData {
    const formData = new FormData();

    // 按照 realUploadProcessor.js 的格式添加参数
    formData.append('file', blob, fileName);

    // 必需的参数 - 与 realUploadProcessor.js 保持一致
    formData.append('region', 'CN');
    formData.append('dir', 'so-web-code');
    formData.append('email', '<EMAIL>');

    return formData;
  }

  /**
   * 上传到CDN
   * 将文件上传到CDN服务器
   *
   * @param files 文件结构对象
   * @returns Promise<UploadResult> 上传结果
   */
  async uploadToCDN(files: FileStructure): Promise<UploadResult> {
    const uploadId = uuidv4().substring(0, 8);

    try {
      if (this.config.enableLogging) {
        console.log(`[UploadService] 开始上传到CDN... (ID: ${uploadId})`);
      }

      const startTime = performance.now();

      // 压缩文件
      const zipBlob = await this.compressFiles(files);

      // 生成唯一文件名
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 10);
      const fileName = `lynx_${timestamp}_${randomString}.zip`;

      // 创建FormData
      const formData = this.createUploadFormData(zipBlob, fileName);

      // 模拟上传模式
      if (this.config.mockMode) {
        await new Promise(resolve => setTimeout(resolve, 500));
        const mockCdnUrl = `https://mock-cdn.bytedance.net/file/${fileName}`;

        if (this.config.enableLogging) {
          console.log(`[UploadService] 模拟上传成功，CDN URL: ${mockCdnUrl}`);
        }

        return {
          success: true,
          cdnUrl: mockCdnUrl,
          timestamp,
          size: zipBlob.size,
        };
      }

      // 实际上传
      let response: Response | null = null;
      let responseData: UploadSuccessResponse | null = null;
      let attempt = 0;
      let error: Error | null = null;

      // 重试循环
      while (attempt < this.config.maxRetries) {
        attempt++;
        try {
          // 创建用于中断请求的控制器
          const controller = new AbortController();
          this.abortControllers.set(uploadId, controller);

          // 超时处理
          const timeoutId = setTimeout(() => {
            controller.abort();
            console.warn(
              `[UploadService] 上传超时 (尝试 ${attempt}/${this.config.maxRetries})`,
            );
          }, this.config.timeout);

          if (this.config.enableLogging && attempt > 1) {
            console.log(
              `[UploadService] 重试上传 ${attempt}/${this.config.maxRetries}`,
            );
          }

          // 记录请求详情
          if (this.config.enableLogging) {
            console.log('[UploadService] 发送上传请求:', {
              endpoint: this.config.cdnEndpoint,
              fileName,
              fileSize: zipBlob.size,
              attempt,
              maxRetries: this.config.maxRetries,
              timeout: this.config.timeout,
            });

            // 记录 FormData 内容
            const formDataEntries: Record<string, any> = {};
            for (const [key, value] of formData.entries()) {
              if (value instanceof File || value instanceof Blob) {
                formDataEntries[key] =
                  `[${value.constructor.name}] ${value.size} bytes`;
              } else {
                formDataEntries[key] = value;
              }
            }
            console.log('[UploadService] FormData 内容:', formDataEntries);
          }

          // 发送请求
          response = await fetch(this.config.cdnEndpoint, {
            method: 'POST',
            body: formData,
            signal: controller.signal,
            headers: {
              // 没有额外的头部，让浏览器自动设置Content-Type为multipart/form-data
            },
            // 确保包含凭证，如果需要
            credentials: 'include',
          });

          // 记录响应详情
          if (this.config.enableLogging) {
            console.log('[UploadService] 收到响应:', {
              status: response.status,
              statusText: response.statusText,
              headers: Object.fromEntries(response.headers.entries()),
              ok: response.ok,
            });
          }

          clearTimeout(timeoutId);
          this.abortControllers.delete(uploadId);

          // 检查响应状态
          if (!response.ok) {
            // 尝试读取错误响应内容
            let errorText = '';
            try {
              errorText = await response.text();
            } catch (e) {
              errorText = '无法读取错误响应内容';
            }

            if (this.config.enableLogging) {
              console.error('[UploadService] HTTP错误响应:', {
                status: response.status,
                statusText: response.statusText,
                errorText,
              });
            }

            throw new Error(
              `HTTP错误: ${response.status} ${response.statusText}${errorText ? ` - ${errorText}` : ''}`,
            );
          }

          // 解析响应
          const responseText = await response.text();

          if (this.config.enableLogging) {
            console.log('[UploadService] 原始响应内容:', responseText);
          }

          try {
            responseData = JSON.parse(responseText);
          } catch (parseError) {
            if (this.config.enableLogging) {
              console.error('[UploadService] JSON解析失败:', parseError);
            }
            throw new Error(`响应数据JSON解析失败: ${parseError}`);
          }

          if (this.config.enableLogging) {
            console.log('[UploadService] 解析后的响应数据:', responseData);
          }

          // 检查响应数据 - 修正字段名为 cdnUrl
          if (!responseData?.cdnUrl) {
            throw new Error(
              `响应数据格式错误，缺少cdnUrl。响应内容: ${JSON.stringify(responseData)}`,
            );
          }

          // 上传成功，跳出重试循环
          break;
        } catch (err) {
          error = err as Error;

          // 记录错误
          console.error(
            `[UploadService] 上传尝试 ${attempt}/${this.config.maxRetries} 失败:`,
            error.message,
          );

          // 最后一次尝试失败，不再重试
          if (attempt >= this.config.maxRetries) {
            break;
          }

          // 等待一段时间再重试
          await new Promise(resolve =>
            setTimeout(resolve, this.config.retryInterval),
          );
        }
      }

      // 所有重试都失败了
      if (!responseData) {
        throw error || new Error('上传失败，达到最大重试次数');
      }

      const endTime = performance.now();

      if (this.config.enableLogging) {
        console.log(
          `[UploadService] 上传成功，CDN URL: ${responseData.cdnUrl}`,
        );
        console.log(
          `[UploadService] 上传耗时: ${(endTime - startTime).toFixed(2)}ms`,
        );
      }

      return {
        success: true,
        cdnUrl: responseData.cdnUrl,
        fileId: responseData.fileId,
        timestamp,
        size: zipBlob.size,
        // 包含完整的响应数据，用于构建完整URL
        domain: responseData.domain,
        path: responseData.path,
        tosKey: responseData.tosKey,
        code: responseData.code,
      };
    } catch (error) {
      const errorMessage = (error as Error).message || String(error);
      console.error(
        `[UploadService] 上传失败 (ID: ${uploadId}): ${errorMessage}`,
      );

      return {
        success: false,
        error: errorMessage,
        cdnUrl: undefined,
        timestamp: undefined,
      };
    } finally {
      // 清理可能存在的中止控制器
      if (this.abortControllers.has(uploadId)) {
        this.abortControllers.delete(uploadId);
      }
    }
  }

  /**
   * 取消正在进行的上传
   * @param uploadId 上传ID
   * @returns 是否成功取消
   */
  cancelUpload(uploadId: string): boolean {
    const controller = this.abortControllers.get(uploadId);
    if (controller) {
      controller.abort();
      this.abortControllers.delete(uploadId);

      if (this.config.enableLogging) {
        console.log(`[UploadService] 已取消上传 (ID: ${uploadId})`);
      }

      return true;
    }

    return false;
  }

  /**
   * 构建完整的 CDN URL
   * 与 realUploadTestUI.html 的实现保持一致 - 简化版本
   *
   * @param uploadResult 上传结果对象
   * @returns string 完整的 CDN URL
   */
  buildFullCdnUrl(uploadResult: any): string {
    // 直接使用response中的cdnUrl，与realUploadTestUI.html保持一致
    return uploadResult.cdnUrl || uploadResult.url || '';
  }

  /**
   * 构建Playground URL
   * 根据CDN URL生成Playground预览链接
   * 与 realUploadTestUI.html 的实现保持一致
   *
   * @param cdnUrl CDN URL
   * @param exampleType 示例类型，默认为 'ttml-nodiff'
   * @param layout 布局，默认为 'preview'
   * @param sdkVersion SDK版本，默认为 '3.6.0-beta.2'
   * @returns string Playground URL
   */
  buildPlaygroundUrl(
    cdnUrl: string,
    exampleType = 'ttml-nodiff',
    layout = 'preview',
    sdkVersion = '3.6.0-beta.2',
  ): string {
    if (!cdnUrl) {
      console.error('[UploadService] 无法构建Playground URL: CDN URL为空');
      return '';
    }

    try {
      if (this.config.enableLogging) {
        console.log(`[UploadService] 构建Playground URL: ${cdnUrl}`);
      }

      // 按照 realUploadTestUI.html 的格式构建参数
      const params = new URLSearchParams();
      params.append('useSpeedy', 'true');
      params.append('exampleType', exampleType);
      params.append('layout', layout);

      // 确保 project URL 是完整的 https:// 格式
      let projectUrl = cdnUrl;
      if (
        !projectUrl.startsWith('http://') &&
        !projectUrl.startsWith('https://')
      ) {
        projectUrl = `https://${projectUrl}`;
      }
      params.append('project', projectUrl);

      // 添加 sdkVersion 参数
      params.append('sdkVersion', sdkVersion);

      const playgroundUrl = `${this.config.playgroundBaseUrl}?${params.toString()}`;

      if (this.config.enableLogging) {
        console.log(`[UploadService] Playground URL: ${playgroundUrl}`);
      }

      return playgroundUrl;
    } catch (error) {
      console.error(
        `[UploadService] 构建Playground URL失败: ${(error as Error).message}`,
      );
      console.error(`[UploadService] 输入参数 cdnUrl: ${cdnUrl}`);
      console.error(
        `[UploadService] 配置 playgroundBaseUrl: ${this.config.playgroundBaseUrl}`,
      );
      console.error(`[UploadService] 错误详情:`, error);
      return '';
    }
  }

  /**
   * 一键上传并生成Playground链接
   *
   * @param files 文件结构对象
   * @returns Promise<{ cdnUrl: string, playgroundUrl: string }> 上传结果
   */
  async uploadAndGeneratePlayground(files: FileStructure): Promise<{
    cdnUrl: string;
    playgroundUrl: string;
    success: boolean;
    error?: string;
    fileId?: string;
  }> {
    try {
      // 上传到CDN
      const uploadResult = await this.uploadToCDN(files);

      if (!uploadResult.success || !uploadResult.cdnUrl) {
        throw new Error(uploadResult.error || '上传失败，未返回CDN URL');
      }

      // 构建完整的 CDN URL
      const fullCdnUrl = this.buildFullCdnUrl(uploadResult);

      // 构建Playground URL
      const playgroundUrl = this.buildPlaygroundUrl(fullCdnUrl);

      if (!playgroundUrl) {
        throw new Error('构建Playground URL失败');
      }

      if (this.config.enableLogging) {
        console.log(`[UploadService] 完整CDN URL: ${fullCdnUrl}`);
        console.log(`[UploadService] Playground URL: ${playgroundUrl}`);
      }

      return {
        cdnUrl: fullCdnUrl, // 返回完整的 CDN URL
        playgroundUrl,
        success: true,
        fileId: uploadResult.fileId,
      };
    } catch (error) {
      const errorMessage = (error as Error).message || String(error);
      console.error(
        `[UploadService] 上传并生成Playground链接失败: ${errorMessage}`,
      );

      return {
        cdnUrl: '',
        playgroundUrl: '',
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 加载远程文件到本地
   * 用于加载已上传文件的内容进行查看或编辑
   *
   * @param url 远程文件URL
   * @returns Promise<FileStructure> 文件结构
   */
  async loadRemoteFile(url: string): Promise<{
    success: boolean;
    files?: FileStructure;
    error?: string;
  }> {
    try {
      if (this.config.enableLogging) {
        console.log(`[UploadService] 开始加载远程文件: ${url}`);
      }

      // 模拟模式
      if (this.config.mockMode) {
        await new Promise(resolve => setTimeout(resolve, 800));
        return {
          success: true,
          files: {
            'index.js':
              '// 这是一个模拟的文件内容\nconsole.log("Hello World");',
            'styles.css': 'body { color: red; }',
          },
        };
      }

      // 创建请求中断控制器
      const controller = new AbortController();
      const timeoutId = setTimeout(
        () => controller.abort(),
        this.config.timeout,
      );

      // 获取远程文件
      const response = await fetch(url, {
        method: 'GET',
        signal: controller.signal,
        credentials: 'include',
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      // 获取二进制数据
      const blob = await response.blob();

      // 判断是否是ZIP文件
      if (blob.type !== 'application/zip' && !url.endsWith('.zip')) {
        throw new Error(`不支持的文件类型: ${blob.type}`);
      }

      // 解压ZIP
      const zip = await JSZip.loadAsync(blob);
      const files: FileStructure = {};

      // 提取文件内容
      const filePromises = Object.keys(zip.files).map(async fileName => {
        // 跳过目录
        if (zip.files[fileName].dir) {
          return;
        }

        // 跳过元数据文件
        if (fileName === 'meta.json') {
          return;
        }

        const content = await zip.files[fileName].async('string');
        files[fileName] = content;
      });

      await Promise.all(filePromises);

      if (this.config.enableLogging) {
        console.log(
          `[UploadService] 远程文件加载成功，文件数: ${Object.keys(files).length}`,
        );
      }

      return {
        success: true,
        files,
      };
    } catch (error) {
      const errorMessage = (error as Error).message || String(error);
      console.error(`[UploadService] 加载远程文件失败: ${errorMessage}`);

      return {
        success: false,
        error: errorMessage,
      };
    }
  }

  /**
   * 上传单个文件（为ScreenshotService提供支持）
   * @param blob 文件Blob
   * @param fileName 文件名
   * @returns Promise<{url: string}> 上传结果
   */
  async uploadFile(blob: Blob, fileName: string): Promise<{ url: string }> {
    const uploadId = uuidv4().substring(0, 8);

    try {
      if (this.config.enableLogging) {
        console.log(
          `[UploadService] 开始上传单个文件: ${fileName} (ID: ${uploadId})`,
        );
      }

      // 创建FormData
      const formData = this.createUploadFormData(blob, fileName);

      // 模拟上传模式
      if (this.config.mockMode) {
        await new Promise(resolve => setTimeout(resolve, 300));
        const mockUrl = `https://mock-cdn.bytedance.net/file/${fileName}`;
        return { url: mockUrl };
      }

      // 实际上传
      const controller = new AbortController();
      this.abortControllers.set(uploadId, controller);

      const timeoutId = setTimeout(() => {
        controller.abort();
      }, this.config.timeout);

      const response = await fetch(this.config.cdnEndpoint, {
        method: 'POST',
        body: formData,
        signal: controller.signal,
        credentials: 'include',
      });

      clearTimeout(timeoutId);
      this.abortControllers.delete(uploadId);

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status} ${response.statusText}`);
      }

      const responseData = await response.json();

      if (!responseData?.cdnUrl && !responseData?.url) {
        throw new Error('响应数据格式错误，缺少URL');
      }

      return {
        url: responseData.cdnUrl || responseData.url,
      };
    } catch (error) {
      const errorMessage = (error as Error).message || String(error);
      console.error(
        `[UploadService] 单个文件上传失败 (ID: ${uploadId}): ${errorMessage}`,
      );
      throw error;
    } finally {
      if (this.abortControllers.has(uploadId)) {
        this.abortControllers.delete(uploadId);
      }
    }
  }
}
