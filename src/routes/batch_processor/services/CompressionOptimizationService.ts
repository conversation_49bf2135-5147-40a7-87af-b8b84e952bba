/**
 * 压缩优化服务
 * 提供智能文件压缩、代码优化和大小控制功能
 */

import JS<PERSON><PERSON> from 'jszip';
import { FileStructure } from '../types';

export interface CompressionOptions {
  level?: number; // 0-9 压缩级别
  enableMinification?: boolean; // 是否启用代码压缩
  enableTreeShaking?: boolean; // 是否移除未使用代码
  maxFileSize?: number; // 最大文件大小限制 (bytes)
  enableGzip?: boolean; // 是否启用Gzip压缩
  preserveComments?: boolean; // 是否保留注释
  optimizeImages?: boolean; // 是否优化图片
  preserveWhitespace?: boolean; // 是否保留空白字符
  compressionLevel?: string; // 压缩级别字符串
}

export interface CompressionResult {
  success: boolean;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  optimizedFiles: FileStructure;
  warnings: string[];
  errors: string[];
  metadata: {
    filesProcessed: number;
    minifiedFiles: number;
    removedFiles: number;
    processingTime: number;
  };
}

export class CompressionOptimizationService {
  private defaultOptions: CompressionOptions = {
    level: 6,
    enableMinification: true,
    enableTreeShaking: false,
    maxFileSize: 10 * 1024 * 1024, // 10MB
    enableGzip: false,
    preserveComments: false,
    optimizeImages: false,
  };

  /**
   * 压缩和优化文件结构
   */
  async compressAndOptimize(
    files: FileStructure,
    options?: Partial<CompressionOptions>,
  ): Promise<CompressionResult> {
    const startTime = performance.now();
    const opts = { ...this.defaultOptions, ...options };
    const warnings: string[] = [];
    const errors: string[] = [];

    try {
      console.log(
        '[CompressionOptimization] 🔧 开始ZIP打包（不修改代码内容）...',
      );

      // 计算原始大小
      const originalSize = this.calculateTotalSize(files);

      // 检查文件大小限制
      if (originalSize > opts.maxFileSize) {
        warnings.push(
          `文件总大小 ${this.formatBytes(originalSize)} 超过限制 ${this.formatBytes(opts.maxFileSize)}`,
        );
      }

      // 🔧 关键修复：只进行文件清理，不修改内容
      const optimizedFiles = await this.optimizeFiles(
        files,
        opts,
        warnings,
        errors,
      );

      // 移除空文件和重复文件（但不修改文件内容）
      const cleanedFiles = this.removeEmptyAndDuplicateFiles(
        optimizedFiles,
        warnings,
      );

      // 计算最终大小（应该与原始大小相同，因为没有修改内容）
      const compressedSize = this.calculateTotalSize(cleanedFiles);
      const compressionRatio =
        originalSize > 0 ? (originalSize - compressedSize) / originalSize : 0;

      const endTime = performance.now();

      const result: CompressionResult = {
        success: errors.length === 0,
        originalSize,
        compressedSize,
        compressionRatio,
        optimizedFiles: cleanedFiles,
        warnings,
        errors,
        metadata: {
          filesProcessed: Object.keys(files).length,
          minifiedFiles: Object.keys(cleanedFiles).filter(
            key => this.isMinifiableFile(key) && opts.enableMinification,
          ).length,
          removedFiles:
            Object.keys(files).length - Object.keys(cleanedFiles).length,
          processingTime: endTime - startTime,
        },
      };

      console.log(
        `[CompressionOptimization] ✅ ZIP打包完成: ${this.formatBytes(originalSize)} -> ${this.formatBytes(compressedSize)} (代码格式完全保持原样)`,
      );

      return result;
    } catch (error) {
      errors.push(`压缩优化失败: ${(error as Error).message}`);

      return {
        success: false,
        originalSize: this.calculateTotalSize(files),
        compressedSize: 0,
        compressionRatio: 0,
        optimizedFiles: files,
        warnings,
        errors,
        metadata: {
          filesProcessed: 0,
          minifiedFiles: 0,
          removedFiles: 0,
          processingTime: performance.now() - startTime,
        },
      };
    }
  }

  /**
   * 创建优化的ZIP文件
   */
  async createOptimizedZip(
    files: FileStructure,
    options?: Partial<CompressionOptions>,
  ): Promise<{ blob: Blob; metadata: CompressionResult }> {
    const opts = { ...this.defaultOptions, ...options };

    // 先优化文件
    const compressionResult = await this.compressAndOptimize(files, opts);

    // 创建ZIP
    const zip = new JSZip();

    // 添加优化后的文件
    Object.entries(compressionResult.optimizedFiles).forEach(
      ([path, content]) => {
        zip.file(path, content);
      },
    );

    // 添加压缩元数据
    const metadata = {
      generated: new Date().toISOString(),
      originalSize: compressionResult.originalSize,
      compressedSize: compressionResult.compressedSize,
      compressionRatio: compressionResult.compressionRatio,
      optimizations: {
        minification: opts.enableMinification,
        treeShaking: opts.enableTreeShaking,
        compressionLevel: opts.level,
      },
      warnings: compressionResult.warnings,
      processingTime: compressionResult.metadata.processingTime,
    };

    zip.file('compression-metadata.json', JSON.stringify(metadata, null, 2));

    // 生成ZIP
    const zipBlob = await zip.generateAsync({
      type: 'blob',
      compression: 'DEFLATE',
      compressionOptions: {
        level: opts.level,
      },
    });

    return {
      blob: zipBlob,
      metadata: compressionResult,
    };
  }

  /**
   * 🔧 关键修复：完全禁用文件内容优化
   * 压缩服务只负责ZIP打包，绝对不修改代码内容！
   */
  private async optimizeFiles(
    files: FileStructure,
    options: CompressionOptions,
    warnings: string[],
    errors: string[],
  ): Promise<FileStructure> {
    console.log(
      '[CompressionOptimization] 🚨 重要：压缩服务只进行ZIP打包，不修改任何代码内容',
    );

    // 🚨 直接返回原始文件，不做任何内容修改
    const optimizedFiles: FileStructure = {};

    for (const [filePath, content] of Object.entries(files)) {
      // 只检查文件是否为空，不做任何内容修改
      if (content.trim().length === 0) {
        warnings.push(`文件 ${filePath} 为空，已跳过`);
        continue;
      }

      // 🔧 关键：完全保持原始内容，不做任何修改
      optimizedFiles[filePath] = content;

      console.log(
        `[CompressionOptimization] ✅ 保持原始格式: ${filePath} (${content.length} 字符)`,
      );
    }

    return optimizedFiles;
  }

  /**
   * 代码压缩
   */
  private minifyCode(
    content: string,
    filePath: string,
    options: CompressionOptions,
  ): string {
    const extension = this.getFileExtension(filePath);

    switch (extension) {
      case 'js':
      case 'jsx':
        return this.minifyJavaScript(content);
      case 'css':
        return this.minifyCSS(content);
      case 'json':
        return this.minifyJSON(content);
      default:
        return content;
    }
  }

  /**
   * JavaScript 压缩
   */
  private minifyJavaScript(content: string): string {
    // 简单的JavaScript压缩
    return content
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除块注释
      .replace(/\/\/.*$/gm, '') // 移除行注释
      .replace(/\s+/g, ' ') // 压缩空白
      .replace(/;\s*}/g, '}') // 移除分号前的空格
      .replace(/{\s*/g, '{') // 移除大括号后的空格
      .replace(/\s*}/g, '}') // 移除大括号前的空格
      .trim();
  }

  /**
   * CSS 压缩
   */
  private minifyCSS(content: string): string {
    return content
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
      .replace(/\s+/g, ' ') // 压缩空白
      .replace(/;\s*}/g, '}') // 移除分号
      .replace(/{\s*/g, '{') // 移除大括号空格
      .replace(/\s*}/g, '}')
      .replace(/:\s*/g, ':') // 移除冒号后空格
      .replace(/;\s*/g, ';') // 移除分号后空格
      .trim();
  }

  /**
   * JSON 压缩
   */
  private minifyJSON(content: string): string {
    try {
      const parsed = JSON.parse(content);
      return JSON.stringify(parsed);
    } catch {
      return content;
    }
  }

  /**
   * 移除注释
   */
  private removeComments(content: string, filePath: string): string {
    const extension = this.getFileExtension(filePath);

    if (extension === 'js' || extension === 'jsx') {
      return content
        .replace(/\/\*[\s\S]*?\*\//g, '') // 块注释
        .replace(/\/\/.*$/gm, ''); // 行注释
    }

    if (extension === 'css') {
      return content.replace(/\/\*[\s\S]*?\*\//g, '');
    }

    return content;
  }

  /**
   * 移除多余空白
   */
  private removeExtraWhitespace(content: string): string {
    return content
      .replace(/\r\n/g, '\n') // 统一换行符
      .replace(/\n\s*\n/g, '\n') // 移除空行
      .replace(/^\s+/gm, '') // 移除行首空白
      .replace(/\s+$/gm, '') // 移除行尾空白
      .trim();
  }

  /**
   * 移除空文件和重复文件
   */
  private removeEmptyAndDuplicateFiles(
    files: FileStructure,
    warnings: string[],
  ): FileStructure {
    const cleanedFiles: FileStructure = {};
    const contentHashes = new Map<string, string>();

    for (const [filePath, content] of Object.entries(files)) {
      // 跳过空文件
      if (content.trim().length === 0) {
        warnings.push(`跳过空文件: ${filePath}`);
        continue;
      }

      // 检查重复文件
      const contentHash = this.hashContent(content);
      const existingFile = contentHashes.get(contentHash);

      if (existingFile) {
        warnings.push(`发现重复文件: ${filePath} 与 ${existingFile} 内容相同`);
        continue;
      }

      contentHashes.set(contentHash, filePath);
      cleanedFiles[filePath] = content;
    }

    return cleanedFiles;
  }

  /**
   * 计算文件总大小
   */
  private calculateTotalSize(files: FileStructure): number {
    return Object.values(files).reduce(
      (total, content) => total + new Blob([content]).size,
      0,
    );
  }

  /**
   * 检查文件是否可压缩
   */
  private isMinifiableFile(filePath: string): boolean {
    const extension = this.getFileExtension(filePath);
    return ['js', 'jsx', 'css', 'json'].includes(extension);
  }

  /**
   * 获取文件扩展名
   */
  private getFileExtension(filePath: string): string {
    return filePath.split('.').pop()?.toLowerCase() || '';
  }

  /**
   * 生成内容哈希
   */
  private hashContent(content: string): string {
    let hash = 0;
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }

  /**
   * 格式化字节大小
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) {
      return '0 Bytes';
    }

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
  }
}

// 单例实例
export const compressionOptimizer = new CompressionOptimizationService();
