/**
 * 增强的错误处理服务
 * 提供统一的错误分类、处理和恢复机制
 */

export interface ErrorInfo {
  id: string;
  type: ErrorType;
  message: string;
  originalError?: Error;
  timestamp: number;
  context?: Record<string, any>;
  severity: ErrorSeverity;
  recoverable: boolean;
  retryCount?: number;
  maxRetries?: number;
}

export enum ErrorType {
  NETWORK_ERROR = 'network_error',
  TIMEOUT_ERROR = 'timeout_error',
  PARSE_ERROR = 'parse_error',
  VALIDATION_ERROR = 'validation_error',
  API_ERROR = 'api_error',
  UPLOAD_ERROR = 'upload_error',
  COMPRESSION_ERROR = 'compression_error',
  UNKNOWN_ERROR = 'unknown_error',
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

export interface ErrorRecoveryStrategy {
  canRecover: (error: ErrorInfo) => boolean;
  recover: (error: ErrorInfo) => Promise<boolean>;
  getRecoveryMessage: (error: ErrorInfo) => string;
}

export class ErrorHandlingService {
  private errorHistory: ErrorInfo[] = [];
  private recoveryStrategies: Map<ErrorType, ErrorRecoveryStrategy> = new Map();
  private errorCallbacks: ((error: ErrorInfo) => void)[] = [];

  constructor() {
    this.initializeRecoveryStrategies();
  }

  /**
   * 初始化错误恢复策略
   */
  private initializeRecoveryStrategies() {
    // 网络错误恢复策略
    this.recoveryStrategies.set(ErrorType.NETWORK_ERROR, {
      canRecover: error => (error.retryCount || 0) < (error.maxRetries || 3),
      recover: async error => {
        await this.delay(Math.pow(2, error.retryCount || 0) * 1000);
        return true;
      },
      getRecoveryMessage: error =>
        `网络连接异常，正在重试... (${(error.retryCount || 0) + 1}/${error.maxRetries || 3})`,
    });

    // 超时错误恢复策略
    this.recoveryStrategies.set(ErrorType.TIMEOUT_ERROR, {
      canRecover: error => (error.retryCount || 0) < (error.maxRetries || 2),
      recover: async error => {
        await this.delay(2000);
        return true;
      },
      getRecoveryMessage: error =>
        `请求超时，正在重试... (${(error.retryCount || 0) + 1}/${error.maxRetries || 2})`,
    });

    // API错误恢复策略
    this.recoveryStrategies.set(ErrorType.API_ERROR, {
      canRecover: error => {
        const statusCode = error.context?.statusCode;
        return (
          statusCode >= 500 && (error.retryCount || 0) < (error.maxRetries || 2)
        );
      },
      recover: async error => {
        await this.delay(3000);
        return true;
      },
      getRecoveryMessage: error =>
        `服务器错误，正在重试... (${(error.retryCount || 0) + 1}/${error.maxRetries || 2})`,
    });

    // 上传错误恢复策略
    this.recoveryStrategies.set(ErrorType.UPLOAD_ERROR, {
      canRecover: error => (error.retryCount || 0) < (error.maxRetries || 3),
      recover: async error => {
        await this.delay(1500);
        return true;
      },
      getRecoveryMessage: error =>
        `上传失败，正在重试... (${(error.retryCount || 0) + 1}/${error.maxRetries || 3})`,
    });
  }

  /**
   * 处理错误
   */
  async handleError(
    error: Error | string,
    type: ErrorType = ErrorType.UNKNOWN_ERROR,
    context?: Record<string, any>,
  ): Promise<ErrorInfo> {
    const errorInfo: ErrorInfo = {
      id: this.generateErrorId(),
      type,
      message: typeof error === 'string' ? error : error.message,
      originalError: typeof error === 'string' ? undefined : error,
      timestamp: Date.now(),
      context,
      severity: this.determineSeverity(type, context),
      recoverable: this.isRecoverable(type),
      retryCount: 0,
      maxRetries: this.getMaxRetries(type),
    };

    this.errorHistory.push(errorInfo);
    this.notifyErrorCallbacks(errorInfo);

    console.error(`[ErrorHandling] ${type}: ${errorInfo.message}`, {
      context,
      severity: errorInfo.severity,
      recoverable: errorInfo.recoverable,
    });

    return errorInfo;
  }

  /**
   * 尝试恢复错误
   */
  async attemptRecovery(errorInfo: ErrorInfo): Promise<boolean> {
    const strategy = this.recoveryStrategies.get(errorInfo.type);

    if (!strategy || !strategy.canRecover(errorInfo)) {
      return false;
    }

    try {
      console.log(`[ErrorHandling] ${strategy.getRecoveryMessage(errorInfo)}`);

      errorInfo.retryCount = (errorInfo.retryCount || 0) + 1;
      const recovered = await strategy.recover(errorInfo);

      if (recovered) {
        console.log(`[ErrorHandling] 错误恢复成功: ${errorInfo.type}`);
      }

      return recovered;
    } catch (recoveryError) {
      console.error('[ErrorHandling] 错误恢复失败:', recoveryError);
      return false;
    }
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recentErrors: ErrorInfo[];
  } {
    const byType = {} as Record<ErrorType, number>;
    const bySeverity = {} as Record<ErrorSeverity, number>;

    this.errorHistory.forEach(error => {
      byType[error.type] = (byType[error.type] || 0) + 1;
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1;
    });

    return {
      total: this.errorHistory.length,
      byType,
      bySeverity,
      recentErrors: this.errorHistory.slice(-10),
    };
  }

  /**
   * 清理错误历史
   */
  clearErrorHistory(olderThan?: number) {
    if (olderThan) {
      this.errorHistory = this.errorHistory.filter(
        error => error.timestamp > olderThan,
      );
    } else {
      this.errorHistory = [];
    }
  }

  /**
   * 注册错误回调
   */
  onError(callback: (error: ErrorInfo) => void) {
    this.errorCallbacks.push(callback);
  }

  /**
   * 移除错误回调
   */
  offError(callback: (error: ErrorInfo) => void) {
    const index = this.errorCallbacks.indexOf(callback);
    if (index > -1) {
      this.errorCallbacks.splice(index, 1);
    }
  }

  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private determineSeverity(
    type: ErrorType,
    context?: Record<string, any>,
  ): ErrorSeverity {
    switch (type) {
      case ErrorType.NETWORK_ERROR:
      case ErrorType.TIMEOUT_ERROR:
        return ErrorSeverity.MEDIUM;
      case ErrorType.API_ERROR:
        const statusCode = context?.statusCode;
        if (statusCode >= 500) {
          return ErrorSeverity.HIGH;
        }
        if (statusCode >= 400) {
          return ErrorSeverity.MEDIUM;
        }
        return ErrorSeverity.LOW;
      case ErrorType.UPLOAD_ERROR:
      case ErrorType.COMPRESSION_ERROR:
        return ErrorSeverity.HIGH;
      case ErrorType.PARSE_ERROR:
      case ErrorType.VALIDATION_ERROR:
        return ErrorSeverity.MEDIUM;
      default:
        return ErrorSeverity.LOW;
    }
  }

  private isRecoverable(type: ErrorType): boolean {
    return this.recoveryStrategies.has(type);
  }

  private getMaxRetries(type: ErrorType): number {
    switch (type) {
      case ErrorType.NETWORK_ERROR:
      case ErrorType.UPLOAD_ERROR:
        return 3;
      case ErrorType.TIMEOUT_ERROR:
      case ErrorType.API_ERROR:
        return 2;
      default:
        return 1;
    }
  }

  private notifyErrorCallbacks(error: ErrorInfo) {
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error);
      } catch (err) {
        console.error('[ErrorHandling] 错误回调执行失败:', err);
      }
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 单例实例
export const errorHandlingService = new ErrorHandlingService();
