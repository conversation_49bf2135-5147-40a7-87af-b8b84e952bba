/**
 * PromptHistoryService.ts
 * 提示词历史记录服务，实现 LRU 缓存机制和 IndexedDB 持久化
 */

export interface PromptHistoryItem {
  id: string; // 唯一标识符
  title: string; // 提示词标题（可重命名）
  content: string; // 提示词内容
  createdAt: number; // 创建时间戳
  updatedAt: number; // 最后更新时间戳
  usageCount: number; // 使用次数
  lastUsedAt: number; // 最后使用时间戳
}

export interface PromptLRUCache {
  items: PromptHistoryItem[]; // LRU 排序的提示词列表
  maxSize: number; // 最大容量（10）
  currentId?: string; // 当前正在编辑的提示词ID
}

/**
 * 提示词历史记录服务
 * 实现 LRU 缓存机制和 IndexedDB 持久化存储
 */
export class PromptHistoryService {
  private readonly DB_NAME = 'PromptHistoryDB';
  private readonly DB_VERSION = 1;
  private readonly STORE_NAME = 'promptHistory';
  private readonly MAX_SIZE = 10;

  private db: IDBDatabase | null = null;
  private dbReady: Promise<boolean>;
  private dbReadyResolver!: (value: boolean) => void;
  private cache: PromptHistoryItem[] = [];
  private cacheLoaded = false;

  constructor() {
    // 初始化数据库连接
    this.dbReady = new Promise<boolean>(resolve => {
      this.dbReadyResolver = resolve;
    });
    this.initDB().catch(error => {
      console.error('Failed to initialize PromptHistoryDB:', error);
      this.dbReadyResolver(false);
    });
  }

  /**
   * 初始化 IndexedDB 连接
   */
  private async initDB(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (this.db) {
        resolve();
        return;
      }

      const request = window.indexedDB.open(this.DB_NAME, this.DB_VERSION);

      request.onerror = event => {
        console.error('PromptHistoryDB error:', event);
        reject(new Error('Failed to open PromptHistoryDB'));
        this.dbReadyResolver(false);
      };

      request.onsuccess = event => {
        this.db = (event.target as IDBOpenDBRequest).result;
        console.log('PromptHistoryDB connection established');
        this.dbReadyResolver(true);
        resolve();
      };

      request.onupgradeneeded = event => {
        const db = (event.target as IDBOpenDBRequest).result;

        // 创建存储对象
        if (!db.objectStoreNames.contains(this.STORE_NAME)) {
          const store = db.createObjectStore(this.STORE_NAME, {
            keyPath: 'id',
          });

          // 创建索引
          store.createIndex('lastUsedAt', 'lastUsedAt', { unique: false });
          store.createIndex('createdAt', 'createdAt', { unique: false });
          store.createIndex('title', 'title', { unique: false });

          console.log('PromptHistory object store created');
        }
      };
    });
  }

  /**
   * 等待数据库准备就绪
   */
  private async waitForDB(): Promise<void> {
    const isReady = await this.dbReady;
    if (!isReady || !this.db) {
      throw new Error('PromptHistoryDB is not available');
    }
  }

  /**
   * 闲时加载历史记录到内存缓存
   */
  async loadHistoryWhenIdle(): Promise<void> {
    if (this.cacheLoaded) {
      return;
    }

    // 使用 requestIdleCallback 在浏览器空闲时加载
    if ('requestIdleCallback' in window) {
      window.requestIdleCallback(async () => {
        await this.loadHistoryToCache();
      });
    } else {
      // 降级方案：使用 setTimeout
      setTimeout(async () => {
        await this.loadHistoryToCache();
      }, 100);
    }
  }

  /**
   * 加载历史记录到内存缓存
   */
  private async loadHistoryToCache(): Promise<void> {
    try {
      await this.waitForDB();

      const items = await this.getAllItemsFromDB();
      this.cache = items.sort((a, b) => b.lastUsedAt - a.lastUsedAt);
      this.cacheLoaded = true;

      console.log(`Loaded ${this.cache.length} prompt history items to cache`);
    } catch (error) {
      console.error('Failed to load prompt history to cache:', error);
    }
  }

  /**
   * 从数据库获取所有历史记录
   */
  private async getAllItemsFromDB(): Promise<PromptHistoryItem[]> {
    await this.waitForDB();

    return new Promise<PromptHistoryItem[]>((resolve, reject) => {
      const transaction = this.db!.transaction([this.STORE_NAME], 'readonly');
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.getAll();

      request.onsuccess = () => {
        resolve(request.result || []);
      };

      request.onerror = () => {
        reject(new Error('Failed to get all prompt history items'));
      };
    });
  }

  /**
   * 获取历史记录列表（LRU 排序）
   */
  async getHistory(): Promise<PromptHistoryItem[]> {
    if (!this.cacheLoaded) {
      await this.loadHistoryToCache();
    }
    return [...this.cache];
  }

  /**
   * 添加或更新提示词到历史记录
   */
  async addOrUpdatePrompt(
    content: string,
    title?: string,
  ): Promise<PromptHistoryItem> {
    // 确保缓存已加载
    if (!this.cacheLoaded) {
      await this.loadHistoryToCache();
    }

    // 检查是否已存在相同内容的提示词
    const existingIndex = this.cache.findIndex(
      item => item.content === content,
    );

    let item: PromptHistoryItem;
    const now = Date.now();

    if (existingIndex >= 0) {
      // 更新现有项目
      item = {
        ...this.cache[existingIndex],
        title: title || this.cache[existingIndex].title,
        updatedAt: now,
        lastUsedAt: now,
        usageCount: this.cache[existingIndex].usageCount + 1,
      };

      // 移动到列表首位
      this.cache.splice(existingIndex, 1);
      this.cache.unshift(item);
    } else {
      // 创建新项目
      item = {
        id: this.generateId(),
        title: title || this.generateTitle(content),
        content,
        createdAt: now,
        updatedAt: now,
        usageCount: 1,
        lastUsedAt: now,
      };

      // 添加到列表首位
      this.cache.unshift(item);

      // 保持最大容量
      if (this.cache.length > this.MAX_SIZE) {
        const removedItems = this.cache.splice(this.MAX_SIZE);
        // 从数据库删除超出的项目
        for (const removedItem of removedItems) {
          this.deleteFromDB(removedItem.id).catch(console.error);
        }
      }
    }

    // 保存到数据库
    await this.saveToDB(item);

    return item;
  }

  /**
   * 使用提示词（更新 LRU 顺序）
   */
  async usePrompt(id: string): Promise<PromptHistoryItem | null> {
    if (!this.cacheLoaded) {
      await this.loadHistoryToCache();
    }

    const index = this.cache.findIndex(item => item.id === id);
    if (index === -1) {
      return null;
    }

    const item = {
      ...this.cache[index],
      lastUsedAt: Date.now(),
      usageCount: this.cache[index].usageCount + 1,
    };

    // 移动到列表首位
    this.cache.splice(index, 1);
    this.cache.unshift(item);

    // 保存到数据库
    await this.saveToDB(item);

    return item;
  }

  /**
   * 重命名提示词
   */
  async renamePrompt(id: string, newTitle: string): Promise<boolean> {
    if (!this.cacheLoaded) {
      await this.loadHistoryToCache();
    }

    const index = this.cache.findIndex(item => item.id === id);
    if (index === -1) {
      return false;
    }

    const item = {
      ...this.cache[index],
      title: newTitle,
      updatedAt: Date.now(),
    };

    this.cache[index] = item;
    await this.saveToDB(item);

    return true;
  }

  /**
   * 删除提示词
   */
  async deletePrompt(id: string): Promise<boolean> {
    if (!this.cacheLoaded) {
      await this.loadHistoryToCache();
    }

    const index = this.cache.findIndex(item => item.id === id);
    if (index === -1) {
      return false;
    }

    this.cache.splice(index, 1);
    await this.deleteFromDB(id);

    return true;
  }

  /**
   * 清空所有历史记录
   */
  async clearHistory(): Promise<void> {
    this.cache = [];
    await this.clearDB();
  }

  /**
   * 保存项目到数据库
   */
  private async saveToDB(item: PromptHistoryItem): Promise<void> {
    await this.waitForDB();

    return new Promise<void>((resolve, reject) => {
      const transaction = this.db!.transaction([this.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.put(item);

      request.onsuccess = () => resolve();
      request.onerror = () =>
        reject(new Error('Failed to save prompt history item'));
    });
  }

  /**
   * 从数据库删除项目
   */
  private async deleteFromDB(id: string): Promise<void> {
    await this.waitForDB();

    return new Promise<void>((resolve, reject) => {
      const transaction = this.db!.transaction([this.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.delete(id);

      request.onsuccess = () => resolve();
      request.onerror = () =>
        reject(new Error('Failed to delete prompt history item'));
    });
  }

  /**
   * 清空数据库
   */
  private async clearDB(): Promise<void> {
    await this.waitForDB();

    return new Promise<void>((resolve, reject) => {
      const transaction = this.db!.transaction([this.STORE_NAME], 'readwrite');
      const store = transaction.objectStore(this.STORE_NAME);
      const request = store.clear();

      request.onsuccess = () => resolve();
      request.onerror = () =>
        reject(new Error('Failed to clear prompt history'));
    });
  }

  /**
   * 生成唯一 ID
   */
  private generateId(): string {
    return `prompt_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 从内容生成标题
   */
  private generateTitle(content: string): string {
    const lines = content.trim().split('\n');
    const firstLine = lines[0] || '';

    // 移除 markdown 标记和特殊字符
    const cleanLine = firstLine
      .replace(/^#+\s*/, '') // 移除 markdown 标题标记
      .replace(/[*_`]/g, '') // 移除 markdown 格式标记
      .trim();

    // 截取前30个字符作为标题
    return cleanLine.length > 30
      ? `${cleanLine.substring(0, 30)}...`
      : cleanLine || '未命名提示词';
  }
}

// 单例实例
export const promptHistoryService = new PromptHistoryService();
