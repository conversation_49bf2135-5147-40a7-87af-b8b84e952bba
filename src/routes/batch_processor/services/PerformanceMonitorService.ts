/**
 * 性能监控服务
 * 监控批处理系统的性能指标和资源使用情况
 */

export interface PerformanceMetrics {
  id: string;
  operation: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  memoryUsage?: {
    used: number;
    total: number;
    percentage: number;
  };
  networkStats?: {
    requestCount: number;
    totalBytes: number;
    averageResponseTime: number;
  };
  metadata?: Record<string, any>;
}

export interface SystemStats {
  totalOperations: number;
  successfulOperations: number;
  failedOperations: number;
  averageOperationTime: number;
  peakMemoryUsage: number;
  currentMemoryUsage: number;
  networkThroughput: number;
  errorRate: number;
  uptime: number;
}

export class PerformanceMonitorService {
  private metrics: PerformanceMetrics[] = [];
  private activeOperations: Map<string, PerformanceMetrics> = new Map();
  private startTime: number = Date.now();
  private memoryCheckInterval?: NodeJS.Timeout;
  private maxMetricsHistory = 1000;

  constructor() {}

  /**
   * 开始监控操作
   */
  startOperation(operation: string, metadata?: Record<string, any>): string {
    const id = this.generateOperationId();
    const metric: PerformanceMetrics = {
      id,
      operation,
      startTime: performance.now(),
      success: false,
      metadata,
    };

    this.activeOperations.set(id, metric);

    console.debug(`[Performance] 开始监控操作: ${operation} (ID: ${id})`);
    return id;
  }

  /**
   * 结束监控操作
   */
  endOperation(
    id: string,
    success = true,
    metadata?: Record<string, any>,
  ): PerformanceMetrics | null {
    const metric = this.activeOperations.get(id);
    if (!metric) {
      console.warn(`[Performance] 未找到操作: ${id}`);
      return null;
    }

    metric.endTime = performance.now();
    metric.duration = metric.endTime - metric.startTime;
    metric.success = success;
    metric.memoryUsage = this.getCurrentMemoryUsage();

    if (metadata) {
      metric.metadata = { ...metric.metadata, ...metadata };
    }

    this.activeOperations.delete(id);
    this.metrics.push(metric);

    // 限制历史记录数量
    if (this.metrics.length > this.maxMetricsHistory) {
      this.metrics = this.metrics.slice(-this.maxMetricsHistory);
    }

    console.debug(
      `[Performance] 操作完成: ${metric.operation} - ${metric.duration?.toFixed(2)}ms (成功: ${success})`,
    );
    return metric;
  }

  /**
   * 记录网络请求
   */
  recordNetworkRequest(
    url: string,
    method: string,
    responseTime: number,
    bytes: number,
    success: boolean,
  ) {
    const networkMetric: PerformanceMetrics = {
      id: this.generateOperationId(),
      operation: 'network_request',
      startTime: performance.now() - responseTime,
      endTime: performance.now(),
      duration: responseTime,
      success,
      networkStats: {
        requestCount: 1,
        totalBytes: bytes,
        averageResponseTime: responseTime,
      },
      metadata: {
        url,
        method,
        bytes,
      },
    };

    this.metrics.push(networkMetric);
  }

  /**
   * 获取系统统计信息
   */
  getSystemStats(): SystemStats {
    const now = performance.now();
    const completedMetrics = this.metrics.filter(m => m.endTime);

    const totalOperations = completedMetrics.length;
    const successfulOperations = completedMetrics.filter(m => m.success).length;
    const failedOperations = totalOperations - successfulOperations;

    const durations = completedMetrics.map(m => m.duration || 0);
    const averageOperationTime =
      durations.length > 0
        ? durations.reduce((sum, d) => sum + d, 0) / durations.length
        : 0;

    const memoryUsages = completedMetrics
      .map(m => m.memoryUsage?.used || 0)
      .filter(usage => usage > 0);
    const peakMemoryUsage =
      memoryUsages.length > 0 ? Math.max(...memoryUsages) : 0;

    const networkMetrics = completedMetrics.filter(m => m.networkStats);
    const totalBytes = networkMetrics.reduce(
      (sum, m) => sum + (m.networkStats?.totalBytes || 0),
      0,
    );
    const networkThroughput = totalBytes / ((now - this.startTime) / 1000); // bytes per second

    return {
      totalOperations,
      successfulOperations,
      failedOperations,
      averageOperationTime,
      peakMemoryUsage,
      currentMemoryUsage: this.getCurrentMemoryUsage().used,
      networkThroughput,
      errorRate: totalOperations > 0 ? failedOperations / totalOperations : 0,
      uptime: now - this.startTime,
    };
  }

  /**
   * 获取操作性能报告
   */
  getOperationReport(operation?: string): {
    operation: string;
    count: number;
    successRate: number;
    averageDuration: number;
    minDuration: number;
    maxDuration: number;
    totalDuration: number;
  }[] {
    const filteredMetrics = operation
      ? this.metrics.filter(m => m.operation === operation)
      : this.metrics;

    const operationGroups = new Map<string, PerformanceMetrics[]>();

    filteredMetrics.forEach(metric => {
      const op = metric.operation;
      if (!operationGroups.has(op)) {
        operationGroups.set(op, []);
      }
      operationGroups.get(op)!.push(metric);
    });

    return Array.from(operationGroups.entries()).map(([op, metrics]) => {
      const durations = metrics.map(m => m.duration || 0);
      const successCount = metrics.filter(m => m.success).length;

      return {
        operation: op,
        count: metrics.length,
        successRate: metrics.length > 0 ? successCount / metrics.length : 0,
        averageDuration:
          durations.length > 0
            ? durations.reduce((sum, d) => sum + d, 0) / durations.length
            : 0,
        minDuration: durations.length > 0 ? Math.min(...durations) : 0,
        maxDuration: durations.length > 0 ? Math.max(...durations) : 0,
        totalDuration: durations.reduce((sum, d) => sum + d, 0),
      };
    });
  }

  /**
   * 获取最近的性能指标
   */
  getRecentMetrics(count = 10): PerformanceMetrics[] {
    return this.metrics.slice(-count);
  }

  /**
   * 清理性能数据
   */
  clearMetrics(olderThan?: number) {
    if (olderThan) {
      this.metrics = this.metrics.filter(m => m.startTime > olderThan);
    } else {
      this.metrics = [];
    }
  }

  /**
   * 导出性能数据
   */
  exportMetrics(): {
    systemStats: SystemStats;
    operationReports: ReturnType<typeof this.getOperationReport>;
    recentMetrics: PerformanceMetrics[];
    exportTime: number;
  } {
    return {
      systemStats: this.getSystemStats(),
      operationReports: this.getOperationReport(),
      recentMetrics: this.getRecentMetrics(50),
      exportTime: Date.now(),
    };
  }

  /**
   * 检查性能警告
   */
  checkPerformanceWarnings(): {
    type: string;
    message: string;
    severity: 'low' | 'medium' | 'high';
  }[] {
    const warnings: {
      type: string;
      message: string;
      severity: 'low' | 'medium' | 'high';
    }[] = [];
    const stats = this.getSystemStats();

    // 检查错误率
    if (stats.errorRate > 0.1) {
      warnings.push({
        type: 'high_error_rate',
        message: `错误率过高: ${(stats.errorRate * 100).toFixed(1)}%`,
        severity: stats.errorRate > 0.2 ? 'high' : 'medium',
      });
    }

    return warnings;
  }

  private generateOperationId(): string {
    return `op_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getCurrentMemoryUsage(): {
    used: number;
    total: number;
    percentage: number;
  } {
    if (typeof performance !== 'undefined' && 'memory' in performance) {
      const { memory } = performance as any;
      return {
        used: memory.usedJSHeapSize,
        total: memory.totalJSHeapSize,
        percentage: (memory.usedJSHeapSize / memory.totalJSHeapSize) * 100,
      };
    }

    // 浏览器不支持内存API时的fallback
    return { used: 0, total: 0, percentage: 0 };
  }

  private startMemoryMonitoring() {
    this.memoryCheckInterval = setInterval(() => {
      const warnings = this.checkPerformanceWarnings();
      warnings.forEach(warning => {
        if (warning.severity === 'high') {
          console.warn(`[Performance] ${warning.message}`);
        }
      });
    }, 60000); // 每30秒检查一次
  }

  /**
   * 销毁监控服务
   */
  destroy() {
    if (this.memoryCheckInterval) {
      clearInterval(this.memoryCheckInterval);
    }
    this.metrics = [];
    this.activeOperations.clear();
  }
}

// 单例实例
export const performanceMonitor = new PerformanceMonitorService();
