<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔄 重试失败项按钮图标修复测试</title>
    <link rel="stylesheet" href="styles/index.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 32px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 24px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            background: #f8fafc;
        }
        
        .test-section h3 {
            margin: 0 0 20px 0;
            color: #1e293b;
            font-size: 18px;
            font-weight: 600;
        }
        
        .button-row {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
            margin-bottom: 16px;
        }
        
        .description {
            font-size: 14px;
            color: #64748b;
            line-height: 1.6;
            background: white;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #3b82f6;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin-top: 20px;
        }
        
        .before, .after {
            padding: 20px;
            border-radius: 8px;
        }
        
        .before {
            background: #fef2f2;
            border: 1px solid #fecaca;
        }
        
        .after {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
        }
        
        .before h4 {
            color: #dc2626;
            margin: 0 0 16px 0;
        }
        
        .after h4 {
            color: #16a34a;
            margin: 0 0 16px 0;
        }
        
        /* 模拟问题按钮 - 图标不可见 */
        .problematic-btn .retry-icon {
            color: transparent !important;
            stroke: transparent !important;
        }
        
        /* 检查工具 */
        .icon-check {
            margin-top: 20px;
            padding: 16px;
            background: #fffbeb;
            border: 1px solid #fde68a;
            border-radius: 8px;
        }
        
        .icon-check h4 {
            margin: 0 0 12px 0;
            color: #92400e;
            font-size: 16px;
        }
        
        .check-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .check-pass {
            color: #16a34a;
        }
        
        .check-fail {
            color: #dc2626;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔄 重试失败项按钮图标修复测试</h1>
        <p>测试修复后的按钮图标显示问题，确保SVG图标在所有状态下都能正确显示。</p>
        
        <div class="test-section">
            <h3>✅ 修复后的按钮（应该可以看到图标）</h3>
            <div class="button-row">
                <button class="btn btn-sm btn--primary-gold retry-failed-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试失败项</span>
                    <span class="retry-failed-badge">4</span>
                </button>

                <button class="btn btn-sm btn--primary-gold retry-failed-btn opacity-50 cursor-not-allowed" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试中...</span>
                    <span class="retry-failed-badge">4</span>
                </button>
            </div>
            <div class="description">
                ✅ 修复内容：<br>
                • 强制设置图标颜色为 #92400e（深棕色）<br>
                • 添加多层 !important 确保样式优先级<br>
                • 设置 z-index: 10 防止被遮挡<br>
                • 确保 display: inline-block 和 visibility: visible<br>
                • 悬停时图标颜色变为更深的 #78350f<br>
                • 添加全局样式覆盖保护，防止被其他CSS规则影响
            </div>
        </div>
        
        <div class="test-section">
            <h3>❌ 问题演示（模拟修复前的状态）</h3>
            <div class="button-row">
                <button class="btn btn-sm btn--primary-gold retry-failed-btn problematic-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试失败项</span>
                    <span class="retry-failed-badge">4</span>
                </button>
            </div>
            <div class="description">
                ❌ 问题现象：图标不可见，可能的原因包括：<br>
                • 图标颜色与背景色相同<br>
                • CSS样式被其他规则覆盖<br>
                • z-index层级问题<br>
                • display 或 visibility 属性问题
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔍 图标检查工具</h3>
            <div class="icon-check">
                <h4>自动检测图标显示状态</h4>
                <div id="check-results"></div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🎨 不同数量的徽章测试</h3>
            <div class="button-row">
                <button class="btn btn-sm btn--primary-gold retry-failed-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试失败项</span>
                    <span class="retry-failed-badge">1</span>
                </button>
                
                <button class="btn btn-sm btn--primary-gold retry-failed-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试失败项</span>
                    <span class="retry-failed-badge">99</span>
                </button>
                
                <button class="btn btn-sm btn--primary-gold retry-failed-btn">
                    <svg xmlns="http://www.w3.org/2000/svg" class="retry-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                    </svg>
                    <span class="retry-text">重试失败项</span>
                    <span class="retry-failed-badge">999+</span>
                </button>
            </div>
            <div class="description">
                测试不同数字长度的徽章显示效果，确保布局稳定。
            </div>
        </div>
    </div>
    
    <script>
        // 自动检测图标显示状态
        function checkIconVisibility() {
            const checkResults = document.getElementById('check-results');
            const icons = document.querySelectorAll('.retry-icon:not(.problematic-btn .retry-icon)');

            let results = [];

            icons.forEach((icon, index) => {
                const rect = icon.getBoundingClientRect();
                const computedStyle = window.getComputedStyle(icon);

                const checks = [
                    {
                        name: '图标尺寸',
                        pass: rect.width > 0 && rect.height > 0,
                        value: `${rect.width.toFixed(1)}x${rect.height.toFixed(1)}px`
                    },
                    {
                        name: '可见性',
                        pass: computedStyle.visibility === 'visible',
                        value: computedStyle.visibility
                    },
                    {
                        name: '透明度',
                        pass: parseFloat(computedStyle.opacity) > 0,
                        value: computedStyle.opacity
                    },
                    {
                        name: '颜色',
                        pass: computedStyle.color !== 'rgba(0, 0, 0, 0)' && computedStyle.color !== 'transparent',
                        value: computedStyle.color
                    },
                    {
                        name: 'Stroke',
                        pass: computedStyle.stroke !== 'none' && computedStyle.stroke !== 'transparent',
                        value: computedStyle.stroke || 'none'
                    },
                    {
                        name: 'Fill',
                        pass: computedStyle.fill === 'none' || computedStyle.fill === 'transparent',
                        value: computedStyle.fill || 'none'
                    },
                    {
                        name: 'Display',
                        pass: computedStyle.display !== 'none',
                        value: computedStyle.display
                    },
                    {
                        name: 'Z-Index',
                        pass: parseInt(computedStyle.zIndex) >= 0 || computedStyle.zIndex === 'auto',
                        value: computedStyle.zIndex
                    }
                ];

                results.push({
                    index: index + 1,
                    checks: checks,
                    overall: checks.every(check => check.pass)
                });
            });

            checkResults.innerHTML = results.map(result => `
                <div style="margin-bottom: 16px; padding: 12px; border-radius: 6px; background: ${result.overall ? '#f0fdf4' : '#fef2f2'}; border: 1px solid ${result.overall ? '#bbf7d0' : '#fecaca'};">
                    <strong>图标 ${result.index} ${result.overall ? '✅ 正常' : '❌ 异常'}</strong>
                    ${result.checks.map(check => `
                        <div class="check-item">
                            <span class="${check.pass ? 'check-pass' : 'check-fail'}">${check.pass ? '✅' : '❌'}</span>
                            <span>${check.name}: ${check.value}</span>
                        </div>
                    `).join('')}
                </div>
            `).join('');
        }
        
        // 页面加载完成后检测
        window.addEventListener('load', checkIconVisibility);
        
        // 每2秒重新检测一次
        setInterval(checkIconVisibility, 2000);
    </script>
</body>
</html>
