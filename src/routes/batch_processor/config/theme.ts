/**
 * theme.ts
 * 批量处理工具的全局主题配置
 * 定义颜色、字体、尺寸等主题相关变量
 */

/**
 * 统一颜色系统 - 商务风蓝灰主题
 */
export const colors = {
  // 主色调 - 商务天蓝色系
  primary: {
    50: '#E8F4FD', // 极浅天蓝色
    100: '#C3E2FB', // 浅天蓝色
    200: '#9BCEF8', // 中浅天蓝色
    300: '#73BAF5', // 中天蓝色
    400: '#4BA6F2', // 深天蓝色
    500: '#2392EF', // 主天蓝色
    600: '#1F7ED6', // 深主天蓝色
    700: '#1B6ABD', // 更深天蓝色
    800: '#1756A4', // 深色天蓝色
    900: '#13428B', // 最深天蓝色
  },

  // 辅助色 - 商务蓝色系
  secondary: {
    50: '#E3F2FD', // 极浅蓝色
    100: '#BBDEFB', // 浅蓝色
    200: '#90CAF9', // 中浅蓝色
    300: '#64B5F6', // 中蓝色
    400: '#42A5F5', // 深蓝色
    500: '#2196F3', // 主蓝色
    600: '#1E88E5', // 深主蓝色
    700: '#1976D2', // 更深蓝色
    800: '#1565C0', // 深色蓝色
    900: '#0D47A1', // 最深蓝色
  },

  // 状态色 - 商务蓝灰主题
  success: {
    light: '#F0F8FF', // 极浅天蓝色背景
    main: '#64B5F6', // 成功蓝色 - 更浅
    dark: '#42A5F5',
    gradient:
      'linear-gradient(135deg, #F0F8FF 0%, #E3F2FD 30%, #BBDEFB 70%, #90CAF9 100%)',
    highlight:
      'linear-gradient(135deg, rgba(240, 248, 255, 0.9) 0%, rgba(227, 242, 253, 0.8) 50%, rgba(187, 222, 251, 0.7) 100%)',
  },
  warning: {
    light: '#FFFEF7',
    main: '#FFD54F', // 商务黄色 - 更浅
    dark: '#FFC107',
  },
  error: {
    light: '#FFFEF9', // 极浅浅浅黄色背景
    main: '#D4AF37', // 失败暗金黄色
    dark: '#B8860B',
    gradient:
      'linear-gradient(135deg, #FFFEF9 0%, #FFFCF0 30%, #F5F5F5 70%, #E8E8E8 100%)',
    highlight:
      'linear-gradient(135deg, rgba(255, 254, 249, 0.9) 0%, rgba(255, 252, 240, 0.8) 50%, rgba(245, 245, 245, 0.7) 100%)',
    disabled: 'linear-gradient(135deg, #FFFEF9 0%, #F8F8F8 100%)', // 不可点击状态
  },
  info: {
    light: '#E3F2FD',
    main: '#2196F3', // 商务蓝色
    dark: '#1976D2',
  },
  processing: {
    light: '#F3F4F6',
    main: '#6B7280', // 商务灰色
    dark: '#4B5563',
  },

  // 中性色 - 商务灰色系
  gray: {
    50: '#F8FAFC',
    100: '#F1F5F9',
    200: '#E2E8F0',
    300: '#CBD5E1',
    400: '#94A3B8',
    500: '#64748B',
    600: '#475569',
    700: '#334155',
    800: '#1E293B',
    900: '#0F172A',
  },

  // 基础色
  black: '#212121',
  white: '#FFFFFF',
  transparent: 'transparent',
};

/**
 * 现代化字体系统
 */
export const fonts = {
  // 优雅字体家族
  family: {
    base: "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'SF Pro Display', 'SF Pro Text', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei UI', 'Microsoft YaHei', sans-serif",
    mono: "'SFMono-Regular', 'SF Mono', Consolas, 'Liberation Mono', Menlo, Courier, monospace",
  },

  // 现代化字体大小
  size: {
    xs: '0.75rem', // 12px - 标签文字
    sm: '0.8125rem', // 13px - 小字
    base: '0.9375rem', // 15px - 正文
    lg: '1.125rem', // 18px - 小标题
    xl: '1.375rem', // 22px - 中标题
    '2xl': '1.75rem', // 28px - 大标题
    '3xl': '2.25rem', // 36px - 超大标题
    '4xl': '3rem', // 48px - 展示标题
  },

  // 字体粗细
  weight: {
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700,
    extrabold: 800,
  },

  // 精致行高
  lineHeight: {
    none: 1,
    tight: 1.2, // 标题行高
    snug: 1.3, // 紧凑行高
    normal: 1.5, // 标准行高
    relaxed: 1.6, // 舒适行高
    loose: 1.75, // 宽松行高
  },

  // 字间距
  letterSpacing: {
    tighter: '-0.02em',
    tight: '-0.01em',
    normal: '0',
    wide: '0.01em',
    wider: '0.02em',
  },
};

/**
 * 间距与尺寸
 */
export const spacing = {
  0: '0',
  0.5: '0.125rem', // 2px
  1: '0.25rem', // 4px
  1.5: '0.375rem', // 6px
  2: '0.5rem', // 8px
  2.5: '0.625rem', // 10px
  3: '0.75rem', // 12px
  3.5: '0.875rem', // 14px
  4: '1rem', // 16px
  5: '1.25rem', // 20px
  6: '1.5rem', // 24px
  8: '2rem', // 32px
  10: '2.5rem', // 40px
  12: '3rem', // 48px
  16: '4rem', // 64px
  20: '5rem', // 80px
  24: '6rem', // 96px
};

/**
 * 层级系统
 */
export const zIndices = {
  hide: -1,
  auto: 'auto',
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  toast: 1600,
  tooltip: 1700,
};

/**
 * 边框与圆角
 */
export const borders = {
  radius: {
    none: '0',
    sm: '0.125rem', // 2px
    base: '0.25rem', // 4px
    md: '0.375rem', // 6px
    lg: '0.5rem', // 8px
    xl: '0.75rem', // 12px
    '2xl': '1rem', // 16px
    '3xl': '1.5rem', // 24px
    full: '9999px',
  },
  width: {
    0: '0',
    1: '1px',
    2: '2px',
    4: '4px',
    8: '8px',
  },
  style: {
    solid: 'solid',
    dashed: 'dashed',
    dotted: 'dotted',
  },
};

/**
 * 精致阴影系统 - 商务蓝灰主题
 */
export const shadows = {
  none: 'none',
  xs: '0 1px 2px rgba(100, 116, 139, 0.08)',
  sm: '0 1px 3px rgba(100, 116, 139, 0.12), 0 1px 2px rgba(100, 116, 139, 0.18)',
  base: '0 4px 6px rgba(100, 116, 139, 0.12), 0 2px 4px rgba(100, 116, 139, 0.08)',
  md: '0 4px 6px rgba(100, 116, 139, 0.12), 0 2px 4px rgba(100, 116, 139, 0.08)',
  lg: '0 10px 15px rgba(100, 116, 139, 0.15), 0 4px 6px rgba(100, 116, 139, 0.08)',
  xl: '0 20px 25px rgba(100, 116, 139, 0.15), 0 10px 10px rgba(100, 116, 139, 0.04)',
  '2xl': '0 25px 50px rgba(100, 116, 139, 0.20)',
  inner: 'inset 0 2px 4px rgba(100, 116, 139, 0.06)',
  outline: '0 0 0 3px rgba(35, 146, 239, 0.12)', // 蓝色焦点轮廓

  // 按钮专用阴影
  button: '0 4px 12px rgba(35, 146, 239, 0.20)',
  buttonHover: '0 6px 16px rgba(35, 146, 239, 0.28)',

  // 卡片悬停阴影
  cardHover:
    '0 8px 25px rgba(100, 116, 139, 0.15), 0 4px 10px rgba(100, 116, 139, 0.08)',

  // 层级阴影
  elevated:
    '0 2px 8px rgba(100, 116, 139, 0.10), 0 1px 4px rgba(100, 116, 139, 0.06)',
  floating:
    '0 12px 28px rgba(100, 116, 139, 0.15), 0 4px 12px rgba(100, 116, 139, 0.08)',
};

/**
 * 流畅动画系统
 */
export const animations = {
  // 过渡动画
  transition: {
    fast: 'all 0.15s cubic-bezier(0.4, 0, 0.2, 1)',
    base: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    slow: 'all 0.5s cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },

  // 缓动函数
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
    easeOut: 'cubic-bezier(0, 0, 0.2, 1)',
    easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  },

  // 关键帧动画
  keyframes: {
    fadeIn: `
      from {
        opacity: 0;
        transform: translateY(10px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    `,
    slideInRight: `
      from {
        opacity: 0;
        transform: translateX(20px);
      }
      to {
        opacity: 1;
        transform: translateX(0);
      }
    `,
    pulse: `
      0%, 100% {
        opacity: 1;
        transform: scale(1);
      }
      50% {
        opacity: 0.8;
        transform: scale(1.02);
      }
    `,
    shimmer: `
      0% {
        background-position: -200px 0;
      }
      100% {
        background-position: calc(200px + 100%) 0;
      }
    `,
  },
};

/**
 * 精致组件主题
 */
export const components = {
  // 卡片组件
  card: {
    background:
      'linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(248, 250, 252, 0.95) 100%)',
    border: '1px solid rgba(226, 232, 240, 0.6)',
    borderRadius: borders.radius.xl,
    shadow: shadows.sm,
    padding: spacing[6],
    backdropFilter: 'blur(12px)',
    hoverShadow: shadows.cardHover,
    hoverBorder: '1px solid rgba(35, 146, 239, 0.3)',
    hoverBackground:
      'linear-gradient(135deg, rgba(255, 255, 255, 1) 0%, rgba(232, 244, 253, 0.9) 100%)',
  },

  // 按钮组件
  button: {
    primary: {
      background: `linear-gradient(135deg, ${colors.primary[400]} 0%, ${colors.primary[600]} 100%)`,
      color: colors.white,
      shadow: shadows.button,
      hoverBg: `linear-gradient(135deg, ${colors.primary[500]} 0%, ${colors.primary[700]} 100%)`,
      hoverShadow: shadows.buttonHover,
      activeBg: `linear-gradient(135deg, ${colors.primary[600]} 0%, ${colors.primary[800]} 100%)`,
      disabledBg: colors.gray[300],
      borderRadius: borders.radius.md,
    },
    secondary: {
      background: `linear-gradient(135deg, ${colors.white} 0%, ${colors.gray[50]} 100%)`,
      color: colors.gray[700],
      border: `1px solid ${colors.gray[200]}`,
      hoverBg: `linear-gradient(135deg, ${colors.primary[50]} 0%, ${colors.primary[100]} 100%)`,
      hoverBorder: `1px solid ${colors.primary[300]}`,
      hoverColor: colors.primary[700],
      activeBg: colors.primary[100],
      disabledBg: colors.gray[100],
      borderRadius: borders.radius.md,
    },
    danger: {
      background: colors.error.main,
      color: colors.white,
      hoverBg: colors.error.dark,
      activeBg: colors.error.dark,
      disabledBg: colors.error.light,
      borderRadius: borders.radius.md,
    },
  },

  // 输入框组件
  input: {
    background: colors.white,
    border: '1px solid rgba(195, 226, 251, 0.15)',
    borderRadius: borders.radius.md,
    shadow: shadows.sm,
    focusBorder: colors.primary[500],
    focusShadow: shadows.outline,
    padding: `${spacing[2]} ${spacing[3]}`,
    fontSize: fonts.size.base,
    transition: animations.transition.fast,
  },

  // 进度条组件
  progressBar: {
    background: colors.primary[100],
    fill: `linear-gradient(to right, ${colors.primary[500]}, ${colors.secondary[500]})`,
    height: spacing[2.5],
    borderRadius: borders.radius.full,
    shadow: shadows.xs,
  },

  // 三栏布局组件
  layout: {
    gap: '12px',
    padding: '20px',
    sidebarWidthSm: '180px',
    sidebarWidthLg: '220px',
    consoleWidthSm: '260px',
    consoleWidthLg: '320px',
  },
};

/**
 * 断点
 */
export const breakpoints = {
  sm: '640px',
  md: '768px',
  lg: '1024px',
  xl: '1280px',
  '2xl': '1536px',
};

/**
 * 导出默认主题
 */
const theme = {
  colors,
  fonts,
  spacing,
  borders,
  shadows,
  zIndices,
  animations,
  components,
  breakpoints,
};

export default theme;
