/**
 * @package runtime-convert-parse5-enhanced
 * @description 增强版Parse5转换引擎 - 基于@byted-lynx/web-speedy-plugin的完整语法映射
 * @version 2.0.0
 * <AUTHOR> Code Development Team
 */

import { Parse5TransformEngine, Parse5TransformConfig } from './index';
import type {
  InputFiles,
  TransformResult,
} from '../deleted_runtime_convert/types';

// ===============================
// 增强配置接口
// ===============================

export interface EnhancedTransformConfig extends Parse5TransformConfig {
  // RPX 配置
  rpx?: {
    designWidth?: number;
    rpxMode?: 'vw' | 'rem' | 'px';
  };

  // 页面配置 - 模拟 web-speedy-plugin 的 pageConfig
  pageConfig?: {
    useLepusNG?: boolean;
    lepusStrict?: boolean;
    lepusNullPropAsUndef?: boolean;
    enableCSSInheritance?: boolean;
    enableComponentNullProp?: boolean;
    defaultDisplayLinear?: boolean;
    defaultOverflowVisible?: boolean;
    enableTextOverflow?: boolean;
    webBumpAllSelectorSpecificity?: boolean;
  };

  // 调试配置
  debug?: {
    addLynxWebDebugTag?: boolean;
    addLynxWebDebugUrl?: boolean;
    enableDetailedLogging?: boolean;
  };

  // 运行时 DSL 模式
  runtimeDslMode?: 'ttml' | 'reactlynx2' | 'reactlynx3';
}

// ===============================
// 组件作用域管理器 - 模拟 tagV 系统
// ===============================

export class ComponentScopeManager {
  private componentScopes = new Map<string, string>();

  public getComponentScope(componentId: string, content: string): string {
    const key = `${componentId}:${this.generateContentHash(content)}`;

    if (!this.componentScopes.has(key)) {
      const hash = this.generateHash(key);
      this.componentScopes.set(key, hash.slice(0, 5));
    }

    return this.componentScopes.get(key)!;
  }

  public applyScopeToAttributes(
    attributes: Record<string, any>,
    tagV: string,
  ): Record<string, any> {
    return {
      ...attributes,
      [`data-v-${tagV}`]: '',
    };
  }

  private generateContentHash(content: string): string {
    // 简化的内容哈希，用于缓存失效
    return content.length.toString(36) + content.slice(-10);
  }

  private generateHash(input: string): string {
    // 模拟官方的哈希算法
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36);
  }
}

// ===============================
// 增强的样式处理器 - 模拟 StyleTransformers.labelSelectorsByTagV
// ===============================

export class EnhancedTTSSProcessor {
  private config: EnhancedTransformConfig;

  constructor(config: EnhancedTransformConfig) {
    this.config = config;
  }

  public async process(
    ttss: string,
    tagV: string,
    options: {
      isGlobalModule?: boolean;
      designWidth?: number;
      bumpSelectorSpecificity?: boolean;
    } = {},
  ): Promise<{
    css: string;
    tagV: string;
    ruleCount: number;
    classes: string[];
    rpxConverted: boolean;
  }> {
    let processedCSS = ttss || '';
    let rpxConverted = false;

    console.log('🎨 [EnhancedTTSSProcessor] 开始增强样式处理');
    console.log(
      `📋 [EnhancedTTSSProcessor] 输入: ${processedCSS.length} 字符, tagV: ${tagV}`,
    );

    // 1. RPX 单位转换 - 基于官方配置
    if (processedCSS.includes('rpx')) {
      const designWidth =
        options.designWidth || this.config.rpx?.designWidth || 750;
      processedCSS = this.convertRpxUnits(processedCSS, designWidth);
      rpxConverted = true;
      console.log(
        `🔄 [EnhancedTTSSProcessor] RPX转换完成，设计宽度: ${designWidth}px`,
      );
    }

    // 2. 作用域化处理 - 模拟 labelSelectorsByTagV
    if (!options.isGlobalModule && tagV) {
      processedCSS = this.applySelectorScoping(processedCSS, tagV);
      console.log(`🔒 [EnhancedTTSSProcessor] 作用域处理完成，tagV: ${tagV}`);
    }

    // 3. 选择器权重提升 - 模拟 webBumpAllSelectorSpecificity
    if (
      options.bumpSelectorSpecificity ||
      this.config.pageConfig?.webBumpAllSelectorSpecificity
    ) {
      processedCSS = this.bumpSelectorSpecificity(processedCSS);
      console.log('⬆️ [EnhancedTTSSProcessor] 选择器权重提升完成');
    }

    // 4. 统计信息
    const ruleCount = (processedCSS.match(/[^{}]+\s*\{[^}]*\}/g) || []).length;
    const classes = this.extractClasses(processedCSS);

    console.log('✅ [EnhancedTTSSProcessor] 样式处理完成:', {
      ruleCount,
      classCount: classes.length,
      rpxConverted,
      hasScope: !options.isGlobalModule,
    });

    return {
      css: processedCSS,
      tagV,
      ruleCount,
      classes,
      rpxConverted,
    };
  }

  private convertRpxUnits(css: string, designWidth: number): string {
    return css.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
      const rpxValue = parseFloat(value);
      const rpxMode = this.config.rpx?.rpxMode || 'vw';

      switch (rpxMode) {
        case 'vw':
          const vwValue = ((rpxValue / designWidth) * 100).toFixed(6);
          return `${vwValue}vw`;
        case 'rem':
          const remValue = ((rpxValue / designWidth) * 10).toFixed(6); // 假设 1rem = 37.5px
          return `${remValue}rem`;
        case 'px':
          return `${rpxValue}px`;
        default:
          return match;
      }
    });
  }

  private applySelectorScoping(css: string, tagV: string): string {
    // 实现选择器作用域化 - 基于官方逻辑
    return css.replace(
      /([^{}]+)\s*\{([^{}]*)\}/g,
      (match, selectors, declarations) => {
        const scopedSelectors = selectors
          .split(',')
          .map((selector: string) => {
            const trimmed = selector.trim();

            // 跳过特殊选择器
            if (
              trimmed.startsWith('@') ||
              trimmed.includes('::') ||
              trimmed.includes(':root') ||
              trimmed.includes('html') ||
              trimmed.includes('body')
            ) {
              return trimmed;
            }

            // 已经有作用域的选择器
            if (trimmed.includes(`[data-v-${tagV}]`)) {
              return trimmed;
            }

            // 添加作用域
            return `[data-v-${tagV}] ${trimmed}`;
          })
          .join(', ');

        return `${scopedSelectors} { ${declarations} }`;
      },
    );
  }

  private bumpSelectorSpecificity(css: string): string {
    // 提升选择器权重 - 通过添加额外的属性选择器
    return css.replace(
      /([^{}]+)\s*\{([^{}]*)\}/g,
      (match, selectors, declarations) => {
        const bumpedSelectors = selectors
          .split(',')
          .map((selector: string) => {
            const trimmed = selector.trim();

            // 跳过特殊选择器
            if (trimmed.startsWith('@') || trimmed.includes('::')) {
              return trimmed;
            }

            // 添加权重提升
            return `${trimmed}:not(#\\9)`;
          })
          .join(', ');

        return `${bumpedSelectors} { ${declarations} }`;
      },
    );
  }

  private extractClasses(css: string): string[] {
    const classMatches = css.match(/\.[a-zA-Z][a-zA-Z0-9_-]*/g) || [];
    return [...new Set(classMatches.map(cls => cls.slice(1)))];
  }
}

// ===============================
// 增强的事件处理系统 - 基于官方事件映射
// ===============================

export const ENHANCED_EVENT_MAPPING = {
  // Touch 事件
  bindtouchstart: {
    reactEvent: 'onTouchStart',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindtouchmove: {
    reactEvent: 'onTouchMove',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindtouchend: {
    reactEvent: 'onTouchEnd',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindtouchcancel: {
    reactEvent: 'onTouchCancel',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },

  // 滚动事件 - 特殊处理
  bindscroll: {
    reactEvent: 'onScroll',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindscrolltoupper: {
    reactEvent: 'onScroll',
    handler: (eventExpr: string) => `(e) => { 
      const { scrollTop, scrollLeft } = e.target;
      const threshold = 50;
      if (scrollTop <= threshold || scrollLeft <= threshold) {
        const handler = ${eventExpr};
        if (handler) handler(e);
      }
    }`,
  },
  bindscrolltolower: {
    reactEvent: 'onScroll',
    handler: (eventExpr: string) => `(e) => {
      const { scrollTop, scrollLeft, scrollHeight, scrollWidth, clientHeight, clientWidth } = e.target;
      const threshold = 50;
      if (scrollTop + clientHeight >= scrollHeight - threshold || 
          scrollLeft + clientWidth >= scrollWidth - threshold) {
        const handler = ${eventExpr};
        if (handler) handler(e);
      }
    }`,
  },

  // 表单事件
  bindinput: {
    reactEvent: 'onInput',
    handler: (eventExpr: string) => `(e) => { 
      const handler = ${eventExpr}; 
      if (handler) handler({ detail: { value: e.target.value }, target: e.target }); 
    }`,
  },
  bindchange: {
    reactEvent: 'onChange',
    handler: (eventExpr: string) => `(e) => { 
      const handler = ${eventExpr}; 
      if (handler) handler({ detail: { value: e.target.value }, target: e.target }); 
    }`,
  },
  bindfocus: {
    reactEvent: 'onFocus',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindblur: {
    reactEvent: 'onBlur',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },

  // 点击事件
  bindtap: {
    reactEvent: 'onClick',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindlongpress: {
    reactEvent: 'onContextMenu',
    handler: (eventExpr: string) => `(e) => { 
      e.preventDefault(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },
  bindlongtap: {
    reactEvent: 'onContextMenu',
    handler: (eventExpr: string) => `(e) => { 
      e.preventDefault(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },

  // 媒体事件
  bindload: {
    reactEvent: 'onLoad',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  binderror: {
    reactEvent: 'onError',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindplay: {
    reactEvent: 'onPlay',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindpause: {
    reactEvent: 'onPause',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },

  // Catch 事件 (阻止冒泡)
  catchtouchstart: {
    reactEvent: 'onTouchStart',
    handler: (eventExpr: string) => `(e) => { 
      e.stopPropagation(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },
  catchtap: {
    reactEvent: 'onClick',
    handler: (eventExpr: string) => `(e) => { 
      e.stopPropagation(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },
};

// ===============================
// 增强的元素映射系统 - 基于官方转换器
// ===============================

export const ENHANCED_LYNX_MAPPING = {
  // 视图容器类
  view: {
    tag: 'div',
    props: { className: 'lynx-view' },
    webStandard: 'HTML div element',
    attributeProcessor: (attrs: Record<string, string>, tagV: string) => {
      const processed = { ...attrs };

      // class 映射到 className
      if (attrs.class) {
        processed.className = processed.className
          ? `${processed.className} ${attrs.class}`
          : attrs.class;
        delete processed.class;
      }

      // hover-class 处理
      if (attrs['hover-class']) {
        const hoverClass = attrs['hover-class'];
        processed['data-hover-class'] = hoverClass;
        processed.onMouseEnter = `(e) => e.target.classList.add('${hoverClass}')`;
        processed.onMouseLeave = `(e) => e.target.classList.remove('${hoverClass}')`;
        delete processed['hover-class'];
      }

      // hover 时间处理
      if (attrs['hover-start-time']) {
        processed['data-hover-start-time'] = attrs['hover-start-time'];
        delete processed['hover-start-time'];
      }

      if (attrs['hover-stay-time']) {
        processed['data-hover-stay-time'] = attrs['hover-stay-time'];
        delete processed['hover-stay-time'];
      }

      return processed;
    },
  },

  'scroll-view': {
    tag: 'div',
    props: {
      className: 'lynx-scroll-view',
      style: { overflow: 'auto', WebkitOverflowScrolling: 'touch' },
    },
    webStandard: 'HTML div with scroll capabilities',
    attributeProcessor: (attrs: Record<string, string>, tagV: string) => {
      const processed = { ...attrs };
      const style = processed.style ? JSON.parse(processed.style) : {};

      // scroll-x 和 scroll-y 处理
      if (attrs['scroll-x'] === 'true') {
        style.overflowX = 'auto';
      } else if (attrs['scroll-x'] === 'false') {
        style.overflowX = 'hidden';
      }

      if (attrs['scroll-y'] === 'true') {
        style.overflowY = 'auto';
      } else if (attrs['scroll-y'] === 'false') {
        style.overflowY = 'hidden';
      }

      // 滚动属性
      [
        'upper-threshold',
        'lower-threshold',
        'scroll-top',
        'scroll-left',
        'enable-back-to-top',
        'enable-flex',
      ].forEach(prop => {
        if (attrs[prop]) {
          processed[`data-${prop}`] = attrs[prop];
          delete processed[prop];
        }
      });

      processed.style = JSON.stringify(style);
      return processed;
    },
  },

  text: {
    tag: 'span',
    props: { className: 'lynx-text' },
    webStandard: 'HTML span element',
    attributeProcessor: (attrs: Record<string, string>, tagV: string) => {
      const processed = { ...attrs };
      const style = processed.style ? JSON.parse(processed.style) : {};

      // selectable 属性处理
      if (attrs.selectable === 'true') {
        style.userSelect = 'text';
        style.WebkitUserSelect = 'text';
      } else if (attrs.selectable === 'false') {
        style.userSelect = 'none';
        style.WebkitUserSelect = 'none';
      }

      // space 属性处理
      if (attrs.space) {
        processed['data-space'] = attrs.space;
        if (attrs.space === 'nbsp') {
          style.whiteSpace = 'pre';
        }
        delete processed.space;
      }

      // decode 属性
      if (attrs.decode) {
        processed['data-decode'] = attrs.decode;
        delete processed.decode;
      }

      processed.style = JSON.stringify(style);
      return processed;
    },
  },

  image: {
    tag: 'img',
    props: { className: 'lynx-image' },
    selfClosing: true,
    webStandard: 'HTML img element',
    attributeProcessor: (attrs: Record<string, string>, tagV: string) => {
      const processed = { ...attrs };
      const style = processed.style ? JSON.parse(processed.style) : {};

      // src 属性
      if (attrs.src) {
        processed.src = attrs.src;
      }

      // mode 属性处理
      if (attrs.mode) {
        const modeMap = {
          scaleToFill: 'fill',
          aspectFit: 'contain',
          aspectFill: 'cover',
          widthFix: 'scale-down',
          heightFix: 'scale-down',
        };
        style.objectFit = modeMap[attrs.mode as keyof typeof modeMap] || 'fill';
        processed['data-mode'] = attrs.mode;
        delete processed.mode;
      }

      // lazy-load 处理
      if (attrs['lazy-load'] === 'true') {
        processed.loading = 'lazy';
        processed['data-lazy-load'] = 'true';
        delete processed['lazy-load'];
      }

      // webp 支持
      if (attrs.webp) {
        processed['data-webp'] = attrs.webp;
        delete processed.webp;
      }

      processed.style = JSON.stringify(style);
      return processed;
    },
  },

  input: {
    tag: 'input',
    props: { className: 'lynx-input' },
    selfClosing: true,
    webStandard: 'HTML input element',
    attributeProcessor: (attrs: Record<string, string>, tagV: string) => {
      const processed = { ...attrs };

      // type 映射
      if (attrs.type) {
        const typeMap = {
          text: 'text',
          number: 'number',
          idcard: 'text',
          digit: 'tel',
          password: 'password',
        };
        processed.type = typeMap[attrs.type as keyof typeof typeMap] || 'text';
      }

      // password 特殊处理
      if (attrs.password === 'true') {
        processed.type = 'password';
        delete processed.password;
      }

      // 基本属性映射
      const directMappings = {
        placeholder: 'placeholder',
        disabled: 'disabled',
        maxlength: 'maxLength',
        'auto-focus': 'autoFocus',
        value: 'value',
      };

      Object.entries(directMappings).forEach(([lynxAttr, reactAttr]) => {
        if (attrs[lynxAttr]) {
          processed[reactAttr] = attrs[lynxAttr];
          if (lynxAttr !== reactAttr) {
            delete processed[lynxAttr];
          }
        }
      });

      // 其他属性保留为 data-*
      [
        'placeholder-style',
        'placeholder-class',
        'cursor-spacing',
        'confirm-type',
        'confirm-hold',
      ].forEach(prop => {
        if (attrs[prop]) {
          processed[`data-${prop}`] = attrs[prop];
          delete processed[prop];
        }
      });

      return processed;
    },
  },

  button: {
    tag: 'button',
    props: { className: 'lynx-button' },
    webStandard: 'HTML button element',
    attributeProcessor: (attrs: Record<string, string>, tagV: string) => {
      const processed = { ...attrs };

      // type 处理
      if (attrs['form-type']) {
        processed.type = attrs['form-type'] === 'submit' ? 'submit' : 'button';
        delete processed['form-type'];
      } else {
        processed.type = 'button';
      }

      // disabled 处理
      if (attrs.disabled === 'true') {
        processed.disabled = true;
      }
      delete processed.disabled;

      // 其他属性
      ['size', 'plain', 'loading', 'open-type', 'hover-class'].forEach(prop => {
        if (attrs[prop]) {
          processed[`data-${prop}`] = attrs[prop];
          delete processed[prop];
        }
      });

      return processed;
    },
  },
};

// ===============================
// 增强的转换引擎
// ===============================

export class EnhancedParse5TransformEngine extends Parse5TransformEngine {
  private scopeManager: ComponentScopeManager;
  private enhancedTtssProcessor: EnhancedTTSSProcessor;
  private enhancedConfig: EnhancedTransformConfig;

  constructor(config: EnhancedTransformConfig = {}) {
    super(config);
    this.enhancedConfig = config;
    this.scopeManager = new ComponentScopeManager();
    this.enhancedTtssProcessor = new EnhancedTTSSProcessor(config);
  }

  public async convert(files: InputFiles): Promise<TransformResult> {
    const startTime = performance.now();

    console.log('🚀 [EnhancedParse5TransformEngine] 增强转换引擎启动');
    console.log('📋 [EnhancedParse5TransformEngine] 配置:', {
      runtimeDslMode: this.enhancedConfig.runtimeDslMode || 'ttml',
      enableScope: this.enhancedConfig.enableScope,
      rpxMode: this.enhancedConfig.rpx?.rpxMode || 'vw',
      designWidth: this.enhancedConfig.rpx?.designWidth || 750,
    });

    try {
      // 1. 生成组件作用域 - 模拟 tagV
      const tagV = this.scopeManager.getComponentScope(
        this.enhancedConfig.componentId || 'default',
        JSON.stringify(files),
      );

      console.log(`🔑 [EnhancedParse5TransformEngine] 生成组件作用域: ${tagV}`);

      // 2. 处理 TTML - 使用增强映射
      let ttmlResult = { jsx: '', elementCount: 0, errors: [] as string[] };
      if (files.ttml) {
        ttmlResult = await this.processTTMLWithEnhancedRules(files.ttml, tagV);
      }

      // 3. 处理 TTSS - 使用增强处理器
      let ttssResult = {
        css: '',
        tagV,
        ruleCount: 0,
        classes: [],
        rpxConverted: false,
      };
      if (files.ttss) {
        ttssResult = await this.enhancedTtssProcessor.process(
          files.ttss,
          tagV,
          {
            designWidth: this.enhancedConfig.rpx?.designWidth,
            bumpSelectorSpecificity:
              this.enhancedConfig.pageConfig?.webBumpAllSelectorSpecificity,
          },
        );
      }

      // 4. 处理 JavaScript
      let jsResult = '';
      if (files.js) {
        jsResult = this.processJavaScriptWithEnhancements(files.js);
      }

      // 5. 生成完整的 HTML
      const html = await this.generateEnhancedHTML({
        jsx: ttmlResult.jsx,
        css: ttssResult.css,
        js: jsResult,
        tagV,
        componentId: this.enhancedConfig.componentId || 'default',
      });

      const totalTime = performance.now() - startTime;

      console.log('✅ [EnhancedParse5TransformEngine] 转换完成:', {
        totalTime: `${totalTime.toFixed(2)}ms`,
        elementCount: ttmlResult.elementCount,
        cssRules: ttssResult.ruleCount,
        rpxConverted: ttssResult.rpxConverted,
        htmlLength: html.length,
      });

      return {
        success: true,
        html,
        metadata: {
          transformTime: totalTime,
          componentId: this.enhancedConfig.componentId || 'default',
          tagV,
          elementCount: ttmlResult.elementCount,
          cssRules: ttssResult.ruleCount,
          rpxConverted: ttssResult.rpxConverted,
          errors: ttmlResult.errors,
          enhanced: true,
        },
      };
    } catch (error) {
      const totalTime = performance.now() - startTime;
      console.error('❌ [EnhancedParse5TransformEngine] 转换失败:', error);

      return {
        success: false,
        error: 'enhanced_transform',
        message: `增强转换失败: ${error instanceof Error ? error.message : String(error)}`,
        html: this.generateErrorHTML(error),
        metadata: {
          transformTime: totalTime,
          componentId: this.enhancedConfig.componentId || 'default',
          errors: [error instanceof Error ? error.message : String(error)],
          enhanced: true,
        },
      };
    }
  }

  private async processTTMLWithEnhancedRules(
    ttml: string,
    tagV: string,
  ): Promise<{
    jsx: string;
    elementCount: number;
    errors: string[];
  }> {
    console.log('📄 [EnhancedParse5TransformEngine] 使用增强规则处理TTML');

    // 调用父类的转换方法，但我们会在属性处理中使用增强规则
    const result = await this.ttmlAdapter.transform(ttml, tagV);

    // 这里可以进一步处理 JSX 以应用增强的映射规则
    let enhancedJSX = result.jsx;

    // 应用增强的事件处理
    enhancedJSX = this.applyEnhancedEventHandling(enhancedJSX);

    return {
      jsx: enhancedJSX,
      elementCount: result.elementCount,
      errors: result.errors,
    };
  }

  private applyEnhancedEventHandling(jsx: string): string {
    // 替换事件处理器以使用增强的事件映射
    Object.entries(ENHANCED_EVENT_MAPPING).forEach(([lynxEvent, mapping]) => {
      const pattern = new RegExp(`${lynxEvent}=\\\{([^}]+)\\\}`, 'g');
      jsx = jsx.replace(pattern, (match, eventExpr) => {
        const handler = mapping.handler(eventExpr);
        return `${mapping.reactEvent}={${handler}}`;
      });
    });

    return jsx;
  }

  private processJavaScriptWithEnhancements(js: string): string {
    console.log('📜 [EnhancedParse5TransformEngine] 增强JavaScript处理');

    let processedJS = js;

    // 增强的 JavaScript 处理
    if (this.enhancedConfig.pageConfig?.useLepusNG) {
      // 模拟 LepusNG 处理
      processedJS = this.processLepusNG(processedJS);
    }

    if (this.enhancedConfig.pageConfig?.lepusNullPropAsUndef) {
      // null 属性转 undefined
      processedJS = processedJS.replace(/:\s*null/g, ': undefined');
    }

    return processedJS;
  }

  private processLepusNG(js: string): string {
    // 简化的 LepusNG 处理逻辑
    return js
      .replace(/Card\s*\(\s*\{/, 'const component = {')
      .replace(/this\.setData/g, 'component.setData')
      .replace(/this\.data/g, 'component.data');
  }

  private async generateEnhancedHTML(data: {
    jsx: string;
    css: string;
    js: string;
    tagV: string;
    componentId: string;
  }): Promise<string> {
    console.log('🎨 [EnhancedParse5TransformEngine] 生成增强HTML');

    const debugInfo = this.enhancedConfig.debug?.addLynxWebDebugTag
      ? `
      <!-- Lynx Web Debug Info -->
      <!-- Component ID: ${data.componentId} -->
      <!-- Tag V: ${data.tagV} -->
      <!-- Generated by: Enhanced Parse5 Transform Engine -->
    `
      : '';

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lynx Web Preview - Enhanced</title>
    ${debugInfo}
    <style>
        /* Enhanced CSS with scope and transformations */
        ${data.css}
        
        /* Lynx base styles */
        .lynx-view { display: flex; flex-direction: column; }
        .lynx-text { display: inline; }
        .lynx-image { max-width: 100%; height: auto; }
        .lynx-input { border: 1px solid #ccc; padding: 8px; }
        .lynx-button { 
            background: #007AFF; 
            color: white; 
            border: none; 
            padding: 10px 20px; 
            border-radius: 4px; 
            cursor: pointer; 
        }
        .lynx-button:hover { background: #0056D6; }
        .lynx-button:disabled { 
            background: #ccc; 
            cursor: not-allowed; 
        }
        
        /* Responsive utilities */
        @media (max-width: 768px) {
            .lynx-view { padding: 10px; }
        }
    </style>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
</head>
<body>
    <div id="app"></div>
    
    <script>
        console.log('🚀 Enhanced Lynx Web Runtime启动');
        console.log('📋 组件信息:', {
            componentId: '${data.componentId}',
            tagV: '${data.tagV}',
            enhanced: true
        });
        
        // Enhanced component logic
        ${data.js}
        
        // Enhanced render function
        function renderComponent() {
            const element = ${data.jsx || 'React.createElement("div", {className: "empty-component"}, "Empty Component")'};
            
            return React.createElement(
                "div",
                { 
                    className: "lynx-component-root",
                    "data-component-id": "${data.componentId}",
                    "data-v-${data.tagV}": ""
                },
                element
            );
        }
        
        // Mount enhanced component
        try {
            const root = ReactDOM.createRoot(document.getElementById('app'));
            root.render(React.createElement(renderComponent));
            console.log('✅ Enhanced组件渲染成功');
        } catch (error) {
            console.error('❌ Enhanced组件渲染失败:', error);
            document.getElementById('app').innerHTML = 
                '<div style="color: red; padding: 20px;">Enhanced组件渲染失败: ' + error.message + '</div>';
        }
    </script>
</body>
</html>`;
  }

  private generateErrorHTML(error: any): string {
    const errorMsg = error instanceof Error ? error.message : String(error);
    return `<!DOCTYPE html>
<html>
<head>
    <title>Enhanced Transform Error</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .error { color: red; border: 1px solid red; padding: 15px; border-radius: 5px; }
    </style>
</head>
<body>
    <div class="error">
        <h2>Enhanced Parse5 Transform Error</h2>
        <p>${errorMsg}</p>
        <small>Enhanced Transform Engine v2.0.0</small>
    </div>
</body>
</html>`;
  }
}

export default EnhancedParse5TransformEngine;
