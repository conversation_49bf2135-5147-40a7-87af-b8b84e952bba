/**
 * Lynx Preview Manager - Lynx在线预览管理器
 * 管理Web Worker、预览生成和iframe渲染
 *
 * 核心功能:
 * - Web Worker生命周期管理
 * - 实时预览生成
 * - iframe预览渲染
 * - 错误处理和重试机制
 * - 性能监控和统计
 */

import type { InputFiles } from '../deleted_runtime_convert/types';
import type {
  WorkerMessage,
  WorkerResponse,
  LynxConvertRequest,
  LynxConvertResponse,
} from './enhanced-lynx-preview-worker';

// 预览配置
interface PreviewConfig {
  workerUrl?: string;
  enableDebug?: boolean;
  previewMode?: 'iframe' | 'component';
  autoRetry?: boolean;
  maxRetries?: number;
  timeout?: number;
}

// 预览结果
interface PreviewResult {
  success: boolean;
  html: string;
  webCode?: {
    html: string;
    css: string;
    js: string;
  };
  metadata?: any;
  error?: string;
  retryCount?: number;
}

// 预览事件
type PreviewEventType = 'progress' | 'success' | 'error' | 'retry' | 'timeout';

interface PreviewEvent {
  type: PreviewEventType;
  data?: any;
  timestamp: number;
}

type PreviewEventHandler = (event: PreviewEvent) => void;

/**
 * Lynx Preview Manager Class
 * 管理Lynx预览的完整生命周期
 */
export class LynxPreviewManager {
  private worker: Worker | null = null;
  private isWorkerReady: boolean = false;
  private messageId: number = 0;
  private pendingRequests: Map<
    string,
    {
      resolve: (value: PreviewResult) => void;
      reject: (reason: any) => void;
      timeout: NodeJS.Timeout;
      retryCount: number;
    }
  > = new Map();

  private config: Required<PreviewConfig>;
  private eventHandlers: Map<PreviewEventType, PreviewEventHandler[]> =
    new Map();
  private stats: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageTime: number;
    workerRestarts: number;
  } = {
    totalRequests: 0,
    successfulRequests: 0,
    failedRequests: 0,
    averageTime: 0,
    workerRestarts: 0,
  };

  constructor(config: PreviewConfig = {}) {
    this.config = {
      workerUrl:
        config.workerUrl ||
        '/src/routes/batch_processor/runtime_convert_parse5/enhanced-lynx-preview-worker.ts',
      enableDebug: config.enableDebug || false,
      previewMode: config.previewMode || 'iframe',
      autoRetry: config.autoRetry !== false,
      maxRetries: config.maxRetries || 3,
      timeout: config.timeout || 30000,
    };

    this.log('info', '🚀 Lynx Preview Manager 初始化中...', this.config);
    this.initializeWorker();
  }

  /**
   * 初始化Web Worker
   */
  private async initializeWorker(): Promise<void> {
    try {
      this.log('info', '🔄 初始化Web Worker...');

      // 创建Worker
      this.worker = new Worker(this.config.workerUrl, { type: 'module' });

      // 设置Worker事件监听
      this.worker.addEventListener(
        'message',
        this.handleWorkerMessage.bind(this),
      );
      this.worker.addEventListener('error', this.handleWorkerError.bind(this));

      // 等待Worker准备就绪
      await this.waitForWorkerReady();

      this.isWorkerReady = true;
      this.log('success', '✅ Web Worker 初始化成功');
    } catch (error) {
      this.log('error', '❌ Web Worker 初始化失败:', error);
      this.isWorkerReady = false;
      throw error;
    }
  }

  /**
   * 等待Worker准备就绪
   */
  private waitForWorkerReady(): Promise<void> {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Worker初始化超时'));
      }, 5000);

      const checkStatus = async () => {
        try {
          await this.getWorkerStatus();
          clearTimeout(timeout);
          resolve();
        } catch (error) {
          // Worker还未准备好，继续等待
          setTimeout(checkStatus, 100);
        }
      };

      checkStatus();
    });
  }

  /**
   * 获取Worker状态
   */
  private async getWorkerStatus(): Promise<any> {
    if (!this.worker) {
      throw new Error('Worker未初始化');
    }

    return new Promise((resolve, reject) => {
      const messageId = this.generateMessageId();
      const timeout = setTimeout(() => {
        reject(new Error('获取Worker状态超时'));
      }, 5000);

      const handleResponse = (event: MessageEvent<WorkerResponse>) => {
        if (event.data.id === messageId) {
          clearTimeout(timeout);
          this.worker?.removeEventListener('message', handleResponse);

          if (event.data.type === 'STATUS') {
            resolve(event.data.payload);
          } else {
            reject(new Error(event.data.error || '获取状态失败'));
          }
        }
      };

      this.worker.addEventListener('message', handleResponse);
      this.worker.postMessage({
        id: messageId,
        type: 'GET_STATUS',
      } as WorkerMessage);
    });
  }

  /**
   * 转换Lynx代码为Web预览
   */
  async convertToPreview(files: InputFiles): Promise<PreviewResult> {
    const startTime = performance.now();
    this.stats.totalRequests++;

    this.log('info', '🔄 开始Lynx预览转换...', {
      fileTypes: Object.keys(files),
      previewMode: this.config.previewMode,
    });

    this.emitEvent('progress', { stage: 'starting', files });

    try {
      // 确保Worker就绪
      if (!this.isWorkerReady) {
        await this.initializeWorker();
      }

      // 执行转换
      const result = await this.performConversion(files, 0);

      // 更新统计信息
      const conversionTime = performance.now() - startTime;
      this.updateStats(conversionTime, true);

      this.emitEvent('success', { result, conversionTime });
      this.log('success', '✅ Lynx预览转换成功', {
        htmlLength: result.html.length,
        conversionTime: `${conversionTime.toFixed(2)}ms`,
      });

      return result;
    } catch (error) {
      const conversionTime = performance.now() - startTime;
      this.updateStats(conversionTime, false);

      this.emitEvent('error', { error, conversionTime });
      this.log('error', '❌ Lynx预览转换失败:', error);

      // 返回错误结果
      return {
        success: false,
        html: this.generateErrorHtml(files, error),
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          conversionTime,
          stats: this.getStats(),
        },
      };
    }
  }

  /**
   * 执行转换（支持重试）
   */
  private async performConversion(
    files: InputFiles,
    retryCount: number,
  ): Promise<PreviewResult> {
    if (!this.worker) {
      throw new Error('Worker未就绪');
    }

    return new Promise((resolve, reject) => {
      const messageId = this.generateMessageId();

      // 设置超时
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(messageId);

        if (this.config.autoRetry && retryCount < this.config.maxRetries) {
          this.log(
            'warn',
            `⏰ 转换超时，准备重试 (${retryCount + 1}/${this.config.maxRetries})`,
          );
          this.emitEvent('retry', {
            retryCount: retryCount + 1,
            reason: 'timeout',
          });

          // 重启Worker并重试
          this.restartWorker()
            .then(() => {
              this.performConversion(files, retryCount + 1)
                .then(resolve)
                .catch(reject);
            })
            .catch(reject);
        } else {
          this.emitEvent('timeout', { retryCount });
          reject(new Error('转换超时'));
        }
      }, this.config.timeout);

      // 存储待处理请求
      this.pendingRequests.set(messageId, {
        resolve: (result: PreviewResult) => {
          clearTimeout(timeout);
          result.retryCount = retryCount;
          resolve(result);
        },
        reject: (error: any) => {
          clearTimeout(timeout);

          if (this.config.autoRetry && retryCount < this.config.maxRetries) {
            this.log(
              'warn',
              `🔄 转换失败，准备重试 (${retryCount + 1}/${this.config.maxRetries}):`,
              error,
            );
            this.emitEvent('retry', {
              retryCount: retryCount + 1,
              reason: 'error',
              error,
            });

            setTimeout(
              () => {
                this.performConversion(files, retryCount + 1)
                  .then(resolve)
                  .catch(reject);
              },
              1000 * (retryCount + 1),
            ); // 递增延迟
          } else {
            reject(error);
          }
        },
        timeout,
        retryCount,
      });

      // 发送转换请求
      const request: LynxConvertRequest = {
        files,
        config: {
          enableDirectHTML: true,
          useSimplifiedMode: this.config.previewMode === 'iframe',
          workerMode: true,
        },
        previewMode: this.config.previewMode,
        enableDebug: this.config.enableDebug,
      };

      this.worker.postMessage({
        id: messageId,
        type: 'CONVERT_LYNX',
        payload: request,
      } as WorkerMessage);

      this.log('debug', '📤 发送转换请求', { messageId, request });
    });
  }

  /**
   * 处理Worker消息
   */
  private handleWorkerMessage(event: MessageEvent<WorkerResponse>): void {
    const { id, type, payload, error } = event.data;

    this.log('debug', '📥 收到Worker消息', { id, type });

    const pendingRequest = this.pendingRequests.get(id);
    if (!pendingRequest) {
      this.log('warn', '⚠️ 收到未知请求的响应:', id);
      return;
    }

    this.pendingRequests.delete(id);

    if (type === 'SUCCESS') {
      const result = payload as LynxConvertResponse;
      pendingRequest.resolve({
        success: result.success,
        html: result.html,
        webCode: result.webCode,
        metadata: result.metadata,
        error: result.error,
      });
    } else if (type === 'ERROR') {
      pendingRequest.reject(new Error(error || '未知Worker错误'));
    }
  }

  /**
   * 处理Worker错误
   */
  private handleWorkerError(error: ErrorEvent): void {
    this.log('error', '🚨 Worker发生错误:', error);

    // 清理所有待处理请求
    this.pendingRequests.forEach(request => {
      request.reject(new Error('Worker错误: ' + error.message));
    });
    this.pendingRequests.clear();

    // 标记Worker为未就绪
    this.isWorkerReady = false;
  }

  /**
   * 重启Worker
   */
  private async restartWorker(): Promise<void> {
    this.log('info', '🔄 重启Worker...');
    this.stats.workerRestarts++;

    // 终止现有Worker
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    this.isWorkerReady = false;

    // 重新初始化
    await this.initializeWorker();
  }

  /**
   * 渲染预览到iframe
   */
  renderToIframe(iframe: HTMLIFrameElement, html: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!iframe) {
        reject(new Error('无效的iframe元素'));
        return;
      }

      this.log('info', '🖼️ 渲染预览到iframe...');

      // 设置加载完成监听
      const handleLoad = () => {
        iframe.removeEventListener('load', handleLoad);
        iframe.removeEventListener('error', handleError);
        this.log('success', '✅ iframe加载完成');
        resolve();
      };

      const handleError = (error: Event) => {
        iframe.removeEventListener('load', handleLoad);
        iframe.removeEventListener('error', handleError);
        this.log('error', '❌ iframe加载失败:', error);
        reject(new Error('iframe加载失败'));
      };

      iframe.addEventListener('load', handleLoad);
      iframe.addEventListener('error', handleError);

      // 设置iframe内容
      try {
        iframe.srcdoc = html;
      } catch (error) {
        iframe.removeEventListener('load', handleLoad);
        iframe.removeEventListener('error', handleError);
        reject(error);
      }
    });
  }

  /**
   * 生成错误HTML
   */
  private generateErrorHtml(files: InputFiles, error: any): string {
    const errorMessage = error instanceof Error ? error.message : String(error);

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>预览生成失败</title>
  <style>
    body { 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
      margin: 20px; 
      background: #f5f5f5; 
    }
    .error-container { 
      background: white; 
      padding: 24px; 
      border-radius: 8px; 
      box-shadow: 0 2px 8px rgba(0,0,0,0.1); 
      max-width: 600px; 
      margin: 0 auto; 
    }
    .error-title { 
      color: #e74c3c; 
      margin-bottom: 16px; 
      font-size: 20px; 
      font-weight: 600; 
    }
    .error-message { 
      background: #f8f9fa; 
      padding: 16px; 
      border-radius: 4px; 
      font-family: monospace; 
      white-space: pre-wrap; 
      border-left: 4px solid #e74c3c; 
      margin-bottom: 20px; 
    }
    .retry-button { 
      background: #3498db; 
      color: white; 
      border: none; 
      padding: 10px 20px; 
      border-radius: 4px; 
      cursor: pointer; 
      font-size: 14px; 
    }
    .retry-button:hover { 
      background: #2980b9; 
    }
  </style>
</head>
<body>
  <div class="error-container">
    <div class="error-title">⚠️ 预览生成失败</div>
    <div class="error-message">${errorMessage}</div>
    <button class="retry-button" onclick="window.parent.postMessage({type: 'RETRY_PREVIEW'}, '*')">
      🔄 重试
    </button>
  </div>
  
  <script>
    console.error('Preview generation failed:', ${JSON.stringify(errorMessage)});
    window.parent.postMessage({type: 'PREVIEW_ERROR', error: ${JSON.stringify(errorMessage)}}, '*');
  </script>
</body>
</html>`;
  }

  /**
   * 事件管理
   */
  on(eventType: PreviewEventType, handler: PreviewEventHandler): void {
    if (!this.eventHandlers.has(eventType)) {
      this.eventHandlers.set(eventType, []);
    }
    this.eventHandlers.get(eventType)!.push(handler);
  }

  off(eventType: PreviewEventType, handler: PreviewEventHandler): void {
    const handlers = this.eventHandlers.get(eventType);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  private emitEvent(type: PreviewEventType, data?: any): void {
    const event: PreviewEvent = {
      type,
      data,
      timestamp: Date.now(),
    };

    const handlers = this.eventHandlers.get(type) || [];
    handlers.forEach(handler => {
      try {
        handler(event);
      } catch (error) {
        this.log('error', '🚨 事件处理器错误:', error);
      }
    });
  }

  /**
   * 统计信息管理
   */
  private updateStats(conversionTime: number, success: boolean): void {
    if (success) {
      this.stats.successfulRequests++;
    } else {
      this.stats.failedRequests++;
    }

    this.stats.averageTime =
      (this.stats.averageTime * (this.stats.totalRequests - 1) +
        conversionTime) /
      this.stats.totalRequests;
  }

  getStats(): any {
    return {
      ...this.stats,
      successRate:
        this.stats.totalRequests > 0
          ? (this.stats.successfulRequests / this.stats.totalRequests) * 100
          : 0,
      isWorkerReady: this.isWorkerReady,
      pendingRequests: this.pendingRequests.size,
    };
  }

  /**
   * 工具方法
   */
  private generateMessageId(): string {
    return `msg_${++this.messageId}_${Date.now()}`;
  }

  private log(level: string, message: string, data?: any): void {
    if (!this.config.enableDebug && level === 'debug') {
      return;
    }

    try {
      const timestamp = new Date().toISOString();
      const prefix = `[LynxPreviewManager][${timestamp}]`;

      if (data) {
        console.log(`${prefix} ${message}`, data);
      } else {
        console.log(`${prefix} ${message}`);
      }
    } catch (error) {
      console.log(message, data || '');
    }
  }

  /**
   * 清理资源
   */
  dispose(): void {
    this.log('info', '🗑️ 清理Lynx Preview Manager...');

    // 清理待处理请求
    this.pendingRequests.forEach(request => {
      clearTimeout(request.timeout);
      request.reject(new Error('Manager已销毁'));
    });
    this.pendingRequests.clear();

    // 终止Worker
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }

    // 清理事件处理器
    this.eventHandlers.clear();

    this.isWorkerReady = false;
    this.log('success', '✅ Lynx Preview Manager 清理完成');
  }
}

// 导出类型
export type {
  PreviewConfig,
  PreviewResult,
  PreviewEvent,
  PreviewEventType,
  PreviewEventHandler,
};
