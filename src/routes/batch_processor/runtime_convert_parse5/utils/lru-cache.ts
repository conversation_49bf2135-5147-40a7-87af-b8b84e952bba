/**
 * @package runtime-convert-parse5
 * @description LRU缓存实现 - 为转换结果提供高效缓存
 */

interface CacheItem<T> {
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccess: number;
}

/**
 * LRU缓存实现
 */
export class LRUCache<K, V> {
  private cache = new Map<K, CacheItem<V>>();
  private accessOrder: K[] = [];
  private maxSize: number;
  private ttl: number; // Time to live in milliseconds

  constructor(maxSize = 100, ttl: number = 30 * 60 * 1000) {
    // 30分钟默认TTL
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  /**
   * 获取缓存值
   */
  get(key: K): V | null {
    const item = this.cache.get(key);

    if (!item) {
      return null;
    }

    // 检查TTL
    const now = Date.now();
    if (now - item.timestamp > this.ttl) {
      this.delete(key);
      return null;
    }

    // 更新访问信息
    item.accessCount++;
    item.lastAccess = now;

    // 更新访问顺序
    this.updateAccessOrder(key);

    return item.value;
  }

  /**
   * 设置缓存值
   */
  set(key: K, value: V): void {
    const now = Date.now();

    // 如果已存在，直接更新
    if (this.cache.has(key)) {
      const item = this.cache.get(key)!;
      item.value = value;
      item.timestamp = now;
      item.lastAccess = now;
      item.accessCount++;
      this.updateAccessOrder(key);
      return;
    }

    // 如果缓存已满，删除最少使用的项
    if (this.cache.size >= this.maxSize) {
      this.evictLRU();
    }

    // 添加新项
    this.cache.set(key, {
      value,
      timestamp: now,
      accessCount: 1,
      lastAccess: now,
    });

    this.accessOrder.push(key);
  }

  /**
   * 删除缓存项
   */
  delete(key: K): boolean {
    const existed = this.cache.delete(key);
    if (existed) {
      const index = this.accessOrder.indexOf(key);
      if (index !== -1) {
        this.accessOrder.splice(index, 1);
      }
    }
    return existed;
  }

  /**
   * 检查是否存在
   */
  has(key: K): boolean {
    const item = this.cache.get(key);
    if (!item) {
      return false;
    }

    // 检查TTL
    const now = Date.now();
    if (now - item.timestamp > this.ttl) {
      this.delete(key);
      return false;
    }

    return true;
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear();
    this.accessOrder = [];
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size;
  }

  /**
   * 清理过期项
   */
  cleanup(): number {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.cache.entries()) {
      if (now - item.timestamp > this.ttl) {
        this.delete(key);
        cleanedCount++;
      }
    }

    return cleanedCount;
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const now = Date.now();
    let totalAccessCount = 0;
    let oldestItem = now;
    let newestItem = 0;

    for (const item of this.cache.values()) {
      totalAccessCount += item.accessCount;
      oldestItem = Math.min(oldestItem, item.timestamp);
      newestItem = Math.max(newestItem, item.timestamp);
    }

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      hitRate: this.calculateHitRate(),
      totalAccess: totalAccessCount,
      oldestAge: oldestItem === now ? 0 : now - oldestItem,
      newestAge: newestItem === 0 ? 0 : now - newestItem,
      ttl: this.ttl,
    };
  }

  /**
   * 批量设置
   */
  setMany(entries: Array<[K, V]>): void {
    for (const [key, value] of entries) {
      this.set(key, value);
    }
  }

  /**
   * 批量获取
   */
  getMany(keys: K[]): Map<K, V> {
    const result = new Map<K, V>();

    for (const key of keys) {
      const value = this.get(key);
      if (value !== null) {
        result.set(key, value);
      }
    }

    return result;
  }

  /**
   * 更新访问顺序
   */
  private updateAccessOrder(key: K): void {
    const index = this.accessOrder.indexOf(key);
    if (index !== -1) {
      this.accessOrder.splice(index, 1);
    }
    this.accessOrder.push(key);
  }

  /**
   * 驱逐最少使用的项
   */
  private evictLRU(): void {
    if (this.accessOrder.length === 0) {
      return;
    }

    // 找到最少使用的项（访问次数最少且最久未访问）
    let lruKey = this.accessOrder[0];
    let lruScore = Infinity;

    for (const key of this.accessOrder) {
      const item = this.cache.get(key);
      if (item) {
        // 综合考虑访问频率和时间
        const timeFactor = Date.now() - item.lastAccess;
        const accessFactor = 1 / (item.accessCount + 1);
        const score = timeFactor * accessFactor;

        if (score < lruScore) {
          lruScore = score;
          lruKey = key;
        }
      }
    }

    this.delete(lruKey);
  }

  /**
   * 计算命中率（简化实现）
   */
  private calculateHitRate(): number {
    // 这里是简化实现，实际应该记录hit/miss统计
    return this.cache.size > 0 ? 0.8 : 0;
  }
}

/**
 * 转换结果缓存
 */
export class TransformCache {
  private htmlCache: LRUCache<string, string>;
  private metadataCache: LRUCache<string, any>;

  constructor(maxSize = 50) {
    this.htmlCache = new LRUCache(maxSize, 20 * 60 * 1000); // 20分钟TTL
    this.metadataCache = new LRUCache(maxSize, 30 * 60 * 1000); // 30分钟TTL
  }

  /**
   * 生成缓存键
   */
  generateKey(content: string, config: any): string {
    const combined = content + JSON.stringify(config);
    return this.simpleHash(combined);
  }

  /**
   * 缓存转换结果
   */
  setTransformResult(key: string, html: string, metadata: any): void {
    this.htmlCache.set(key, html);
    this.metadataCache.set(key, metadata);
  }

  /**
   * 获取转换结果
   */
  getTransformResult(key: string): { html: string; metadata: any } | null {
    const html = this.htmlCache.get(key);
    const metadata = this.metadataCache.get(key);

    if (html && metadata) {
      return { html, metadata };
    }

    return null;
  }

  /**
   * 清理过期缓存
   */
  cleanup(): void {
    this.htmlCache.cleanup();
    this.metadataCache.cleanup();
  }

  /**
   * 获取缓存统计
   */
  getStats() {
    return {
      html: this.htmlCache.getStats(),
      metadata: this.metadataCache.getStats(),
    };
  }

  /**
   * 清空所有缓存
   */
  clear(): void {
    this.htmlCache.clear();
    this.metadataCache.clear();
  }

  /**
   * 简单哈希函数
   */
  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return hash.toString(36);
  }
}

// 导出全局缓存实例
export const globalTransformCache = new TransformCache();
