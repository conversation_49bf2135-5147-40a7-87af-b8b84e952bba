/**
 * @package runtime-convert-parse5
 * @description 统一模板转换服务 - Worker和主线程共享的单一模板处理逻辑
 * @version 2.0.0 - 集成web-speedy-plugin级别的专业转换引擎
 */

// 尝试导入复杂依赖，如果失败则使用简化版本
let TTMLTemplateEngine: any;
let createDefaultTemplateContext: any;
let EnhancedLynxConverter: any;
let EnhancedEventProcessor: any;
let EnhancedStyleProcessor: any;

try {
  // 尝试导入完整的模板引擎系统
  const templateEngine = require('../template-engine');
  TTMLTemplateEngine = templateEngine.TTMLTemplateEngine;
  createDefaultTemplateContext = templateEngine.createDefaultTemplateContext;

  const enhancedConverter = require('../engines/enhanced-lynx-converter');
  EnhancedLynxConverter = enhancedConverter.EnhancedLynxConverter;

  const eventProcessor = require('../engines/enhanced-event-processor');
  EnhancedEventProcessor = eventProcessor.EnhancedEventProcessor;

  const styleProcessor = require('../engines/enhanced-style-processor');
  EnhancedStyleProcessor = styleProcessor.EnhancedStyleProcessor;
} catch (error) {
  console.log(
    '⚠️ [UnifiedTemplateService] 复杂依赖加载失败，使用简化版本:',
    error.message,
  );
}

// 导入简化版本作为后备
import {
  SimpleTemplateConverter,
  convertTTMLSimple,
  type SimpleTemplateConfig,
  type SimpleTemplateResult,
} from './simple-template-converter';

export interface UnifiedTemplateConfig {
  componentId?: string;
  enableDebugLogs?: boolean;
  enableEventHandling?: boolean;
  enableFallback?: boolean;
  customData?: Record<string, any>;

  // 增强转换配置 - web-speedy-plugin级别
  enhancedMode?: boolean; // 是否启用专业级转换引擎
  webSpeedyCompatible?: boolean; // web-speedy-plugin兼容模式

  // 样式处理配置
  styleConfig?: {
    rpxMode?: 'vw' | 'rem' | 'px' | 'calc';
    designWidth?: number;
    enableCSSScoping?: boolean;
    enableMinification?: boolean;
  };

  // 事件处理配置
  eventConfig?: {
    enableTouchEvents?: boolean;
    enableCustomEvents?: boolean;
    enableEventCapture?: boolean;
  };

  // 布局配置
  layoutConfig?: {
    enableLynxLayout?: boolean;
    enableFlexboxFallback?: boolean;
    enableGridFallback?: boolean;
  };
}

export interface TemplateConversionResult {
  success: boolean;
  html: string;
  css?: string; // 增强模式下的CSS输出
  js?: string; // 增强模式下的JS输出
  metadata: {
    source: 'unified-template-service';
    processingTime: number;
    elementCount: number;
    hasTemplateProcessing: boolean;
    processedBy: 'worker' | 'main-thread';
    componentId: string;
    conversionMode: 'basic' | 'enhanced'; // 转换模式
    engineUsed: 'template-engine' | 'enhanced-lynx-converter'; // 使用的引擎
    // 增强模式的额外统计
    enhancedStats?: {
      rpxConversions?: number;
      eventsProcessed?: number;
      stylesProcessed?: number;
      directivesProcessed?: number;
    };
  };
  error?: string;
}

/**
 * 统一模板转换服务
 * Worker和主线程都使用此服务，确保转换逻辑完全一致
 */
export class UnifiedTemplateService {
  private static instance: UnifiedTemplateService;
  private debugEnabled = false;

  private constructor() {
    // 私有构造函数，确保单例
  }

  /**
   * 获取服务实例（单例模式）
   */
  public static getInstance(): UnifiedTemplateService {
    if (!UnifiedTemplateService.instance) {
      UnifiedTemplateService.instance = new UnifiedTemplateService();
    }
    return UnifiedTemplateService.instance;
  }

  /**
   * 统一的TTML模板转换方法 - 2.0版本支持增强转换引擎
   * Worker和主线程都调用此方法，确保完全一致的处理逻辑
   */
  public convertTTMLTemplate(
    ttml: string,
    config: UnifiedTemplateConfig = {},
    ttss?: string,
    js?: string,
  ): TemplateConversionResult {
    const startTime = performance.now();
    const componentId = config.componentId || this.generateComponentId();
    const processedBy = this.detectEnvironment();

    this.debugEnabled = config.enableDebugLogs ?? false;

    this.log('🚀 [UnifiedTemplateService] 开始统一模板转换 v2.0');
    this.log(`📝 环境: ${processedBy}, 组件ID: ${componentId}`);
    this.log(`📝 增强模式: ${config.enhancedMode ?? false}`);
    this.log(`📝 输入TTML长度: ${ttml.length}`);
    this.log(`📝 包含tt:for: ${ttml.includes('tt:for')}`);
    this.log(`📝 包含{{}}: ${ttml.includes('{{')}`);

    try {
      // 🚀 判断使用哪种转换引擎
      const useEnhancedMode =
        config.enhancedMode ?? this.shouldUseEnhancedMode(ttml, config);

      if (useEnhancedMode) {
        this.log('🎯 使用增强转换引擎 (web-speedy-plugin级别)');
        return this.convertWithEnhancedEngine(
          ttml,
          ttss,
          js,
          config,
          componentId,
          processedBy,
          startTime,
        );
      } else {
        this.log('📝 使用基础模板引擎');
        return this.convertWithBasicEngine(
          ttml,
          config,
          componentId,
          processedBy,
          startTime,
        );
      }
    } catch (error) {
      const processingTime = performance.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      this.log(`❌ 模板转换失败: ${errorMessage}`);

      // 🔧 降级处理（可选）
      if (config.enableFallback !== false) {
        this.log('🔄 启动降级处理...');
        const fallbackResult = this.fallbackConversion(ttml, componentId);

        return {
          success: true, // 降级成功也算成功
          html: fallbackResult,
          metadata: {
            source: 'unified-template-service',
            processingTime,
            elementCount: (fallbackResult.match(/<[^>]+>/g) || []).length,
            hasTemplateProcessing: false, // 降级处理标记
            processedBy,
            componentId,
            conversionMode: 'basic',
            engineUsed: 'template-engine',
          },
          error: `Template engine failed, used fallback: ${errorMessage}`,
        };
      }

      return {
        success: false,
        html: ttml, // 返回原始内容
        metadata: {
          source: 'unified-template-service',
          processingTime,
          elementCount: 0,
          hasTemplateProcessing: false,
          processedBy,
          componentId,
          conversionMode: 'basic',
          engineUsed: 'template-engine',
        },
        error: errorMessage,
      };
    }
  }

  /**
   * 使用增强转换引擎进行专业级转换
   */
  private convertWithEnhancedEngine(
    ttml: string,
    ttss: string | undefined,
    js: string | undefined,
    config: UnifiedTemplateConfig,
    componentId: string,
    processedBy: 'worker' | 'main-thread',
    startTime: number,
  ): TemplateConversionResult {
    // 构建增强配置
    const enhancedConfig: EnhancedConversionConfig = {
      componentId,
      enableDebugLogs: config.enableDebugLogs,
      webSpeedyCompatible: config.webSpeedyCompatible ?? true,
      useTagVScoping: true,

      rpxConfig: {
        designWidth: config.styleConfig?.designWidth ?? 750,
        mode: config.styleConfig?.rpxMode ?? 'vw',
      },

      enableCSSScoping: config.styleConfig?.enableCSSScoping ?? true,
      scopePrefix: componentId,
      enableCache: true,
      enableOptimization: true,
    };

    // 创建增强转换器
    const enhancedConverter = new EnhancedLynxConverter(enhancedConfig);

    // 执行专业级转换
    const conversionResult = enhancedConverter.convert(ttml, ttss, js);

    const processingTime = performance.now() - startTime;

    this.log(`✅ 增强转换完成: ${processingTime.toFixed(2)}ms`);
    this.log(`📊 元素: ${conversionResult.metadata.elementsProcessed}`);
    this.log(`📊 指令: ${conversionResult.metadata.directivesProcessed}`);
    this.log(`📊 事件: ${conversionResult.metadata.eventsProcessed}`);
    this.log(`📊 样式: ${conversionResult.metadata.stylesProcessed}`);

    return {
      success: true,
      html: conversionResult.html,
      css: conversionResult.css,
      js: conversionResult.js,
      metadata: {
        source: 'unified-template-service',
        processingTime,
        elementCount: conversionResult.metadata.elementsProcessed,
        hasTemplateProcessing: true,
        processedBy,
        componentId,
        conversionMode: 'enhanced',
        engineUsed: 'enhanced-lynx-converter',
        enhancedStats: {
          rpxConversions:
            conversionResult.css.match(/vw|rem|calc/g)?.length || 0,
          eventsProcessed: conversionResult.metadata.eventsProcessed,
          stylesProcessed: conversionResult.metadata.stylesProcessed,
          directivesProcessed: conversionResult.metadata.directivesProcessed,
        },
      },
    };
  }

  /**
   * 使用基础模板引擎进行转换
   */
  private convertWithBasicEngine(
    ttml: string,
    config: UnifiedTemplateConfig,
    componentId: string,
    processedBy: 'worker' | 'main-thread',
    startTime: number,
  ): TemplateConversionResult {
    try {
      // 🔧 优先尝试使用完整模板引擎（如果可用）
      if (TTMLTemplateEngine && createDefaultTemplateContext) {
        this.log('📝 使用完整模板引擎进行转换...');

        const context = this.createTemplateContext(
          componentId,
          config.customData,
        );
        const templateEngine = new TTMLTemplateEngine(context);

        const renderedHTML = templateEngine.renderTemplate(ttml);

        // 🔧 事件处理脚本生成（可选）
        let finalHTML = renderedHTML;
        if (config.enableEventHandling !== false) {
          const eventScript = templateEngine.generateEventHandlingScript();
          finalHTML = renderedHTML + eventScript;
          this.log('📝 已添加事件处理脚本');
        }

        const processingTime = performance.now() - startTime;
        const elementCount = (renderedHTML.match(/<[^>]+>/g) || []).length;
        const stillHasTemplateMarkup =
          finalHTML.includes('tt:for') || finalHTML.includes('{{');

        this.log(
          `📊 完整引擎转换完成: ${processingTime.toFixed(2)}ms, ${elementCount}个元素`,
        );

        if (stillHasTemplateMarkup && config.enableFallback !== false) {
          this.log('⚠️ 检测到未处理的模板语法，应用后处理清理');
          finalHTML = this.postProcessCleanup(finalHTML);
        }

        return {
          success: true,
          html: finalHTML,
          metadata: {
            source: 'unified-template-service',
            processingTime,
            elementCount,
            hasTemplateProcessing: true,
            processedBy,
            componentId,
            conversionMode: 'basic',
            engineUsed: 'template-engine',
          },
        };
      } else {
        throw new Error('完整模板引擎不可用，使用简化转换器');
      }
    } catch (error) {
      this.log(`⚠️ 完整模板引擎失败，使用简化转换器: ${error.message}`);

      // 🔧 降级到简化转换器
      return this.convertWithSimpleEngine(
        ttml,
        config,
        componentId,
        processedBy,
        startTime,
      );
    }
  }

  /**
   * 使用简化转换器进行转换
   */
  private convertWithSimpleEngine(
    ttml: string,
    config: UnifiedTemplateConfig,
    componentId: string,
    processedBy: 'worker' | 'main-thread',
    startTime: number,
  ): TemplateConversionResult {
    this.log('📝 使用简化转换器进行转换...');

    const simpleConfig: SimpleTemplateConfig = {
      componentId,
      enableDebugLogs: config.enableDebugLogs,
      enableEventHandling: config.enableEventHandling,
      enableFallback: config.enableFallback,
      customData: config.customData,
    };

    const simpleResult = convertTTMLSimple(ttml, simpleConfig);
    const processingTime = performance.now() - startTime;

    this.log(`📊 简化转换完成: ${processingTime.toFixed(2)}ms`);

    // 转换简化结果格式为统一格式
    return {
      success: simpleResult.success,
      html: simpleResult.html,
      metadata: {
        source: 'unified-template-service',
        processingTime,
        elementCount: simpleResult.metadata.elementCount,
        hasTemplateProcessing: simpleResult.metadata.hasTemplateProcessing,
        processedBy,
        componentId,
        conversionMode: 'basic',
        engineUsed: 'simple-converter',
      },
      error: simpleResult.error,
    };
  }

  /**
   * 判断是否应该使用增强模式
   */
  private shouldUseEnhancedMode(
    ttml: string,
    config: UnifiedTemplateConfig,
  ): boolean {
    // 检查是否包含复杂的Lynx语法特性
    const hasComplexFeatures = [
      ttml.includes('bind'), // 事件绑定
      ttml.includes('catch:'), // 事件捕获
      ttml.includes('rpx'), // RPX单位
      ttml.includes('lynx-'), // Lynx特有组件
      ttml.includes('hover-class'), // hover类
      ttml.includes('animation'), // 动画
      /(tt:|lx:)\w+/.test(ttml), // 指令系统
      config.webSpeedyCompatible, // 明确要求兼容
      config.styleConfig?.rpxMode, // 样式配置
      config.eventConfig, // 事件配置
      config.layoutConfig, // 布局配置
    ].some(Boolean);

    this.log(`🔍 复杂特性检测: ${hasComplexFeatures}`);
    return hasComplexFeatures;
  }

  /**
   * 创建模板上下文
   */
  private createTemplateContext(
    componentId: string,
    customData?: Record<string, any>,
  ): TemplateContext {
    const defaultContext = createDefaultTemplateContext(componentId);

    if (customData) {
      // 合并自定义数据
      Object.assign(defaultContext.data, customData);
      this.log(`📊 合并了 ${Object.keys(customData).length} 个自定义数据字段`);
    }

    return defaultContext;
  }

  /**
   * 检测当前运行环境
   */
  private detectEnvironment(): 'worker' | 'main-thread' {
    try {
      // 在Worker环境中，self !== window
      if (typeof self !== 'undefined' && typeof window === 'undefined') {
        return 'worker';
      }
      return 'main-thread';
    } catch {
      return 'main-thread';
    }
  }

  /**
   * 生成组件ID
   */
  private generateComponentId(): string {
    return `component_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  /**
   * 后处理清理 - 移除残留的模板语法
   */
  private postProcessCleanup(html: string): string {
    this.log('🧹 执行后处理清理...');

    return (
      html
        // 移除残留的tt:属性
        .replace(/\s+tt:\w+="[^"]*"/g, '')
        // 移除残留的插值表达式（简单处理）
        .replace(/\{\{[^}]+\}\}/g, '')
        // 移除FILE标签
        .replace(/<FILE[^>]*>[\s\S]*?<\/FILE>/g, '')
        // 清理多余空白
        .replace(/\s+/g, ' ')
        .trim()
    );
  }

  /**
   * 降级转换处理
   */
  private fallbackConversion(ttml: string, componentId: string): string {
    this.log('🔄 执行降级转换处理...');

    // 基础的标签转换和清理
    let result = ttml
      // TTML标签转换
      .replace(/<lynx-view/g, '<div')
      .replace(/<\/lynx-view>/g, '</div>')
      .replace(/<lynx-text/g, '<span')
      .replace(/<\/lynx-text>/g, '</span>')
      .replace(/<view/g, '<div')
      .replace(/<\/view>/g, '</div>')
      .replace(/<text/g, '<span')
      .replace(/<\/text>/g, '</span>')
      // 移除模板指令
      .replace(/\s+tt:\w+="[^"]*"/g, '')
      // 简单处理插值表达式
      .replace(/\{\{([^}]+)\}\}/g, '[$1]')
      // 移除FILE标签
      .replace(/<FILE[^>]*>[\s\S]*?<\/FILE>/g, '');

    // 添加降级标识
    result = `<!-- Fallback conversion for ${componentId} -->\n${result}`;

    this.log('✅ 降级转换完成');
    return result;
  }

  /**
   * 条件日志输出
   */
  private log(message: string): void {
    if (this.debugEnabled) {
      // 在Worker和主线程都能正常工作的日志输出
      if (typeof console !== 'undefined') {
        console.log(message);
      }
    }
  }

  /**
   * 批量转换多个TTML（用于批处理场景）
   */
  public convertMultiple(
    templates: Array<{ ttml: string; id?: string }>,
    config: UnifiedTemplateConfig = {},
  ): TemplateConversionResult[] {
    this.log(`🔄 [UnifiedTemplateService] 批量转换 ${templates.length} 个模板`);

    return templates.map((template, index) => {
      const templateConfig = {
        ...config,
        componentId:
          template.id || `batch_${index}_${this.generateComponentId()}`,
      };

      return this.convertTTMLTemplate(template.ttml, templateConfig);
    });
  }

  /**
   * 验证TTML格式
   */
  public validateTTML(ttml: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!ttml || typeof ttml !== 'string') {
      errors.push('TTML内容为空或格式无效');
    }

    if (ttml && !ttml.includes('<')) {
      errors.push('TTML内容不包含标签元素');
    }

    // 检查基本标签配对
    const openTags = ttml.match(/<[^/>]+>/g) || [];
    const closeTags = ttml.match(/<\/[^>]+>/g) || [];

    if (openTags.length === 0 && closeTags.length === 0) {
      errors.push('TTML内容不包含有效的HTML标签');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  /**
   * 获取服务统计信息
   */
  public getStats() {
    return {
      environment: this.detectEnvironment(),
      serviceVersion: '1.0.0',
      debugEnabled: this.debugEnabled,
      timestamp: new Date().toISOString(),
    };
  }
}

/**
 * 导出单例实例，方便直接使用
 */
export const unifiedTemplateService = UnifiedTemplateService.getInstance();

/**
 * 便捷函数：转换单个TTML模板 - 2.0版本支持TTSS和JS输入
 */
export function convertTTML(
  ttml: string,
  config: UnifiedTemplateConfig = {},
  ttss?: string,
  js?: string,
): TemplateConversionResult {
  return unifiedTemplateService.convertTTMLTemplate(ttml, config, ttss, js);
}

/**
 * 便捷函数：批量转换TTML模板
 */
export function convertMultipleTTML(
  templates: Array<{ ttml: string; id?: string }>,
  config: UnifiedTemplateConfig = {},
): TemplateConversionResult[] {
  return unifiedTemplateService.convertMultiple(templates, config);
}
