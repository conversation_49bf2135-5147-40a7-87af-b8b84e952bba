/**
 * @package runtime-convert-parse5
 * @description 简化的模板转换器 - 解决依赖问题的可靠回退方案
 * @version 1.0.0 - 专注于核心TTML转换，无复杂依赖
 */

export interface SimpleTemplateConfig {
  componentId?: string;
  enableDebugLogs?: boolean;
  enableEventHandling?: boolean;
  enableFallback?: boolean;
  customData?: Record<string, any>;
}

export interface SimpleTemplateResult {
  success: boolean;
  html: string;
  metadata: {
    source: 'simple-template-converter';
    processingTime: number;
    elementCount: number;
    hasTemplateProcessing: boolean;
    componentId: string;
    conversionMode: 'simple';
    engineUsed: 'simple-converter';
  };
  error?: string;
}

/**
 * 简化的TTML模板转换器
 * 专注于核心转换逻辑，不依赖外部复杂模块
 */
export class SimpleTemplateConverter {
  private debugEnabled = false;

  /**
   * 转换TTML模板为HTML
   */
  public convert(
    ttml: string,
    config: SimpleTemplateConfig = {},
  ): SimpleTemplateResult {
    const startTime = performance.now();
    const componentId = config.componentId || this.generateComponentId();
    this.debugEnabled = config.enableDebugLogs ?? false;

    this.log('🚀 [SimpleTemplateConverter] 开始简化模板转换');
    this.log(`📝 组件ID: ${componentId}`);
    this.log(`📝 输入TTML长度: ${ttml.length}`);
    this.log(`📝 包含tt:for: ${ttml.includes('tt:for')}`);
    this.log(`📝 包含{{}}: ${ttml.includes('{{')}`);

    try {
      let html = ttml;

      // 🔧 核心转换流程
      console.log('🔄 [SimpleTemplateConverter] 执行核心转换流程');

      // 1. 处理模板数据绑定和循环
      html = this.processTemplateDirectives(html, config.customData || {});

      // 2. 转换Lynx组件为HTML标签
      html = this.convertLynxComponents(html);

      // 3. 处理事件绑定
      if (config.enableEventHandling !== false) {
        html = this.processEventBindings(html);
      }

      // 4. 清理和后处理
      html = this.cleanupTemplate(html);

      // 5. 添加包装器和样式（如果需要）
      html = this.wrapWithContainer(html, componentId);

      const processingTime = performance.now() - startTime;
      const elementCount = (html.match(/<[^>]+>/g) || []).length;

      this.log(
        `✅ 简化转换完成: ${processingTime.toFixed(2)}ms, ${elementCount}个元素`,
      );

      return {
        success: true,
        html,
        metadata: {
          source: 'simple-template-converter',
          processingTime,
          elementCount,
          hasTemplateProcessing: true,
          componentId,
          conversionMode: 'simple',
          engineUsed: 'simple-converter',
        },
      };
    } catch (error) {
      const processingTime = performance.now() - startTime;
      const errorMessage =
        error instanceof Error ? error.message : String(error);

      this.log(`❌ 简化转换失败: ${errorMessage}`);

      // 降级处理
      if (config.enableFallback !== false) {
        this.log('🔄 启动降级处理...');
        const fallbackHTML = this.fallbackConversion(ttml, componentId);

        return {
          success: true,
          html: fallbackHTML,
          metadata: {
            source: 'simple-template-converter',
            processingTime,
            elementCount: (fallbackHTML.match(/<[^>]+>/g) || []).length,
            hasTemplateProcessing: false,
            componentId,
            conversionMode: 'simple',
            engineUsed: 'simple-converter',
          },
          error: `Conversion failed, used fallback: ${errorMessage}`,
        };
      }

      return {
        success: false,
        html: ttml,
        metadata: {
          source: 'simple-template-converter',
          processingTime,
          elementCount: 0,
          hasTemplateProcessing: false,
          componentId,
          conversionMode: 'simple',
          engineUsed: 'simple-converter',
        },
        error: errorMessage,
      };
    }
  }

  /**
   * 处理模板指令（tt:for, tt:if, {{}}）
   */
  private processTemplateDirectives(
    html: string,
    data: Record<string, any>,
  ): string {
    this.log('🔄 处理模板指令...');

    // 处理 tt:for 循环
    html = this.processForDirectives(html, data);

    // 处理 tt:if 条件
    html = this.processIfDirectives(html, data);

    // 处理插值表达式 {{}}
    html = this.processInterpolations(html, data);

    return html;
  }

  /**
   * 处理 tt:for 循环指令
   */
  private processForDirectives(
    html: string,
    data: Record<string, any>,
  ): string {
    const forRegex = /<([^>]+)\s+tt:for="([^"]+)"\s*([^>]*)>([\s\S]*?)<\/\1>/g;
    let match;
    let result = html;

    while ((match = forRegex.exec(html)) !== null) {
      const [fullMatch, tagName, forExpression, attributes, content] = match;

      try {
        // 解析 for 表达式：例如 "item in items" 或 "(item, index) in items"
        const forParts = forExpression.match(
          /\(?([^,\s)]+)(?:,\s*([^)]+))?\)?\s+in\s+([^)]+)/,
        );

        if (forParts) {
          const [, itemVar, indexVar, arrayExp] = forParts;
          const arrayName = arrayExp.trim();

          // 获取数组数据
          let arrayData = data[arrayName] || [];

          // 支持简单的数组字面量
          if (typeof arrayData === 'string' && arrayData.startsWith('[')) {
            try {
              arrayData = JSON.parse(arrayData);
            } catch {
              arrayData = [];
            }
          }

          // 确保是数组
          if (!Array.isArray(arrayData)) {
            arrayData = [];
          }

          this.log(
            `📝 处理循环: ${itemVar} in ${arrayName} (${arrayData.length}项)`,
          );

          // 生成循环内容
          let repeatedContent = '';
          arrayData.forEach((item: any, index: number) => {
            let itemContent = content;

            // 替换项目变量
            itemContent = itemContent.replace(
              new RegExp(`\\{\\{\\s*${itemVar}\\s*\\}\\}`, 'g'),
              typeof item === 'string' ? item : JSON.stringify(item),
            );

            // 替换索引变量（如果有）
            if (indexVar) {
              itemContent = itemContent.replace(
                new RegExp(`\\{\\{\\s*${indexVar}\\s*\\}\\}`, 'g'),
                index.toString(),
              );
            }

            repeatedContent += itemContent;
          });

          // 构建新标签
          const newTag = `<${tagName} ${attributes} data-for-processed="true">${repeatedContent}</${tagName}>`;
          result = result.replace(fullMatch, newTag);
        }
      } catch (error) {
        this.log(`❌ 循环处理失败: ${error}`);
        // 保留原始内容
      }
    }

    return result;
  }

  /**
   * 处理 tt:if 条件指令
   */
  private processIfDirectives(html: string, data: Record<string, any>): string {
    const ifRegex = /<([^>]+)\s+tt:if="([^"]+)"\s*([^>]*)>([\s\S]*?)<\/\1>/g;
    let match;
    let result = html;

    while ((match = ifRegex.exec(html)) !== null) {
      const [fullMatch, tagName, condition, attributes, content] = match;

      try {
        // 简单的条件评估
        let shouldShow = false;

        // 支持简单的变量检查
        if (condition in data) {
          shouldShow = !!data[condition];
        } else if (condition === 'true') {
          shouldShow = true;
        } else if (condition === 'false') {
          shouldShow = false;
        }

        this.log(`📝 处理条件: ${condition} = ${shouldShow}`);

        if (shouldShow) {
          // 保留内容，移除tt:if属性
          const newTag = `<${tagName} ${attributes} data-if-processed="true">${content}</${tagName}>`;
          result = result.replace(fullMatch, newTag);
        } else {
          // 移除整个元素
          result = result.replace(fullMatch, '');
        }
      } catch (error) {
        this.log(`❌ 条件处理失败: ${error}`);
        // 保留原始内容
      }
    }

    return result;
  }

  /**
   * 处理插值表达式 {{}}
   */
  private processInterpolations(
    html: string,
    data: Record<string, any>,
  ): string {
    return html.replace(/\{\{([^}]+)\}\}/g, (match, expression) => {
      const varName = expression.trim();

      try {
        // 简单的变量替换
        if (varName in data) {
          const value = data[varName];
          return typeof value === 'string' ? value : JSON.stringify(value);
        }

        // 默认值处理
        return `[${varName}]`;
      } catch (error) {
        this.log(`❌ 插值处理失败: ${expression} -> ${error}`);
        return match; // 保留原始表达式
      }
    });
  }

  /**
   * 转换Lynx组件为HTML标签
   */
  private convertLynxComponents(html: string): string {
    this.log('🌐 转换Lynx组件...');

    // 基础的Lynx组件映射
    const componentMappings = {
      // 容器组件
      view: 'div',
      'scroll-view': 'div',

      // 文本组件
      text: 'span',
      'rich-text': 'div',

      // 表单组件
      input: 'input',
      textarea: 'textarea',
      button: 'button',
      switch: 'input',
      slider: 'input',

      // 媒体组件
      image: 'img',
      video: 'video',
      audio: 'audio',

      // 导航组件
      navigator: 'a',

      // 其他组件
      canvas: 'canvas',
      'web-view': 'iframe',
      progress: 'progress',
      swiper: 'div',
      'swiper-item': 'div',
    };

    // 自闭合标签
    const selfClosingTags = new Set([
      'input',
      'img',
      'br',
      'hr',
      'meta',
      'link',
    ]);

    // 转换开始和结束标签
    Object.entries(componentMappings).forEach(([lynxTag, htmlTag]) => {
      // 转换开始标签
      const openTagRegex = new RegExp(`<${lynxTag}(\\s[^>]*)?(?:\\s*/)?>`, 'g');
      html = html.replace(openTagRegex, (match, attributes = '') => {
        const attrs = this.processAttributes(attributes, lynxTag);
        const className = `lynx-${lynxTag}`;

        // 添加Lynx样式类
        if (attrs.includes('class=')) {
          attrs = attrs.replace(/class="([^"]*)"/, `class="$1 ${className}"`);
        } else {
          attrs += ` class="${className}"`;
        }

        if (selfClosingTags.has(htmlTag) || match.endsWith('/>')) {
          return `<${htmlTag}${attrs} />`;
        } else {
          return `<${htmlTag}${attrs}>`;
        }
      });

      // 转换结束标签（除了自闭合标签）
      if (!selfClosingTags.has(htmlTag)) {
        const closeTagRegex = new RegExp(`</${lynxTag}>`, 'g');
        html = html.replace(closeTagRegex, `</${htmlTag}>`);
      }
    });

    return html;
  }

  /**
   * 处理属性转换
   */
  private processAttributes(attributes: string, lynxTag: string): string {
    if (!attributes) return '';

    // 基础属性映射
    let result = attributes
      // 通用属性
      .replace(/\bhidden\b/g, 'hidden')
      .replace(/\bid="([^"]*)"/g, 'id="$1"')
      // 样式相关
      .replace(/\bstyle="([^"]*)"/g, 'style="$1"')
      // 移除Lynx特有属性（已处理的指令）
      .replace(/\s+tt:\w+="[^"]*"/g, '')
      .replace(/\s+lx:\w+="[^"]*"/g, '');

    // 特定组件的属性处理
    switch (lynxTag) {
      case 'input':
        result = result
          .replace(/\btype="([^"]*)"/g, 'type="$1"')
          .replace(/\bplaceholder="([^"]*)"/g, 'placeholder="$1"')
          .replace(/\bvalue="([^"]*)"/g, 'value="$1"')
          .replace(/\bmaxlength="([^"]*)"/g, 'maxlength="$1"')
          .replace(/\bdisabled\b/g, 'disabled');
        break;

      case 'image':
        result = result
          .replace(/\bsrc="([^"]*)"/g, 'src="$1"')
          .replace(/\bmode="([^"]*)"/g, 'style="object-fit: $1"')
          .replace(/\blazy-load="([^"]*)"/g, 'loading="lazy"');
        break;

      case 'button':
        result = result
          .replace(/\btype="([^"]*)"/g, 'type="$1"')
          .replace(/\bdisabled\b/g, 'disabled');
        break;

      case 'navigator':
        result = result
          .replace(/\burl="([^"]*)"/g, 'href="$1"')
          .replace(/\btarget="([^"]*)"/g, 'target="$1"');
        break;
    }

    return result;
  }

  /**
   * 处理事件绑定
   */
  private processEventBindings(html: string): string {
    this.log('🔗 处理事件绑定...');

    // Lynx事件到HTML事件的映射
    const eventMappings = {
      bindtap: 'onclick',
      'catch:tap': 'onclick',
      bindinput: 'oninput',
      bindchange: 'onchange',
      bindfocus: 'onfocus',
      bindblur: 'onblur',
      bindtouchstart: 'ontouchstart',
      bindtouchend: 'ontouchend',
    };

    Object.entries(eventMappings).forEach(([lynxEvent, htmlEvent]) => {
      const eventRegex = new RegExp(`\\s+${lynxEvent}="([^"]*)"`, 'g');
      html = html.replace(eventRegex, ` ${htmlEvent}="$1"`);
    });

    return html;
  }

  /**
   * 清理模板
   */
  private cleanupTemplate(html: string): string {
    this.log('🧹 清理模板...');

    return (
      html
        // 移除残留的模板指令
        .replace(/\s+tt:\w+="[^"]*"/g, '')
        .replace(/\s+lx:\w+="[^"]*"/g, '')
        // 移除FILE标签
        .replace(/<FILE[^>]*>[\s\S]*?<\/FILE>/g, '')
        // 清理多余空白
        .replace(/\s+/g, ' ')
        .replace(/>\s+</g, '><')
        .trim()
    );
  }

  /**
   * 添加容器包装
   */
  private wrapWithContainer(html: string, componentId: string): string {
    // 如果已经有根容器，直接返回
    if (html.startsWith('<div') && html.includes('data-component')) {
      return html;
    }

    // 添加组件包装器
    return `<div class="lynx-component-wrapper" data-component="${componentId}">${html}</div>`;
  }

  /**
   * 降级转换处理
   */
  private fallbackConversion(ttml: string, componentId: string): string {
    this.log('🔄 执行降级转换...');

    return (
      ttml
        // 基础标签转换
        .replace(/<view/g, '<div')
        .replace(/<\/view>/g, '</div>')
        .replace(/<text/g, '<span')
        .replace(/<\/text>/g, '</span>')
        .replace(/<image/g, '<img')
        .replace(/<\/image>/g, '')
        // 移除所有指令
        .replace(/\s+tt:\w+="[^"]*"/g, '')
        .replace(/\s+lx:\w+="[^"]*"/g, '')
        // 简单处理插值
        .replace(/\{\{([^}]+)\}\}/g, '[$1]')
        // 清理
        .replace(/\s+/g, ' ')
        .trim()
    );
  }

  /**
   * 生成组件ID
   */
  private generateComponentId(): string {
    return `simple_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  /**
   * 条件日志输出
   */
  private log(message: string): void {
    if (this.debugEnabled) {
      console.log(message);
    }
  }
}

/**
 * 便捷函数：转换TTML模板
 */
export function convertTTMLSimple(
  ttml: string,
  config: SimpleTemplateConfig = {},
): SimpleTemplateResult {
  const converter = new SimpleTemplateConverter();
  return converter.convert(ttml, config);
}
