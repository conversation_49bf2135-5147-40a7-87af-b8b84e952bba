/**
 * Enhanced Lynx Preview Worker - 增强的Lynx在线预览转换Worker
 * 基于Template-Assembler规则和@byted-lynx/web-speedy-plugin映射
 *
 * 核心功能:
 * - Lynx TTML/TTSS/JS = Web HTML/CSS/JS 转换
 * - 实时预览生成
 * - Web Worker异步处理
 * - 错误处理和降级机制
 */

import { Parse5TransformEngine, Parse5TransformConfig } from './index';
import type {
  InputFiles,
  TransformResult,
} from '../deleted_runtime_convert/types';

// Worker消息类型定义
interface WorkerMessage {
  id: string;
  type: 'CONVERT_LYNX' | 'GET_STATUS' | 'CLEAR_CACHE';
  payload?: any;
}

interface WorkerResponse {
  id: string;
  type: 'SUCCESS' | 'ERROR' | 'STATUS';
  payload?: any;
  error?: string;
}

// Lynx转换请求
interface LynxConvertRequest {
  files: InputFiles;
  config?: Parse5TransformConfig;
  previewMode?: 'iframe' | 'component';
  enableDebug?: boolean;
}

// Lynx转换响应
interface LynxConvertResponse {
  success: boolean;
  html: string;
  webCode?: {
    html: string;
    css: string;
    js: string;
  };
  metadata?: any;
  error?: string;
  debug?: any;
}

/**
 * Enhanced Lynx Preview Worker Class
 * 在Web Worker环境中运行的Lynx预览转换器
 */
class EnhancedLynxPreviewWorker {
  private transformEngine: Parse5TransformEngine;
  private isInitialized: boolean = false;
  private stats: {
    totalConversions: number;
    successfulConversions: number;
    failedConversions: number;
    averageTime: number;
    lastConversionTime: number;
  } = {
    totalConversions: 0,
    successfulConversions: 0,
    failedConversions: 0,
    averageTime: 0,
    lastConversionTime: 0,
  };

  constructor() {
    this.log('info', '🚀 Enhanced Lynx Preview Worker 初始化中...');
    this.initialize();
  }

  /**
   * 初始化Worker
   */
  private initialize(): void {
    try {
      // 初始化Parse5转换引擎，配置为Worker模式
      const config: Parse5TransformConfig = {
        workerMode: true,
        useWebSpeedyPlugin: true,
        enableDirectHTML: true,
        useSimplifiedMode: true,
        enableOptimization: true,
        enableCache: true,
        maxCacheSize: 50,
        strictMode: false,
      };

      this.transformEngine = new Parse5TransformEngine(config);
      this.isInitialized = true;

      this.log('success', '✅ Enhanced Lynx Preview Worker 初始化完成');
      this.log('debug', '🔧 Worker配置:', config);
    } catch (error) {
      this.log('error', '❌ Worker初始化失败:', error);
      this.isInitialized = false;
    }
  }

  /**
   * 处理Lynx转换请求
   */
  async convertLynxToWeb(
    request: LynxConvertRequest,
  ): Promise<LynxConvertResponse> {
    const startTime = performance.now();
    this.stats.totalConversions++;

    this.log('info', '🔄 开始Lynx转换', {
      hasFiles: !!request.files,
      fileTypes: Object.keys(request.files || {}),
      previewMode: request.previewMode || 'iframe',
    });

    try {
      if (!this.isInitialized) {
        throw new Error('Worker未正确初始化');
      }

      // 验证输入
      if (!request.files || typeof request.files !== 'object') {
        throw new Error('无效的输入文件');
      }

      // 动态更新配置
      if (request.config) {
        this.transformEngine.updateConfig({
          ...request.config,
          workerMode: true, // 强制Worker模式
        });
      }

      // 执行转换
      const transformResult: TransformResult =
        await this.transformEngine.convert(request.files);

      if (!transformResult.success) {
        throw new Error(transformResult.message || '转换失败');
      }

      // 生成Web代码分离版本（用于调试和进一步处理）
      const webCode = this.extractWebCode(transformResult.html);

      // 计算统计信息
      const conversionTime = performance.now() - startTime;
      this.updateStats(conversionTime, true);

      const response: LynxConvertResponse = {
        success: true,
        html: transformResult.html,
        webCode,
        metadata: {
          ...transformResult.metadata,
          conversionTime,
          workerStats: this.getWorkerStats(),
        },
      };

      // 添加调试信息
      if (request.enableDebug) {
        response.debug = {
          inputAnalysis: this.analyzeInput(request.files),
          outputAnalysis: this.analyzeOutput(transformResult.html),
          conversionTime,
          engineStats: this.transformEngine.getStats(),
        };
      }

      this.log('success', '✅ Lynx转换完成', {
        htmlLength: transformResult.html.length,
        conversionTime: `${conversionTime.toFixed(2)}ms`,
        successRate: `${((this.stats.successfulConversions / this.stats.totalConversions) * 100).toFixed(1)}%`,
      });

      return response;
    } catch (error) {
      const conversionTime = performance.now() - startTime;
      this.updateStats(conversionTime, false);

      this.log('error', '❌ Lynx转换失败:', error);

      // 尝试生成降级预览
      const fallbackHtml = this.generateFallbackPreview(request.files, error);

      return {
        success: false,
        html: fallbackHtml,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          conversionTime,
          workerStats: this.getWorkerStats(),
          fallbackMode: true,
        },
      };
    }
  }

  /**
   * 从完整HTML中提取分离的Web代码
   */
  private extractWebCode(html: string): {
    html: string;
    css: string;
    js: string;
  } {
    try {
      // 提取CSS
      const cssMatch = html.match(/<style[^>]*>([\s\S]*?)<\/style>/i);
      const css = cssMatch ? cssMatch[1].trim() : '';

      // 提取JavaScript
      const jsMatch = html.match(/<script[^>]*>([\s\S]*?)<\/script>/i);
      const js = jsMatch ? jsMatch[1].trim() : '';

      // 提取HTML内容（去除head和script标签）
      let bodyContent = html;

      // 移除doctype、html、head标签
      bodyContent = bodyContent.replace(/<!DOCTYPE[^>]*>/i, '');
      bodyContent = bodyContent.replace(/<html[^>]*>/i, '');
      bodyContent = bodyContent.replace(/<\/html>/i, '');
      bodyContent = bodyContent.replace(/<head[\s\S]*?<\/head>/i, '');
      bodyContent = bodyContent.replace(/<script[\s\S]*?<\/script>/gi, '');

      // 提取body内容
      const bodyMatch = bodyContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i);
      const htmlContent = bodyMatch ? bodyMatch[1].trim() : bodyContent.trim();

      return {
        html: htmlContent,
        css,
        js,
      };
    } catch (error) {
      this.log('warn', '⚠️ Web代码提取失败，返回默认值:', error);
      return {
        html: '<div>内容提取失败</div>',
        css: '',
        js: '',
      };
    }
  }

  /**
   * 生成降级预览HTML
   */
  private generateFallbackPreview(files: InputFiles, error: any): string {
    this.log('info', '🔧 生成降级预览HTML...');

    const errorMessage = error instanceof Error ? error.message : String(error);

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lynx 预览 - 降级模式</title>
  <style>
    * { margin: 0; padding: 0; box-sizing: border-box; }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      padding: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .fallback-container {
      background: white;
      border-radius: 16px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
      max-width: 600px;
      width: 100%;
      overflow: hidden;
    }
    
    .fallback-header {
      background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
      color: white;
      padding: 24px;
      text-align: center;
    }
    
    .fallback-title {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
    }
    
    .fallback-subtitle {
      font-size: 16px;
      opacity: 0.9;
    }
    
    .fallback-content {
      padding: 32px;
    }
    
    .error-section {
      background: #fff5f5;
      border: 1px solid #fed7d7;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 24px;
    }
    
    .error-title {
      color: #e53e3e;
      font-weight: 600;
      margin-bottom: 8px;
      font-size: 16px;
    }
    
    .error-message {
      color: #742a2a;
      font-size: 14px;
      line-height: 1.5;
      font-family: 'Monaco', 'Menlo', monospace;
      background: white;
      padding: 12px;
      border-radius: 4px;
      border: 1px solid #fed7d7;
    }
    
    .content-section {
      margin-bottom: 24px;
    }
    
    .content-title {
      font-size: 18px;
      font-weight: 600;
      color: #2d3748;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 2px solid #e2e8f0;
    }
    
    .content-preview {
      background: #f7fafc;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
      padding: 16px;
      font-family: 'Monaco', 'Menlo', monospace;
      font-size: 13px;
      line-height: 1.4;
      max-height: 200px;
      overflow-y: auto;
      white-space: pre-wrap;
      color: #4a5568;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 16px;
      margin-bottom: 24px;
    }
    
    .stat-item {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: 700;
      color: #667eea;
      margin-bottom: 4px;
    }
    
    .stat-label {
      font-size: 12px;
      color: #6c757d;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .retry-section {
      text-align: center;
      padding-top: 24px;
      border-top: 1px solid #e2e8f0;
    }
    
    .retry-button {
      background: linear-gradient(45deg, #667eea, #764ba2);
      color: white;
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: transform 0.2s ease;
    }
    
    .retry-button:hover {
      transform: translateY(-2px);
    }
    
    /* 动画效果 */
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(20px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .fallback-container {
      animation: fadeIn 0.6s ease-out;
    }
  </style>
</head>
<body>
  <div class="fallback-container">
    <div class="fallback-header">
      <div class="fallback-title">🔧 Lynx 预览生成器</div>
      <div class="fallback-subtitle">正在使用降级模式显示内容</div>
    </div>
    
    <div class="fallback-content">
      <div class="error-section">
        <div class="error-title">⚠️ 转换过程遇到问题</div>
        <div class="error-message">${errorMessage}</div>
      </div>
      
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">${files.ttml ? '✓' : '✗'}</div>
          <div class="stat-label">TTML 文件</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">${files.ttss ? '✓' : '✗'}</div>
          <div class="stat-label">TTSS 文件</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">${files.js ? '✓' : '✗'}</div>
          <div class="stat-label">JS 文件</div>
        </div>
        <div class="stat-item">
          <div class="stat-value">${Object.keys(files).length}</div>
          <div class="stat-label">总文件数</div>
        </div>
      </div>
      
      ${
        files.ttml
          ? `
      <div class="content-section">
        <div class="content-title">📄 TTML 内容预览</div>
        <div class="content-preview">${this.escapeHtml(files.ttml.substring(0, 500))}${files.ttml.length > 500 ? '\n\n... (内容已截断)' : ''}</div>
      </div>
      `
          : ''
      }
      
      ${
        files.ttss
          ? `
      <div class="content-section">
        <div class="content-title">🎨 TTSS 样式预览</div>
        <div class="content-preview">${this.escapeHtml(files.ttss.substring(0, 300))}${files.ttss.length > 300 ? '\n\n... (样式已截断)' : ''}</div>
      </div>
      `
          : ''
      }
      
      <div class="retry-section">
        <button class="retry-button" onclick="window.parent.postMessage({type: 'RETRY_CONVERSION'}, '*')">
          🔄 重试转换
        </button>
      </div>
    </div>
  </div>
  
  <script>
    console.log('🔧 Lynx预览降级模式已激活');
    console.log('📊 错误信息:', ${JSON.stringify(errorMessage)});
    console.log('📂 输入文件:', ${JSON.stringify(Object.keys(files))});
    
    // 通知父窗口预览已加载
    window.parent.postMessage({
      type: 'PREVIEW_LOADED',
      mode: 'fallback',
      error: ${JSON.stringify(errorMessage)}
    }, '*');
  </script>
</body>
</html>`;
  }

  /**
   * HTML转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 分析输入文件
   */
  private analyzeInput(files: InputFiles): any {
    return {
      fileCount: Object.keys(files).length,
      ttml: files.ttml
        ? {
            length: files.ttml.length,
            hasElements: /<[^>]+>/.test(files.ttml),
            hasDirectives: /(tt:|lx:)/.test(files.ttml),
            hasBindings: /\{\{.*?\}\}/.test(files.ttml),
          }
        : null,
      ttss: files.ttss
        ? {
            length: files.ttss.length,
            hasRules: /[^{}]+\s*\{[^}]*\}/.test(files.ttss),
            hasRpx: /\d+rpx/.test(files.ttss),
          }
        : null,
      js: files.js
        ? {
            length: files.js.length,
            hasFunction: /function/.test(files.js),
            hasCard: /Card\s*\(/.test(files.js),
          }
        : null,
    };
  }

  /**
   * 分析输出HTML
   */
  private analyzeOutput(html: string): any {
    return {
      length: html.length,
      hasDoctype: html.includes('<!DOCTYPE'),
      hasReact: html.includes('React'),
      hasCSS: /<style[^>]*>/.test(html),
      hasJS: /<script[^>]*>/.test(html),
      hasBody: /<body[^>]*>/.test(html),
    };
  }

  /**
   * 更新统计信息
   */
  private updateStats(conversionTime: number, success: boolean): void {
    if (success) {
      this.stats.successfulConversions++;
    } else {
      this.stats.failedConversions++;
    }

    this.stats.lastConversionTime = conversionTime;
    this.stats.averageTime =
      (this.stats.averageTime * (this.stats.totalConversions - 1) +
        conversionTime) /
      this.stats.totalConversions;
  }

  /**
   * 获取Worker统计信息
   */
  private getWorkerStats(): any {
    return {
      ...this.stats,
      successRate:
        this.stats.totalConversions > 0
          ? (this.stats.successfulConversions / this.stats.totalConversions) *
            100
          : 0,
      isInitialized: this.isInitialized,
      uptime: performance.now(),
    };
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.log('info', '🗑️ 清除Worker缓存...');
    this.transformEngine.clearCache();
  }

  /**
   * Worker状态
   */
  getStatus(): any {
    return {
      isInitialized: this.isInitialized,
      stats: this.getWorkerStats(),
      engineConfig: this.transformEngine.getConfig(),
    };
  }

  /**
   * 安全日志方法
   */
  private log(level: string, message: string, data?: any): void {
    try {
      const timestamp = new Date().toISOString();
      const prefix = `[EnhancedLynxPreviewWorker][${timestamp}]`;

      if (data) {
        console.log(`${prefix} ${message}`, data);
      } else {
        console.log(`${prefix} ${message}`);
      }
    } catch (error) {
      console.log(message, data || '');
    }
  }
}

// Worker实例
const worker = new EnhancedLynxPreviewWorker();

// Worker消息处理
self.addEventListener('message', async (event: MessageEvent<WorkerMessage>) => {
  const { id, type, payload } = event.data;

  try {
    let response: WorkerResponse;

    switch (type) {
      case 'CONVERT_LYNX':
        const convertResult = await worker.convertLynxToWeb(
          payload as LynxConvertRequest,
        );
        response = {
          id,
          type: 'SUCCESS',
          payload: convertResult,
        };
        break;

      case 'GET_STATUS':
        response = {
          id,
          type: 'STATUS',
          payload: worker.getStatus(),
        };
        break;

      case 'CLEAR_CACHE':
        worker.clearCache();
        response = {
          id,
          type: 'SUCCESS',
          payload: { message: '缓存已清除' },
        };
        break;

      default:
        throw new Error(`未知的消息类型: ${type}`);
    }

    self.postMessage(response);
  } catch (error) {
    const errorResponse: WorkerResponse = {
      id,
      type: 'ERROR',
      error: error instanceof Error ? error.message : String(error),
    };

    self.postMessage(errorResponse);
  }
});

// Worker错误处理
self.addEventListener('error', error => {
  console.error('🚨 Worker发生未捕获错误:', error);
});

self.addEventListener('unhandledrejection', event => {
  console.error('🚨 Worker发生未处理的Promise拒绝:', event.reason);
});

// 导出类型（仅用于TypeScript）
export type {
  WorkerMessage,
  WorkerResponse,
  LynxConvertRequest,
  LynxConvertResponse,
};
