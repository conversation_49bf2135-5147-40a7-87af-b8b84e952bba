/**
 * @package runtime-convert-parse5
 * @description 增强事件处理器 - 完整支持Lynx事件系统到Web事件的转换
 * @version 2.0.0
 */

export interface EventProcessorConfig {
  enableEventCapture?: boolean;
  enableTouchEvents?: boolean;
  enableCustomEvents?: boolean;
  debugMode?: boolean;
}

export interface ProcessedEvent {
  eventName: string;
  handler: string;
  options?: {
    capture?: boolean;
    passive?: boolean;
    once?: boolean;
  };
  metadata: {
    originalEvent: string;
    eventType: 'bind' | 'catch' | 'capture';
    isTouch?: boolean;
    isCustom?: boolean;
  };
}

/**
 * 增强的事件处理器
 * 完全兼容@byted-lynx/web-speedy-plugin的事件处理机制
 */
export class EnhancedEventProcessor {
  private config: EventProcessorConfig;

  // 完整的事件映射表 - 基于web-speedy-plugin
  private static readonly COMPLETE_EVENT_MAPPING: Record<string, any> = {
    // === 基础交互事件 ===
    bindtap: {
      webEvent: 'onClick',
      handler: (expr: string) => `onClick={${expr}}`,
      metadata: { type: 'click', bubbles: true },
    },
    'catch:tap': {
      webEvent: 'onClick',
      handler: (expr: string) =>
        `onClick={(e) => { e.stopPropagation(); (${expr})(e); }}`,
      metadata: { type: 'click', bubbles: false },
    },
    'capture:tap': {
      webEvent: 'onClickCapture',
      handler: (expr: string) => `onClickCapture={${expr}}`,
      metadata: { type: 'click', capture: true },
    },

    // === 触摸事件 ===
    bindtouchstart: {
      webEvent: 'onTouchStart',
      handler: (expr: string) => `onTouchStart={${expr}}`,
      metadata: { type: 'touch', phase: 'start' },
    },
    bindtouchmove: {
      webEvent: 'onTouchMove',
      handler: (expr: string) => `onTouchMove={${expr}}`,
      metadata: { type: 'touch', phase: 'move' },
    },
    bindtouchend: {
      webEvent: 'onTouchEnd',
      handler: (expr: string) => `onTouchEnd={${expr}}`,
      metadata: { type: 'touch', phase: 'end' },
    },
    bindtouchcancel: {
      webEvent: 'onTouchCancel',
      handler: (expr: string) => `onTouchCancel={${expr}}`,
      metadata: { type: 'touch', phase: 'cancel' },
    },

    // === 手势事件 ===
    bindlongtap: {
      webEvent: 'onTouchStart',
      handler: (expr: string) => `onTouchStart={(e) => { 
        const timer = setTimeout(() => (${expr})(e), 350);
        e.target.addEventListener('touchend', () => clearTimeout(timer), { once: true });
      }}`,
      metadata: { type: 'gesture', gesture: 'longtap' },
    },

    // === 输入事件 ===
    bindinput: {
      webEvent: 'onChange',
      handler: (expr: string) => `onChange={(e) => (${expr})(e)}`,
      metadata: { type: 'input', realtime: true },
    },
    bindchange: {
      webEvent: 'onChange',
      handler: (expr: string) => `onChange={${expr}}`,
      metadata: { type: 'input', realtime: false },
    },
    bindfocus: {
      webEvent: 'onFocus',
      handler: (expr: string) => `onFocus={${expr}}`,
      metadata: { type: 'focus' },
    },
    bindblur: {
      webEvent: 'onBlur',
      handler: (expr: string) => `onBlur={${expr}}`,
      metadata: { type: 'focus' },
    },

    // === 表单事件 ===
    bindsubmit: {
      webEvent: 'onSubmit',
      handler: (expr: string) =>
        `onSubmit={(e) => { e.preventDefault(); (${expr})(e); }}`,
      metadata: { type: 'form', preventDefault: true },
    },
    bindreset: {
      webEvent: 'onReset',
      handler: (expr: string) => `onReset={${expr}}`,
      metadata: { type: 'form' },
    },

    // === 滚动事件 ===
    bindscroll: {
      webEvent: 'onScroll',
      handler: (expr: string) => `onScroll={${expr}}`,
      metadata: { type: 'scroll' },
    },
    bindscrolltoupper: {
      webEvent: 'onScroll',
      handler: (expr: string) => `onScroll={(e) => {
        if (e.target.scrollTop <= 50) (${expr})(e);
      }}`,
      metadata: { type: 'scroll', direction: 'upper' },
    },
    bindscrolltolower: {
      webEvent: 'onScroll',
      handler: (expr: string) => `onScroll={(e) => {
        const { scrollTop, scrollHeight, clientHeight } = e.target;
        if (scrollTop + clientHeight >= scrollHeight - 50) (${expr})(e);
      }}`,
      metadata: { type: 'scroll', direction: 'lower' },
    },

    // === 媒体事件 ===
    bindload: {
      webEvent: 'onLoad',
      handler: (expr: string) => `onLoad={${expr}}`,
      metadata: { type: 'media' },
    },
    binderror: {
      webEvent: 'onError',
      handler: (expr: string) => `onError={${expr}}`,
      metadata: { type: 'media' },
    },
    bindplay: {
      webEvent: 'onPlay',
      handler: (expr: string) => `onPlay={${expr}}`,
      metadata: { type: 'media', action: 'play' },
    },
    bindpause: {
      webEvent: 'onPause',
      handler: (expr: string) => `onPause={${expr}}`,
      metadata: { type: 'media', action: 'pause' },
    },

    // === 键盘事件 ===
    bindkeydown: {
      webEvent: 'onKeyDown',
      handler: (expr: string) => `onKeyDown={${expr}}`,
      metadata: { type: 'keyboard', phase: 'down' },
    },
    bindkeyup: {
      webEvent: 'onKeyUp',
      handler: (expr: string) => `onKeyUp={${expr}}`,
      metadata: { type: 'keyboard', phase: 'up' },
    },

    // === 组件生命周期事件 ===
    bindready: {
      webEvent: 'useEffect',
      handler: (expr: string) => `/* useEffect(() => (${expr})(), []); */`,
      metadata: { type: 'lifecycle', phase: 'ready' },
    },
    bindattach: {
      webEvent: 'useEffect',
      handler: (expr: string) => `/* useEffect(() => (${expr})(), []); */`,
      metadata: { type: 'lifecycle', phase: 'attach' },
    },
    binddetach: {
      webEvent: 'useEffect',
      handler: (expr: string) =>
        `/* useEffect(() => () => (${expr})(), []); */`,
      metadata: { type: 'lifecycle', phase: 'detach' },
    },
  };

  // 触摸事件模拟器配置
  private static readonly TOUCH_SIMULATION = {
    tap: {
      events: ['touchstart', 'touchend'],
      maxDuration: 200,
      maxDistance: 10,
    },
    longtap: {
      events: ['touchstart'],
      minDuration: 350,
    },
    swipe: {
      events: ['touchstart', 'touchmove', 'touchend'],
      minDistance: 30,
      maxDuration: 300,
    },
  };

  constructor(config: EventProcessorConfig = {}) {
    this.config = {
      enableEventCapture: true,
      enableTouchEvents: true,
      enableCustomEvents: true,
      debugMode: false,
      ...config,
    };
  }

  /**
   * 处理单个事件表达式
   */
  public processEvent(
    eventExpression: string,
    handlerExpression: string,
  ): ProcessedEvent | null {
    const mapping =
      EnhancedEventProcessor.COMPLETE_EVENT_MAPPING[eventExpression];

    if (!mapping) {
      this.log(`⚠️ 未知事件类型: ${eventExpression}`);
      return null;
    }

    const handler = mapping.handler(handlerExpression);

    return {
      eventName: mapping.webEvent,
      handler,
      options: this.buildEventOptions(mapping.metadata),
      metadata: {
        originalEvent: eventExpression,
        eventType: this.parseEventType(eventExpression),
        isTouch: mapping.metadata.type === 'touch',
        isCustom: mapping.metadata.type === 'custom',
      },
    };
  }

  /**
   * 批量处理事件
   */
  public processEvents(
    eventMap: Record<string, string>,
  ): Record<string, string> {
    const processedEvents: Record<string, string> = {};

    Object.entries(eventMap).forEach(([event, handler]) => {
      const processed = this.processEvent(event, handler);
      if (processed) {
        processedEvents[processed.eventName] = processed.handler;
      }
    });

    return processedEvents;
  }

  /**
   * 生成事件处理代码
   */
  public generateEventHandlers(events: ProcessedEvent[]): string {
    const handlers: string[] = [];
    const lifecycleHooks: string[] = [];

    events.forEach(event => {
      if (
        event.metadata.originalEvent.includes('ready') ||
        event.metadata.originalEvent.includes('attach') ||
        event.metadata.originalEvent.includes('detach')
      ) {
        lifecycleHooks.push(event.handler);
      } else {
        handlers.push(`${event.eventName}={${event.handler}}`);
      }
    });

    let result = handlers.join(' ');

    // 添加生命周期Hook
    if (lifecycleHooks.length > 0) {
      result += `\n/* Lifecycle Hooks:\n${lifecycleHooks.join('\n')}\n*/`;
    }

    return result;
  }

  /**
   * 生成触摸事件模拟器
   */
  public generateTouchSimulator(): string {
    if (!this.config.enableTouchEvents) return '';

    return `
      // 触摸事件模拟器 - 基于web-speedy-plugin
      const TouchSimulator = {
        handleTouchStart(element, handler, type = 'tap') {
          let startTime = Date.now();
          let startPos = null;
          
          const onTouchStart = (e) => {
            startTime = Date.now();
            startPos = { x: e.touches[0].clientX, y: e.touches[0].clientY };
          };
          
          const onTouchEnd = (e) => {
            const duration = Date.now() - startTime;
            const endPos = { x: e.changedTouches[0].clientX, y: e.changedTouches[0].clientY };
            const distance = Math.sqrt(
              Math.pow(endPos.x - startPos.x, 2) + Math.pow(endPos.y - startPos.y, 2)
            );
            
            if (type === 'tap' && duration < 200 && distance < 10) {
              handler(e);
            } else if (type === 'longtap' && duration >= 350) {
              handler(e);
            }
          };
          
          element.addEventListener('touchstart', onTouchStart);
          element.addEventListener('touchend', onTouchEnd);
          
          return () => {
            element.removeEventListener('touchstart', onTouchStart);
            element.removeEventListener('touchend', onTouchEnd);
          };
        }
      };
    `;
  }

  /**
   * 解析事件类型
   */
  private parseEventType(
    eventExpression: string,
  ): 'bind' | 'catch' | 'capture' {
    if (eventExpression.startsWith('bind')) return 'bind';
    if (eventExpression.startsWith('catch:')) return 'catch';
    if (eventExpression.startsWith('capture:')) return 'capture';
    return 'bind';
  }

  /**
   * 构建事件选项
   */
  private buildEventOptions(metadata: any): any {
    const options: any = {};

    if (metadata.capture) {
      options.capture = true;
    }

    if (metadata.type === 'touch') {
      options.passive = true;
    }

    if (metadata.once) {
      options.once = true;
    }

    return Object.keys(options).length > 0 ? options : undefined;
  }

  /**
   * 验证事件处理器语法
   */
  public validateHandler(handler: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // 检查基本语法
    if (!handler.trim()) {
      errors.push('事件处理器不能为空');
    }

    // 检查函数调用语法
    if (handler.includes('(') && !handler.includes(')')) {
      errors.push('函数调用语法不完整');
    }

    // 检查变量引用
    if (handler.includes('.') && !handler.match(/\w+\.\w+/)) {
      errors.push('属性访问语法错误');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * 生成事件处理函数包装器
   */
  public generateEventWrapper(
    eventType: string,
    handler: string,
    options: any = {},
  ): string {
    const baseWrapper = `(e) => {
      try {
        ${options.preventDefault ? 'e.preventDefault();' : ''}
        ${options.stopPropagation ? 'e.stopPropagation();' : ''}
        
        // 调用用户处理器
        (${handler})(e);
        
        ${options.debugMode ? 'console.log(`Event ${eventType} handled:`, e);' : ''}
      } catch (error) {
        console.error('Event handler error:', error);
        ${options.debugMode ? 'debugger;' : ''}
      }
    }`;

    return baseWrapper;
  }

  /**
   * 获取支持的事件列表
   */
  public getSupportedEvents(): string[] {
    return Object.keys(EnhancedEventProcessor.COMPLETE_EVENT_MAPPING);
  }

  /**
   * 获取事件处理统计
   */
  public getEventStats(html: string): {
    totalEvents: number;
    eventTypes: Record<string, number>;
    touchEvents: number;
    customEvents: number;
  } {
    const eventTypes: Record<string, number> = {};
    let totalEvents = 0;
    let touchEvents = 0;
    let customEvents = 0;

    // 分析HTML中的事件
    const eventPattern = /on([A-Z][a-zA-Z]*)/g;
    let match;

    while ((match = eventPattern.exec(html)) !== null) {
      const eventType = match[1];
      eventTypes[eventType] = (eventTypes[eventType] || 0) + 1;
      totalEvents++;

      if (eventType.toLowerCase().includes('touch')) {
        touchEvents++;
      }

      if (!['Click', 'Change', 'Focus', 'Blur', 'Submit'].includes(eventType)) {
        customEvents++;
      }
    }

    return {
      totalEvents,
      eventTypes,
      touchEvents,
      customEvents,
    };
  }

  private log(message: string): void {
    if (this.config.debugMode) {
      console.log(`[EnhancedEventProcessor] ${message}`);
    }
  }
}

/**
 * 事件处理工具函数
 */
export class EventUtils {
  /**
   * 创建防抖事件处理器
   */
  static debounce(handler: string, delay: number = 300): string {
    return `
      (() => {
        let timer;
        return (e) => {
          clearTimeout(timer);
          timer = setTimeout(() => (${handler})(e), ${delay});
        };
      })()
    `;
  }

  /**
   * 创建节流事件处理器
   */
  static throttle(handler: string, delay: number = 100): string {
    return `
      (() => {
        let lastTime = 0;
        return (e) => {
          const now = Date.now();
          if (now - lastTime >= ${delay}) {
            lastTime = now;
            (${handler})(e);
          }
        };
      })()
    `;
  }

  /**
   * 创建一次性事件处理器
   */
  static once(handler: string): string {
    return `
      (() => {
        let called = false;
        return (e) => {
          if (!called) {
            called = true;
            (${handler})(e);
          }
        };
      })()
    `;
  }
}
