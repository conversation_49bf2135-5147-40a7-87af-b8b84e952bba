/**
 * @package runtime-convert-parse5
 * @description 增强样式处理器 - 完整支持TTSS到CSS的转换，包括RPX、作用域化、布局系统等
 * @version 2.0.0
 */

export interface StyleProcessorConfig {
  // RPX转换配置
  rpxConfig: {
    designWidth: number; // 设计稿宽度，默认750
    mode: 'vw' | 'rem' | 'px' | 'calc'; // 转换模式
    baseFontSize?: number; // rem模式的基础字体大小
    deviceWidth?: number; // 设备宽度，px模式使用
  };

  // 作用域配置
  scopingConfig: {
    enable: boolean;
    scopeId: string;
    isolateGlobalStyles?: boolean;
  };

  // 布局系统配置
  layoutConfig: {
    enableFlexboxFallback?: boolean;
    enableGridFallback?: boolean;
    enableLynxLayout?: boolean; // 支持Lynx特有的布局属性
  };

  // 性能配置
  optimizationConfig: {
    enableMinification?: boolean;
    enableAutoprefixer?: boolean;
    enableCSSModules?: boolean;
  };

  debugMode?: boolean;
}

export interface ProcessedStyleResult {
  css: string;
  scopedCSS: string;
  cssModules?: Record<string, string>;
  metadata: {
    rpxConversions: number;
    scopedClasses: number;
    layoutRules: number;
    mediaQueries: number;
    processingTime: number;
  };
}

/**
 * 增强的样式处理器
 * 完全兼容@byted-lynx/web-speedy-plugin的样式转换机制
 */
export class EnhancedStyleProcessor {
  private config: StyleProcessorConfig;

  // TTSS到CSS的完整属性映射 - 基于web-speedy-plugin
  private static readonly CSS_PROPERTY_MAPPING: Record<
    string,
    string | ((value: string) => string)
  > = {
    // === 布局属性 ===
    'flex-direction': 'flexDirection',
    'flex-wrap': 'flexWrap',
    'justify-content': 'justifyContent',
    'align-items': 'alignItems',
    'align-content': 'alignContent',
    'align-self': 'alignSelf',
    'flex-grow': 'flexGrow',
    'flex-shrink': 'flexShrink',
    'flex-basis': 'flexBasis',

    // === 定位属性 ===
    position: 'position',
    top: 'top',
    right: 'right',
    bottom: 'bottom',
    left: 'left',
    'z-index': 'zIndex',

    // === 尺寸属性 ===
    width: 'width',
    height: 'height',
    'min-width': 'minWidth',
    'min-height': 'minHeight',
    'max-width': 'maxWidth',
    'max-height': 'maxHeight',

    // === 外边距属性 ===
    margin: 'margin',
    'margin-top': 'marginTop',
    'margin-right': 'marginRight',
    'margin-bottom': 'marginBottom',
    'margin-left': 'marginLeft',

    // === 内边距属性 ===
    padding: 'padding',
    'padding-top': 'paddingTop',
    'padding-right': 'paddingRight',
    'padding-bottom': 'paddingBottom',
    'padding-left': 'paddingLeft',

    // === 背景属性 ===
    background: 'background',
    'background-color': 'backgroundColor',
    'background-image': 'backgroundImage',
    'background-size': 'backgroundSize',
    'background-position': 'backgroundPosition',
    'background-repeat': 'backgroundRepeat',

    // === 边框属性 ===
    border: 'border',
    'border-width': 'borderWidth',
    'border-style': 'borderStyle',
    'border-color': 'borderColor',
    'border-radius': 'borderRadius',
    'border-top': 'borderTop',
    'border-right': 'borderRight',
    'border-bottom': 'borderBottom',
    'border-left': 'borderLeft',

    // === 文本属性 ===
    color: 'color',
    'font-size': 'fontSize',
    'font-weight': 'fontWeight',
    'font-family': 'fontFamily',
    'line-height': 'lineHeight',
    'text-align': 'textAlign',
    'text-decoration': 'textDecoration',
    'letter-spacing': 'letterSpacing',
    'word-spacing': 'wordSpacing',
    'text-transform': 'textTransform',
    'white-space': 'whiteSpace',
    'word-wrap': 'wordWrap',
    'text-overflow': 'textOverflow',

    // === 显示属性 ===
    display: 'display',
    visibility: 'visibility',
    opacity: 'opacity',
    overflow: 'overflow',
    'overflow-x': 'overflowX',
    'overflow-y': 'overflowY',

    // === 变换属性 ===
    transform: 'transform',
    'transform-origin': 'transformOrigin',
    transition: 'transition',
    animation: 'animation',

    // === 阴影和效果 ===
    'box-shadow': 'boxShadow',
    'text-shadow': 'textShadow',
    filter: 'filter',

    // === Lynx特有属性映射 ===
    'lynx-flex': (value: string) => `display: flex; ${value}`,
    'lynx-grid': (value: string) => `display: grid; ${value}`,
    'lynx-center': () =>
      'display: flex; justify-content: center; align-items: center;',
    'lynx-scroll': (value: string) =>
      `overflow: auto; -webkit-overflow-scrolling: touch; ${value}`,
  };

  // Lynx布局系统映射
  private static readonly LYNX_LAYOUT_MAPPING: Record<string, string> = {
    'flex-direction': 'flexDirection',
    'flex-wrap': 'flexWrap',
    'justify-content': 'justifyContent',
    'align-items': 'alignItems',
    flex: 'flex',
    'flex-grow': 'flexGrow',
    'flex-shrink': 'flexShrink',
    'flex-basis': 'flexBasis',

    // Lynx特有布局属性
    layout: (value: string) => {
      switch (value) {
        case 'flex-row':
          return 'display: flex; flex-direction: row;';
        case 'flex-column':
          return 'display: flex; flex-direction: column;';
        case 'grid':
          return 'display: grid;';
        default:
          return `display: ${value};`;
      }
    },
    'main-axis': (value: string) => `justify-content: ${value};`,
    'cross-axis': (value: string) => `align-items: ${value};`,
  };

  // CSS简写属性展开
  private static readonly CSS_SHORTHAND_EXPANSION: Record<
    string,
    (value: string) => Record<string, string>
  > = {
    margin: (value: string) =>
      this.expandShorthand(value, [
        'marginTop',
        'marginRight',
        'marginBottom',
        'marginLeft',
      ]),
    padding: (value: string) =>
      this.expandShorthand(value, [
        'paddingTop',
        'paddingRight',
        'paddingBottom',
        'paddingLeft',
      ]),
    'border-radius': (value: string) => this.expandBorderRadius(value),
    background: (value: string) => this.expandBackground(value),
  };

  constructor(config: StyleProcessorConfig) {
    this.config = {
      rpxConfig: {
        designWidth: 750,
        mode: 'vw',
        baseFontSize: 16,
        deviceWidth: 375,
        ...config.rpxConfig,
      },
      scopingConfig: {
        enable: true,
        isolateGlobalStyles: true,
        ...config.scopingConfig,
      },
      layoutConfig: {
        enableFlexboxFallback: true,
        enableGridFallback: true,
        enableLynxLayout: true,
        ...config.layoutConfig,
      },
      optimizationConfig: {
        enableMinification: false,
        enableAutoprefixer: true,
        enableCSSModules: false,
        ...config.optimizationConfig,
      },
      debugMode: config.debugMode || false,
    };
  }

  /**
   * 主样式处理方法
   */
  public processStyles(ttss: string): ProcessedStyleResult {
    const startTime = performance.now();

    this.log('🎨 [EnhancedStyleProcessor] 开始专业级样式处理');

    let css = ttss;
    let rpxConversions = 0;
    let scopedClasses = 0;
    let layoutRules = 0;
    let mediaQueries = 0;

    // 1. 预处理TTSS语法
    css = this.preprocessTTSS(css);
    this.log('📝 TTSS预处理完成');

    // 2. RPX单位转换
    const rpxResult = this.convertRPXUnits(css);
    css = rpxResult.css;
    rpxConversions = rpxResult.conversions;
    this.log(`📝 RPX转换完成: ${rpxConversions} 个转换`);

    // 3. Lynx布局系统转换
    const layoutResult = this.convertLynxLayout(css);
    css = layoutResult.css;
    layoutRules = layoutResult.rules;
    this.log(`📝 布局转换完成: ${layoutRules} 个规则`);

    // 4. CSS属性映射
    css = this.mapCSSProperties(css);
    this.log('📝 CSS属性映射完成');

    // 5. 作用域化处理
    const scopingResult = this.applyCSSScoping(css);
    const scopedCSS = scopingResult.css;
    scopedClasses = scopingResult.classes;
    this.log(`📝 作用域化完成: ${scopedClasses} 个类`);

    // 6. 媒体查询处理
    const mediaResult = this.processMediaQueries(scopedCSS);
    css = mediaResult.css;
    mediaQueries = mediaResult.queries;
    this.log(`📝 媒体查询处理完成: ${mediaQueries} 个查询`);

    // 7. CSS优化
    if (this.config.optimizationConfig.enableMinification) {
      css = this.minifyCSS(css);
      this.log('📝 CSS压缩完成');
    }

    // 8. Autoprefixer（模拟）
    if (this.config.optimizationConfig.enableAutoprefixer) {
      css = this.addVendorPrefixes(css);
      this.log('📝 厂商前缀添加完成');
    }

    const processingTime = performance.now() - startTime;

    return {
      css,
      scopedCSS,
      cssModules: this.config.optimizationConfig.enableCSSModules
        ? this.generateCSSModules(css)
        : undefined,
      metadata: {
        rpxConversions,
        scopedClasses,
        layoutRules,
        mediaQueries,
        processingTime,
      },
    };
  }

  /**
   * TTSS预处理 - 处理Lynx特有的CSS语法
   */
  private preprocessTTSS(ttss: string): string {
    let css = ttss;

    // 处理TTSS变量
    css = css.replace(/\$([a-zA-Z-_][a-zA-Z0-9-_]*)/g, 'var(--$1)');

    // 处理TTSS嵌套
    css = this.processNestedCSS(css);

    // 处理TTSS混入
    css = this.processMixins(css);

    return css;
  }

  /**
   * RPX单位转换 - 基于web-speedy-plugin的RPX处理
   */
  private convertRPXUnits(css: string): { css: string; conversions: number } {
    const { designWidth, mode, baseFontSize, deviceWidth } =
      this.config.rpxConfig;
    let conversions = 0;

    const convertedCSS = css.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
      const rpx = parseFloat(value);
      conversions++;

      switch (mode) {
        case 'vw':
          const vw = (rpx / designWidth) * 100;
          return `${vw.toFixed(6)}vw`;

        case 'rem':
          const rem = rpx / (designWidth / 20); // 750px = 20rem
          return `${rem.toFixed(6)}rem`;

        case 'px':
          const px = rpx * (deviceWidth! / designWidth);
          return `${px.toFixed(2)}px`;

        case 'calc':
          return `calc(${rpx} * 100vw / ${designWidth})`;

        default:
          return match;
      }
    });

    return { css: convertedCSS, conversions };
  }

  /**
   * Lynx布局系统转换
   */
  private convertLynxLayout(css: string): { css: string; rules: number } {
    if (!this.config.layoutConfig.enableLynxLayout) {
      return { css, rules: 0 };
    }

    let rules = 0;
    let convertedCSS = css;

    // 转换Lynx布局属性
    Object.entries(EnhancedStyleProcessor.LYNX_LAYOUT_MAPPING).forEach(
      ([lynxProp, cssProp]) => {
        const regex = new RegExp(`${lynxProp}\\s*:\\s*([^;]+);`, 'g');
        convertedCSS = convertedCSS.replace(regex, (match, value) => {
          rules++;
          if (typeof cssProp === 'function') {
            return cssProp(value.trim()) + ';';
          }
          return `${cssProp}: ${value.trim()};`;
        });
      },
    );

    // 添加Flexbox降级
    if (this.config.layoutConfig.enableFlexboxFallback) {
      convertedCSS = this.addFlexboxFallback(convertedCSS);
    }

    return { css: convertedCSS, rules };
  }

  /**
   * CSS属性映射
   */
  private mapCSSProperties(css: string): string {
    let mappedCSS = css;

    Object.entries(EnhancedStyleProcessor.CSS_PROPERTY_MAPPING).forEach(
      ([originalProp, mappedProp]) => {
        const regex = new RegExp(`${originalProp}\\s*:\\s*([^;]+);`, 'g');
        mappedCSS = mappedCSS.replace(regex, (match, value) => {
          if (typeof mappedProp === 'function') {
            return mappedProp(value.trim()) + ';';
          }
          return `${mappedProp}: ${value.trim()};`;
        });
      },
    );

    return mappedCSS;
  }

  /**
   * CSS作用域化
   */
  private applyCSSScoping(css: string): { css: string; classes: number } {
    if (!this.config.scopingConfig.enable) {
      return { css, classes: 0 };
    }

    const { scopeId } = this.config.scopingConfig;
    let classes = 0;

    // 作用域化类选择器
    const scopedCSS = css.replace(
      /\.([a-zA-Z-_][a-zA-Z0-9-_]*)/g,
      (match, className) => {
        classes++;
        return `.${scopeId}-${className}`;
      },
    );

    return { css: scopedCSS, classes };
  }

  /**
   * 媒体查询处理
   */
  private processMediaQueries(css: string): { css: string; queries: number } {
    let queries = 0;

    // 转换Lynx特有的媒体查询
    const processedCSS = css.replace(
      /@lynx-media\s+([^{]+)\s*\{/g,
      (match, condition) => {
        queries++;
        const webCondition = this.convertLynxMediaQuery(condition.trim());
        return `@media ${webCondition} {`;
      },
    );

    return { css: processedCSS, queries };
  }

  /**
   * 转换Lynx媒体查询到标准CSS媒体查询
   */
  private convertLynxMediaQuery(condition: string): string {
    // Lynx特有的媒体查询语法转换
    const conversions: Record<string, string> = {
      mobile: '(max-width: 768px)',
      tablet: '(min-width: 769px) and (max-width: 1024px)',
      desktop: '(min-width: 1025px)',
      retina: '(-webkit-min-device-pixel-ratio: 2)',
      portrait: '(orientation: portrait)',
      landscape: '(orientation: landscape)',
    };

    return conversions[condition] || condition;
  }

  /**
   * CSS嵌套处理
   */
  private processNestedCSS(css: string): string {
    // 简化的CSS嵌套处理
    return css.replace(
      /([^{]+)\s*\{\s*([^{}]*)\s*([^{}]+\s*\{[^}]*\})\s*\}/g,
      (match, parent, directStyles, nestedRule) => {
        const parentSelector = parent.trim();
        const processedNested = nestedRule.replace(
          /([^{]+)\s*\{/g,
          (_, selector) => {
            return `${parentSelector} ${selector.trim()} {`;
          },
        );

        return `${parentSelector} { ${directStyles} }\n${processedNested}`;
      },
    );
  }

  /**
   * 处理TTSS混入
   */
  private processMixins(css: string): string {
    // 处理@mixin定义和@include调用
    const mixins: Record<string, string> = {};

    // 提取mixin定义
    css = css.replace(
      /@mixin\s+([a-zA-Z-_][a-zA-Z0-9-_]*)\s*\{([^}]*)\}/g,
      (match, name, content) => {
        mixins[name] = content.trim();
        return '';
      },
    );

    // 替换mixin调用
    css = css.replace(
      /@include\s+([a-zA-Z-_][a-zA-Z0-9-_]*);/g,
      (match, name) => {
        return mixins[name] || '';
      },
    );

    return css;
  }

  /**
   * 添加Flexbox降级支持
   */
  private addFlexboxFallback(css: string): string {
    return css.replace(
      /display\s*:\s*flex;/g,
      'display: -webkit-box; display: -webkit-flex; display: -ms-flexbox; display: flex;',
    );
  }

  /**
   * CSS压缩
   */
  private minifyCSS(css: string): string {
    return css
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除注释
      .replace(/\s+/g, ' ') // 压缩空白
      .replace(/;\s*}/g, '}') // 移除最后的分号
      .replace(/\s*{\s*/g, '{') // 压缩大括号
      .replace(/}\s*/g, '}') // 压缩大括号
      .replace(/:\s*/g, ':') // 压缩冒号
      .replace(/;\s*/g, ';') // 压缩分号
      .trim();
  }

  /**
   * 添加厂商前缀
   */
  private addVendorPrefixes(css: string): string {
    const prefixRules: Record<string, string[]> = {
      transform: ['-webkit-transform', '-moz-transform', '-ms-transform'],
      transition: ['-webkit-transition', '-moz-transition', '-ms-transition'],
      animation: ['-webkit-animation', '-moz-animation', '-ms-animation'],
      'border-radius': ['-webkit-border-radius', '-moz-border-radius'],
      'box-shadow': ['-webkit-box-shadow', '-moz-box-shadow'],
      'user-select': [
        '-webkit-user-select',
        '-moz-user-select',
        '-ms-user-select',
      ],
    };

    let prefixedCSS = css;

    Object.entries(prefixRules).forEach(([property, prefixes]) => {
      const regex = new RegExp(`${property}\\s*:\\s*([^;]+);`, 'g');
      prefixedCSS = prefixedCSS.replace(regex, (match, value) => {
        const prefixed = prefixes
          .map(prefix => `${prefix}: ${value};`)
          .join(' ');
        return `${prefixed} ${match}`;
      });
    });

    return prefixedCSS;
  }

  /**
   * 生成CSS模块映射
   */
  private generateCSSModules(css: string): Record<string, string> {
    const modules: Record<string, string> = {};
    const classRegex = /\.([a-zA-Z-_][a-zA-Z0-9-_]*)/g;

    let match;
    while ((match = classRegex.exec(css)) !== null) {
      const originalClass = match[1];
      const hashedClass = `${originalClass}_${this.generateHash(originalClass)}`;
      modules[originalClass] = hashedClass;
    }

    return modules;
  }

  /**
   * 生成哈希值
   */
  private generateHash(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(36).substring(0, 6);
  }

  // 静态工具方法
  private static expandShorthand(
    value: string,
    properties: string[],
  ): Record<string, string> {
    const values = value.split(/\s+/);
    const result: Record<string, string> = {};

    switch (values.length) {
      case 1:
        properties.forEach(prop => (result[prop] = values[0]));
        break;
      case 2:
        result[properties[0]] = values[0]; // top
        result[properties[1]] = values[1]; // right
        result[properties[2]] = values[0]; // bottom
        result[properties[3]] = values[1]; // left
        break;
      case 3:
        result[properties[0]] = values[0]; // top
        result[properties[1]] = values[1]; // right
        result[properties[2]] = values[2]; // bottom
        result[properties[3]] = values[1]; // left
        break;
      case 4:
        properties.forEach((prop, index) => (result[prop] = values[index]));
        break;
    }

    return result;
  }

  private static expandBorderRadius(value: string): Record<string, string> {
    // 简化的border-radius展开
    return {
      borderTopLeftRadius: value,
      borderTopRightRadius: value,
      borderBottomRightRadius: value,
      borderBottomLeftRadius: value,
    };
  }

  private static expandBackground(value: string): Record<string, string> {
    // 简化的background展开 - 实际需要更复杂的解析
    return {
      background: value,
    };
  }

  private log(message: string): void {
    if (this.config.debugMode) {
      console.log(`[EnhancedStyleProcessor] ${message}`);
    }
  }
}

/**
 * 样式工具函数
 */
export class StyleUtils {
  /**
   * 检测CSS特性支持
   */
  static supportsCSSFeature(feature: string): boolean {
    if (typeof window === 'undefined') return false;

    const testEl = document.createElement('div');
    const prefixes = ['', '-webkit-', '-moz-', '-ms-', '-o-'];

    return prefixes.some(prefix => {
      testEl.style.cssText = `${prefix}${feature}: test;`;
      return testEl.style.length > 0;
    });
  }

  /**
   * 生成关键帧动画
   */
  static generateKeyframes(
    name: string,
    frames: Record<string, Record<string, string>>,
  ): string {
    const keyframeRules = Object.entries(frames)
      .map(([percentage, styles]) => {
        const styleRules = Object.entries(styles)
          .map(([prop, value]) => `${prop}: ${value};`)
          .join(' ');
        return `${percentage} { ${styleRules} }`;
      })
      .join(' ');

    return `@keyframes ${name} { ${keyframeRules} }`;
  }

  /**
   * 计算最佳RPX转换模式
   */
  static getBestRPXMode(
    designWidth: number,
    targetDevices: string[],
  ): 'vw' | 'rem' | 'px' {
    // 基于设计宽度和目标设备推荐最佳转换模式
    if (targetDevices.includes('mobile') && designWidth <= 750) {
      return 'vw';
    } else if (targetDevices.includes('desktop')) {
      return 'rem';
    } else {
      return 'px';
    }
  }
}
