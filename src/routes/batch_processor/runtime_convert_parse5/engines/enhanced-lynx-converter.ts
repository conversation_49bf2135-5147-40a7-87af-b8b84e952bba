/**
 * @package runtime-convert-parse5
 * @description 增强的Lynx转换引擎 - 基于@byted-lynx/web-speedy-plugin的专业实现
 * @version 2.0.0
 */

import { COMPREHENSIVE_LYNX_MAPPING } from '../mappings/comprehensive-lynx-mapping';

export interface EnhancedConversionConfig {
  // 基础配置
  componentId: string;
  enableDebugLogs?: boolean;

  // web-speedy-plugin 兼容配置
  webSpeedyCompatible?: boolean;
  useTagVScoping?: boolean;

  // 样式转换配置
  rpxConfig?: {
    designWidth: number; // 设计稿宽度，默认750
    mode: 'vw' | 'rem' | 'px' | 'calc'; // 转换模式
  };

  // 作用域配置
  enableCSSScoping?: boolean;
  scopePrefix?: string;

  // 性能配置
  enableCache?: boolean;
  enableOptimization?: boolean;
}

export interface ConversionResult {
  html: string;
  css: string;
  js: string;
  metadata: {
    elementsProcessed: number;
    directivesProcessed: number;
    eventsProcessed: number;
    stylesProcessed: number;
    conversionTime: number;
  };
}

/**
 * 词法分析器 - 基于web-speedy-plugin的词法规则
 */
export class LynxLexer {
  private static readonly LEXER_PATTERNS = {
    TAG_PATTERN: /<\/?([a-zA-Z-]+)([^>]*?)(\/>|>)/g,
    ATTR_PATTERN: /([a-zA-Z:-]+)(?:=["']([^"']*?)["'])?/g,
    MUSTACHE_PATTERN: /\{\{([^}]+)\}\}/g,
    DIRECTIVE_PATTERN: /(tt:|lx:)([a-zA-Z-]+)(?:=["']([^"']*?)["'])?/g,
    EVENT_PATTERN: /(bind|catch:)([a-zA-Z-]+)(?:=["']([^"']*?)["'])?/g,
    COMMENT_PATTERN: /<!--([^-]*)-->/g,
    STYLE_PATTERN: /style=["']([^"']*?)["']/g,
  };

  public tokenize(ttml: string): LexerToken[] {
    const tokens: LexerToken[] = [];
    const patterns = LynxLexer.LEXER_PATTERNS;

    // 标签Token
    let match;
    while ((match = patterns.TAG_PATTERN.exec(ttml)) !== null) {
      tokens.push({
        type: 'TAG',
        value: match[0],
        tagName: match[1],
        attributes: match[2],
        position: match.index,
      });
    }

    // 指令Token
    patterns.DIRECTIVE_PATTERN.lastIndex = 0;
    while ((match = patterns.DIRECTIVE_PATTERN.exec(ttml)) !== null) {
      tokens.push({
        type: 'DIRECTIVE',
        value: match[0],
        prefix: match[1],
        name: match[2],
        expression: match[3],
        position: match.index,
      });
    }

    // 事件Token
    patterns.EVENT_PATTERN.lastIndex = 0;
    while ((match = patterns.EVENT_PATTERN.exec(ttml)) !== null) {
      tokens.push({
        type: 'EVENT',
        value: match[0],
        prefix: match[1],
        eventType: match[2],
        handler: match[3],
        position: match.index,
      });
    }

    // Mustache表达式Token
    patterns.MUSTACHE_PATTERN.lastIndex = 0;
    while ((match = patterns.MUSTACHE_PATTERN.exec(ttml)) !== null) {
      tokens.push({
        type: 'MUSTACHE',
        value: match[0],
        expression: match[1],
        position: match.index,
      });
    }

    return tokens.sort((a, b) => a.position - b.position);
  }
}

export interface LexerToken {
  type: 'TAG' | 'DIRECTIVE' | 'EVENT' | 'MUSTACHE' | 'TEXT';
  value: string;
  position: number;
  tagName?: string;
  attributes?: string;
  prefix?: string;
  name?: string;
  expression?: string;
  eventType?: string;
  handler?: string;
}

/**
 * AST构建器 - 基于web-speedy-plugin的AST结构
 */
export class LynxASTBuilder {
  public buildAST(tokens: LexerToken[]): LynxASTNode {
    const root: LynxASTNode = {
      type: 'Root',
      children: [],
      metadata: {},
    };

    let current = root;
    const stack: LynxASTNode[] = [root];

    for (const token of tokens) {
      switch (token.type) {
        case 'TAG':
          if (token.value.startsWith('</')) {
            // 闭合标签
            if (stack.length > 1) {
              stack.pop();
              current = stack[stack.length - 1];
            }
          } else {
            // 开始标签
            const element: LynxASTNode = {
              type: 'Element',
              tagName: token.tagName!,
              attributes: this.parseAttributes(token.attributes || ''),
              children: [],
              metadata: { originalTag: token.tagName },
            };

            current.children!.push(element);

            if (!token.value.endsWith('/>')) {
              stack.push(element);
              current = element;
            }
          }
          break;

        case 'DIRECTIVE':
          // 指令处理
          const directive: LynxASTNode = {
            type: 'Directive',
            directiveType: token.name!,
            expression: token.expression,
            metadata: { prefix: token.prefix },
          };
          current.children!.push(directive);
          break;

        case 'MUSTACHE':
          // 插值表达式
          const mustache: LynxASTNode = {
            type: 'Mustache',
            expression: token.expression!,
            metadata: {},
          };
          current.children!.push(mustache);
          break;
      }
    }

    return root;
  }

  private parseAttributes(attrString: string): Record<string, string> {
    const attributes: Record<string, string> = {};
    const attrPattern = /([a-zA-Z:-]+)(?:=["']([^"']*?)["'])?/g;

    let match;
    while ((match = attrPattern.exec(attrString)) !== null) {
      attributes[match[1]] = match[2] || '';
    }

    return attributes;
  }
}

export interface LynxASTNode {
  type: 'Root' | 'Element' | 'Text' | 'Mustache' | 'Directive';
  tagName?: string;
  attributes?: Record<string, string>;
  children?: LynxASTNode[];
  directiveType?: string;
  expression?: string;
  content?: string;
  metadata: Record<string, any>;
}

/**
 * 增强的Lynx转换引擎
 * 完全参考@byted-lynx/web-speedy-plugin的实现细节
 */
export class EnhancedLynxConverter {
  private config: EnhancedConversionConfig;
  private lexer: LynxLexer;
  private astBuilder: LynxASTBuilder;
  private cache: Map<string, any> = new Map();

  // web-speedy-plugin兼容的事件映射
  private static readonly ENHANCED_EVENT_MAPPING = {
    bindtap: 'onClick',
    'catch:tap': (handler: string) =>
      `onClick={(e) => { e.stopPropagation(); ${handler}(e); }}`,
    bindtouchstart: 'onTouchStart',
    bindtouchmove: 'onTouchMove',
    bindtouchend: 'onTouchEnd',
    bindinput: 'onChange',
    bindchange: 'onChange',
    bindsubmit: 'onSubmit',
    bindfocus: 'onFocus',
    bindblur: 'onBlur',
    bindscroll: 'onScroll',
    bindload: 'onLoad',
    binderror: 'onError',
  };

  // web-speedy-plugin兼容的指令映射
  private static readonly ENHANCED_DIRECTIVE_MAPPING = {
    'tt:for': (expr: string, content: string) =>
      this.processForDirective(expr, content),
    'tt:if': (expr: string, content: string) =>
      this.processIfDirective(expr, content),
    'tt:elif': (expr: string, content: string) =>
      this.processElifDirective(expr, content),
    'tt:else': (expr: string, content: string) =>
      this.processElseDirective(expr, content),
    'tt:key': (expr: string) => ({ key: expr }),
    'lx:for': (expr: string, content: string) =>
      this.processForDirective(expr, content),
    'lx:if': (expr: string, content: string) =>
      this.processIfDirective(expr, content),
    'lx:model': (expr: string) => this.processModelDirective(expr),
  };

  constructor(config: EnhancedConversionConfig) {
    this.config = {
      rpxConfig: { designWidth: 750, mode: 'vw' },
      enableCSSScoping: true,
      webSpeedyCompatible: true,
      useTagVScoping: true,
      ...config,
    };

    this.lexer = new LynxLexer();
    this.astBuilder = new LynxASTBuilder();
  }

  /**
   * 主转换方法
   */
  public convert(ttml: string, ttss?: string, js?: string): ConversionResult {
    const startTime = performance.now();

    this.log('🚀 [EnhancedLynxConverter] 开始专业级转换');
    this.log(`📝 web-speedy兼容模式: ${this.config.webSpeedyCompatible}`);

    // 1. 词法分析
    const tokens = this.lexer.tokenize(ttml);
    this.log(`📝 词法分析完成: ${tokens.length} 个token`);

    // 2. AST构建
    const ast = this.astBuilder.buildAST(tokens);
    this.log(`📝 AST构建完成`);

    // 3. 专业级转换
    const html = this.transformAST(ast);
    const css = this.processCSS(ttss || '');
    const jsCode = this.processJS(js || '');

    const conversionTime = performance.now() - startTime;

    return {
      html,
      css,
      js: jsCode,
      metadata: {
        elementsProcessed: this.countNodes(ast, 'Element'),
        directivesProcessed: this.countNodes(ast, 'Directive'),
        eventsProcessed: this.countEventHandlers(html),
        stylesProcessed: css.split('{').length - 1,
        conversionTime,
      },
    };
  }

  /**
   * AST转换 - 基于web-speedy-plugin的转换规则
   */
  private transformAST(node: LynxASTNode): string {
    switch (node.type) {
      case 'Root':
        return (
          node.children?.map(child => this.transformAST(child)).join('') || ''
        );

      case 'Element':
        return this.transformElement(node);

      case 'Directive':
        return this.transformDirective(node);

      case 'Mustache':
        return this.transformMustache(node);

      case 'Text':
        return node.content || '';

      default:
        return '';
    }
  }

  /**
   * 元素转换 - 完整的web-speedy-plugin映射
   */
  private transformElement(node: LynxASTNode): string {
    const tagName = node.tagName!;
    const mapping = COMPREHENSIVE_LYNX_MAPPING[tagName];

    if (!mapping) {
      this.log(`⚠️ 未找到映射规则: ${tagName}`);
      return `<${tagName}>${this.transformChildren(node)}</${tagName}>`;
    }

    // 应用web-speedy-plugin兼容的属性处理器
    let processedAttrs = node.attributes || {};
    if (mapping.attributeProcessor && this.config.webSpeedyCompatible) {
      const tagV = this.config.useTagVScoping ? this.generateTagV() : undefined;
      processedAttrs = mapping.attributeProcessor(processedAttrs, tagV);
    }

    // 属性映射
    const htmlAttrs = this.mapAttributes(processedAttrs, mapping);

    // 样式处理
    const styles = this.processElementStyles(processedAttrs);
    if (styles) {
      htmlAttrs.style = styles;
    }

    // 构建HTML
    const attrsString = this.buildAttributesString(htmlAttrs);
    const children = this.transformChildren(node);

    return mapping.selfClosing
      ? `<${mapping.tag}${attrsString} />`
      : `<${mapping.tag}${attrsString}>${children}</${mapping.tag}>`;
  }

  /**
   * 指令转换 - 完整的指令系统
   */
  private transformDirective(node: LynxASTNode): string {
    const directiveType = node.directiveType!;
    const expression = node.expression || '';

    switch (directiveType) {
      case 'for':
        return this.processEnhancedForDirective(expression);
      case 'if':
        return this.processEnhancedIfDirective(expression);
      case 'model':
        return this.processEnhancedModelDirective(expression);
      default:
        this.log(`⚠️ 未支持的指令: ${directiveType}`);
        return '';
    }
  }

  /**
   * 增强的for指令处理 - 支持web-speedy-plugin的完整for语法
   */
  private processEnhancedForDirective(expression: string): string {
    // 解析for表达式: item in items, (item, index) in items, 等
    const forPatterns = [
      // (item, index) in items
      /^\(\s*(\w+)\s*,\s*(\w+)\s*\)\s+in\s+(.+)$/,
      // item in items
      /^(\w+)\s+in\s+(.+)$/,
      // {{items}}
      /^\{\{(.+)\}\}$/,
    ];

    for (const pattern of forPatterns) {
      const match = expression.match(pattern);
      if (match) {
        if (pattern.source.includes('\\(')) {
          // (item, index) in items
          const [, itemVar, indexVar, arrayExpr] = match;
          return `{${arrayExpr}.map((${itemVar}, ${indexVar}) => (`;
        } else if (pattern.source.includes('in')) {
          // item in items
          const [, itemVar, arrayExpr] = match;
          return `{${arrayExpr}.map((${itemVar}, index) => (`;
        } else {
          // {{items}}
          const arrayExpr = match[1];
          return `{${arrayExpr}.map((item, index) => (`;
        }
      }
    }

    this.log(`⚠️ 无法解析for表达式: ${expression}`);
    return '';
  }

  /**
   * 增强的if指令处理
   */
  private processEnhancedIfDirective(expression: string): string {
    const cleanExpr = expression.replace(/^\{\{|\}\}$/g, '');
    return `{${cleanExpr} && (`;
  }

  /**
   * 增强的model指令处理 - 双向绑定
   */
  private processEnhancedModelDirective(expression: string): string {
    const cleanExpr = expression.replace(/^\{\{|\}\}$/g, '');
    return `value={${cleanExpr}} onChange={(e) => set${this.capitalize(cleanExpr)}(e.target.value)}`;
  }

  /**
   * Mustache表达式转换 - 支持复杂表达式
   */
  private transformMustache(node: LynxASTNode): string {
    const expression = node.expression!;

    // 处理条件表达式
    if (expression.includes('?') && expression.includes(':')) {
      return `{${expression}}`;
    }

    // 处理方法调用
    if (expression.includes('(') && expression.includes(')')) {
      return `{${expression}}`;
    }

    // 处理属性访问
    if (expression.includes('.')) {
      return `{${expression}}`;
    }

    // 简单变量
    return `{${expression}}`;
  }

  /**
   * CSS处理 - 支持RPX转换和作用域化
   */
  private processCSS(ttss: string): string {
    if (!ttss.trim()) return '';

    let css = ttss;

    // 1. RPX转换
    if (this.config.rpxConfig) {
      css = this.convertRPX(css);
    }

    // 2. 作用域化
    if (this.config.enableCSSScoping) {
      css = this.applyCSSScoping(css);
    }

    return css;
  }

  /**
   * RPX转换 - 基于web-speedy-plugin的RPX处理
   */
  private convertRPX(css: string): string {
    const { designWidth, mode } = this.config.rpxConfig!;

    return css.replace(/(\d+(?:\.\d+)?)rpx/g, (match, value) => {
      const rpx = parseFloat(value);

      switch (mode) {
        case 'vw':
          const vw = (rpx / designWidth) * 100;
          return `${vw.toFixed(6)}vw`;
        case 'rem':
          const rem = rpx / 37.5; // 1rem = 37.5rpx (750px/20)
          return `${rem.toFixed(6)}rem`;
        case 'px':
          const px = rpx * (375 / designWidth); // 按375px计算
          return `${px.toFixed(2)}px`;
        case 'calc':
          return `calc(${rpx} * 100vw / ${designWidth})`;
        default:
          return match;
      }
    });
  }

  /**
   * CSS作用域化
   */
  private applyCSSScoping(css: string): string {
    const scopeId = this.config.scopePrefix || this.config.componentId;
    return css.replace(/\.([a-zA-Z-_][a-zA-Z0-9-_]*)/g, `.${scopeId}-$1`);
  }

  /**
   * 属性映射
   */
  private mapAttributes(
    attrs: Record<string, string>,
    mapping: any,
  ): Record<string, any> {
    const mapped: Record<string, any> = {};

    Object.entries(attrs).forEach(([key, value]) => {
      const mappedKey = mapping.attributeMapping?.[key] || key;

      if (typeof mappedKey === 'function') {
        Object.assign(mapped, mappedKey(value));
      } else {
        mapped[mappedKey] = value;
      }
    });

    return mapped;
  }

  // 工具方法
  private transformChildren(node: LynxASTNode): string {
    return node.children?.map(child => this.transformAST(child)).join('') || '';
  }

  private buildAttributesString(attrs: Record<string, any>): string {
    return Object.entries(attrs)
      .map(([key, value]) => {
        if (value === true) return ` ${key}`;
        if (value === false || value === null || value === undefined) return '';
        return ` ${key}="${value}"`;
      })
      .join('');
  }

  private processElementStyles(attrs: Record<string, string>): string {
    return attrs.style || '';
  }

  private generateTagV(): string {
    return `v-${Math.random().toString(36).substr(2, 8)}`;
  }

  private capitalize(str: string): string {
    return str.charAt(0).toUpperCase() + str.slice(1);
  }

  private countNodes(node: LynxASTNode, type: string): number {
    let count = node.type === type ? 1 : 0;
    node.children?.forEach(child => {
      count += this.countNodes(child, type);
    });
    return count;
  }

  private countEventHandlers(html: string): number {
    const eventPattern = /on[A-Z][a-zA-Z]*=/g;
    return (html.match(eventPattern) || []).length;
  }

  private processJS(js: string): string {
    // 基础的JavaScript处理
    return js;
  }

  private log(message: string): void {
    if (this.config.enableDebugLogs) {
      console.log(message);
    }
  }

  // 静态方法（用于指令映射）
  private static processForDirective(expr: string, content: string): string {
    return `{/* FOR: ${expr} */}${content}`;
  }

  private static processIfDirective(expr: string, content: string): string {
    return `{/* IF: ${expr} */}${content}`;
  }

  private static processElifDirective(expr: string, content: string): string {
    return `{/* ELIF: ${expr} */}${content}`;
  }

  private static processElseDirective(expr: string, content: string): string {
    return `{/* ELSE */}${content}`;
  }

  private static processModelDirective(expr: string): any {
    return { 'data-model': expr };
  }
}
