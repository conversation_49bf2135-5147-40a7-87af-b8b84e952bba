/**
 * @package runtime-convert-parse5
 * @description Parse5转换Web Worker - 在独立线程中进行TTML转换计算
 */

import Parse5TransformEngine from '../index';
import type { Parse5TransformConfig } from '../index';
import {
  convertTTML,
  type UnifiedTemplateConfig,
} from '../services/unified-template-service';

interface WorkerMessage {
  id: string;
  type: 'TRANSFORM' | 'VALIDATE' | 'CONFIGURE';
  payload: any;
}

interface TransformPayload {
  ttml: string;
  componentId: string;
  config?: Partial<Parse5TransformConfig>;
}

interface WorkerResponse {
  id: string;
  success: boolean;
  result?: any;
  error?: string;
  timing?: {
    parseTime: number;
    transformTime: number;
    totalTime: number;
  };
}

/**
 * Web Worker环境的Parse5转换引擎
 */
class Parse5TransformWorker {
  private engine: Parse5TransformEngine;
  private startTime = 0;

  constructor() {
    // 初始化引擎，启用性能优化
    this.engine = new Parse5TransformEngine({
      enableCache: true,
      enableScope: true,
      enableOptimization: true,
      strictMode: false,
      workerMode: true, // 标识Worker环境
    });

    console.log('🚀 [Parse5Worker] 转换引擎已初始化');
  }

  /**
   * 处理来自主线程的消息
   */
  async handleMessage(event: MessageEvent<WorkerMessage>) {
    const { id, type, payload } = event.data;
    this.startTime = performance.now();

    try {
      let result: any;

      switch (type) {
        case 'TRANSFORM':
          result = await this.handleTransform(payload as TransformPayload);
          break;
        case 'VALIDATE':
          result = await this.handleValidate(payload);
          break;
        case 'CONFIGURE':
          result = await this.handleConfigure(payload);
          break;
        default:
          throw new Error(`Unknown message type: ${type}`);
      }

      const totalTime = performance.now() - this.startTime;

      const response: WorkerResponse = {
        id,
        success: true,
        result,
        timing: {
          parseTime: result.parseTime || 0,
          transformTime: result.transformTime || 0,
          totalTime,
        },
      };

      this.postMessage(response);
    } catch (error) {
      const totalTime = performance.now() - this.startTime;

      const response: WorkerResponse = {
        id,
        success: false,
        error: error instanceof Error ? error.message : String(error),
        timing: {
          parseTime: 0,
          transformTime: 0,
          totalTime,
        },
      };

      console.error('💥 [Parse5Worker] 转换失败:', error);
      this.postMessage(response);
    }
  }

  /**
   * 处理TTML转换请求
   */
  private async handleTransform(payload: TransformPayload) {
    const { ttml, componentId, config } = payload;

    console.log(
      `🔄 [Parse5Worker] 开始转换 ${componentId}，内容长度: ${ttml.length}`,
    );

    // 🔧 P0修复：添加Worker中的模板语法检测
    console.log(
      '🔍 [Parse5Worker] 输入TTML包含tt:for:',
      ttml.includes('tt:for'),
    );
    console.log('🔍 [Parse5Worker] 输入TTML包含{{}}:', ttml.includes('{{'));

    // 如果提供了配置，更新引擎配置
    if (config) {
      this.engine.updateConfig(config);
    }

    const parseStart = performance.now();

    // 🚀 重构：Worker使用统一模板转换服务
    console.log('🚀 [Parse5Worker] 使用统一模板转换服务...');

    let result: any;

    try {
      // 🔧 调用统一服务进行模板转换
      console.log('📝 [Parse5Worker] 调用 UnifiedTemplateService...');

      const templateConfig: UnifiedTemplateConfig = {
        componentId,
        enableDebugLogs: true,
        enableEventHandling: true,
        enableFallback: true,
      };

      const conversionResult = convertTTML(ttml, templateConfig);

      if (conversionResult.success) {
        result = {
          success: true,
          html: conversionResult.html,
          metadata: {
            ...conversionResult.metadata,
            source: 'unified-template-service-worker',
            workerProcessed: true,
          },
        };

        console.log('✅ [Parse5Worker] 统一服务转换成功');
      } else {
        throw new Error(
          conversionResult.error || 'Unified service conversion failed',
        );
      }
    } catch (serviceError) {
      console.warn(
        '⚠️ [Parse5Worker] 统一服务失败，回退到原引擎:',
        serviceError,
      );

      // 降级到原Parse5引擎
      result = await this.engine.convert({
        ttml,
        ttss: '',
        js: '',
      });
    }

    // 🔧 P0修复：检查Worker转换结果
    console.log(
      '📝 [Parse5Worker] 转换结果HTML长度:',
      result.html?.length || 0,
    );
    console.log(
      '📝 [Parse5Worker] 结果仍包含tt:for:',
      result.html?.includes('tt:for') || false,
    );
    console.log(
      '📝 [Parse5Worker] 结果仍包含{{}}:',
      result.html?.includes('{{') || false,
    );

    const parseTime = performance.now() - parseStart;

    if (!result.success) {
      throw new Error(result.error || 'Transform failed');
    }

    console.log(
      `✅ [Parse5Worker] 转换完成 ${componentId}，解析耗时: ${parseTime.toFixed(2)}ms`,
    );

    return {
      success: true,
      html: result.html,
      metadata: result.metadata,
      parseTime,
      transformTime: parseTime, // 在Worker中，解析即转换
      componentId,
    };
  }

  /**
   * 处理TTML验证请求
   */
  private async handleValidate(payload: { ttml: string }) {
    const { ttml } = payload;

    try {
      // 简单的TTML格式验证
      const isValid = this.validateTTMLStructure(ttml);

      return {
        isValid,
        errors: isValid ? [] : ['Invalid TTML structure'],
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [error instanceof Error ? error.message : String(error)],
      };
    }
  }

  /**
   * 处理引擎配置更新
   */
  private async handleConfigure(payload: Partial<Parse5TransformConfig>) {
    this.engine.updateConfig(payload);

    return {
      success: true,
      config: this.engine.getConfig(),
    };
  }

  /**
   * 验证TTML结构
   */
  private validateTTMLStructure(ttml: string): boolean {
    if (!ttml || typeof ttml !== 'string') {
      return false;
    }

    // 基本结构检查
    const hasBasicStructure = ttml.includes('<') && ttml.includes('>');
    const hasNoMalformedTags = !ttml.includes('<>') && !ttml.includes('</>');

    return hasBasicStructure && hasNoMalformedTags;
  }

  /**
   * 向主线程发送消息
   */
  private postMessage(response: WorkerResponse) {
    // @ts-expect-error - Worker环境
    self.postMessage(response);
  }
}

// 初始化Worker
const worker = new Parse5TransformWorker();

// 监听主线程消息
// @ts-expect-error - Worker环境
self.addEventListener('message', (event: MessageEvent<WorkerMessage>) => {
  worker.handleMessage(event);
});

// 通知主线程Worker已准备就绪
// @ts-expect-error - Worker环境
self.postMessage({ type: 'WORKER_READY' });

export default Parse5TransformWorker;
