/**
 * @package runtime-convert-parse5
 * @description Web Worker管理器 - 管理转换Worker的生命周期和任务分发
 */

import type { Parse5TransformConfig } from '../index';

interface WorkerTask {
  id: string;
  type: 'TRANSFORM' | 'VALIDATE' | 'CONFIGURE';
  payload: any;
  resolve: (result: any) => void;
  reject: (error: Error) => void;
  timeout?: NodeJS.Timeout;
}

interface WorkerStats {
  tasksCompleted: number;
  averageTime: number;
  lastActivity: number;
  isActive: boolean;
}

/**
 * Worker池管理器
 */
export class Parse5WorkerManager {
  private workers: Worker[] = [];
  private workerStats: Map<Worker, WorkerStats> = new Map();
  private pendingTasks: Map<string, WorkerTask> = new Map();
  private taskQueue: WorkerTask[] = [];
  private maxWorkers: number;
  private workerTimeout: number;
  private isInitialized = false;

  constructor(maxWorkers = 2, workerTimeout = 10000) {
    this.maxWorkers = Math.min(maxWorkers, navigator.hardwareConcurrency || 2);
    this.workerTimeout = workerTimeout;
  }

  /**
   * 初始化Worker池
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    console.log(`🚀 [WorkerManager] 初始化 ${this.maxWorkers} 个转换Worker`);

    const initPromises = Array.from({ length: this.maxWorkers }, () =>
      this.createWorker(),
    );

    await Promise.all(initPromises);
    this.isInitialized = true;

    console.log('✅ [WorkerManager] Worker池初始化完成');
  }

  /**
   * 创建单个Worker
   */
  private async createWorker(): Promise<Worker> {
    return new Promise((resolve, reject) => {
      try {
        // 创建Worker（需要构建系统支持）
        const worker = new Worker(
          new URL('./transform-worker.ts', import.meta.url),
          { type: 'module' },
        );

        // 监听Worker消息
        worker.onmessage = event => {
          this.handleWorkerMessage(worker, event);
        };

        // 监听Worker错误
        worker.onerror = error => {
          console.error('💥 [WorkerManager] Worker错误:', error);
          this.handleWorkerError(worker, error);
        };

        // 等待Worker准备就绪
        const readyTimeout = setTimeout(() => {
          reject(new Error('Worker初始化超时'));
        }, 5000);

        const handleReady = (event: MessageEvent) => {
          if (event.data.type === 'WORKER_READY') {
            clearTimeout(readyTimeout);
            worker.removeEventListener('message', handleReady);

            // 初始化Worker统计
            this.workerStats.set(worker, {
              tasksCompleted: 0,
              averageTime: 0,
              lastActivity: Date.now(),
              isActive: false,
            });

            this.workers.push(worker);
            resolve(worker);
          }
        };

        worker.addEventListener('message', handleReady);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 执行TTML转换任务
   */
  async transform(
    ttml: string,
    componentId: string,
    config?: Partial<Parse5TransformConfig>,
  ): Promise<any> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const taskId = this.generateTaskId();

      const task: WorkerTask = {
        id: taskId,
        type: 'TRANSFORM',
        payload: { ttml, componentId, config },
        resolve,
        reject,
      };

      // 设置超时
      task.timeout = setTimeout(() => {
        this.handleTaskTimeout(taskId);
      }, this.workerTimeout);

      this.pendingTasks.set(taskId, task);
      this.scheduleTask(task);
    });
  }

  /**
   * 验证TTML
   */
  async validate(ttml: string): Promise<any> {
    if (!this.isInitialized) {
      await this.initialize();
    }

    return new Promise((resolve, reject) => {
      const taskId = this.generateTaskId();

      const task: WorkerTask = {
        id: taskId,
        type: 'VALIDATE',
        payload: { ttml },
        resolve,
        reject,
      };

      task.timeout = setTimeout(() => {
        this.handleTaskTimeout(taskId);
      }, this.workerTimeout);

      this.pendingTasks.set(taskId, task);
      this.scheduleTask(task);
    });
  }

  /**
   * 任务调度
   */
  private scheduleTask(task: WorkerTask): void {
    const availableWorker = this.findAvailableWorker();

    if (availableWorker) {
      this.assignTaskToWorker(task, availableWorker);
    } else {
      // 所有Worker都忙，加入队列
      this.taskQueue.push(task);
    }
  }

  /**
   * 查找可用Worker
   */
  private findAvailableWorker(): Worker | null {
    for (const worker of this.workers) {
      const stats = this.workerStats.get(worker);
      if (stats && !stats.isActive) {
        return worker;
      }
    }
    return null;
  }

  /**
   * 将任务分配给Worker
   */
  private assignTaskToWorker(task: WorkerTask, worker: Worker): void {
    const stats = this.workerStats.get(worker)!;
    stats.isActive = true;
    stats.lastActivity = Date.now();

    worker.postMessage({
      id: task.id,
      type: task.type,
      payload: task.payload,
    });
  }

  /**
   * 处理Worker消息
   */
  private handleWorkerMessage(worker: Worker, event: MessageEvent): void {
    const { id, success, result, error, timing } = event.data;

    if (!id) {
      return;
    } // 忽略系统消息

    const task = this.pendingTasks.get(id);
    if (!task) {
      return;
    }

    // 清理任务
    this.cleanupTask(id);

    // 更新Worker统计
    const stats = this.workerStats.get(worker)!;
    stats.isActive = false;
    stats.tasksCompleted++;

    if (timing?.totalTime) {
      stats.averageTime =
        (stats.averageTime * (stats.tasksCompleted - 1) + timing.totalTime) /
        stats.tasksCompleted;
    }

    // 处理结果
    if (success) {
      task.resolve(result);
    } else {
      task.reject(new Error(error || 'Worker task failed'));
    }

    // 处理队列中的下一个任务
    this.processNextTask(worker);
  }

  /**
   * 处理Worker错误
   */
  private handleWorkerError(worker: Worker, error: ErrorEvent): void {
    console.error('💥 [WorkerManager] Worker发生错误:', error);

    // 标记Worker为不可用
    const stats = this.workerStats.get(worker);
    if (stats) {
      stats.isActive = false;
    }

    // 重启Worker
    this.restartWorker(worker);
  }

  /**
   * 处理任务超时
   */
  private handleTaskTimeout(taskId: string): void {
    const task = this.pendingTasks.get(taskId);
    if (!task) {
      return;
    }

    this.cleanupTask(taskId);
    task.reject(new Error('Task timeout'));

    console.warn(`⏰ [WorkerManager] 任务超时: ${taskId}`);
  }

  /**
   * 清理任务
   */
  private cleanupTask(taskId: string): void {
    const task = this.pendingTasks.get(taskId);
    if (task?.timeout) {
      clearTimeout(task.timeout);
    }
    this.pendingTasks.delete(taskId);
  }

  /**
   * 处理队列中的下一个任务
   */
  private processNextTask(worker: Worker): void {
    const nextTask = this.taskQueue.shift();
    if (nextTask) {
      this.assignTaskToWorker(nextTask, worker);
    }
  }

  /**
   * 重启Worker
   */
  private async restartWorker(failedWorker: Worker): Promise<void> {
    try {
      // 移除失败的Worker
      const index = this.workers.indexOf(failedWorker);
      if (index !== -1) {
        this.workers.splice(index, 1);
        this.workerStats.delete(failedWorker);
        failedWorker.terminate();
      }

      // 创建新Worker
      await this.createWorker();

      console.log('🔄 [WorkerManager] Worker已重启');
    } catch (error) {
      console.error('💥 [WorkerManager] Worker重启失败:', error);
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取Worker池状态
   */
  getStats() {
    return {
      totalWorkers: this.workers.length,
      activeWorkers: Array.from(this.workerStats.values()).filter(
        s => s.isActive,
      ).length,
      queueLength: this.taskQueue.length,
      pendingTasks: this.pendingTasks.size,
      workerStats: Array.from(this.workerStats.entries()).map(
        ([worker, stats]) => ({
          id: this.workers.indexOf(worker),
          ...stats,
        }),
      ),
    };
  }

  /**
   * 销毁Worker池
   */
  destroy(): void {
    console.log('🔚 [WorkerManager] 销毁Worker池');

    // 清理所有任务
    for (const task of this.pendingTasks.values()) {
      if (task.timeout) {
        clearTimeout(task.timeout);
      }
      task.reject(new Error('WorkerManager destroyed'));
    }

    // 终止所有Worker
    for (const worker of this.workers) {
      worker.terminate();
    }

    // 清理状态
    this.workers.length = 0;
    this.workerStats.clear();
    this.pendingTasks.clear();
    this.taskQueue.length = 0;
    this.isInitialized = false;
  }
}

// 单例Worker管理器
export const workerManager = new Parse5WorkerManager();
