/**
 * @package runtime-convert-parse5
 * @description Parse5 TTML适配器 - 增强版
 * 使用Parse5解析TTML并转换为React JSX
 * 基于pePromptLoader.ts完整规则，支持所有Lynx组件、事件和属性
 */

import * as parse5 from 'parse5';
import type { Parse5TransformConfig } from '../index';
import {
  TTML_ELEMENT_MAPPING,
  TTML_DIRECTIVE_MAPPING,
  EVENT_DIRECTIVE_MAPPING,
  COMMON_ATTRIBUTE_MAPPING,
  SELF_CLOSING_TAGS,
  FALLBACK_ELEMENT_MAPPING,
  getElementMapping,
  validateMappingRules,
} from '../mappings';
import { COMPREHENSIVE_LYNX_MAPPING } from '../mappings/comprehensive-lynx-mapping';
import {
  eventProcessorManager,
  type ProcessedEvents,
} from '../mappings/event-processors';
import {
  convertTTML,
  type UnifiedTemplateConfig,
} from '../services/unified-template-service';

/**
 * Parse5节点类型
 */
interface Parse5Node {
  nodeName: string;
  tagName?: string;
  childNodes?: Parse5Node[];
  attrs?: Array<{ name: string; value: string }>;
  value?: string;
  parentNode?: Parse5Node;
  sourceCodeLocation?: any;
}

/**
 * 转换上下文
 */
interface TransformContext {
  componentId: string;
  scope: Record<string, boolean>;
  isRoot?: boolean;
  parentNode?: Parse5Node;
}

/**
 * JSX节点类型
 */
interface JSXNode {
  type: string;
  children?: JSXNode[];
  openingElement?: any;
  closingElement?: any;
  value?: any;
  expression?: any;
}

/**
 * Parse5 TTML适配器
 * 使用Parse5强大的HTML5解析能力来处理TTML
 */
export class Parse5TTMLAdapter {
  private config: Parse5TransformConfig & { debugMode?: boolean };

  constructor(config: Parse5TransformConfig & { debugMode?: boolean }) {
    this.config = { debugMode: false, ...config };
  }

  /**
   * 转换TTML为JSX - 修复版，优先使用模板引擎
   */
  async transform(
    ttml: string,
    componentId: string,
  ): Promise<{
    jsx: string;
    ast: any;
    elementCount: number;
    errors: string[];
  }> {
    const errors: string[] = [];
    const transformStartTime = performance.now();

    try {
      // 验证输入参数
      if (!ttml || typeof ttml !== 'string') {
        throw new Error(
          `Invalid TTML input: expected string, got ${typeof ttml}`,
        );
      }

      console.log(
        '🚨🚨🚨 [Parse5TTMLAdapter] 开始TTML转换 - 强制调试模式 🚨🚨🚨',
      );

      // 🔧 P0修复：强制使用模板引擎处理TTML（移除所有条件判断）
      console.log('🎯🎯🎯 [Parse5TTMLAdapter] 强制使用模板引擎转换 🎯🎯🎯');
      console.log('📝 输入TTML长度:', ttml.length);
      console.log('📝 输入TTML包含tt:for:', ttml.includes('tt:for'));
      console.log('📝 输入TTML包含{{}}:', ttml.includes('{{'));
      console.log('📝 输入TTML预览:', ttml.substring(0, 200));

      // 🚀 重构：主线程使用统一模板转换服务
      const finalComponentId =
        componentId || this.config.componentId || this.generateUniqueId();

      console.log(
        '📝 主线程调用 UnifiedTemplateService，componentId:',
        finalComponentId,
      );

      const templateConfig: UnifiedTemplateConfig = {
        componentId: finalComponentId,
        enableDebugLogs: true,
        enableEventHandling: true,
        enableFallback: true,
      };

      const conversionResult = convertTTML(ttml, templateConfig);

      console.log('📝 统一服务输出长度:', conversionResult.html?.length || 0);
      console.log(
        '📝 输出仍包含tt:for:',
        conversionResult.html?.includes('tt:for') || false,
      );
      console.log(
        '📝 输出仍包含{{}}:',
        conversionResult.html?.includes('{{') || false,
      );
      console.log(
        '📝 输出包含<div:',
        conversionResult.html?.includes('<div') || false,
      );
      console.log(
        '📝 输出包含React:',
        conversionResult.html?.includes('React') || false,
      );
      console.log('📝 转换成功:', conversionResult.success);
      console.log(
        '📝 输出预览:',
        conversionResult.html?.substring(0, 300) || 'EMPTY',
      );

      if (conversionResult.errors && conversionResult.errors.length > 0) {
        console.log('⚠️ 统一服务错误:', conversionResult.errors);
      }

      // 🔧 返回统一服务结果
      console.log('✅ [Parse5TTMLAdapter] 使用统一模板服务结果');

      return {
        jsx: conversionResult.html || ttml,
        ast: null, // 统一服务不需要AST
        elementCount: conversionResult.metadata.elementCount,
        errors: conversionResult.success
          ? []
          : [conversionResult.error || 'Unknown error'],
      };
    } catch (error) {
      console.error('❌ [Parse5TTMLAdapter] Transform失败:', error);
      errors.push(error instanceof Error ? error.message : String(error));

      return {
        jsx: `React.createElement('div', {className: 'ttml-error'}, "转换失败: ${error instanceof Error ? error.message : String(error)}")`,
        ast: null,
        elementCount: 0,
        errors,
      };
    }
  }

  /**
   * 使用Parse5解析TTML
   */
  private parseTTMLWithParse5(ttml: string): Parse5Node {
    try {
      // 验证输入
      if (!ttml || typeof ttml !== 'string') {
        throw new Error(
          `Invalid TTML for Parse5: expected string, got ${typeof ttml}`,
        );
      }

      console.log('🔧 [Parse5TTMLAdapter] 使用Parse5解析器处理TTML');

      // 预处理TTML使其符合HTML5标准
      const processedTtml = this.preprocessTTML(ttml);

      console.log(
        '🔄 [Parse5TTMLAdapter] 预处理完成，处理后内容长度:',
        processedTtml.length,
      );
      console.log(
        '📋 [Parse5TTMLAdapter] 预处理后内容预览:',
        processedTtml.substring(0, 400) +
          (processedTtml.length > 400 ? '...' : ''),
      );

      // 使用Parse5解析HTML5兼容的TTML
      const document = parse5.parse(processedTtml, {
        sourceCodeLocationInfo: true,
        onParseError: error => {
          console.warn('Parse5解析警告:', error);
        },
      });

      // 从document.childNodes中找到html节点，然后找到body中的ttml-root
      console.log('🔍 [Parse5TTMLAdapter] 开始遍历Parse5解析结果...');
      console.log(
        '📦 [Parse5TTMLAdapter] Document子节点数量:',
        document.childNodes?.length || 0,
      );

      const htmlNode = document.childNodes.find(
        (node: any) => node.nodeName === 'html',
      ) as any;

      if (!htmlNode) {
        console.error('❌ [Parse5TTMLAdapter] 无法找到HTML根节点');
        console.log(
          '🔍 [Parse5TTMLAdapter] 可用的顶级节点:',
          document.childNodes?.map((n: any) => n.nodeName) || [],
        );
        throw new Error('无法找到HTML根节点');
      }

      console.log(
        '✅ [Parse5TTMLAdapter] 找到HTML节点，子节点数量:',
        htmlNode.childNodes?.length || 0,
      );

      const bodyNode = htmlNode.childNodes.find(
        (node: any) => node.nodeName === 'body',
      ) as any;

      if (!bodyNode) {
        console.error('❌ [Parse5TTMLAdapter] 无法找到BODY节点');
        console.log(
          '🔍 [Parse5TTMLAdapter] HTML子节点:',
          htmlNode.childNodes?.map((n: any) => n.nodeName) || [],
        );
        throw new Error('无法找到BODY节点');
      }

      console.log(
        '✅ [Parse5TTMLAdapter] 找到BODY节点，子节点数量:',
        bodyNode.childNodes?.length || 0,
      );
      console.log(
        '🔍 [Parse5TTMLAdapter] BODY子节点列表:',
        bodyNode.childNodes?.map((n: any) => n.nodeName) || [],
      );

      const ttmlRoot = bodyNode.childNodes.find(
        (node: any) => node.nodeName === 'ttml-root',
      ) as any;

      if (ttmlRoot) {
        console.log(
          '✅ [Parse5TTMLAdapter] 找到TTML-ROOT节点，子节点数量:',
          ttmlRoot.childNodes?.length || 0,
        );
        console.log(
          '🔍 [Parse5TTMLAdapter] TTML-ROOT子节点列表:',
          ttmlRoot.childNodes?.map((n: any) => ({
            nodeName: n.nodeName,
            tagName: n.tagName,
            hasAttrs: !!n.attrs?.length,
          })) || [],
        );
        return ttmlRoot;
      } else {
        console.warn(
          '⚠️ [Parse5TTMLAdapter] 未找到TTML-ROOT节点，使用降级解析',
        );
        return this.fallbackParse(ttml);
      }
    } catch (error) {
      if (this.config.strictMode) {
        throw error;
      }

      // 宽松模式：降级处理
      console.warn('Parse5解析失败，使用降级模式:', error);
      return this.fallbackParse(ttml);
    }
  }

  /**
   * 创建简化的AST结构
   */
  private createSimplifiedAST(ttml: string): Parse5Node {
    // 简化的标签解析，安全地处理可能为undefined的输入
    const safeTtml = ttml || '';
    const cleanTtml = safeTtml.trim();

    // 创建虚拟根节点
    const root: Parse5Node = {
      nodeName: 'ttml-root',
      tagName: 'ttml-root',
      childNodes: [],
      attrs: [],
    };

    // 简化的文本节点处理
    if (cleanTtml) {
      const textNode: Parse5Node = {
        nodeName: '#text',
        value: cleanTtml,
        parentNode: root,
      };
      root.childNodes = [textNode];
    }

    return root;
  }

  /**
   * 预处理TTML
   */
  private preprocessTTML(ttml: string): string {
    // 1. 包装在有效的HTML结构中
    let processed = `<!DOCTYPE html>
<html>
<head><meta charset="UTF-8"></head>
<body>
<ttml-root>${ttml}</ttml-root>
</body>
</html>`;

    // 2. 处理TTML特殊语法，使其Parse5友好
    // 转换自闭合标签格式
    processed = processed.replace(
      /<(image|input|switch|slider|progress|web-view|canvas|cover-image|checkbox|radio)([^>]*?)(?:\s*\/)?>(?!\s*<\/\1>)/g,
      '<$1$2></$1>',
    );

    // 3. 处理模板语法中的特殊字符
    // 转义插值表达式中的HTML字符
    processed = processed.replace(/\{\{([^}]+)\}\}/g, (match, expr) => {
      const escaped = expr.replace(/</g, '&lt;').replace(/>/g, '&gt;');
      return `{{${escaped}}}`;
    });

    // 4. 处理指令属性中的表达式
    processed = processed.replace(/(lx:|tt:)[^=]*="[^"]*"/g, match =>
      match.replace(/</g, '&lt;').replace(/>/g, '&gt;'),
    );

    return processed;
  }

  /**
   * 降级解析器 - 增强版本，确保始终返回有效的AST结构
   */
  private fallbackParse(ttml: string): Parse5Node {
    console.log('🔧 [Parse5TTMLAdapter] 启动降级解析器');
    console.log('📄 [Parse5TTMLAdapter] 输入内容长度:', ttml?.length || 0);

    const safeTtml = ttml || '';

    try {
      // 尝试基础的标签解析
      const basicElements = this.parseBasicTTMLElements(safeTtml);

      if (basicElements.length > 0) {
        console.log(
          '✅ [Parse5TTMLAdapter] 降级解析成功，解析出',
          basicElements.length,
          '个元素',
        );
        return {
          nodeName: 'ttml-root',
          tagName: 'ttml-root',
          childNodes: basicElements,
          attrs: [],
        };
      }
    } catch (error) {
      console.warn('⚠️ [Parse5TTMLAdapter] 基础元素解析失败:', error);
    }

    // 最终降级：将所有内容作为文本处理
    console.log('🔚 [Parse5TTMLAdapter] 使用最终降级方案：纯文本处理');
    const textNode: Parse5Node = {
      nodeName: '#text',
      value: safeTtml.length > 0 ? safeTtml : '<!-- 空内容 -->',
      parentNode: undefined,
    };

    return {
      nodeName: 'ttml-root',
      tagName: 'ttml-root',
      childNodes: [textNode],
      attrs: [],
    };
  }

  /**
   * 解析基础TTML元素
   */
  private parseBasicTTMLElements(ttml: string): Parse5Node[] {
    const elements: Parse5Node[] = [];

    // 简单的标签匹配
    const tagRegex = /<(\/?[a-zA-Z][a-zA-Z0-9-]*)[^>]*>/g;
    let lastIndex = 0;
    let match;

    while ((match = tagRegex.exec(ttml)) !== null) {
      const tagName = match[1];

      // 跳过闭合标签
      if (tagName.startsWith('/')) {
        continue;
      }

      // 检查前面是否有文本内容
      const beforeText = ttml.slice(lastIndex, match.index).trim();
      if (beforeText) {
        elements.push({
          nodeName: '#text',
          value: beforeText,
        });
      }

      // 创建元素节点（简化版）
      const elementNode: Parse5Node = {
        nodeName: tagName,
        tagName,
        childNodes: [],
        attrs: this.parseBasicAttributes(match[0]),
      };

      elements.push(elementNode);
      lastIndex = match.index + match[0].length;
    }

    // 处理最后的文本内容
    const remainingText = ttml.slice(lastIndex).trim();
    if (remainingText) {
      elements.push({
        nodeName: '#text',
        value: remainingText,
      });
    }

    return elements;
  }

  /**
   * 解析基础属性
   */
  private parseBasicAttributes(
    tagHtml: string,
  ): Array<{ name: string; value: string }> {
    const attrs: Array<{ name: string; value: string }> = [];
    const attrRegex = /(\w+(?:-\w+)*)=["']([^"']*)["']/g;
    let match;

    while ((match = attrRegex.exec(tagHtml)) !== null) {
      attrs.push({
        name: match[1],
        value: match[2],
      });
    }

    return attrs;
  }

  /**
   * 转换Parse5节点为JSX
   */
  private transformNode(
    node: Parse5Node,
    context: TransformContext,
  ): JSXNode | JSXNode[] | null {
    if (!node) {
      return null;
    }

    // 只记录关键节点和根节点的转换
    const isRootOrCritical = node.nodeName === 'ttml-root' || context.isRoot;
    if (isRootOrCritical) {
      console.log(
        `🔄 [Parse5TTMLAdapter] 转换关键节点: ${node.nodeName}${node.tagName ? `(${node.tagName})` : ''}`,
      );
    }

    // 根据节点类型处理
    switch (node.nodeName) {
      case 'ttml-root':
        return this.transformRoot(node, context);
      case '#text':
        return this.transformText(node, context);
      case '#comment':
        return this.transformComment(node, context);
      default:
        // 元素节点
        if (node.tagName) {
          return this.transformElement(node, context);
        }
        return null;
    }
  }

  /**
   * 转换根节点
   */
  private transformRoot(node: Parse5Node, context: TransformContext): JSXNode {
    console.log(
      '🌲 [Parse5TTMLAdapter] 转换根节点，子节点数量:',
      node.childNodes?.length || 0,
    );

    const children = this.transformChildren(node, context);

    console.log(
      '✅ [Parse5TTMLAdapter] 根节点转换完成，有效子节点数量:',
      children.filter(Boolean).length,
    );

    return {
      type: 'JSXFragment',
      children: children.filter(Boolean),
    };
  }

  /**
   * 转换元素节点 - 增强映射规则版，集成综合Lynx映射
   */
  private transformElement(
    node: Parse5Node,
    context: TransformContext,
  ): JSXNode | null {
    const tagName = node.tagName!;
    const attributes = this.parseAttributes(node.attrs || []);

    // 检查是否为指令元素
    if (this.hasDirectives(attributes)) {
      return this.transformDirectiveElement(node, attributes, context);
    }

    // 优先使用综合Lynx映射系统
    const comprehensiveMapping = COMPREHENSIVE_LYNX_MAPPING[tagName];

    if (comprehensiveMapping) {
      if (this.config.debugMode) {
        console.log(
          `🎯 [Parse5TTMLAdapter] 使用综合映射: ${tagName} -> ${comprehensiveMapping.tag}`,
        );
      }
      return this.transformWithComprehensiveMapping(
        node,
        attributes,
        context,
        comprehensiveMapping,
      );
    }

    // 降级到基础映射
    const mapping = getElementMapping(tagName);

    if (!mapping) {
      // 使用fallback映射规则
      const fallbackMapping = FALLBACK_ELEMENT_MAPPING[tagName] || {
        tag: 'div',
        props: { className: `lynx-${tagName}`, 'data-original-tag': tagName },
        attributeMapping: COMMON_ATTRIBUTE_MAPPING,
      };

      if (this.config.debugMode) {
        console.warn(
          `⚠️ [Parse5TTMLAdapter] 使用fallback映射规则: ${tagName} -> ${fallbackMapping.tag}`,
        );
      }
      return this.transformMappedElement(
        node,
        attributes,
        context,
        fallbackMapping,
      );
    }

    // 记录映射信息用于调试
    if (this.config.debugMode) {
      console.log(
        `🔄 [Parse5TTMLAdapter] 元素映射: ${tagName} -> ${mapping.tag} (${mapping.props?.className || 'no-class'})`,
      );
    }

    // 验证映射规则的基本有效性
    if (!mapping.tag) {
      console.warn(
        `⚠️ [Parse5TTMLAdapter] 映射规则无效tag: ${tagName}, 使用div作为降级`,
      );
      mapping.tag = 'div';
    }

    return this.transformMappedElement(node, attributes, context, mapping);
  }

  /**
   * 使用综合Lynx映射转换元素
   */
  private transformWithComprehensiveMapping(
    node: Parse5Node,
    attributes: Record<string, string>,
    context: TransformContext,
    mapping: any,
  ): JSXNode {
    console.log(
      `🎯 [Parse5TTMLAdapter] 综合映射转换: ${node.tagName} -> ${mapping.tag}`,
    );

    // 处理综合映射的属性
    const jsxAttributes = this.transformComprehensiveAttributes(
      attributes,
      mapping,
      context,
    );
    const isSelfClosing =
      mapping.selfClosing || SELF_CLOSING_TAGS.has(node.tagName!);

    const jsxElement: JSXNode = {
      type: 'JSXElement',
      openingElement: {
        type: 'JSXOpeningElement',
        name: { type: 'JSXIdentifier', name: mapping.tag },
        attributes: jsxAttributes,
        selfClosing: isSelfClosing,
      },
    };

    if (!isSelfClosing) {
      jsxElement.children = this.transformChildren(node, context);
      jsxElement.closingElement = {
        type: 'JSXClosingElement',
        name: { type: 'JSXIdentifier', name: mapping.tag },
      };
    }

    return jsxElement;
  }

  /**
   * 转换综合映射的属性
   */
  private transformComprehensiveAttributes(
    attributes: Record<string, string>,
    mapping: any,
    context: TransformContext,
  ): any[] {
    const jsxAttributes: any[] = [];
    const classNames: string[] = []; // 收集所有class名称，统一处理

    // 1. 添加默认属性（特殊处理className）
    if (mapping.props) {
      Object.entries(mapping.props).forEach(([key, value]) => {
        if (key === 'className' && typeof value === 'string') {
          // className 特殊处理，收集到 classNames 数组
          classNames.push(value);
        } else {
          // 其他属性直接添加
          jsxAttributes.push({
            type: 'JSXAttribute',
            name: { type: 'JSXIdentifier', name: key },
            value:
              typeof value === 'string'
                ? { type: 'Literal', value }
                : { type: 'JSXExpressionContainer', expression: value },
          });
        }
      });
    }

    // 2. 处理用户属性
    Object.entries(attributes).forEach(([name, value]) => {
      // 跳过指令属性
      if (this.isDirectiveAttribute(name)) {
        return;
      }

      // 特殊处理class属性 - 收集到classNames数组中
      if (name === 'class') {
        if (value && value.trim()) {
          classNames.push(value.trim());
        }
        return;
      }

      // 处理事件映射
      if (mapping.eventMapping?.[name]) {
        const reactEvent = mapping.eventMapping[name];
        const eventAttr = this.createReactEventAttribute(
          reactEvent,
          value,
          context,
        );
        if (eventAttr) {
          jsxAttributes.push(eventAttr);
        }
        return;
      }

      // 处理属性映射
      if (mapping.attributeMapping?.[name]) {
        const mappedResult = mapping.attributeMapping[name];

        if (typeof mappedResult === 'function') {
          // 函数类型映射，执行转换
          const transformed = mappedResult(value, name);
          if (transformed && typeof transformed === 'object') {
            Object.entries(transformed).forEach(([key, val]) => {
              jsxAttributes.push(this.createJSXAttribute(key, val, context));
            });
          } else {
            jsxAttributes.push(
              this.createJSXAttribute(mappedResult, value, context),
            );
          }
        } else {
          // 字符串类型映射
          jsxAttributes.push(
            this.createJSXAttribute(mappedResult, value, context),
          );
        }
        return;
      }

      // 处理事件绑定
      if (name.startsWith('bind') || name.startsWith('catch:')) {
        const eventAttr = this.transformEventAttribute(name, value, context);
        if (eventAttr) {
          jsxAttributes.push(eventAttr);
        }
        return;
      }

      // 普通属性映射（不包括class，已经单独处理）
      const attr = this.createJSXAttribute(name, value, context);
      if (attr) {
        jsxAttributes.push(attr);
      }
    });

    // 3. 添加映射的CSS类
    if (mapping.cssClasses && mapping.cssClasses.length > 0) {
      classNames.push(...mapping.cssClasses);
    }

    // 4. 统一处理className属性 - 去重并合并
    if (classNames.length > 0) {
      // 去重并过滤空值
      const uniqueClasses = [
        ...new Set(classNames.filter(cls => cls && cls.trim())),
      ];
      const finalClassName = uniqueClasses.join(' ').trim();

      if (finalClassName) {
        jsxAttributes.push({
          type: 'JSXAttribute',
          name: { type: 'JSXIdentifier', name: 'className' },
          value: { type: 'Literal', value: finalClassName },
        });
      }
    }

    // 5. 添加组件作用域
    if (this.config.enableScope) {
      jsxAttributes.push({
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: `data-v-${context.componentId}` },
        value: { type: 'Literal', value: '' },
      });
    }

    // 🔧 修复：确保属性合并，防止重复的 className
    return this.mergeJSXAttributes(jsxAttributes);
  }

  /**
   * 创建React事件属性
   */
  private createReactEventAttribute(
    reactEvent: string,
    handler: string,
    context: TransformContext,
  ): any {
    return {
      type: 'JSXAttribute',
      name: { type: 'JSXIdentifier', name: reactEvent },
      value: {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'ArrowFunctionExpression',
          params: [{ type: 'Identifier', name: 'e' }],
          body: {
            type: 'BlockStatement',
            body: [
              {
                type: 'ExpressionStatement',
                expression: {
                  type: 'CallExpression',
                  callee: this.parseExpression(handler, context),
                  arguments: [{ type: 'Identifier', name: 'e' }],
                },
              },
            ],
          },
        },
      },
    };
  }

  /**
   * 转换映射的元素
   */
  private transformMappedElement(
    node: Parse5Node,
    attributes: Record<string, string>,
    context: TransformContext,
    mapping: any,
  ): JSXNode {
    const jsxAttributes = this.transformAttributes(
      attributes,
      mapping,
      context,
    );
    const isSelfClosing =
      mapping.selfClosing || SELF_CLOSING_TAGS.has(node.tagName!);

    const jsxElement: JSXNode = {
      type: 'JSXElement',
      openingElement: {
        type: 'JSXOpeningElement',
        name: { type: 'JSXIdentifier', name: mapping.tag },
        attributes: jsxAttributes,
        selfClosing: isSelfClosing,
      },
    };

    if (!isSelfClosing) {
      jsxElement.children = this.transformChildren(node, context);
      jsxElement.closingElement = {
        type: 'JSXClosingElement',
        name: { type: 'JSXIdentifier', name: mapping.tag },
      };
    }

    return jsxElement;
  }

  /**
   * 转换通用元素
   */
  private transformGenericElement(
    node: Parse5Node,
    attributes: Record<string, string>,
    context: TransformContext,
    defaultTag = 'div',
  ): JSXNode {
    const jsxAttributes = this.transformGenericAttributes(attributes, context);

    return {
      type: 'JSXElement',
      openingElement: {
        type: 'JSXOpeningElement',
        name: { type: 'JSXIdentifier', name: defaultTag },
        attributes: jsxAttributes,
        selfClosing: false,
      },
      children: this.transformChildren(node, context),
      closingElement: {
        type: 'JSXClosingElement',
        name: { type: 'JSXIdentifier', name: defaultTag },
      },
    };
  }

  /**
   * 转换指令元素
   */
  private transformDirectiveElement(
    node: Parse5Node,
    attributes: Record<string, string>,
    context: TransformContext,
  ): JSXNode | null {
    // 处理条件渲染
    if (attributes['lx:if'] || attributes['tt:if']) {
      return this.transformConditionalElement(node, attributes, context);
    }

    // 处理列表渲染
    if (attributes['lx:for'] || attributes['tt:for']) {
      return this.transformForElement(node, attributes, context);
    }

    // 移除指令属性后正常转换
    const cleanAttributes = this.removeDirectiveAttributes(attributes);

    // 递归转换为普通元素
    const cleanNode = {
      ...node,
      attrs: this.attributesToParse5Format(cleanAttributes),
    };
    return this.transformElement(cleanNode, context);
  }

  /**
   * 转换条件渲染元素 - 企业级增强版
   */
  private transformConditionalElement(
    node: Parse5Node,
    attributes: Record<string, string>,
    context: TransformContext,
  ): JSXNode {
    console.log('🎯 [Parse5TTMLAdapter] ==> 条件渲染转换开始 <==');

    const condition = attributes['lx:if'] || attributes['tt:if'];

    if (!condition) {
      throw new Error('条件渲染缺少条件表达式');
    }

    console.log(`🔍 [Parse5TTMLAdapter] 条件表达式: ${condition}`);
    console.log(`🏷️ [Parse5TTMLAdapter] 目标元素: ${node.tagName}`);

    // 检查是否有链式条件（elif, else）
    const hasElif = attributes['lx:elif'] || attributes['tt:elif'];
    const hasElse = attributes['lx:else'] || attributes['tt:else'];

    if (hasElif || hasElse) {
      console.log('🔗 [Parse5TTMLAdapter] 检测到链式条件渲染，使用高级处理');
      return this.transformConditionalChain(node, attributes, context);
    }

    // 处理简单条件渲染
    const cleanAttributes = this.removeDirectiveAttributes(attributes);
    const element = this.transformElement(
      {
        ...node,
        attrs: this.attributesToParse5Format(cleanAttributes),
      },
      context,
    );

    const result = {
      type: 'JSXExpressionContainer',
      expression: {
        type: 'LogicalExpression',
        operator: '&&',
        left: this.parseExpression(condition, context),
        right: element,
      },
    };

    console.log(
      `✅ [Parse5TTMLAdapter] 条件渲染转换完成: ${condition} && <${node.tagName}>`,
    );

    return result;
  }

  /**
   * 转换链式条件渲染（if-elif-else）
   */
  private transformConditionalChain(
    node: Parse5Node,
    attributes: Record<string, string>,
    context: TransformContext,
  ): JSXNode {
    console.log('🔗 [Parse5TTMLAdapter] 处理链式条件渲染');

    const ifCondition = attributes['lx:if'] || attributes['tt:if'];
    const elifCondition = attributes['lx:elif'] || attributes['tt:elif'];
    const hasElse = attributes['lx:else'] || attributes['tt:else'];

    const cleanAttributes = this.removeDirectiveAttributes(attributes);
    const element = this.transformElement(
      {
        ...node,
        attrs: this.attributesToParse5Format(cleanAttributes),
      },
      context,
    );

    if (elifCondition) {
      // elif 分支
      console.log(`🔀 [Parse5TTMLAdapter] elif条件: ${elifCondition}`);
      return {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'ConditionalExpression',
          test: this.parseExpression(elifCondition, context),
          consequent: element,
          alternate: { type: 'Literal', value: null },
        },
      };
    } else if (hasElse) {
      // else 分支
      console.log('🔚 [Parse5TTMLAdapter] else分支');
      return element;
    } else if (ifCondition) {
      // if 分支（链式中的主条件）
      console.log(`🎯 [Parse5TTMLAdapter] 主if条件: ${ifCondition}`);
      return {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'LogicalExpression',
          operator: '&&',
          left: this.parseExpression(ifCondition, context),
          right: element,
        },
      };
    }

    return element;
  }

  /**
   * 转换列表渲染元素 - 企业级增强版
   */
  private transformForElement(
    node: Parse5Node,
    attributes: Record<string, string>,
    context: TransformContext,
  ): JSXNode {
    console.log('🔁 [Parse5TTMLAdapter] ==> 列表渲染转换开始 <==');

    const forValue = attributes['lx:for'] || attributes['tt:for'];
    const keyValue = attributes['lx:key'] || attributes['tt:key'];

    if (!forValue) {
      throw new Error('列表渲染缺少循环表达式');
    }

    console.log(`🔍 [Parse5TTMLAdapter] 循环表达式: ${forValue}`);
    console.log(`🔑 [Parse5TTMLAdapter] Key表达式: ${keyValue || '无'}`);
    console.log(`🏷️ [Parse5TTMLAdapter] 目标元素: ${node.tagName}`);

    const { itemName, indexName, listExpression } =
      this.parseForExpression(forValue);

    console.log('📋 [Parse5TTMLAdapter] 解析结果:', {
      itemName,
      indexName,
      listExpression,
    });

    // 创建新的作用域上下文
    const newScope = {
      ...context.scope,
      [itemName]: true,
      [indexName]: true,
    };

    const cleanAttributes = this.removeDirectiveAttributes(attributes);

    // 添加 key 属性
    if (keyValue) {
      cleanAttributes.key = keyValue;
      console.log(`🔑 [Parse5TTMLAdapter] 添加React key属性: ${keyValue}`);
    } else {
      // 如果没有提供key，使用索引作为默认key
      cleanAttributes.key = `{${indexName}}`;
      console.log(`🔑 [Parse5TTMLAdapter] 使用默认索引key: ${indexName}`);
    }

    const element = this.transformElement(
      {
        ...node,
        attrs: this.attributesToParse5Format(cleanAttributes),
      },
      {
        ...context,
        scope: newScope,
      },
    );

    const result = {
      type: 'JSXExpressionContainer',
      expression: {
        type: 'CallExpression',
        callee: {
          type: 'MemberExpression',
          object: this.parseExpression(listExpression, context),
          property: { type: 'Identifier', name: 'map' },
        },
        arguments: [
          {
            type: 'ArrowFunctionExpression',
            params: [
              { type: 'Identifier', name: itemName },
              { type: 'Identifier', name: indexName },
            ],
            body: element,
          },
        ],
      },
    };

    console.log(
      `✅ [Parse5TTMLAdapter] 列表渲染转换完成: ${listExpression}.map((${itemName}, ${indexName}) => <${node.tagName}>)`,
    );

    return result;
  }

  /**
   * 转换文本节点 - 支持插值表达式
   */
  private transformText(
    node: Parse5Node,
    context: TransformContext,
  ): JSXNode | JSXNode[] | null {
    // 安全地处理可能为undefined的node.value
    const rawContent = node.value || '';
    const content = rawContent.trim();

    if (!content) {
      return null;
    }

    console.log(`📝 [Parse5TTMLAdapter] 处理文本内容: "${content}"`);

    // 检查是否包含插值表达式
    if (content.includes('{{') && content.includes('}}')) {
      console.log('🔍 [Parse5TTMLAdapter] 检测到插值表达式，开始解析');
      return this.parseInterpolatedText(content, context);
    }

    // 普通文本节点
    console.log('📄 [Parse5TTMLAdapter] 普通文本节点');
    return {
      type: 'JSXText',
      value: content,
    };
  }

  /**
   * 解析包含插值表达式的文本
   */
  private parseInterpolatedText(
    text: string,
    context: TransformContext,
  ): JSXNode | JSXNode[] {
    console.log(`🔍 [Parse5TTMLAdapter] 解析插值文本: "${text}"`);

    // 分割文本和插值表达式
    const parts: JSXNode[] = [];
    let lastIndex = 0;
    const interpolationRegex = /\{\{([^}]+)\}\}/g;
    let match;

    while ((match = interpolationRegex.exec(text)) !== null) {
      const beforeText = text.slice(lastIndex, match.index).trim();
      const expression = match[1].trim();

      console.log(`  📄 [Parse5TTMLAdapter] 文本部分: "${beforeText}"`);
      console.log(`  🔧 [Parse5TTMLAdapter] 表达式部分: "${expression}"`);

      // 添加前面的文本部分
      if (beforeText) {
        parts.push({
          type: 'JSXText',
          value: beforeText,
        });
      }

      // 添加插值表达式
      parts.push({
        type: 'JSXExpressionContainer',
        expression: this.parseExpression(expression, context),
      });

      lastIndex = match.index + match[0].length;
    }

    // 添加剩余的文本部分
    const remainingText = text.slice(lastIndex).trim();
    if (remainingText) {
      console.log(`  📄 [Parse5TTMLAdapter] 剩余文本: "${remainingText}"`);
      parts.push({
        type: 'JSXText',
        value: remainingText,
      });
    }

    console.log(`✅ [Parse5TTMLAdapter] 插值解析完成，共${parts.length}个部分`);

    // 如果只有一个部分，直接返回该部分
    if (parts.length === 1) {
      return parts[0];
    }

    // 多个部分返回数组
    return parts;
  }

  /**
   * 计算AST中的元素数量
   */
  private countElements(node: Parse5Node): number {
    let count = 0;

    if (node.tagName) {
      count = 1;
    }

    if (node.childNodes) {
      for (const child of node.childNodes) {
        count += this.countElements(child);
      }
    }

    return count;
  }

  /**
   * 转换插值文本
   */
  private transformInterpolatedText(
    content: string,
    context: TransformContext,
  ): JSXNode {
    // 简单实现：如果是纯插值表达式，转换为表达式容器
    const mustacheMatch = content.match(/^\{\{(.+)\}\}$/);
    if (mustacheMatch) {
      return {
        type: 'JSXExpressionContainer',
        expression: this.parseExpression(mustacheMatch[1], context),
      };
    }

    // 复杂模板字符串（混合文本和插值）
    // 这里简化处理，实际需要更复杂的解析
    return {
      type: 'JSXText',
      value: content,
    };
  }

  /**
   * 转换注释节点
   */
  private transformComment(
    node: Parse5Node,
    context: TransformContext,
  ): JSXNode {
    return {
      type: 'JSXComment',
      value: node.value || '',
    };
  }

  /**
   * 转换子节点
   */
  private transformChildren(
    node: Parse5Node,
    context: TransformContext,
  ): JSXNode[] {
    if (!node.childNodes) {
      return [];
    }

    const transformedChildren = node.childNodes
      .map(child => {
        const result = this.transformNode(child, { ...context, isRoot: false });
        return result;
      })
      .filter(Boolean)
      .flat() as JSXNode[];

    // 只在处理大量子节点时记录
    if (node.childNodes.length > 10) {
      console.log(
        `👥 [Parse5TTMLAdapter] 批量转换 ${node.childNodes.length} 个子节点 -> ${transformedChildren.length} 个有效节点`,
      );
    }

    return transformedChildren;
  }

  /**
   * 合并JSX属性，防止重复
   */
  private mergeJSXAttributes(attributes: any[]): any[] {
    const attributeMap = new Map<string, any>();
    const classNames: string[] = [];

    attributes.forEach(attr => {
      const { name } = attr.name;

      if (name === 'class' || name === 'className') {
        // 收集所有 class 值
        if (attr.value.type === 'Literal') {
          classNames.push(attr.value.value);
        }
      } else {
        // 其他属性直接覆盖（后面的覆盖前面的）
        attributeMap.set(name, attr);
      }
    });

    // 合并所有 class 为单一 className 属性
    if (classNames.length > 0) {
      const uniqueClasses = [
        ...new Set(classNames.filter(cls => cls && cls.trim())),
      ];
      if (uniqueClasses.length > 0) {
        attributeMap.set('className', {
          type: 'JSXAttribute',
          name: { type: 'JSXIdentifier', name: 'className' },
          value: { type: 'Literal', value: uniqueClasses.join(' ') },
        });
      }
    }

    return Array.from(attributeMap.values());
  }

  /**
   * 转换属性
   */
  private transformAttributes(
    attributes: Record<string, string>,
    mapping: any,
    context: TransformContext,
  ): any[] {
    const jsxAttributes: any[] = [];

    // 添加默认属性
    if (mapping.props) {
      Object.entries(mapping.props).forEach(([key, value]) => {
        jsxAttributes.push({
          type: 'JSXAttribute',
          name: { type: 'JSXIdentifier', name: key },
          value:
            typeof value === 'string'
              ? { type: 'Literal', value }
              : { type: 'JSXExpressionContainer', expression: value },
        });
      });
    }

    // 转换用户属性
    Object.entries(attributes).forEach(([name, value]) => {
      // 跳过指令属性（已在上层处理）
      if (this.isDirectiveAttribute(name)) {
        return;
      }

      // 处理事件属性
      if (name.startsWith('bind') || name.startsWith('catch:')) {
        const eventAttr = this.transformEventAttribute(name, value, context);
        if (eventAttr) {
          jsxAttributes.push(eventAttr);
        }
        return;
      }

      // 普通属性转换
      const transformedAttr = this.transformAttribute(
        name,
        value,
        mapping,
        context,
      );
      if (transformedAttr) {
        jsxAttributes.push(transformedAttr);
      }
    });

    // 添加组件作用域属性
    if (this.config.enableScope) {
      jsxAttributes.push({
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: `data-v-${context.componentId}` },
        value: { type: 'Literal', value: '' },
      });
    }

    // 合并属性以防止重复
    return this.mergeJSXAttributes(jsxAttributes);
  }

  /**
   * 转换通用属性
   */
  private transformGenericAttributes(
    attributes: Record<string, string>,
    context: TransformContext,
  ): any[] {
    const jsxAttributes: any[] = [];

    Object.entries(attributes).forEach(([name, value]) => {
      if (this.isDirectiveAttribute(name) || this.isEventAttribute(name)) {
        return;
      }

      const mappedName = COMMON_ATTRIBUTE_MAPPING[name] || name;
      const transformedAttr = this.createJSXAttribute(
        mappedName,
        value,
        context,
      );
      if (transformedAttr) {
        jsxAttributes.push(transformedAttr);
      }
    });

    // 🔧 修复：确保属性合并，防止重复
    return this.mergeJSXAttributes(jsxAttributes);
  }

  /**
   * 转换单个属性
   */
  private transformAttribute(
    name: string,
    value: string,
    mapping: any,
    context: TransformContext,
  ): any {
    // 优先使用元素特定的属性映射，然后使用通用映射
    let mappedName = name;

    // 1. 检查元素特定的属性映射
    if (mapping.attributeMapping?.[name]) {
      mappedName = mapping.attributeMapping[name];
    }
    // 2. 检查通用属性映射
    else if (COMMON_ATTRIBUTE_MAPPING[name]) {
      mappedName = COMMON_ATTRIBUTE_MAPPING[name];
    }
    // 3. 处理特殊属性转换（如rpx到vw）
    else if (name === 'style' && value.includes('rpx')) {
      return this.createJSXAttribute(
        'style',
        this.convertRpxInStyle(value),
        context,
      );
    }
    // 4. 处理hover相关属性
    else if (name.startsWith('hover-')) {
      mappedName = `data-${name}`;
    }
    // 5. 保持原始属性名（添加data-前缀以避免React警告）
    else if (!this.isValidReactAttribute(name)) {
      mappedName = `data-${name}`;
    }

    return this.createJSXAttribute(mappedName, value, context);
  }

  /**
   * 检查是否为有效的React属性
   */
  private isValidReactAttribute(name: string): boolean {
    const validReactAttributes = [
      'className',
      'style',
      'onClick',
      'onChange',
      'onInput',
      'onFocus',
      'onBlur',
      'id',
      'key',
      'ref',
      'title',
      'tabIndex',
      'role',
      'aria-label',
      'src',
      'alt',
      'width',
      'height',
      'href',
      'target',
      'type',
      'value',
      'placeholder',
      'disabled',
      'checked',
      'selected',
      'hidden',
      'readOnly',
      'autoFocus',
      'multiple',
    ];
    return (
      validReactAttributes.includes(name) ||
      name.startsWith('aria-') ||
      name.startsWith('data-')
    );
  }

  /**
   * 转换样式中的rpx单位
   */
  private convertRpxInStyle(styleValue: string): string {
    return styleValue.replace(/(\d+(?:\.\d+)?)rpx/g, (match, num) => {
      const rpxValue = parseFloat(num);
      const vwValue = ((rpxValue / 750) * 100).toFixed(6);
      return `${vwValue}vw`;
    });
  }

  /**
   * 创建JSX属性
   */
  private createJSXAttribute(
    name: string,
    value: string,
    context: TransformContext,
  ): any {
    // 插值表达式处理
    if (value.includes('{{') && value.includes('}}')) {
      return {
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name },
        value: {
          type: 'JSXExpressionContainer',
          expression: this.parseTemplateString(value, context),
        },
      };
    }

    // 普通字符串值
    return {
      type: 'JSXAttribute',
      name: { type: 'JSXIdentifier', name },
      value: { type: 'Literal', value },
    };
  }

  /**
   * 转换事件属性
   */
  private transformEventAttribute(
    eventName: string,
    handlerExpression: string,
    context: TransformContext,
  ): any {
    const eventConfig = EVENT_DIRECTIVE_MAPPING[eventName];
    if (!eventConfig) {
      console.warn(`未知事件: ${eventName}`);
      return null;
    }

    console.log(
      `🎯 [Parse5TTMLAdapter] 转换事件: ${eventName} -> ${eventConfig.reactEvent}${eventConfig.special ? ` (${eventConfig.special})` : ''}`,
    );

    const eventHandlerBody = [];

    // 阻止事件传播
    if (eventConfig.stopPropagation) {
      eventHandlerBody.push({
        type: 'ExpressionStatement',
        expression: {
          type: 'CallExpression',
          callee: {
            type: 'MemberExpression',
            object: { type: 'Identifier', name: 'e' },
            property: { type: 'Identifier', name: 'stopPropagation' },
          },
          arguments: [],
        },
      });
    }

    // 特殊事件处理
    if (eventConfig.special) {
      const specialHandling = this.generateSpecialEventHandling(
        eventConfig.special,
        eventName,
      );
      eventHandlerBody.push(...specialHandling);
    }

    // 执行用户处理器
    eventHandlerBody.push({
      type: 'ExpressionStatement',
      expression: {
        type: 'CallExpression',
        callee: this.parseExpression(handlerExpression, context),
        arguments: [{ type: 'Identifier', name: 'e' }],
      },
    });

    return {
      type: 'JSXAttribute',
      name: { type: 'JSXIdentifier', name: eventConfig.reactEvent },
      value: {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'ArrowFunctionExpression',
          params: [{ type: 'Identifier', name: 'e' }],
          body: {
            type: 'BlockStatement',
            body: eventHandlerBody,
          },
        },
      },
    };
  }

  /**
   * 生成特殊事件处理逻辑
   */
  private generateSpecialEventHandling(
    special: string,
    eventName: string,
  ): any[] {
    console.log(
      `🔧 [Parse5TTMLAdapter] 生成特殊事件处理: ${special} for ${eventName}`,
    );

    switch (special) {
      case 'upper':
        // 滚动到顶部/左边的检测逻辑
        return [
          {
            type: 'IfStatement',
            test: {
              type: 'LogicalExpression',
              operator: '<=',
              left: {
                type: 'MemberExpression',
                object: {
                  type: 'MemberExpression',
                  object: { type: 'Identifier', name: 'e' },
                  property: { type: 'Identifier', name: 'target' },
                },
                property: { type: 'Identifier', name: 'scrollTop' },
              },
              right: { type: 'Literal', value: 50 },
            },
            consequent: {
              type: 'BlockStatement',
              body: [
                {
                  type: 'ExpressionStatement',
                  expression: {
                    type: 'CallExpression',
                    callee: {
                      type: 'MemberExpression',
                      object: { type: 'Identifier', name: 'console' },
                      property: { type: 'Identifier', name: 'log' },
                    },
                    arguments: [
                      { type: 'Literal', value: 'Scrolled to upper threshold' },
                    ],
                  },
                },
              ],
            },
          },
        ];

      case 'lower':
        // 滚动到底部/右边的检测逻辑
        return [
          {
            type: 'IfStatement',
            test: {
              type: 'LogicalExpression',
              operator: '>=',
              left: {
                type: 'BinaryExpression',
                operator: '+',
                left: {
                  type: 'MemberExpression',
                  object: {
                    type: 'MemberExpression',
                    object: { type: 'Identifier', name: 'e' },
                    property: { type: 'Identifier', name: 'target' },
                  },
                  property: { type: 'Identifier', name: 'scrollTop' },
                },
                right: {
                  type: 'MemberExpression',
                  object: {
                    type: 'MemberExpression',
                    object: { type: 'Identifier', name: 'e' },
                    property: { type: 'Identifier', name: 'target' },
                  },
                  property: { type: 'Identifier', name: 'clientHeight' },
                },
              },
              right: {
                type: 'BinaryExpression',
                operator: '-',
                left: {
                  type: 'MemberExpression',
                  object: {
                    type: 'MemberExpression',
                    object: { type: 'Identifier', name: 'e' },
                    property: { type: 'Identifier', name: 'target' },
                  },
                  property: { type: 'Identifier', name: 'scrollHeight' },
                },
                right: { type: 'Literal', value: 50 },
              },
            },
            consequent: {
              type: 'BlockStatement',
              body: [
                {
                  type: 'ExpressionStatement',
                  expression: {
                    type: 'CallExpression',
                    callee: {
                      type: 'MemberExpression',
                      object: { type: 'Identifier', name: 'console' },
                      property: { type: 'Identifier', name: 'log' },
                    },
                    arguments: [
                      { type: 'Literal', value: 'Scrolled to lower threshold' },
                    ],
                  },
                },
              ],
            },
          },
        ];

      case 'appear':
        // 元素进入可视区域的检测逻辑
        return [
          {
            type: 'ExpressionStatement',
            expression: {
              type: 'CallExpression',
              callee: {
                type: 'MemberExpression',
                object: { type: 'Identifier', name: 'console' },
                property: { type: 'Identifier', name: 'log' },
              },
              arguments: [
                { type: 'Literal', value: 'Element appeared in viewport' },
              ],
            },
          },
          {
            type: 'IfStatement',
            test: {
              type: 'LogicalExpression',
              operator: '&&',
              left: {
                type: 'MemberExpression',
                object: { type: 'Identifier', name: 'window' },
                property: { type: 'Identifier', name: 'IntersectionObserver' },
              },
              right: {
                type: 'MemberExpression',
                object: {
                  type: 'MemberExpression',
                  object: { type: 'Identifier', name: 'e' },
                  property: { type: 'Identifier', name: 'target' },
                },
                property: { type: 'Identifier', name: 'dataset' },
              },
            },
            consequent: {
              type: 'BlockStatement',
              body: [
                {
                  type: 'ExpressionStatement',
                  expression: {
                    type: 'AssignmentExpression',
                    operator: '=',
                    left: {
                      type: 'MemberExpression',
                      object: {
                        type: 'MemberExpression',
                        object: {
                          type: 'MemberExpression',
                          object: { type: 'Identifier', name: 'e' },
                          property: { type: 'Identifier', name: 'target' },
                        },
                        property: { type: 'Identifier', name: 'dataset' },
                      },
                      property: { type: 'Identifier', name: 'appearObserved' },
                    },
                    right: { type: 'Literal', value: 'true' },
                  },
                },
              ],
            },
          },
        ];

      case 'disappear':
        // 元素离开可视区域的检测逻辑
        return [
          {
            type: 'ExpressionStatement',
            expression: {
              type: 'CallExpression',
              callee: {
                type: 'MemberExpression',
                object: { type: 'Identifier', name: 'console' },
                property: { type: 'Identifier', name: 'log' },
              },
              arguments: [
                { type: 'Literal', value: 'Element disappeared from viewport' },
              ],
            },
          },
          {
            type: 'IfStatement',
            test: {
              type: 'MemberExpression',
              object: {
                type: 'MemberExpression',
                object: {
                  type: 'MemberExpression',
                  object: { type: 'Identifier', name: 'e' },
                  property: { type: 'Identifier', name: 'target' },
                },
                property: { type: 'Identifier', name: 'dataset' },
              },
              property: { type: 'Identifier', name: 'appearObserved' },
            },
            consequent: {
              type: 'BlockStatement',
              body: [
                {
                  type: 'ExpressionStatement',
                  expression: {
                    type: 'AssignmentExpression',
                    operator: '=',
                    left: {
                      type: 'MemberExpression',
                      object: {
                        type: 'MemberExpression',
                        object: {
                          type: 'MemberExpression',
                          object: { type: 'Identifier', name: 'e' },
                          property: { type: 'Identifier', name: 'target' },
                        },
                        property: { type: 'Identifier', name: 'dataset' },
                      },
                      property: { type: 'Identifier', name: 'appearObserved' },
                    },
                    right: { type: 'Literal', value: 'false' },
                  },
                },
              ],
            },
          },
        ];

      default:
        console.warn(`未知的特殊事件类型: ${special}`);
        return [];
    }
  }

  // ===============================
  // 工具方法
  // ===============================

  /**
   * 解析Parse5属性为对象
   */
  private parseAttributes(
    attrs: Array<{ name: string; value: string }>,
  ): Record<string, string> {
    const attributes: Record<string, string> = {};
    attrs.forEach(attr => {
      attributes[attr.name] = attr.value;
    });
    return attributes;
  }

  /**
   * 检查是否有指令属性
   */
  private hasDirectives(attributes: Record<string, string>): boolean {
    return Object.keys(attributes).some(name =>
      this.isDirectiveAttribute(name),
    );
  }

  /**
   * 检查是否为指令属性
   */
  private isDirectiveAttribute(name: string): boolean {
    return name.startsWith('lx:') || name.startsWith('tt:');
  }

  /**
   * 检查是否为事件属性
   */
  private isEventAttribute(name: string): boolean {
    return name.startsWith('bind') || name.startsWith('catch:');
  }

  /**
   * 移除指令属性
   */
  private removeDirectiveAttributes(
    attributes: Record<string, string>,
  ): Record<string, string> {
    const clean: Record<string, string> = {};
    Object.entries(attributes).forEach(([name, value]) => {
      if (!this.isDirectiveAttribute(name)) {
        clean[name] = value;
      }
    });
    return clean;
  }

  /**
   * 转换属性为Parse5格式
   */
  private attributesToParse5Format(
    attributes: Record<string, string>,
  ): Array<{ name: string; value: string }> {
    return Object.entries(attributes).map(([name, value]) => ({ name, value }));
  }

  /**
   * 在节点树中查找指定名称的节点
   */
  private findNodeByName(node: Parse5Node, name: string): Parse5Node | null {
    if (node.nodeName === name || node.tagName === name) {
      return node;
    }

    if (node.childNodes) {
      for (const child of node.childNodes) {
        const found = this.findNodeByName(child, name);
        if (found) {
          return found;
        }
      }
    }

    return null;
  }

  /**
   * 解析表达式 - 增强版，支持更多复杂表达式
   */
  private parseExpression(expression: string, context: TransformContext): any {
    const safeExpression = expression || '';
    const trimmed = safeExpression.trim();

    console.log(`🔍 [Parse5TTMLAdapter] 解析表达式: "${trimmed}"`);

    // 🔧 修复：处理条件表达式 (三元运算符) - 使用正则表达式
    const ternaryRegex = /^(.+?)\s*\?\s*(.+?)\s*:\s*(.+)$/;
    const ternaryMatch = trimmed.match(ternaryRegex);
    if (ternaryMatch) {
      const [, test, consequent, alternate] = ternaryMatch;
      return {
        type: 'ConditionalExpression',
        test: this.parseExpression(test.trim(), context),
        consequent: this.parseExpression(consequent.trim(), context),
        alternate: this.parseExpression(alternate.trim(), context),
      };
    }

    // 🔧 修复：处理比较运算符
    const comparisonOps = ['===', '!==', '==', '!=', '<=', '>=', '<', '>'];
    for (const op of comparisonOps) {
      if (trimmed.includes(op)) {
        const parts = trimmed.split(op);
        if (parts.length === 2) {
          return {
            type: 'BinaryExpression',
            operator: op,
            left: this.parseExpression(parts[0].trim(), context),
            right: this.parseExpression(parts[1].trim(), context),
          };
        }
      }
    }

    // 🔧 修复：处理算术运算符
    const arithmeticOps = ['%', '+', '-', '*', '/'];
    for (const op of arithmeticOps) {
      if (trimmed.includes(op)) {
        const parts = trimmed.split(op);
        if (parts.length === 2) {
          return {
            type: 'BinaryExpression',
            operator: op,
            left: this.parseExpression(parts[0].trim(), context),
            right: this.parseExpression(parts[1].trim(), context),
          };
        }
      }
    }

    // 🔧 修复：处理逻辑运算符
    if (trimmed.includes('&&')) {
      const parts = trimmed.split('&&');
      if (parts.length === 2) {
        return {
          type: 'LogicalExpression',
          operator: '&&',
          left: this.parseExpression(parts[0].trim(), context),
          right: this.parseExpression(parts[1].trim(), context),
        };
      }
    }

    if (trimmed.includes('||')) {
      const parts = trimmed.split('||');
      if (parts.length === 2) {
        return {
          type: 'LogicalExpression',
          operator: '||',
          left: this.parseExpression(parts[0].trim(), context),
          right: this.parseExpression(parts[1].trim(), context),
        };
      }
    }

    // 简单变量
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(trimmed)) {
      return { type: 'Identifier', name: trimmed };
    }

    // 属性访问
    if (trimmed.includes('.')) {
      const parts = trimmed.split('.');
      let expr = { type: 'Identifier', name: parts[0] };

      for (let i = 1; i < parts.length; i++) {
        expr = {
          type: 'MemberExpression',
          object: expr,
          property: { type: 'Identifier', name: parts[i] },
        };
      }

      return expr;
    }

    // 字面量
    if (/^['"].*['"]$/.test(trimmed)) {
      return { type: 'Literal', value: trimmed.slice(1, -1) };
    }

    if (/^\d+$/.test(trimmed)) {
      return { type: 'Literal', value: parseInt(trimmed) };
    }

    if (/^\d+\.\d+$/.test(trimmed)) {
      return { type: 'Literal', value: parseFloat(trimmed) };
    }

    if (trimmed === 'true' || trimmed === 'false') {
      return { type: 'Literal', value: trimmed === 'true' };
    }

    if (trimmed === 'null') {
      return { type: 'Literal', value: null };
    }

    if (trimmed === 'undefined') {
      return { type: 'Identifier', name: 'undefined' };
    }

    // 🔧 修复：默认情况，将未识别的表达式作为标识符处理
    console.log(
      `⚠️ [Parse5TTMLAdapter] 未识别的表达式，作为标识符处理: "${trimmed}"`,
    );
    return { type: 'Identifier', name: trimmed };
  }

  /**
   * 解析for表达式
   */
  /**
   * 解析for表达式 - 企业级增强版，支持多种语法模式和插值表达式
   */
  private parseForExpression(forValue: string): {
    itemName: string;
    indexName: string;
    listExpression: string;
  } {
    console.log(`🔍 [Parse5TTMLAdapter] 解析for表达式: ${forValue}`);

    const safeForValue = (forValue || '').trim();

    // 检查是否为插值表达式 {{variable}}
    if (safeForValue.startsWith('{{') && safeForValue.endsWith('}}')) {
      console.log('🎯 [Parse5TTMLAdapter] 检测到插值表达式，转换为标准for循环');
      const variableName = safeForValue.slice(2, -2).trim();
      const result = {
        itemName: 'item',
        indexName: 'index',
        listExpression: variableName,
      };
      console.log('📋 [Parse5TTMLAdapter] 插值转换结果:', result);
      return result;
    }

    // 支持的标准语法模式
    const patterns = [
      // item in list
      /^(\w+)\s+in\s+(.+)$/,
      // item, index in list
      /^(\w+),\s*(\w+)\s+in\s+(.+)$/,
      // (item, index) in list
      /^\((\w+),\s*(\w+)\)\s+in\s+(.+)$/,
      // item of list (ES6 风格)
      /^(\w+)\s+of\s+(.+)$/,
      // (item, index) of list
      /^\((\w+),\s*(\w+)\)\s+of\s+(.+)$/,
    ];

    console.log('🔄 [Parse5TTMLAdapter] 尝试匹配for语法模式...');

    for (let i = 0; i < patterns.length; i++) {
      const pattern = patterns[i];
      const match = safeForValue.match(pattern);

      if (match) {
        console.log(
          `✅ [Parse5TTMLAdapter] 匹配模式 ${i + 1}:`,
          pattern.source,
        );

        if (match.length === 3) {
          // 基础模式: item in/of list
          const result = {
            itemName: match[1],
            indexName: 'index', // 默认索引名
            listExpression: match[2].trim(),
          };
          console.log('📋 [Parse5TTMLAdapter] 基础模式解析结果:', result);
          return result;
        } else if (match.length === 4) {
          // 完整模式: item, index in/of list
          const result = {
            itemName: match[1],
            indexName: match[2],
            listExpression: match[3].trim(),
          };
          console.log('📋 [Parse5TTMLAdapter] 完整模式解析结果:', result);
          return result;
        }
      }
    }

    // 如果所有模式都不匹配，尝试简单分割（向后兼容）
    console.log('⚠️ [Parse5TTMLAdapter] 未匹配标准模式，使用向后兼容解析');

    const parts = safeForValue.split(' in ');
    if (parts.length >= 2) {
      const itemPart = (parts[0] || '').trim();
      const listExpression = (parts[1] || '').trim();

      let itemName: string, indexName: string;
      if (itemPart.includes(',')) {
        [itemName, indexName] = itemPart.split(',').map(s => (s || '').trim());
      } else {
        itemName = itemPart;
        indexName = 'index';
      }

      const result = { itemName, indexName, listExpression };
      console.log('📋 [Parse5TTMLAdapter] 兼容模式解析结果:', result);
      return result;
    }

    // 最后的兜底：如果是单纯的变量名，也当作插值处理
    if (/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(safeForValue)) {
      console.log('🎯 [Parse5TTMLAdapter] 检测到单变量，按插值处理');
      const result = {
        itemName: 'item',
        indexName: 'index',
        listExpression: safeForValue,
      };
      console.log('📋 [Parse5TTMLAdapter] 单变量转换结果:', result);
      return result;
    }

    // 如果完全无法解析，抛出详细错误
    const error = `Invalid for expression: ${forValue}. 支持的格式: 
    - item in list
    - item, index in list  
    - (item, index) in list
    - item of list
    - (item, index) of list
    - {{variable}} (插值表达式)
    - variable (单变量)`;

    console.error('❌ [Parse5TTMLAdapter] for表达式解析失败:', error);
    throw new Error(error);
  }

  /**
   * 解析模板字符串 - 修复版，支持混合模板
   */
  private parseTemplateString(
    template: string,
    context: TransformContext,
  ): any {
    // 纯插值表达式
    if (template.startsWith('{{') && template.endsWith('}}')) {
      return this.parseExpression(template.slice(2, -2), context);
    }

    // 🔧 修复：处理混合模板字符串
    if (template.includes('{{') && template.includes('}}')) {
      // 分割模板字符串
      const parts = template.split(/(\{\{[^}]+\}\})/);
      const hasInterpolation = parts.some(part => part.startsWith('{{'));

      if (hasInterpolation) {
        // 生成模板字符串表达式
        const expressions = [];
        const quasis = [];

        for (let i = 0; i < parts.length; i++) {
          const part = parts[i];
          if (part.startsWith('{{') && part.endsWith('}}')) {
            expressions.push(this.parseExpression(part.slice(2, -2), context));
          } else if (part) {
            quasis.push({
              type: 'TemplateElement',
              value: { raw: part, cooked: part },
            });
          }
        }

        return {
          type: 'TemplateLiteral',
          quasis,
          expressions,
        };
      }
    }

    // 普通字符串
    return { type: 'Literal', value: template };
  }

  /**
   * 生成JSX代码
   */
  private generateJSXCode(jsx: JSXNode | JSXNode[]): string {
    console.log('📝 [Parse5TTMLAdapter] 开始生成JSX代码...');
    console.log(
      '🏗️ [Parse5TTMLAdapter] JSX输入类型:',
      Array.isArray(jsx) ? `Array(${jsx.length})` : jsx?.type || 'null',
    );

    if (Array.isArray(jsx)) {
      console.log('📋 [Parse5TTMLAdapter] 处理JSX数组，元素数量:', jsx.length);
      const result = jsx
        .map((node, index) => {
          console.log(`   ${index + 1}. 转换节点: ${node?.type || 'null'}`);
          return this.jsxToString(node);
        })
        .join(', ');
      console.log(
        '✅ [Parse5TTMLAdapter] JSX数组代码生成完成，长度:',
        result.length,
      );
      return result;
    }

    console.log('🔄 [Parse5TTMLAdapter] 处理单个JSX节点');
    const result = this.jsxToString(jsx);
    console.log('✅ [Parse5TTMLAdapter] JSX代码生成完成，长度:', result.length);
    return result;
  }

  /**
   * JSX节点转字符串
   */
  private jsxToString(node: JSXNode): string {
    if (!node) {
      console.log('⚠️ [Parse5TTMLAdapter] jsxToString: 节点为空');
      return '';
    }

    console.log(`🔄 [Parse5TTMLAdapter] jsxToString: 处理${node.type}类型节点`);

    switch (node.type) {
      case 'JSXFragment':
        console.log(
          `🧩 [Parse5TTMLAdapter] Fragment包含${node.children?.length || 0}个子节点`,
        );

        const fragmentChildren =
          node.children?.map(child => this.jsxToString(child)).join(', ') || '';
        // 🔧 修复：生成正确的 React.createElement 语法
        const fragmentResult = `React.createElement(React.Fragment, null${fragmentChildren ? `, ${fragmentChildren}` : ''})`;
        console.log(
          `✅ [Parse5TTMLAdapter] Fragment转换完成，长度: ${fragmentResult.length}`,
        );
        return fragmentResult;

      case 'JSXElement':
        const tagName = node.openingElement.name.name;
        const attributes = this.safeAttributesToString(
          node.openingElement.attributes,
        );

        console.log(
          `🏷️ [Parse5TTMLAdapter] Element: ${tagName}, 自闭合: ${node.openingElement.selfClosing}, 子节点: ${node.children?.length || 0}`,
        );

        if (node.openingElement.selfClosing) {
          const selfClosingResult = `React.createElement('${tagName}', ${attributes})`;
          console.log(`✅ [Parse5TTMLAdapter] 自闭合元素转换完成: ${tagName}`);
          return selfClosingResult;
        }

        const children =
          node.children?.map(child => this.jsxToString(child)).join(', ') || '';
        const elementResult = `React.createElement('${tagName}', ${attributes}${children ? `, ${children}` : ''})`;
        console.log(
          `✅ [Parse5TTMLAdapter] 元素转换完成: ${tagName}, 结果长度: ${elementResult.length}`,
        );
        return elementResult;

      case 'JSXText':
        const textValue = node.value?.replace(/"/g, '\\"') || '';
        console.log(
          `📝 [Parse5TTMLAdapter] 文本节点: "${textValue.substring(0, 50)}${textValue.length > 50 ? '...' : ''}"`,
        );
        return `"${textValue}"`;

      case 'JSXExpressionContainer':
        console.log('🔧 [Parse5TTMLAdapter] 表达式容器');
        return this.expressionToString(node.expression);

      case 'JSXComment':
        console.log('💬 [Parse5TTMLAdapter] 注释节点');
        return `/* ${node.value} */`;

      default:
        console.warn(`❓ [Parse5TTMLAdapter] 未知JSX节点类型: ${node.type}`);
        return '';
    }
  }

  /**
   * 安全的属性转字符串 - 修复版，正确处理函数和各种属性类型
   */
  private safeAttributesToString(attributes: any[]): string {
    if (!attributes || attributes.length === 0) {
      return 'null';
    }

    try {
      const props = attributes.map(attr => {
        const key = attr.name.name;
        let value: string;

        // 正确处理不同类型的属性值
        if (attr.value.type === 'Literal') {
          // 字符串值需要转义
          if (typeof attr.value.value === 'string') {
            value = JSON.stringify(attr.value.value);
          } else {
            value = String(attr.value.value);
          }
        } else if (attr.value.type === 'JSXExpressionContainer') {
          // 表达式容器 - 直接使用表达式字符串，不添加引号
          const expressionStr = this.expressionToString(attr.value.expression);

          // 🔧 FIX: 检查是否为函数表达式
          if (
            expressionStr.includes('function') ||
            expressionStr.includes('=>') ||
            expressionStr.startsWith('()') ||
            key.startsWith('on')
          ) {
            // 这是一个函数，直接使用
            value = expressionStr;
          } else {
            // 这是一个普通表达式
            value = expressionStr;
          }
        } else if (
          attr.value.type === 'ArrowFunctionExpression' ||
          attr.value.type === 'FunctionExpression'
        ) {
          // 🔧 FIX: 直接处理函数类型，不加引号
          value = this.expressionToString(attr.value);
        } else {
          value = 'null';
        }

        // 确保key是有效的标识符或字符串
        const safeKey = /^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(key)
          ? key
          : JSON.stringify(key);

        return `${safeKey}: ${value}`;
      });

      return `{${props.join(', ')}}`;
    } catch (error) {
      console.error('❌ [Parse5TTMLAdapter] 属性序列化失败:', error);
      return 'null';
    }
  }

  /**
   * 属性转字符串 - 保留原方法作为备用
   */
  private attributesToString(attributes: any[]): string {
    return this.safeAttributesToString(attributes);
  }

  /**
   * 表达式转字符串
   */
  private expressionToString(expression: any): string {
    if (!expression) {
      return 'null';
    }

    switch (expression.type) {
      case 'Identifier':
        return expression.name;
      case 'Literal':
        return JSON.stringify(expression.value);
      case 'MemberExpression':
        return `${this.expressionToString(expression.object)}.${expression.property.name}`;
      case 'CallExpression':
        const args =
          expression.arguments
            ?.map((arg: any) => this.expressionToString(arg))
            .join(', ') || '';
        return `${this.expressionToString(expression.callee)}(${args})`;
      case 'ArrowFunctionExpression':
        const params =
          expression.params?.map((p: any) => p.name).join(', ') || '';
        return `(${params}) => ${this.expressionToString(expression.body)}`;
      case 'LogicalExpression':
        return `${this.expressionToString(expression.left)} ${expression.operator} ${this.expressionToString(expression.right)}`;
      case 'BlockStatement':
        const body =
          expression.body
            ?.map((stmt: any) => this.expressionToString(stmt))
            .join('; ') || '';
        return `{${body}}`;
      case 'ExpressionStatement':
        return this.expressionToString(expression.expression);
      default:
        return 'null';
    }
  }

  /**
   * 统计JSX元素数量
   */
  private countJSXElements(jsx: JSXNode | JSXNode[]): number {
    let count = 0;

    const traverse = (node: JSXNode | JSXNode[]) => {
      if (Array.isArray(node)) {
        node.forEach(traverse);
        return;
      }

      if (!node) {
        return;
      }

      if (node.type === 'JSXElement') {
        count++;
      }

      if (node.children) {
        node.children.forEach(traverse);
      }
    };

    traverse(jsx);
    return count;
  }

  /**
   * 生成错误JSX
   */
  private generateErrorJSX(error: any): string {
    const errorMsg = error instanceof Error ? error.message : String(error);
    return `React.createElement('div', {className: 'ttml-error', style: {color: 'red', padding: '16px', border: '1px solid red', borderRadius: '4px'}}, "TTML解析错误: ${errorMsg}")`;
  }

  // ===============================
  // 调试辅助方法
  // ===============================

  /**
   * 分析TTML结构
   */
  private analyzeTTMLStructure(ttml: string): any {
    const analysis = {
      totalTags: (ttml.match(/<[^>]+>/g) || []).length,
      elementTypes: {} as Record<string, number>,
      directives: {} as Record<string, number>,
      events: {} as Record<string, number>,
      interpolations: (ttml.match(/\{\{[^}]+\}\}/g) || []).length,
      selfClosingTags: (ttml.match(/<[^>]+\/>/g) || []).length,
      invalidSyntax: [] as string[],
    };

    // 统计元素类型
    const elementMatches = ttml.match(/<([a-zA-Z][a-zA-Z0-9-]*)/g) || [];
    elementMatches.forEach(match => {
      const element = match.slice(1);
      analysis.elementTypes[element] =
        (analysis.elementTypes[element] || 0) + 1;
    });

    // 统计指令
    const directiveMatches = ttml.match(/(lx:|tt:)[a-zA-Z]+/g) || [];
    directiveMatches.forEach(directive => {
      analysis.directives[directive] =
        (analysis.directives[directive] || 0) + 1;
    });

    // 统计事件
    const eventMatches = ttml.match(/(bind|catch:)[a-zA-Z]+/g) || [];
    eventMatches.forEach(event => {
      analysis.events[event] = (analysis.events[event] || 0) + 1;
    });

    // 检查潜在的语法问题
    if (ttml.includes('{{') && !ttml.includes('}}')) {
      analysis.invalidSyntax.push('不完整的插值表达式');
    }
    if ((ttml.match(/</g) || []).length !== (ttml.match(/>/g) || []).length) {
      analysis.invalidSyntax.push('标签不匹配');
    }

    return analysis;
  }

  /**
   * 计算AST深度
   */
  private calculateASTDepth(node: Parse5Node): number {
    if (!node?.childNodes) {
      return 1;
    }

    let maxDepth = 1;
    for (const child of node.childNodes) {
      const childDepth = this.calculateASTDepth(child);
      maxDepth = Math.max(maxDepth, childDepth + 1);
    }

    return maxDepth;
  }

  /**
   * 统计总节点数
   */
  private countTotalNodes(node: Parse5Node): number {
    if (!node) {
      return 0;
    }

    let count = 1;
    if (node.childNodes) {
      for (const child of node.childNodes) {
        count += this.countTotalNodes(child);
      }
    }

    return count;
  }

  /**
   * 记录AST结构
   */
  private logASTStructure(node: Parse5Node, depth = 0): void {
    if (!node || depth > 5) {
      return;
    } // 限制深度避免过多输出

    const indent = '  '.repeat(depth);
    const nodeInfo = {
      nodeName: node.nodeName,
      tagName: node.tagName,
      hasAttrs: !!node.attrs?.length,
      attrCount: node.attrs?.length || 0,
      hasChildren: !!node.childNodes?.length,
      childCount: node.childNodes?.length || 0,
    };

    console.log(`${indent}📄 [Parse5TTMLAdapter] AST节点[${depth}]:`, nodeInfo);

    if (node.attrs && node.attrs.length > 0) {
      console.log(
        `${indent}   🏷️ 属性:`,
        node.attrs.map(attr => `${attr.name}="${attr.value}"`).join(', '),
      );
    }

    if (node.childNodes && depth < 3) {
      // 只显示前3层的子节点
      node.childNodes.forEach((child, index) => {
        if (index < 5) {
          // 每层最多显示5个子节点
          this.logASTStructure(child, depth + 1);
        } else if (index === 5) {
          console.log(
            `${indent}  ... 还有${node.childNodes!.length - 5}个子节点`,
          );
        }
      });
    }
  }

  /**
   * 记录JSX结构
   */
  private logJSXStructure(jsx: JSXNode | JSXNode[], depth = 0): void {
    if (depth > 3) {
      return;
    } // 限制深度

    const indent = '  '.repeat(depth);

    if (Array.isArray(jsx)) {
      console.log(
        `${indent}📋 [Parse5TTMLAdapter] JSX数组[${depth}]: ${jsx.length}个元素`,
      );
      jsx.slice(0, 3).forEach((item, index) => {
        console.log(`${indent}  [${index}]:`);
        this.logJSXStructure(item, depth + 1);
      });
      if (jsx.length > 3) {
        console.log(`${indent}  ... 还有${jsx.length - 3}个元素`);
      }
      return;
    }

    if (!jsx) {
      console.log(`${indent}❌ [Parse5TTMLAdapter] JSX节点为空`);
      return;
    }

    const jsxInfo = {
      type: jsx.type,
      hasChildren: !!jsx.children?.length,
      childCount: jsx.children?.length || 0,
    };

    if (jsx.type === 'JSXElement') {
      jsxInfo.elementName = jsx.openingElement?.name?.name;
      jsxInfo.attributeCount = jsx.openingElement?.attributes?.length || 0;
      jsxInfo.selfClosing = jsx.openingElement?.selfClosing;
    }

    console.log(`${indent}🔧 [Parse5TTMLAdapter] JSX节点[${depth}]:`, jsxInfo);

    if (jsx.children && depth < 2) {
      jsx.children.slice(0, 3).forEach((child, index) => {
        this.logJSXStructure(child, depth + 1);
      });
      if (jsx.children.length > 3) {
        console.log(`${indent}  ... 还有${jsx.children.length - 3}个子节点`);
      }
    }
  }

  /**
   * 获取映射统计
   */
  private getMappingStatistics(ast: Parse5Node): any {
    const stats = {
      totalElements: 0,
      mappedElements: 0,
      unmappedElements: 0,
      elementMapping: {} as Record<string, number>,
      directiveCount: 0,
      eventCount: 0,
    };

    const traverse = (node: Parse5Node) => {
      if (node.tagName) {
        stats.totalElements++;
        const mapping = TTML_ELEMENT_MAPPING[node.tagName];
        if (mapping) {
          stats.mappedElements++;
          stats.elementMapping[node.tagName] =
            (stats.elementMapping[node.tagName] || 0) + 1;
        } else {
          stats.unmappedElements++;
        }
      }

      if (node.attrs) {
        node.attrs.forEach(attr => {
          if (attr.name.startsWith('lx:') || attr.name.startsWith('tt:')) {
            stats.directiveCount++;
          }
          if (attr.name.startsWith('bind') || attr.name.startsWith('catch:')) {
            stats.eventCount++;
          }
        });
      }

      if (node.childNodes) {
        node.childNodes.forEach(traverse);
      }
    };

    traverse(ast);
    return stats;
  }

  /**
   * 验证TTML到JSX的映射
   */
  private validateTTMLToJSXMapping(ttml: string, jsx: string): void {
    const validation = {
      sourceElements: (ttml.match(/<([a-zA-Z][a-zA-Z0-9-]*)/g) || []).length,
      targetElements: (jsx.match(/React\.createElement\('([^']+)'/g) || [])
        .length,
      hasContent: jsx.length > 0,
      hasReactCalls: jsx.includes('React.createElement'),
      hasValidStructure:
        jsx.includes('React.createElement') || jsx.includes('React.Fragment'),
      preservedAttributes: jsx.includes('className') || jsx.includes('style'),
      errors: [] as string[],
    };

    // 检查基本的映射有效性
    if (validation.sourceElements > 0 && validation.targetElements === 0) {
      validation.errors.push('源TTML有元素但JSX中没有React元素');
    }

    if (ttml.length > 10 && jsx.length < 10) {
      validation.errors.push('源TTML有内容但JSX输出几乎为空');
    }

    if (!validation.hasValidStructure && ttml.trim().length > 0) {
      validation.errors.push('JSX输出缺少有效的React结构');
    }

    console.log('🔍 [Parse5TTMLAdapter] 映射验证结果:', validation);

    if (validation.errors.length > 0) {
      console.warn(
        '⚠️ [Parse5TTMLAdapter] 映射验证发现问题:',
        validation.errors,
      );
    } else {
      console.log('✅ [Parse5TTMLAdapter] 映射验证通过');
    }
  }

  /**
   * 生成唯一ID
   */
  private generateUniqueId(): string {
    return `comp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
