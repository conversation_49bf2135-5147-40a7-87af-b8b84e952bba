/**
 * @package runtime-convert-parse5
 * @description Parse5 批处理适配器 - 将Parse5转换引擎适配到现有批处理接口
 */

import Parse5TransformEngine, { Parse5TransformConfig } from '../index';
import { workerManager } from '../workers/worker-manager';
import type {
  InputFiles,
  TransformResult,
} from '../../deleted_runtime_convert/types';

/**
 * Parse5 批处理适配器
 * 提供与原有BatchProcessorAdapter兼容的接口
 */
export class Parse5BatchProcessorAdapter {
  private engine: Parse5TransformEngine;
  private disposed = false;
  private useWorker: boolean;
  private fallbackMode = false;

  constructor(config: Partial<Parse5TransformConfig> = {}) {
    // 🔧 P0修复：禁用Worker，强制使用主线程确保模板引擎修复生效
    this.useWorker = false; // 强制禁用Worker

    this.engine = new Parse5TransformEngine({
      enableCache: true,
      enableScope: true,
      enableOptimization: true,
      strictMode: false,
      workerMode: this.useWorker,
      ...config,
    });

    if (this.useWorker) {
      console.log('🚀 [Parse5BatchProcessorAdapter] 启用Worker模式');
      // 预热Worker池
      this.initializeWorkerPool();
    } else {
      console.log('🔧 [Parse5BatchProcessorAdapter] 使用主线程模式');
    }
  }

  /**
   * 初始化Worker池
   */
  private async initializeWorkerPool() {
    try {
      await workerManager.initialize();
    } catch (error) {
      console.warn(
        '⚠️ [Parse5BatchProcessorAdapter] Worker初始化失败，切换到主线程模式:',
        error,
      );
      this.useWorker = false;
      this.fallbackMode = true;
    }
  }

  /**
   * 为InteractiveIframe组件转换内容
   * 保持与原有BatchProcessorAdapter兼容的接口
   */
  async convertForInteractiveIframe(
    result: any,
    enableAutoScreenshot = false,
    width = 280,
    height = 500,
  ): Promise<Parse5PreviewResult> {
    try {
      console.log(
        '🔄 [Parse5BatchProcessorAdapter] 开始转换InteractiveIframe内容',
      );

      // 🐛 DEBUG: 详细分析输入result对象
      console.log('🔍 [DEBUG] Result对象详细分析:');
      console.log('  - result存在:', !!result);
      console.log('  - result.metadata存在:', !!result?.metadata);
      console.log(
        '  - result.metadata.extractedContent存在:',
        !!result?.metadata?.extractedContent,
      );
      console.log('  - result.content存在:', !!result?.content);
      console.log(
        '  - result.metadata.extractedContent长度:',
        result?.metadata?.extractedContent?.length || 0,
      );
      console.log('  - result.content长度:', result?.content?.length || 0);

      // 从result中提取内容，确保安全处理
      const content =
        result?.metadata?.extractedContent || result?.content || '';
      const safeContent = content || '';

      console.log('🔍 [DEBUG] 内容提取结果:');
      console.log(
        '  - 选择的内容来源:',
        result?.metadata?.extractedContent
          ? 'extractedContent'
          : result?.content
            ? 'content'
            : 'default empty',
      );
      console.log('  - 提取的内容长度:', safeContent.length);
      console.log('  - 内容类型:', typeof safeContent);
      console.log('  - 内容预览 (前300字符):', safeContent.substring(0, 300));
      console.log('  - 是否为空或只有空白:', !safeContent.trim());

      if (
        !safeContent ||
        typeof safeContent !== 'string' ||
        !safeContent.trim()
      ) {
        console.error('❌ [DEBUG] 内容验证失败:');
        console.error('  - 完整result对象:', JSON.stringify(result, null, 2));

        // 🔧 FIX: 尝试从其他可能的位置提取内容
        const fallbackContent =
          result?.response ||
          result?.data ||
          result?.text ||
          result?.rawContent ||
          '';
        if (
          fallbackContent &&
          typeof fallbackContent === 'string' &&
          fallbackContent.trim()
        ) {
          console.log(
            '🔄 [FIX] BatchProcessorAdapter找到备用内容，长度:',
            fallbackContent.length,
          );
          // 继续处理备用内容
          const fallbackFiles = this.parseContent(fallbackContent);
          const fallbackResult = await this.engine.convert(fallbackFiles);

          return this.formatPreviewResult(fallbackResult, {
            width,
            height,
            enableAutoScreenshot,
            result,
            fallbackUsed: true,
          });
        }

        // 🔧 FIX: 返回有用的错误信息而不是完全失败
        console.log('🔄 [FIX] 生成错误占位 HTML');
        const errorHtml = this.generateErrorPlaceholder(
          result,
          '没有有效的TTML内容',
        );

        return {
          success: true, // 标记为成功，但显示错误内容
          html: errorHtml,
          error: '内容为空，已生成占位符',
          metadata: {
            transformTime: 0,
            componentId: 'empty',
            engine: 'Parse5',
            version: '1.0.0',
          },
        };
      }

      // 提取TTML内容
      const files = this.extractFileContents(safeContent);
      if (!files.ttml || files.ttml.trim().length === 0) {
        return {
          success: false,
          error: '未找到TTML内容',
          metadata: {
            transformTime: 0,
            componentId: 'no-ttml',
            engine: 'Parse5',
            version: '1.0.0',
          },
        };
      }

      const componentId = result?.id || 'unknown';

      // 选择转换策略：Worker优先，主线程备用
      let conversionResult: any;

      if (this.useWorker && !this.fallbackMode) {
        console.log('🚀 [Parse5BatchProcessorAdapter] 使用Worker转换');
        try {
          conversionResult = await workerManager.transform(
            files.ttml,
            componentId,
            {
              enableCache: true,
              enableOptimization: true,
              strictMode: false,
            },
          );
        } catch (workerError) {
          console.warn(
            '⚠️ [Parse5BatchProcessorAdapter] Worker转换失败，回退到主线程:',
            workerError,
          );
          this.fallbackMode = true;
          const transformResult = await this.engine.convert(files);
          conversionResult = {
            success: transformResult.success,
            html: transformResult.html,
            metadata: transformResult.metadata,
          };
        }
      } else {
        console.log('🔧 [Parse5BatchProcessorAdapter] 使用主线程转换');
        const transformResult = await this.engine.convert(files);
        conversionResult = {
          success: transformResult.success,
          html: transformResult.html,
          metadata: transformResult.metadata,
        };
      }

      if (conversionResult.success) {
        // 包装HTML内容
        const wrappedHtml = this.wrapHTMLContent(conversionResult.html || '');

        return {
          success: true,
          html: wrappedHtml,
          metadata: {
            transformTime: Date.now(),
            componentId,
            elementCount: conversionResult.metadata?.elementCount || 0,
            cssRules: conversionResult.metadata?.cssRules || 0,
            engine:
              this.useWorker && !this.fallbackMode
                ? 'Parse5-Worker'
                : 'Parse5-Main',
            version: '2.0.0',
            resultId: result.id,
            width,
            height,
            enableAutoScreenshot,
          },
        };
      } else {
        return {
          success: false,
          error: conversionResult.error || 'Parse5转换失败',
          metadata: {
            transformTime: 0,
            componentId,
            engine: 'Parse5',
            version: '2.0.0',
          },
        };
      }
    } catch (error) {
      console.error(
        '❌ [Parse5BatchProcessorAdapter] InteractiveIframe转换失败:',
        error,
      );
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        metadata: {
          transformTime: 0,
          componentId: 'error',
          engine: 'Parse5',
          version: '1.0.0',
        },
      };
    }
  }

  /**
   * 转换解析后的内容（兼容原有BatchProcessorAdapter接口）
   * 支持HTML和TTML内容的处理
   */
  async convertParsedContent(
    content: string,
    jobId?: string,
  ): Promise<{
    success: boolean;
    html?: string;
    screenshot?: string;
    error?: string;
    message?: string;
    metadata?: any;
  }> {
    try {
      console.log('🔄 [Parse5BatchProcessorAdapter] 开始转换解析后的内容');

      // 安全地检查内容
      const safeContent = content || '';
      if (
        !safeContent ||
        typeof safeContent !== 'string' ||
        !safeContent.trim()
      ) {
        return {
          success: false,
          error: '内容为空',
          message: '没有有效的内容需要转换',
        };
      }

      // 检测内容类型
      const isHtmlContent = this.detectHTMLContent(safeContent);

      if (isHtmlContent) {
        console.log(
          '📄 [Parse5BatchProcessorAdapter] 检测到HTML内容，直接处理',
        );

        // HTML内容：包装处理并返回
        const wrappedHtml = this.wrapHTMLContent(safeContent);

        return {
          success: true,
          html: wrappedHtml,
          metadata: {
            transformTime: Date.now(),
            componentId: jobId || 'html-direct',
            engine: 'Parse5',
            version: '1.0.0',
            contentType: 'HTML',
            directHTML: true,
          },
        };
      } else {
        console.log(
          '🔧 [Parse5BatchProcessorAdapter] 检测到TTML内容，使用Parse5转换',
        );

        // TTML内容：使用Parse5引擎转换
        const conversionResult = await this.convertLynxContent(safeContent);

        if (conversionResult.success) {
          return {
            success: true,
            html: conversionResult.html,
            metadata: {
              ...conversionResult.metadata,
              jobId,
              contentType: 'TTML',
            },
          };
        } else {
          return {
            success: false,
            error: conversionResult.error,
            message: conversionResult.error || 'Parse5转换失败',
          };
        }
      }
    } catch (error) {
      console.error(
        '❌ [Parse5BatchProcessorAdapter] 转换解析后的内容失败:',
        error,
      );
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
        message: 'Parse5转换过程中发生错误',
      };
    }
  }

  /**
   * 转换Lynx内容为Web预览
   * 兼容原有接口格式
   */
  async convertLynxContent(content: string): Promise<{
    success: boolean;
    html?: string;
    error?: string;
    metadata?: any;
  }> {
    if (this.disposed) {
      throw new Error('Parse5BatchProcessorAdapter has been disposed');
    }

    try {
      // 验证输入参数
      if (!content || typeof content !== 'string') {
        return {
          success: false,
          error: `Invalid content for conversion: expected string, got ${typeof content}`,
          metadata: { engine: 'Parse5', version: '1.0.0' },
        };
      }

      // 从内容中提取TTML和TTSS
      const { ttml, ttss, js } = this.extractFileContents(content);

      // 使用Parse5引擎进行转换
      const result = await this.engine.convert({ ttml, ttss, js });

      if (result.success) {
        return {
          success: true,
          html: result.html,
          metadata: {
            ...result.metadata,
            engine: 'Parse5',
            version: '1.0.0',
          },
        };
      } else {
        return {
          success: false,
          error: result.message || 'Parse5转换失败',
          metadata: result.metadata,
        };
      }
    } catch (error) {
      console.error('❌ [Parse5BatchProcessorAdapter] 转换失败:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 从内容中提取文件内容
   */
  private extractFileContents(content: string): InputFiles {
    // 验证输入内容
    if (!content || typeof content !== 'string') {
      console.warn(
        '[Parse5BatchProcessorAdapter] extractFileContents: invalid content',
        typeof content,
      );
      return { ttml: '', ttss: '', js: '' };
    }

    // 安全地处理字符串操作
    const safeContent = content || '';

    console.log(
      '🔍 [Parse5BatchProcessorAdapter] 提取文件内容，输入格式检测:',
      {
        length: safeContent.length,
        preview: safeContent.substring(0, 200),
        hasXmlFiles: safeContent.includes('<FILES>'),
        hasHtmlComments: safeContent.includes('<!-- TTML -->'),
      },
    );

    // 方法1: 尝试从XML <FILES><FILE> 格式中提取
    if (safeContent.includes('<FILES>') && safeContent.includes('<FILE')) {
      console.log(
        '🔄 [Parse5BatchProcessorAdapter] 检测到XML FILES格式，开始解析',
      );
      return this.extractFromXmlFormat(safeContent);
    }

    // 方法2: 尝试从HTML注释标记化内容中提取各部分
    const ttmlMatch = safeContent.match(
      /<!-- TTML -->([\s\S]*?)<!-- \/TTML -->/,
    );
    const ttssMatch = safeContent.match(
      /<!-- TTSS -->([\s\S]*?)<!-- \/TTSS -->/,
    );
    const jsMatch = safeContent.match(/<!-- JS -->([\s\S]*?)<!-- \/JS -->/);

    if (ttmlMatch || ttssMatch || jsMatch) {
      console.log(
        '🔄 [Parse5BatchProcessorAdapter] 检测到HTML注释格式，开始解析',
      );
      const ttml = ttmlMatch ? (ttmlMatch[1] || '').trim() : '';
      const ttss = ttssMatch ? (ttssMatch[1] || '').trim() : '';
      const js = jsMatch ? (jsMatch[1] || '').trim() : '';
      return { ttml, ttss, js };
    }

    // 方法3: 如果没有找到标记，则将整个内容作为TTML处理
    console.log('🔄 [Parse5BatchProcessorAdapter] 将整个内容作为TTML处理');
    return { ttml: safeContent.trim(), ttss: '', js: '' };
  }

  /**
   * 从XML格式中提取文件内容
   */
  private extractFromXmlFormat(content: string): InputFiles {
    const result: InputFiles = { ttml: '', ttss: '', js: '' };

    try {
      // 提取所有 <FILE> 标签内容 - 优先匹配 name= 格式
      let fileMatches = content.match(
        /<FILE\s+name="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/gi,
      );

      // 如果没找到name=格式，尝试path=格式
      if (!fileMatches) {
        fileMatches = content.match(
          /<FILE\s+path="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/gi,
        );
      }

      if (!fileMatches) {
        console.warn(
          '🔍 [Parse5BatchProcessorAdapter] 未找到有效的FILE标签，尝试替代解析方法',
        );
        // 尝试替代解析方法
        return this.extractFromAlternativeFormat(content);
      }

      console.log(
        '🔍 [Parse5BatchProcessorAdapter] 找到文件数量:',
        fileMatches.length,
      );

      fileMatches.forEach((fileMatch, index) => {
        // 使用新的正则直接提取文件名和内容
        let fileName = '';
        let fileContent = '';

        // 先尝试name=格式
        let match = fileMatch.match(
          /<FILE\s+name="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/i,
        );
        if (match) {
          fileName = match[1];
          fileContent = match[2].trim();
        } else {
          // 再尝试path=格式
          match = fileMatch.match(
            /<FILE\s+path="([^"]+)"[^>]*>([\s\S]*?)<\/FILE>/i,
          );
          if (match) {
            fileName = match[1];
            fileContent = match[2].trim();
          }
        }

        if (fileName && fileContent) {
          console.log(
            `📁 [Parse5BatchProcessorAdapter] 处理文件 ${index + 1}: ${fileName} (${fileContent.length} 字符)`,
          );

          // 根据文件名确定类型
          if (
            fileName.endsWith('.ttml') ||
            fileName.includes('index.ttml') ||
            fileName.includes('.ttml')
          ) {
            result.ttml = fileContent;
            console.log('✅ [Parse5BatchProcessorAdapter] 识别为TTML文件');
          } else if (
            fileName.endsWith('.ttss') ||
            fileName.includes('.ttss') ||
            fileName.includes('style')
          ) {
            result.ttss = fileContent;
            console.log('✅ [Parse5BatchProcessorAdapter] 识别为TTSS文件');
          } else if (fileName.endsWith('.js') || fileName.includes('.js')) {
            result.js = fileContent;
            console.log('✅ [Parse5BatchProcessorAdapter] 识别为JS文件');
          } else {
            // 如果无法从扩展名识别，尝试从内容特征判断
            if (
              fileContent.includes('<view') ||
              fileContent.includes('<text') ||
              fileContent.includes('<scroll-view')
            ) {
              result.ttml = fileContent;
              console.log(
                '✅ [Parse5BatchProcessorAdapter] 根据内容特征识别为TTML',
              );
            } else if (fileContent.includes('.') && fileContent.includes('{')) {
              result.ttss = fileContent;
              console.log(
                '✅ [Parse5BatchProcessorAdapter] 根据内容特征识别为TTSS',
              );
            } else if (
              fileContent.includes('function') ||
              fileContent.includes('const') ||
              fileContent.includes('Card(')
            ) {
              result.js = fileContent;
              console.log(
                '✅ [Parse5BatchProcessorAdapter] 根据内容特征识别为JS',
              );
            }
          }
        }
      });

      console.log('🎯 [Parse5BatchProcessorAdapter] XML解析结果:', {
        ttml: result.ttml.length > 0 ? `${result.ttml.length} chars` : 'empty',
        ttss: result.ttss.length > 0 ? `${result.ttss.length} chars` : 'empty',
        js: result.js.length > 0 ? `${result.js.length} chars` : 'empty',
      });
    } catch (error) {
      console.error('❌ [Parse5BatchProcessorAdapter] XML解析失败:', error);
    }

    return result;
  }

  /**
   * 替代格式解析方法 - 处理各种可能的文件格式
   */
  private extractFromAlternativeFormat(content: string): InputFiles {
    const result: InputFiles = { ttml: '', ttss: '', js: '' };

    console.log('🔄 [Parse5BatchProcessorAdapter] 尝试替代解析方法');

    try {
      // 方法1: 尝试解析各种FILE标签格式
      const filePattern1 = /<FILE\s+name=([^\s>'"]+)[^>]*>([\s\S]*?)<\/FILE>/gi;
      let match;

      while ((match = filePattern1.exec(content)) !== null) {
        const fileName = match[1].replace(/['"]/g, ''); // 移除可能的引号
        const fileContent = match[2].trim();

        console.log(
          `📁 [Parse5BatchProcessorAdapter] 替代解析发现文件: ${fileName} (${fileContent.length} 字符)`,
        );

        if (fileName.includes('ttml') || fileName.includes('index')) {
          result.ttml = fileContent;
        } else if (fileName.includes('ttss') || fileName.includes('style')) {
          result.ttss = fileContent;
        } else if (fileName.includes('js')) {
          result.js = fileContent;
        }
      }

      // 方法2: 如果还是没找到，尝试按内容分块
      if (!result.ttml && !result.ttss) {
        const blocks = content.split(/<\/?FILE[^>]*>/gi);

        for (let i = 0; i < blocks.length; i++) {
          const block = blocks[i].trim();
          if (block.length < 10) {
            continue;
          } // 跳过太短的块

          // 判断内容类型
          if (
            block.includes('<view') ||
            block.includes('<text') ||
            block.includes('<scroll-view')
          ) {
            result.ttml = block;
            console.log(
              '✅ [Parse5BatchProcessorAdapter] 通过内容特征识别TTML',
            );
          } else if (
            block.includes('{') &&
            (block.includes('color:') ||
              block.includes('background:') ||
              block.includes('padding:'))
          ) {
            result.ttss = block;
            console.log(
              '✅ [Parse5BatchProcessorAdapter] 通过内容特征识别TTSS',
            );
          }
        }
      }

      // 方法3: 最后兜底 - 如果仍然没有TTML，将整个内容当作TTML
      if (!result.ttml && content.trim()) {
        // 检查是否包含TTML元素
        if (
          content.includes('<view') ||
          content.includes('<text') ||
          content.includes('{{')
        ) {
          result.ttml = content.trim();
          console.log(
            '🎯 [Parse5BatchProcessorAdapter] 兜底策略：整体内容识别为TTML',
          );
        }
      }
    } catch (error) {
      console.error('❌ [Parse5BatchProcessorAdapter] 替代解析失败:', error);
    }

    console.log('🎯 [Parse5BatchProcessorAdapter] 替代解析结果:', {
      ttml: result.ttml.length > 0 ? `${result.ttml.length} chars` : 'empty',
      ttss: result.ttss.length > 0 ? `${result.ttss.length} chars` : 'empty',
      js: result.js.length > 0 ? `${result.js.length} chars` : 'empty',
    });

    return result;
  }

  /**
   * 检测是否为HTML内容
   */
  private detectHTMLContent(content: string): boolean {
    const htmlIndicators = [
      '<!DOCTYPE html>',
      '<html',
      '<head>',
      '<body>',
      '<div',
      '<span',
      '<p>',
      '<h1>',
      '<h2>',
      '<h3>',
    ];

    return htmlIndicators.some(indicator =>
      content.toLowerCase().includes(indicator.toLowerCase()),
    );
  }

  /**
   * 包装HTML内容
   */
  private wrapHTMLContent(htmlContent: string): string {
    // 如果已经是完整的HTML文档
    if (
      htmlContent.includes('<!DOCTYPE html>') ||
      htmlContent.includes('<html')
    ) {
      return htmlContent;
    }

    // 否则包装成完整文档
    return (
      '<!DOCTYPE html>' +
      '<html lang="zh-CN">' +
      '<head>' +
      '<meta charset="UTF-8">' +
      '<meta name="viewport" content="width=device-width, initial-scale=1.0">' +
      '<title>Parse5 Preview</title>' +
      '<style>' +
      'body {' +
      'margin: 0;' +
      'padding: 16px;' +
      "font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;" +
      'line-height: 1.6;' +
      '}' +
      '.preview-container {' +
      'max-width: 100%;' +
      'overflow-x: auto;' +
      '}' +
      '</style>' +
      '</head>' +
      '<body>' +
      `<div class="preview-container">${htmlContent}</div>` +
      '</body>' +
      '</html>'
    );
  }

  /**
   * 验证TTML语法
   * 兼容原有接口
   */
  async validateTTML(ttml: string): Promise<{
    isValid: boolean;
    errors: Array<{
      line: number;
      column: number;
      message: string;
      type: string;
    }>;
  }> {
    try {
      // 使用Parse5进行预验证
      const result = await this.engine.convert({ ttml });

      if (result.success) {
        return {
          isValid: true,
          errors: [],
        };
      } else {
        const errors = result.metadata?.errors || [];
        return {
          isValid: false,
          errors: errors.map((error: any) => ({
            line: error.position?.line || 0,
            column: error.position?.column || 0,
            message: error.message || 'Parse5验证错误',
            type: error.type || 'syntax',
          })),
        };
      }
    } catch (error) {
      return {
        isValid: false,
        errors: [
          {
            line: 0,
            column: 0,
            message: error instanceof Error ? error.message : String(error),
            type: 'validation',
          },
        ],
      };
    }
  }

  /**
   * 获取转换统计信息
   */
  getStats(): any {
    return {
      ...((this.engine as any).getStats?.() || {}),
      adapter: 'Parse5BatchProcessorAdapter',
      version: '2.0.0',
      useWorker: this.useWorker,
      fallbackMode: this.fallbackMode,
      workerStats: this.useWorker ? workerManager.getStats() : null,
    };
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    (this.engine as any).clearCache?.();
  }

  /**
   * 销毁适配器
   */
  dispose(): void {
    if (!this.disposed) {
      console.log('🔚 [Parse5BatchProcessorAdapter] 正在销毁适配器');

      // 清理Worker池
      if (this.useWorker) {
        workerManager.destroy();
      }

      // 清理引擎缓存
      (this.engine as any).clearCache?.();
      this.disposed = true;

      console.log('✅ [Parse5BatchProcessorAdapter] 适配器已销毁');
    }
  }

  /**
   * 生成错误占位 HTML
   */
  private generateErrorPlaceholder(result: any, errorMessage: string): string {
    const resultId = result?.id || 'unknown';
    const timestamp = new Date().toLocaleString();

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>转换错误 - ${resultId}</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f8fafc;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }
        .error-container {
            max-width: 350px;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
            border: 2px solid #fecaca;
        }
        .error-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        .error-title {
            font-size: 18px;
            font-weight: 600;
            color: #dc2626;
            margin-bottom: 12px;
        }
        .error-message {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 16px;
            line-height: 1.5;
        }
        .error-details {
            font-size: 12px;
            color: #9ca3af;
            background: #f3f4f6;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            font-family: monospace;
        }
        .retry-button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .retry-button:hover {
            background: #b91c1c;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <div class="error-title">内容转换失败</div>
        <div class="error-message">${errorMessage}</div>
        <div class="error-details">
            Result ID: ${resultId}<br>
            时间: ${timestamp}<br>
            引擎: Parse5 v1.0.0
        </div>
        <button class="retry-button" onclick="window.parent.location.reload()">
            重新加载
        </button>
    </div>
</body>
</html>`;
  }

  /**
   * 检查是否已销毁
   */
  isDisposed(): boolean {
    return this.disposed;
  }
}

/**
 * 预览结果接口（兼容性）
 */
export interface Parse5PreviewResult {
  success: boolean;
  html?: string;
  screenshot?: string; // Add screenshot property
  error?: string;
  message?: string; // 兼容原有接口
  metadata?: {
    transformTime: number;
    componentId: string;
    elementCount?: number;
    cssRules?: number;
    engine: string;
    version: string;
    resultId?: string;
    width?: number;
    height?: number;
    enableAutoScreenshot?: boolean;
  };
}

/**
 * 转换选项接口（兼容性）
 */
export interface Parse5ConversionOptions {
  enableScope?: boolean;
  enableCache?: boolean;
  enableOptimization?: boolean;
  strictMode?: boolean;
  enableSourceMap?: boolean;
}

/**
 * 创建Parse5适配器的工厂函数
 */
export function createParse5Adapter(
  config?: Parse5ConversionOptions,
): Parse5BatchProcessorAdapter {
  return new Parse5BatchProcessorAdapter(config);
}

export default Parse5BatchProcessorAdapter;
