/**
 * @package runtime-convert-parse5
 * @description HTML生成器 - 将转换后的JSX和CSS组合为完整的HTML
 * 基于文档提取的规则生成可预览的Web页面
 */

import type { Parse5TransformConfig } from '../index';

/**
 * HTML生成输入
 */
export interface HTMLGeneratorInput {
  jsx: string; // 转换后的JSX代码
  css: string; // 转换后的CSS
  js?: string; // JavaScript代码
  componentId: string; // 组件ID
}

/**
 * HTML生成结果
 */
export interface HTMLGeneratorResult {
  html: string; // 完整HTML
  previewUrl?: string; // 预览URL（如果生成）
  size: number; // 生成文件大小
  dependencies: string[]; // 依赖列表
}

/**
 * HTML生成器
 * 负责将转换后的JSX、CSS、JS组合为完整的可预览HTML页面
 */
export class HTMLGenerator {
  private config: Parse5TransformConfig;

  constructor(config: Parse5TransformConfig) {
    this.config = config;
  }

  /**
   * 生成完整HTML
   */
  async generate(input: HTMLGeneratorInput): Promise<string> {
    try {
      const { jsx, css, js, componentId } = input;

      // 🔍 调试：检查传入的数据
      console.log('🔍 [HTMLGenerator] 输入数据调试:', {
        hasJSX: !!jsx,
        jsxLength: jsx?.length || 0,
        hasCSS: !!css,
        cssLength: css?.length || 0,
        hasJS: !!js,
        jsLength: js?.length || 0,
        componentId,
        jsxPreview: jsx?.substring(0, 100) || 'EMPTY',
        cssPreview: css?.substring(0, 100) || 'EMPTY',
      });

      // 🔧 P0修复：检查是否为模板引擎输出的HTML
      if (jsx && jsx.includes('<!DOCTYPE html>')) {
        console.log(
          '✅ [HTMLGenerator] 检测到模板引擎输出的完整HTML，直接返回',
        );
        return jsx;
      }

      // 🚨 P0修复：强制使用简化模式，避免React模式的问题
      if (jsx && jsx.includes('<')) {
        console.log('🚨 [HTMLGenerator] 强制使用简化模式，避免React语法错误');
        return this.generateSimplified(input);
      }

      // 构建完整的HTML文档
      const html = this.buildHTMLDocument({
        jsx: jsx || '',
        css: css || '',
        js: js || '',
        componentId,
      });

      return html;
    } catch (error) {
      console.error('HTML生成失败:', error);
      return this.generateErrorHTML(error);
    }
  }

  /**
   * 🔧 P0修复：从CSS中提取作用域属性
   */
  private extractScopeAttribute(css: string): string | null {
    if (!css) return null;

    console.log('🔧 [HTMLGenerator] 提取CSS作用域属性');

    // 提取作用域属性，如 [data-v-xxxxx]
    const scopeMatch = css.match(/\[data-v-([a-zA-Z0-9-]+)\]/);
    if (scopeMatch) {
      const scopeAttr = `data-v-${scopeMatch[1]}`;
      console.log('📝 找到作用域属性:', scopeAttr);
      return scopeAttr;
    }

    console.log('📝 未找到作用域属性');
    return null;
  }

  /**
   * 🔧 P0修复：为HTML元素添加作用域属性
   */
  private addScopeAttributesToHTML(
    html: string,
    scopeAttribute: string,
  ): string {
    if (!html || !scopeAttribute) return html;

    console.log('🔧 [HTMLGenerator] 为HTML元素添加作用域属性:', scopeAttribute);

    // 为所有HTML标签添加作用域属性
    const htmlWithScope = html.replace(
      /<([a-zA-Z][a-zA-Z0-9-]*)((?:\s+[^>]*)?)(\/?)>/g,
      (match, tagName, attributes, selfClosing) => {
        // 检查是否已经有该作用域属性
        if (attributes && attributes.includes(scopeAttribute)) {
          return match; // 已经有了，不重复添加
        }

        // 添加作用域属性
        const newAttributes = attributes
          ? `${attributes} ${scopeAttribute}=""`
          : ` ${scopeAttribute}=""`;
        return `<${tagName}${newAttributes}${selfClosing}>`;
      },
    );

    console.log('📝 HTML处理前长度:', html.length);
    console.log('📝 HTML处理后长度:', htmlWithScope.length);

    return htmlWithScope;
  }

  /**
   * 生成简化HTML - 直接使用模板引擎输出
   */
  async generateSimplified(input: HTMLGeneratorInput): Promise<string> {
    const { jsx, css, componentId } = input;

    console.log('🔧 [HTMLGenerator] 使用简化模式生成HTML');

    // 🔧 P0修复：提取CSS作用域属性并应用到HTML
    const scopeAttribute = this.extractScopeAttribute(css || '');
    let processedJSX = jsx || '';

    if (scopeAttribute) {
      console.log('🎯 [HTMLGenerator] 应用作用域属性到HTML元素');
      processedJSX = this.addScopeAttributesToHTML(jsx || '', scopeAttribute);
    } else {
      console.log('📝 [HTMLGenerator] 未找到作用域属性，使用原始HTML');
    }

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TTML预览 - 简化模式</title>
  <style>
    /* 基础样式重置 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
      background: #f8fafc;
      overflow-x: hidden;
      font-size: 14px;
      line-height: 1.6;
    }

    /* 作用域化CSS - 保持原始CSS完整性 */
    ${css}
  </style>
</head>
<body>
  <div id="lynx-app-container" data-component-id="${componentId}">
    ${processedJSX}
  </div>
</body>
</html>`;
  }

  /**
   * 构建HTML文档
   */
  private buildHTMLDocument(input: HTMLGeneratorInput): string {
    const { jsx, css, js, componentId } = input;

    console.log('🔧 [HTMLGenerator] 开始构建 HTML文档组件:');

    // 生成各个部分
    const baseCSS = this.generateBaseCSS();
    const scopedCSS = this.generateScopedCSS(css, componentId);
    const reactDeps = this.generateReactDependencies();
    const reactSetup = this.generateReactSetup();
    const componentScript = this.generateComponentScript(jsx, js, componentId);
    const renderScript = this.generateRenderScript(componentId);

    console.log('📋 [HTMLGenerator] HTML组件生成统计:', {
      baseCSSLength: baseCSS.length,
      scopedCSSLength: scopedCSS.length,
      reactDepsLength: reactDeps.length,
      reactSetupLength: reactSetup.length,
      componentScriptLength: componentScript.length,
      renderScriptLength: renderScript.length,
    });

    const finalHTML = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lynx Preview - ${componentId}</title>
  <style>
${baseCSS}
${scopedCSS}
  </style>
  ${reactDeps}
</head>
<body>
  <div id="lynx-preview-root" data-component-id="${componentId}">
    <div id="lynx-app-container">
      <!-- 组件将在这里渲染 -->
    </div>
  </div>
  
  <script>
${reactSetup}
${componentScript}
${renderScript}
  </script>
</body>
</html>`;

    console.log(
      '✅ [HTMLGenerator] HTML文档构建完成，总长度:',
      finalHTML.length,
    );
    return finalHTML;
  }

  /**
   * 生成基础CSS样式
   */
  private generateBaseCSS(): string {
    return `
    /* Parse5 Lynx Preview Base Styles */
    * {
      box-sizing: border-box;
    }
    
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      background: #f5f5f5;
    }
    
    #lynx-preview-root {
      width: 100%;
      height: 100vh;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      padding: 20px;
    }
    
    #lynx-app-container {
      width: 100%;
      max-width: 375px;
      min-height: 667px;
      background: #ffffff;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      position: relative;
    }
    
    /* Lynx组件基础样式 */
    .lynx-view {
      display: block;
    }
    
    .lynx-scroll-view {
      overflow: auto;
    }
    
    .lynx-text {
      display: inline;
    }
    
    .lynx-image {
      display: block;
      max-width: 100%;
      height: auto;
    }
    
    .lynx-button {
      display: inline-block;
      padding: 8px 16px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background: #fff;
      cursor: pointer;
      font-size: 14px;
    }
    
    .lynx-button:hover {
      background: #f0f0f0;
    }
    
    .lynx-input {
      display: block;
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .lynx-input:focus {
      outline: none;
      border-color: #007aff;
    }
    
    /* 表单元素样式 */
    .lynx-switch {
      width: 40px;
      height: 20px;
    }
    
    .lynx-slider {
      width: 100%;
    }
    
    .lynx-checkbox,
    .lynx-radio {
      margin-right: 8px;
    }
    
    /* 布局辅助类 */
    .lynx-flex {
      display: flex;
    }
    
    .lynx-flex-column {
      flex-direction: column;
    }
    
    .lynx-flex-center {
      justify-content: center;
      align-items: center;
    }
    
    /* 响应式辅助 */
    @media (max-width: 480px) {
      #lynx-preview-root {
        padding: 10px;
      }
      
      #lynx-app-container {
        max-width: 100%;
        border-radius: 0;
      }
    }
    
    /* 错误显示样式 */
    .ttml-error {
      color: #ff3333;
      padding: 16px;
      border: 1px solid #ff3333;
      border-radius: 4px;
      background: #fff5f5;
      font-family: monospace;
      white-space: pre-wrap;
    }
    
    /* 可移动组件样式 */
    .lynx-movable-area {
      position: relative;
      overflow: hidden;
    }
    
    .lynx-movable-view {
      position: absolute;
      top: 0;
      left: 0;
      cursor: move;
      user-select: none;
      touch-action: none;
    }
    
    .lynx-movable-view[data-disabled="true"] {
      cursor: default;
      pointer-events: none;
    }
    
    .lynx-movable-view:active {
      z-index: 999;
    }
    
    /* 高级组件增强样式 */
    .lynx-swiper {
      position: relative;
      overflow: hidden;
    }
    
    .lynx-swiper[data-vertical="true"] {
      height: 200px;
    }
    
    .lynx-swiper-item {
      flex-shrink: 0;
      width: 100%;
      height: 100%;
    }
    
    .lynx-progress {
      width: 100%;
      height: 6px;
      appearance: none;
    }
    
    .lynx-progress::-webkit-progress-bar {
      background-color: #f0f0f0;
      border-radius: 3px;
    }
    
    .lynx-progress::-webkit-progress-value {
      background-color: #007aff;
      border-radius: 3px;
    }
    
    /* 覆盖组件样式 */
    .lynx-cover-view,
    .lynx-cover-image {
      position: absolute;
      z-index: 1000;
    }
`;
  }

  /**
   * 生成作用域CSS
   */
  private generateScopedCSS(css: string, componentId: string): string {
    console.log('🔍 [HTMLGenerator] CSS注入分析:', {
      hasCSS: !!css,
      cssLength: css?.length || 0,
      componentId,
      cssType: typeof css,
      // 🆕 检测 CSS 类型
      isAlreadyScoped: css?.includes('[data-v-') || false,
      hasRpxUnits: css?.includes('rpx') || false,
      cssPreview: css?.substring(0, 200) || 'EMPTY',
    });

    // 🔧 P0修复：即使CSS为空，也提供基础样式支持
    if (!css?.trim()) {
      console.log('🔧 [HTMLGenerator] CSS为空，提供基础降级样式');
      return this.generateFallbackCSS(componentId);
    }

    // 🆕 智能CSS处理：检测是否已经作用域化
    const isAlreadyScoped =
      css.includes(`[data-v-${componentId}]`) || css.includes('[data-v-');

    if (isAlreadyScoped) {
      console.log('🎯 [HTMLGenerator] 检测到已作用域化的CSS，直接使用');
      return `
    /* 已作用域化的组件样式 - ${componentId} */
${css}
    `;
    } else {
      console.log('🎯 [HTMLGenerator] 检测到纯净CSS，添加基础增强');
      return `
    /* 纯净组件样式 - ${componentId} */
${css}
    
    /* 🆕 为iframe环境添加基础增强 */
    .component-wrapper[data-v-${componentId}] {
      position: relative;
      box-sizing: border-box;
      /* 确保iframe中的基础布局 */
    }
    `;
    }
  }

  /**
   * 🆕 生成降级CSS
   */
  private generateFallbackCSS(componentId: string): string {
    return `
    /* 基础组件样式 - ${componentId} */
    [data-v-${componentId}] {
      position: relative;
      box-sizing: border-box;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      font-size: 14px;
      line-height: 1.5;
      color: #333;
    }
    
    /* 基础响应式支持 */
    [data-v-${componentId}] * {
      box-sizing: border-box;
    }
    
    /* 基础 Lynx 组件样式 */
    [data-v-${componentId}] .lynx-view { 
      display: block; 
    }
    [data-v-${componentId}] .lynx-text { 
      display: inline; 
    }
    [data-v-${componentId}] .lynx-image { 
      display: block; 
      max-width: 100%; 
      height: auto; 
    }
    [data-v-${componentId}] .lynx-button { 
      display: inline-block; 
      padding: 8px 16px; 
      border: 1px solid #ddd; 
      border-radius: 4px; 
      background: #fff; 
      cursor: pointer; 
    }
  `;
  }

  /**
   * 生成React依赖
   */
  private generateReactDependencies(): string {
    return `
  <!-- React 核心库 (确保加载顺序) -->
  <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
  <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
`;
  }

  /**
   * 生成React设置代码
   */
  private generateReactSetup(): string {
    return `
    // React环境设置
    const { useState, useEffect, useCallback, useMemo } = React;
    const { createRoot } = ReactDOM;
    
    // 全局错误处理
    window.addEventListener('error', function(e) {
      console.error('Preview错误:', e.error);
    });
    
    // 组件通用工具函数
    const utils = {
      // 模拟小程序API
      setData: function(data, callback) {
        console.log('setData called:', data);
        if (callback) callback();
      },
      
      // 事件处理辅助
      preventDefault: function(e) {
        if (e && e.preventDefault) e.preventDefault();
      },
      
      stopPropagation: function(e) {
        if (e && e.stopPropagation) e.stopPropagation();
      },
      
      // 生命周期模拟
      triggerLifecycle: function(name, ...args) {
        console.log(\`生命周期: \${name}\`, args);
      }
    };
    
    // 全局组件状态管理
    const globalState = {
      data: {},
      methods: {},
      lifecycle: {}
    };
`;
  }

  /**
   * 生成组件脚本
   */
  private generateComponentScript(
    jsx: string,
    js: string,
    componentId: string,
  ): string {
    const componentCode = this.wrapComponentCode(jsx, js, componentId);

    return `
    // 组件定义 - ${componentId}
    const Component_${componentId} = function(props) {
      const [data, setData] = useState({
        // 🔧 修复：添加默认数据以避免未定义变量
        numbers: Array.from({length: 100}, (_, i) => i + 1),
        selectedNumber: null,
        currentIndex: 0,
        list: [],
        items: [],
        text: '',
        value: '',
        isVisible: true,
        count: 0
      });
      const [loading, setLoading] = useState(false);
      
      // 组件方法
      const componentMethods = {
        setData: useCallback((newData, callback) => {
          setData(prevData => ({ ...prevData, ...newData }));
          if (callback) callback();
        }, []),
        
        getData: useCallback(() => data, [data]),
        
        // 事件处理器
        handleTap: useCallback((e) => {
          utils.preventDefault(e);
          console.log('tap event:', e);
        }, []),
        
        handleInput: useCallback((e) => {
          const value = e.target.value;
          console.log('input event:', value);
        }, []),
        
        handleChange: useCallback((e) => {
          const value = e.target.value;
          console.log('change event:', value);
        }, [])
      };
      
      // 生命周期模拟
      useEffect(() => {
        utils.triggerLifecycle('onLoad');
        
        return () => {
          utils.triggerLifecycle('onUnload');
        };
      }, []);
      
      useEffect(() => {
        utils.triggerLifecycle('onShow');
      });
      
      // 用户JavaScript代码
      ${js || '// 无用户JavaScript代码'}
      
      // 渲染组件 - 🔧 P0修复：完全避免JSX语法错误
      try {
        // 安全处理jsx内容，避免语法错误
        var safeContent;
        try {
          safeContent = ${jsx && jsx.trim() ? `(${jsx})` : 'React.createElement("div", { className: "ttml-error" }, "无有效的TTML内容")'};
        } catch (jsxError) {
          console.warn('JSX解析错误，使用降级内容:', jsxError);
          safeContent = React.createElement("div", { className: "ttml-error" }, "JSX解析失败");
        }
        
        // 🔧 修复：构建正确的属性对象
        const wrapperProps = {
          className: "component-wrapper",
          'data-component-type': 'ttml-converted',
          'data-css-variant': 'iframe-clean'
        };
        // 添加动态的 data-v 属性
        wrapperProps['data-v-' + componentId] = '';
        
        return React.createElement(
          "div",
          wrapperProps,
          safeContent
        );
      } catch (error) {
        console.error('组件渲染错误:', error);
        // 🔧 P0修复：最安全的HTML降级方案
        return React.createElement(
          "div", 
          { className: "ttml-error" },
          "渲染失败: " + (error && error.message ? String(error.message) : "未知错误")
        );
      }
    };
`;
  }

  /**
   * 包装组件代码
   */
  private wrapComponentCode(
    jsx: string,
    js: string,
    componentId: string,
  ): string {
    // 确保JSX代码的安全性和有效性
    if (!jsx || jsx.trim() === '') {
      return '<div className="empty-component">空组件</div>';
    }

    // 简单的JSX验证和清理
    let cleanJSX = jsx.trim();

    // 确保有根元素包装
    if (!cleanJSX.startsWith('<') || !cleanJSX.endsWith('>')) {
      cleanJSX = `<div>${cleanJSX}</div>`;
    }

    return cleanJSX;
  }

  /**
   * 生成渲染脚本
   */
  private generateRenderScript(componentId: string): string {
    return `
    // 等待React加载完成
    function waitForReact() {
      return new Promise((resolve) => {
        if (window.React && window.ReactDOM) {
          resolve();
        } else {
          setTimeout(() => waitForReact().then(resolve), 50);
        }
      });
    }
    
    // 渲染应用
    waitForReact().then(() => {
      try {
        const container = document.getElementById('lynx-app-container');
        if (!container) {
          console.error('找不到容器元素: lynx-app-container');
          return;
        }
        
        if (!window.React || !window.ReactDOM) {
          console.error('React或ReactDOM未加载');
          container.innerHTML = '<div class="ttml-error">React依赖加载失败</div>';
          return;
        }
        
        const { createRoot } = ReactDOM;
        const root = createRoot(container);
        
        // 错误边界组件
        const ErrorBoundary = function({ children }) {
          const [hasError, setHasError] = useState(false);
          const [error, setError] = useState(null);
          
          useEffect(() => {
            const handleError = (event) => {
              setHasError(true);
              setError(event.error);
            };
            
            window.addEventListener('error', handleError);
            return () => window.removeEventListener('error', handleError);
          }, []);
          
          if (hasError) {
            return React.createElement('div', {
              className: 'ttml-error'
            }, \`组件运行时错误: \${error?.message || '未知错误'}\`);
          }
          
          return children;
        };
        
        // 渲染组件
        root.render(
          React.createElement(ErrorBoundary, null,
            React.createElement(Component_${componentId}, null)
          )
        );
        
        console.log('Lynx Preview渲染完成 - ${componentId}');
      } catch (error) {
        console.error('渲染失败:', error);
        const container = document.getElementById('lynx-app-container');
        if (container) {
          container.innerHTML = '<div class="ttml-error">渲染失败: ' + error.message + '</div>';
        }
      }
    }).catch(error => {
      console.error('React加载超时:', error);
      const container = document.getElementById('lynx-app-container');
      if (container) {
        container.innerHTML = '<div class="ttml-error">React加载超时</div>';
      }
    });
`;
  }

  /**
   * 生成错误HTML
   */
  private generateErrorHTML(error: any): string {
    const errorMsg = error instanceof Error ? error.message : String(error);

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Preview Error</title>
  <style>
    body {
      font-family: monospace;
      padding: 20px;
      background: #f5f5f5;
    }
    .error-container {
      background: #fff;
      border: 1px solid #ff3333;
      border-radius: 4px;
      padding: 20px;
      color: #ff3333;
    }
    .error-title {
      font-size: 18px;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .error-message {
      white-space: pre-wrap;
      font-size: 14px;
    }
  </style>
</head>
<body>
  <div class="error-container">
    <div class="error-title">Parse5 Preview生成失败</div>
    <div class="error-message">${this.escapeHtml(errorMsg)}</div>
  </div>
</body>
</html>`;
  }

  /**
   * HTML转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * 获取生成统计信息
   */
  public getStats(): any {
    return {
      generator: 'HTMLGenerator',
      version: '1.0.0',
      capabilities: [
        'React JSX rendering',
        'CSS scoping',
        'Error boundaries',
        'Responsive layout',
        'Lynx component support',
      ],
    };
  }

  // ===============================
  // 调试辅助方法
  // ===============================

  /**
   * 分析输入内容
   */
  private analyzeInputContent(input: HTMLGeneratorInput): any {
    const analysis = {
      hasJSX: !!input.jsx && input.jsx.length > 0,
      hasCSS: !!input.css && input.css.length > 0,
      hasJS: !!input.js && input.js.length > 0,
      hasComponentId: !!input.componentId,
      jsxLength: input.jsx?.length || 0,
      cssLength: input.css?.length || 0,
      jsLength: input.js?.length || 0,
      hasContent: false,
      contentTypes: [] as string[],
      jsxStructure: {},
      cssStructure: {},
      jsStructure: {},
    };

    // 确定有效内容
    if (analysis.hasJSX) {
      analysis.contentTypes.push('JSX');
    }
    if (analysis.hasCSS) {
      analysis.contentTypes.push('CSS');
    }
    if (analysis.hasJS) {
      analysis.contentTypes.push('JS');
    }
    analysis.hasContent = analysis.contentTypes.length > 0;

    // 分析JSX结构
    if (input.jsx) {
      analysis.jsxStructure = {
        hasReactCalls: input.jsx.includes('React.createElement'),
        hasFragments: input.jsx.includes('React.Fragment'),
        elementCount: (input.jsx.match(/React\.createElement/g) || []).length,
        hasClassNames: input.jsx.includes('className'),
        hasStyles: input.jsx.includes('style:'),
        hasEventHandlers:
          input.jsx.includes('onClick') || input.jsx.includes('onChange'),
      };
    }

    // 分析CSS结构
    if (input.css) {
      analysis.cssStructure = {
        ruleCount: (input.css.match(/[^{}]+\s*\{[^}]*\}/g) || []).length,
        hasClasses: input.css.includes('.'),
        hasIds: input.css.includes('#'),
        hasMediaQueries: input.css.includes('@media'),
        hasKeyframes: input.css.includes('@keyframes'),
        hasRpx: input.css.includes('rpx'),
        hasVw: input.css.includes('vw'),
        hasRem: input.css.includes('rem'),
      };
    }

    // 分析JS结构
    if (input.js) {
      analysis.jsStructure = {
        hasFunctions: input.js.includes('function'),
        hasArrowFunctions: input.js.includes('=>'),
        hasClasses: input.js.includes('class '),
        hasConsoleLog: input.js.includes('console.log'),
        hasEventListeners: input.js.includes('addEventListener'),
        hasSetData: input.js.includes('setData'),
        hasLifecycle:
          input.js.includes('onLoad') || input.js.includes('onShow'),
      };
    }

    return analysis;
  }

  /**
   * 分析生成的HTML
   */
  private analyzeGeneratedHTML(html: string): any {
    const analysis = {
      totalLength: html.length,
      hasDoctype: html.includes('<!DOCTYPE html>'),
      hasHtmlTag: html.includes('<html'),
      hasHeadTag: html.includes('<head>'),
      hasBodyTag: html.includes('<body>'),
      hasCharset: html.includes('charset='),
      hasViewport: html.includes('viewport'),
      hasTitle: html.includes('<title>'),
      hasStyles: html.includes('<style>'),
      hasScripts: html.includes('<script>'),
      hasReactDeps: html.includes('react'),
      hasComponent: html.includes('lynx-preview-root'),
      hasErrorBoundary: html.includes('ErrorBoundary'),
      estimatedParts: {
        doctype: html.includes('<!DOCTYPE html>') ? 1 : 0,
        head: (html.match(/<head[\s\S]*?<\/head>/g) || []).length,
        style: (html.match(/<style[\s\S]*?<\/style>/g) || []).length,
        script: (html.match(/<script[\s\S]*?<\/script>/g) || []).length,
        body: (html.match(/<body[\s\S]*?<\/body>/g) || []).length,
      },
      potentialIssues: [] as string[],
    };

    // 检查潜在问题
    if (!analysis.hasDoctype) {
      analysis.potentialIssues.push('缺少DOCTYPE声明');
    }
    if (!analysis.hasCharset) {
      analysis.potentialIssues.push('缺少字符集声明');
    }
    if (!analysis.hasViewport) {
      analysis.potentialIssues.push('缺少viewport meta标签');
    }
    if (!analysis.hasReactDeps) {
      analysis.potentialIssues.push('缺少React依赖');
    }
    if (html.length < 1000) {
      analysis.potentialIssues.push('HTML内容可能过短');
    }
    if (!analysis.hasComponent) {
      analysis.potentialIssues.push('缺少主要组件容器');
    }

    return analysis;
  }

  /**
   * 验证HTML结构
   */
  private validateHTMLStructure(html: string, input: HTMLGeneratorInput): void {
    const validation = {
      isValidHTML: false,
      hasRequiredElements: false,
      hasValidStructure: false,
      componentIntegration: false,
      reactIntegration: false,
      cssIntegration: false,
      jsIntegration: false,
      errors: [] as string[],
      warnings: [] as string[],
    };

    // 基本HTML结构验证
    validation.isValidHTML =
      html.includes('<!DOCTYPE html>') &&
      html.includes('<html') &&
      html.includes('<head>') &&
      html.includes('<body>') &&
      html.includes('</html>');

    // 必需元素验证
    validation.hasRequiredElements =
      html.includes('<meta charset=') &&
      html.includes('<title>') &&
      html.includes('lynx-preview-root');

    // 结构完整性验证
    const openTags = (html.match(/<[^\/][^>]*>/g) || []).length;
    const closeTags = (html.match(/<\/[^>]*>/g) || []).length;
    validation.hasValidStructure = Math.abs(openTags - closeTags) < 5; // 允许一些自闭合标签

    // 组件集成验证
    validation.componentIntegration =
      html.includes(input.componentId) && html.includes('lynx-app-container');

    // React集成验证
    validation.reactIntegration =
      html.includes('React') &&
      html.includes('ReactDOM') &&
      html.includes('createRoot');

    // CSS集成验证
    if (input.css && input.css.length > 0) {
      validation.cssIntegration =
        html.includes('<style>') && html.includes(input.css.substring(0, 50));
    } else {
      validation.cssIntegration = true; // 无CSS时认为集成成功
    }

    // JS集成验证
    if (input.js && input.js.length > 0) {
      validation.jsIntegration =
        html.includes('<script>') && html.includes('// 用户JavaScript代码');
    } else {
      validation.jsIntegration = true; // 无JS时认为集成成功
    }

    // 收集错误和警告
    if (!validation.isValidHTML) {
      validation.errors.push('HTML结构不完整');
    }
    if (!validation.hasRequiredElements) {
      validation.errors.push('缺少必需的HTML元素');
    }
    if (!validation.componentIntegration) {
      validation.errors.push('组件集成失败');
    }
    if (!validation.reactIntegration) {
      validation.errors.push('React集成失败');
    }
    if (!validation.cssIntegration) {
      validation.warnings.push('CSS集成可能有问题');
    }
    if (!validation.jsIntegration) {
      validation.warnings.push('JavaScript集成可能有问题');
    }

    console.log('🔍 [HTMLGenerator] HTML结构验证结果:', validation);

    if (validation.errors.length > 0) {
      console.error('❌ [HTMLGenerator] HTML验证发现错误:', validation.errors);
    }
    if (validation.warnings.length > 0) {
      console.warn('⚠️ [HTMLGenerator] HTML验证发现警告:', validation.warnings);
    }
    if (validation.errors.length === 0 && validation.warnings.length === 0) {
      console.log('✅ [HTMLGenerator] HTML结构验证通过');
    }
  }
}
