/**
 * @package runtime-convert-parse5
 * @description Parse5-based TTML/TTSS转换引擎
 * @version 1.0.0
 * <AUTHOR> Code Development Team
 */

import type {
  InputFiles,
  TransformResult,
  TransformConfig,
  TransformError,
  ErrorType,
} from '../deleted_runtime_convert/types';

import { Parse5TTMLAdapter } from './adapters/parse5-ttml-adapter';
import { TTSSProcessor } from './processors/ttss-processor';
import { HTMLGenerator } from './generators/html-generator';
import { TransformCache } from './utils/lru-cache';
// import {
//   TTML_ELEMENT_MAPPING,
//   TTML_DIRECTIVE_MAPPING,
//   TTSS_CONVERSION_CONFIG,
// } from './mappings';

/**
 * Parse5转换引擎配置
 */
export interface Parse5TransformConfig extends TransformConfig {
  useWebSpeedyPlugin?: boolean; // 是否使用@byted-lynx/web-speedy-plugin的映射规则
  strictMode?: boolean; // 严格模式，语法错误时抛出异常
  enableSourceMap?: boolean; // 启用源码映射
  workerMode?: boolean; // Worker模式标识
  disableWorker?: boolean; // 禁用Worker
}

/**
 * 基于Parse5的TTML/TTSS转换引擎
 * 使用Parse5作为HTML解析器，结合文档中提取的映射规则实现转换
 */
export class Parse5TransformEngine {
  private config: Required<Parse5TransformConfig>;
  private ttmlAdapter: Parse5TTMLAdapter;
  private ttssProcessor: TTSSProcessor;
  private htmlGenerator: HTMLGenerator;

  // 增强的缓存系统
  private transformCache: TransformCache;

  constructor(config: Parse5TransformConfig = {}) {
    this.config = {
      componentId: config.componentId || this.generateComponentId(),
      enableScope: config.enableScope !== false,
      enableCache: config.enableCache !== false,
      maxCacheSize: config.maxCacheSize || 100,
      enableOptimization: config.enableOptimization !== false,
      enableStrictMode: config.enableStrictMode || false,
      usingComponents: config.usingComponents || {},
      useWebSpeedyPlugin: config.useWebSpeedyPlugin !== false,
      strictMode: config.strictMode || false,
      enableSourceMap: config.enableSourceMap || false,
      workerMode: config.workerMode || false,
      disableWorker: config.disableWorker || false,

      ...config,
    };

    // 初始化各个处理器
    this.ttmlAdapter = new Parse5TTMLAdapter(this.config);
    this.ttssProcessor = new TTSSProcessor(this.config);
    this.htmlGenerator = new HTMLGenerator(this.config);

    // 初始化缓存系统
    this.transformCache = new TransformCache(this.config.maxCacheSize || 50);
  }

  /**
   * 🔧 P0修复：安全的日志方法，兼容Worker环境
   */
  private log(level: string, message: string, data?: any): void {
    try {
      const prefix = this.config.workerMode ? '[Worker]' : '[Main]';
      if (data) {
        console.log(`${prefix} ${message}`, data);
      } else {
        console.log(`${prefix} ${message}`);
      }
    } catch (error) {
      // 降级到基础console.log
      console.log(message, data || '');
    }
  }

  /**
   * 🔧 P0修复：安全的计时器方法，兼容Worker环境
   */
  private startTimer(name: string): () => number {
    const startTime = performance.now();
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.log('debug', `⏱️ ${name} 耗时: ${duration.toFixed(2)}ms`);
      return duration;
    };
  }

  /**
   * 转换TTML/TTSS文件为Web预览
   * 这是主要的对外API接口
   */
  async convert(files: InputFiles): Promise<TransformResult> {
    const startTime = performance.now();
    const cacheKey = this.transformCache.generateKey(
      JSON.stringify(files),
      this.config,
    );

    // 🚨 P0修复：强制调试信息，确保代码被执行
    console.log(
      '🚨🚨🚨 [Parse5TransformEngine] 转换引擎启动 - 强制调试模式 🚨🚨🚨',
    );
    console.log(
      '🔧 [Parse5TransformEngine] 输入TTML长度:',
      files.ttml?.length || 0,
    );
    console.log(
      '🔧 [Parse5TransformEngine] 输入TTML包含tt:for:',
      files.ttml?.includes('tt:for') || false,
    );
    console.log(
      '🔧 [Parse5TransformEngine] 输入TTML包含{{}}:',
      files.ttml?.includes('{{') || false,
    );
    console.log(
      '🔧 [Parse5TransformEngine] Worker模式:',
      this.config.workerMode,
    );

    // 🔧 优化：使用智能日志
    this.log('info', '🚀 [Parse5TransformEngine] 转换引擎启动');
    this.log(
      'debug',
      '🔧 [Parse5TransformEngine] 模式:',
      this.config.workerMode ? 'Worker' : 'Main Thread',
    );
    this.log('debug', '🔧 [Parse5TransformEngine] 配置:', {
      useSimplifiedMode: this.config.useSimplifiedMode,
      enableDirectHTML: this.config.enableDirectHTML,
      logLevel: this.config.logLevel,
    });

    // 分析输入文件
    const inputAnalysis = this.analyzeInputFiles(files);

    // 🔧 P0修复：强制禁用缓存，确保模板引擎修复生效
    console.log('🔧 [Parse5TransformEngine] 强制禁用缓存，确保使用最新代码');
    // 注释掉缓存检查，强制重新转换
    // if (this.config.enableCache) {
    //   const cachedResult = this.transformCache.getTransformResult(cacheKey);
    //   if (cachedResult) {
    //     this.log('info', '📋 [Parse5TransformEngine] 命中缓存');
    //     return {
    //       success: true,
    //       html: cachedResult.html,
    //       metadata: {
    //         ...cachedResult.metadata,
    //         fromCache: true,
    //         cacheHit: true,
    //       },
    //     };
    //   } else {
    //     this.log('debug', '📋 [Parse5TransformEngine] 缓存未命中，开始转换');
    //   }
    // }

    try {
      // 输入验证
      console.log('\n🔍 [Parse5TransformEngine] ==> 输入验证阶段 <==');
      this.validateInput(files);
      console.log('✅ [Parse5TransformEngine] 输入验证通过');

      // 执行转换
      console.log('\n🔄 [Parse5TransformEngine] ==> 执行转换阶段 <==');
      const result = await this.performTransform(files, startTime);

      // 缓存结果
      if (this.config.enableCache && result.html) {
        console.log('📋 [Parse5TransformEngine] 将结果存入缓存');
        this.transformCache.setTransformResult(
          cacheKey,
          result.html,
          result.metadata,
        );
      }

      return result;
    } catch (error) {
      const totalTime = performance.now() - startTime;

      console.error('\n❌ [Parse5TransformEngine] ==> 转换引擎失败 <==');
      console.error('💥 [Parse5TransformEngine] 错误详情:', {
        errorType:
          error instanceof Error ? error.constructor.name : typeof error,
        errorMessage: error instanceof Error ? error.message : String(error),
        errorStack: error instanceof Error ? error.stack : undefined,
        failureTime: `${totalTime.toFixed(2)}ms`,
        inputAnalysis,
      });

      const transformError: TransformResult = {
        success: false,
        error: this.classifyError(error),
        message: `Parse5转换失败: ${error instanceof Error ? error.message : String(error)}`,
        html: '',
        metadata: {
          transformTime: totalTime,
          componentId: this.config.componentId,
          errors: [this.createTransformError(error)],
        } as any,
      };

      return transformError;
    }
  }

  /**
   * 执行实际的转换工作
   */
  private async performTransform(
    files: InputFiles,
    startTime: number,
  ): Promise<TransformResult> {
    const results = {
      ttml: null as any,
      ttss: null as any,
      js: null as any,
    };

    const phaseStartTime = performance.now();

    // 1. 转换TTML (使用Parse5) - 增强错误处理
    if (files.ttml) {
      this.log('info', '📄 [Parse5TransformEngine] 开始TTML转换...');
      const endTtmlTimer = this.startTimer('TTML转换');

      try {
        results.ttml = await this.ttmlAdapter.transform(
          files.ttml,
          this.config.componentId,
        );
        const ttmlTime = endTtmlTimer();
        this.log('info', '✅ [Parse5TransformEngine] TTML转换完成');
        this.log('debug', '📊 [Parse5TransformEngine] TTML转换结果:', {
          hasJSX: !!results.ttml?.jsx,
          jsxLength: results.ttml?.jsx?.length || 0,
          elementCount: results.ttml?.elementCount || 0,
          hasErrors: results.ttml?.errors?.length > 0,
        });
      } catch (error) {
        this.log('error', '❌ [Parse5TransformEngine] TTML转换失败:', error);

        // 🔧 P0修复：不使用降级，直接返回原始TTML避免破坏模板语法
        console.log(
          '🔧 [Parse5TransformEngine] 返回原始TTML，避免破坏模板语法',
        );
        results.ttml = {
          jsx: files.ttml || '<div class="ttml-error">无TTML内容</div>',
          ast: null,
          elementCount: 0,
          errors: [error.message],
        };
      }
    } else {
      console.log('📭 [Parse5TransformEngine] 无TTML文件，跳过转换');
    }

    // 2. 转换TTSS - 增强错误处理
    if (files.ttss) {
      console.log('🎨 [Parse5TransformEngine] 开始TTSS转换...');
      const ttssStartTime = performance.now();

      try {
        results.ttss = await this.ttssProcessor.process(
          files.ttss,
          this.config.componentId,
        );
        const ttssTime = performance.now() - ttssStartTime;
        console.log(
          '✅ [Parse5TransformEngine] TTSS转换完成，耗时:',
          `${ttssTime.toFixed(2)}ms`,
        );
        console.log('📊 [Parse5TransformEngine] TTSS转换结果:', {
          hasCSS: !!results.ttss?.css,
          cssLength: results.ttss?.css?.length || 0,
          ruleCount: results.ttss?.ruleCount || 0,
          classCount: results.ttss?.classes?.length || 0,
          rpxConverted: results.ttss?.rpxConverted,
          hasErrors: results.ttss?.errors?.length > 0,
        });
      } catch (error) {
        console.error('❌ [Parse5TransformEngine] TTSS转换失败:', error);

        // 🔧 降级策略：生成基础CSS
        results.ttss = {
          css: `
            /* TTSS 转换失败，使用基础样式 */
            .ttml-error {
              padding: 20px;
              border: 1px solid #ff6b6b;
              background: #ffe0e0;
              color: #d63031;
              border-radius: 4px;
              margin: 10px;
            }

            /* 基础 Lynx 样式 */
            .lynx-view { display: block; }
            .lynx-text { display: inline; }
            .lynx-fragment { display: contents; }

            /* 原始样式（如果可解析） */
            ${this.extractBasicCSS(files.ttss)}
          `,
          scopedCss: '',
          ruleCount: 0,
          classes: [],
          rpxConverted: false,
          errors: [error.message],
        };

        console.log('🔧 [Parse5TransformEngine] TTSS降级处理完成');
      }
    } else {
      console.log('📭 [Parse5TransformEngine] 无TTSS文件，跳过转换');
    }

    // 3. 处理JavaScript
    if (files.js) {
      console.log('📜 [Parse5TransformEngine] 开始JS处理...');
      const jsStartTime = performance.now();
      results.js = await this.processJS(files.js);
      const jsTime = performance.now() - jsStartTime;
      console.log(
        '✅ [Parse5TransformEngine] JS处理完成，耗时:',
        `${jsTime.toFixed(2)}ms`,
      );
      console.log('📊 [Parse5TransformEngine] JS处理结果:', {
        hasCode: !!results.js?.code,
        codeLength: results.js?.code?.length || 0,
        hasUserCode: results.js?.hasUserCode,
      });
    } else {
      console.log('📭 [Parse5TransformEngine] 无JS文件，跳过处理');
    }

    // 4. 生成完整HTML
    console.log('\n🎨 [Parse5TransformEngine] ==> HTML生成阶段 <==');
    const htmlStartTime = performance.now();

    // 🔧 P0修复：根据渲染环境智能选择 CSS 版本
    let finalCSS: string;
    let cssVariant: string;

    // 🎯 核心逻辑：iframe 环境使用纯净CSS，组件环境使用作用域CSS
    if (this.config.enableDirectHTML || this.config.useSimplifiedMode) {
      // iframe 独立渲染模式：使用纯净CSS
      finalCSS = results.ttss?.css || '';
      cssVariant = 'iframe-clean';
      console.log('🎯 [Parse5TransformEngine] 选择iframe纯净CSS模式');
    } else {
      // 组件集成模式：使用作用域CSS
      finalCSS = results.ttss?.scopedCss || results.ttss?.css || '';
      cssVariant = 'component-scoped';
      console.log('🎯 [Parse5TransformEngine] 选择组件作用域CSS模式');
    }

    // 🚨 紧急修复：如果选中的CSS为空但有备选，使用备选
    if (!finalCSS && results.ttss) {
      const fallbackCSS =
        cssVariant === 'iframe-clean'
          ? results.ttss?.scopedCss
          : results.ttss?.css;

      if (fallbackCSS) {
        console.log('🔄 [Parse5TransformEngine] CSS回退策略：使用备选CSS版本');
        finalCSS = fallbackCSS;
        cssVariant =
          cssVariant === 'iframe-clean' ? 'fallback-scoped' : 'fallback-clean';
      }
    }

    // 🚨 最终降级：如果CSS仍为空但有原始TTSS，直接处理
    if (!finalCSS && files.ttss) {
      console.log('🚨 [Parse5TransformEngine] CSS仍为空，执行紧急降级处理');
      finalCSS = this.emergencyProcessCSS(files.ttss, this.config.componentId);
      cssVariant = 'emergency-processed';
    }

    // 🔍 详细的CSS传递验证
    this.log('info', '🎨 [Parse5TransformEngine] CSS传递详细分析:', {
      // 输入分析
      输入分析: {
        hasTTSS: !!files.ttss,
        ttssLength: files.ttss?.length || 0,
        ttssPreview: files.ttss?.substring(0, 100) || 'EMPTY',
      },

      // TTSS处理结果
      TTSS处理结果: {
        hasProcessedResult: !!results.ttss,
        originalCSS: {
          存在: !!results.ttss?.css,
          长度: results.ttss?.css?.length || 0,
          包含作用域: results.ttss?.css?.includes('[data-v-') || false,
          预览: results.ttss?.css?.substring(0, 100) || 'EMPTY',
        },
        scopedCSS: {
          存在: !!results.ttss?.scopedCss,
          长度: results.ttss?.scopedCss?.length || 0,
          包含作用域: results.ttss?.scopedCss?.includes('[data-v-') || false,
          预览: results.ttss?.scopedCss?.substring(0, 100) || 'EMPTY',
        },
        处理统计: {
          规则数量: results.ttss?.ruleCount || 0,
          类名数量: results.ttss?.classes?.length || 0,
          RPX转换: results.ttss?.rpxConverted || false,
          错误数量: results.ttss?.errors?.length || 0,
        },
      },

      // 最终选择
      最终选择: {
        选择策略: cssVariant,
        渲染模式: this.config.enableDirectHTML ? 'iframe独立' : '组件集成',
        CSS长度: finalCSS.length,
        CSS有效: finalCSS.length > 0,
        CSS预览: finalCSS.substring(0, 150) || 'EMPTY',
        包含作用域: finalCSS.includes('[data-v-'),
        包含RPX: finalCSS.includes('rpx'),
      },
    });

    // 🔧 新增：详细的 CSS 内容调试
    if (finalCSS) {
      this.log('debug', '🎨 [Parse5TransformEngine] CSS 内容预览:', {
        cssPreview: finalCSS.substring(0, 200),
        hasRules: finalCSS.includes('{'),
        hasClasses: finalCSS.includes('.'),
        hasRpxConverted: !finalCSS.includes('rpx'),
      });
    } else {
      this.log('warn', '⚠️ [Parse5TransformEngine] 警告：最终 CSS 为空！');

      // 调试原始 TTSS
      if (files.ttss) {
        this.log('debug', '🔍 [Parse5TransformEngine] 原始 TTSS 内容:', {
          ttssLength: files.ttss.length,
          ttssPreview: files.ttss.substring(0, 200),
          ttssHasRules: files.ttss.includes('{'),
        });
      }
    }

    // 🔧 修复：使用安全的HTML生成，包含错误处理和降级策略
    let html: string;
    try {
      if (this.config.useSimplifiedMode && this.config.enableDirectHTML) {
        console.log('🔧 [Parse5TransformEngine] 使用简化HTML生成模式');
        html = await this.htmlGenerator.generateSimplified({
          jsx: results.ttml?.jsx || '',
          css: finalCSS,
          js: results.js?.code || '',
          componentId: this.config.componentId,
        });
      } else {
        console.log('🔧 [Parse5TransformEngine] 使用标准HTML生成模式');
        html = await this.htmlGenerator.generate({
          jsx: results.ttml?.jsx || '',
          css: finalCSS,
          js: results.js?.code || '',
          componentId: this.config.componentId,
        });
      }
    } catch (htmlError) {
      console.error(
        '❌ [Parse5TransformEngine] HTML生成失败，尝试降级:',
        htmlError,
      );

      // 🔧 降级策略：尝试简化模式
      try {
        html = await this.htmlGenerator.generateSimplified({
          jsx: results.ttml?.jsx || '',
          css: finalCSS,
          js: results.js?.code || '',
          componentId: this.config.componentId,
        });
        console.log('✅ [Parse5TransformEngine] HTML降级生成成功');
      } catch (fallbackError) {
        console.error(
          '❌ [Parse5TransformEngine] HTML降级也失败，使用最终降级:',
          fallbackError,
        );

        // 最终降级：生成最基本的HTML
        html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Lynx Preview - 降级模式</title>
  <style>
    body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif; margin: 20px; background: #f5f5f5; }
    .error-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); }
    .error-title { color: #e74c3c; margin-bottom: 10px; }
    .content-preview { background: #f8f9fa; padding: 15px; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
    ${finalCSS || ''}
  </style>
</head>
<body>
  <div class="error-container">
    <h2 class="error-title">⚠️ 预览生成失败</h2>
    <p>转换过程中遇到问题，以下是原始内容预览：</p>
    <div class="content-preview">${results.ttml?.jsx || '无内容'}</div>
  </div>
  <script>
    console.log('Lynx Preview - 降级模式');
    ${results.js?.code || '// 无JavaScript代码'}
  </script>
</body>
</html>`;
      }
    }
    const htmlTime = performance.now() - htmlStartTime;

    console.log(
      '✅ [Parse5TransformEngine] HTML生成完成，耗时:',
      `${htmlTime.toFixed(2)}ms`,
    );
    console.log('📊 [Parse5TransformEngine] HTML生成结果:', {
      htmlLength: html.length,
      hasDoctype: html.includes('<!DOCTYPE html>'),
      hasReact: html.includes('React'),
      hasComponent: html.includes(this.config.componentId),
    });

    const endTime = performance.now();
    const totalPhaseTime = endTime - phaseStartTime;

    // 5. 构建转换结果
    const transformResult: TransformResult = {
      success: true,
      html,
      metadata: {
        transformTime: endTime - startTime,
        componentId: this.config.componentId,
        elementCount: results.ttml?.elementCount || 0,
        cssRules: results.ttss?.ruleCount || 0,
        sourceMap: this.config.enableSourceMap
          ? this.generateSourceMap(files, results)
          : undefined,
      } as any,
    };

    // ============== DEBUG LOG: 转换完成总结 ==============
    console.log('\n✅ [Parse5TransformEngine] ==> 转换引擎完成总结 <==');
    console.log(
      '⏰ [Parse5TransformEngine] 总转换耗时:',
      `${(endTime - startTime).toFixed(2)}ms`,
    );
    console.log('📊 [Parse5TransformEngine] 最终转换统计:', {
      success: true,
      totalTime: `${(endTime - startTime).toFixed(2)}ms`,
      phaseTime: `${totalPhaseTime.toFixed(2)}ms`,
      componentId: this.config.componentId,
      elementCount: (transformResult.metadata as any)?.elementCount,
      cssRules: (transformResult.metadata as any)?.cssRules,
      htmlLength: html.length,
      hasSourceMap: !!(transformResult.metadata as any)?.sourceMap,
      phases: {
        ttml: results.ttml ? 'completed' : 'skipped',
        ttss: results.ttss ? 'completed' : 'skipped',
        js: results.js ? 'completed' : 'skipped',
        html: 'completed',
      },
    });

    // 验证转换结果
    this.validateTransformResult(transformResult, files, results);

    return transformResult;
  }

  /**
   * 输入验证
   */
  private validateInput(files: InputFiles): void {
    if (!files || typeof files !== 'object') {
      throw new Error('Invalid input: files must be an object');
    }

    // 检查是否有有效内容，安全地处理可能为undefined的值
    const ttmlContent = (files.ttml || '').trim();
    const ttssContent = (files.ttss || '').trim();
    const jsContent = (files.js || '').trim();

    const hasContent =
      ttmlContent.length > 0 || ttssContent.length > 0 || jsContent.length > 0;

    if (!hasContent) {
      console.error('🔍 [Parse5TransformEngine] 输入验证失败 - 内容详情:', {
        ttml: ttmlContent.length,
        ttss: ttssContent.length,
        js: jsContent.length,
        originalFiles: files,
      });
      throw new Error('Invalid input: No valid content provided');
    }

    // 验证各个文件内容类型
    if (files.ttml && typeof files.ttml !== 'string') {
      throw new Error('Invalid input: TTML must be a string');
    }

    if (files.ttss && typeof files.ttss !== 'string') {
      throw new Error('Invalid input: TTSS must be a string');
    }

    if (files.js && typeof files.js !== 'string') {
      throw new Error('Invalid input: JS must be a string');
    }

    if (files.json && typeof files.json !== 'string') {
      throw new Error('Invalid input: JSON must be a string');
    }
  }

  /**
   * 处理JavaScript代码
   */
  private async processJS(
    js: string,
  ): Promise<{ code: string; hasUserCode: boolean }> {
    const safeJs = js || '';
    if (!safeJs?.trim()) {
      return {
        code: this.getDefaultJS(),
        hasUserCode: false,
      };
    }

    // 简单的JS转换（基于文档中的转换规则）
    let processedJS = js;

    // Card语法转换
    processedJS = processedJS.replace(/Card\s*\(\s*\{/, 'const component = {');

    // this绑定转换
    processedJS = processedJS.replace(/this\.setData/g, 'component.setData');
    processedJS = processedJS.replace(/this\.data/g, 'component.data');

    // 生命周期方法转换
    processedJS = processedJS.replace(
      /onLoad\s*[:=]\s*function/g,
      'function onLoad',
    );
    processedJS = processedJS.replace(
      /onShow\s*[:=]\s*function/g,
      'function onShow',
    );

    return {
      code: processedJS,
      hasUserCode: true,
    };
  }

  /**
   * 获取默认JavaScript代码
   */
  private getDefaultJS(): string {
    return `
      console.log('Parse5 Lynx preview initialized');
      
      // 模拟常用生命周期
      const onLoad = () => console.log('onLoad called');
      const onShow = () => console.log('onShow called');
      
      // 默认组件数据
      const component = {
        data: {},
        setData: function(newData, callback) {
          Object.assign(this.data, newData);
          console.log('setData called:', newData);
          if (callback) callback();
          if (this._forceUpdate) this._forceUpdate();
        }
      };
    `;
  }

  /**
   * 生成源码映射
   */
  private generateSourceMap(files: InputFiles, _results: any): any {
    if (!this.config.enableSourceMap) {
      return undefined;
    }

    return {
      version: 3,
      sources: Object.keys(files),
      mappings: '', // 简化实现，实际需要完整的源码映射
      names: [],
    };
  }

  /**
   * 错误分类
   */
  private classifyError(error: any): ErrorType {
    if (error instanceof Error) {
      if (error.message.includes('Parse5') || error.message.includes('HTML')) {
        return 'parse' as ErrorType;
      }
      if (error.message.includes('TTML') || error.message.includes('syntax')) {
        return 'syntax' as ErrorType;
      }
      if (
        error.message.includes('transform') ||
        error.message.includes('convert')
      ) {
        return 'transform' as ErrorType;
      }
      if (
        error.message.includes('network') ||
        error.message.includes('timeout')
      ) {
        return 'network' as ErrorType;
      }
    }
    return 'unknown' as ErrorType;
  }

  /**
   * 创建转换错误对象
   */
  private createTransformError(error: any): TransformError {
    return {
      name: 'TransformError',
      type: this.classifyError(error),
      message: error instanceof Error ? error.message : String(error),
      position: { line: 0, column: 0, offset: 0 },
      code: 'TRANSFORM_ERROR',
      severity: 'error',
      source: 'Parse5TransformEngine',
    };
  }

  /**
   * 生成缓存键
   */
  // private generateCacheKey(files: InputFiles): string {
  //   const content = JSON.stringify(files) + JSON.stringify(this.config);
  //   return this.simpleHash(content);
  // }

  /**
   * 简单哈希函数
   */
  // private simpleHash(str: string): string {
  //   let hash = 0;
  //   for (let i = 0; i < str.length; i++) {
  //     const char = str.charCodeAt(i);
  //     hash = (hash << 5) - hash + char;
  //     hash = hash & hash; // 转换为32位整数
  //   }
  //   return hash.toString(36);
  // }

  /**
   * 清除缓存
   */
  public clearCache(): void {
    this.transformCache.clear();
    // 定期清理过期缓存
    this.transformCache.cleanup();
  }

  /**
   * 生成组件ID
   */
  private generateComponentId(): string {
    return Math.random().toString(36).substring(2, 10);
  }

  /**
   * 更新引擎配置
   */
  public updateConfig(newConfig: Partial<Parse5TransformConfig>): void {
    this.config = {
      ...this.config,
      ...newConfig,
    };

    // 重新初始化处理器
    this.ttmlAdapter = new Parse5TTMLAdapter(this.config);
    this.ttssProcessor = new TTSSProcessor(this.config);
    this.htmlGenerator = new HTMLGenerator(this.config);
  }

  /**
   * 获取当前配置
   */
  public getConfig(): Parse5TransformConfig {
    return { ...this.config };
  }

  /**
   * 获取转换统计信息
   */
  public getStats(): any {
    return {
      componentId: this.config.componentId,
      workerMode: this.config.workerMode,
      cacheStats: this.transformCache.getStats(),
      config: this.config,
    };
  }

  // ===============================
  // 调试辅助方法
  // ===============================

  /**
   * 分析输入文件
   */
  private analyzeInputFiles(files: InputFiles): any {
    const analysis = {
      hasFiles: false,
      fileTypes: [] as string[],
      fileSizes: {} as Record<string, number>,
      fileAnalysis: {} as Record<string, any>,
      totalSize: 0,
      estimatedComplexity: 'low' as 'low' | 'medium' | 'high',
      potentialIssues: [] as string[],
    };

    // 分析每个文件
    if (files.ttml && files.ttml.length > 0) {
      analysis.fileTypes.push('TTML');
      analysis.fileSizes.ttml = files.ttml.length;
      analysis.fileAnalysis.ttml = {
        hasElements: /<[^>]+>/.test(files.ttml),
        hasDirectives: /(lx:|tt:)/.test(files.ttml),
        hasEvents: /(bind|catch:)/.test(files.ttml),
        hasInterpolation: /\{\{.*?\}\}/.test(files.ttml),
        elementCount: (files.ttml.match(/<[^>]+>/g) || []).length,
        complexity:
          files.ttml.length > 1000
            ? 'high'
            : files.ttml.length > 300
              ? 'medium'
              : 'low',
      };
    }

    if (files.ttss && files.ttss.length > 0) {
      analysis.fileTypes.push('TTSS');
      analysis.fileSizes.ttss = files.ttss.length;
      analysis.fileAnalysis.ttss = {
        hasRules: /[^{}]+\s*\{[^}]*\}/.test(files.ttss),
        hasRpx: /\d+(?:\.\d+)?rpx/.test(files.ttss),
        hasClasses: /\.[a-zA-Z][a-zA-Z0-9_-]*/.test(files.ttss),
        hasMediaQueries: /@media/.test(files.ttss),
        ruleCount: (files.ttss.match(/[^{}]+\s*\{[^}]*\}/g) || []).length,
        complexity:
          files.ttss.length > 800
            ? 'high'
            : files.ttss.length > 200
              ? 'medium'
              : 'low',
      };
    }

    if (files.js && files.js.length > 0) {
      analysis.fileTypes.push('JS');
      analysis.fileSizes.js = files.js.length;
      analysis.fileAnalysis.js = {
        hasFunctions: /function/.test(files.js),
        hasClasses: /class\s+/.test(files.js),
        hasLifecycle: /(onLoad|onShow|onHide)/.test(files.js),
        hasSetData: /setData/.test(files.js),
        functionCount: (files.js.match(/function\s+\w+/g) || []).length,
        complexity:
          files.js.length > 1000
            ? 'high'
            : files.js.length > 300
              ? 'medium'
              : 'low',
      };
    }

    if (files.json && files.json.length > 0) {
      analysis.fileTypes.push('JSON');
      analysis.fileSizes.json = files.json.length;
      try {
        const parsed = JSON.parse(files.json);
        analysis.fileAnalysis.json = {
          isValid: true,
          hasComponents: !!parsed.usingComponents,
          componentCount: Object.keys(parsed.usingComponents || {}).length,
          hasPermissions: !!parsed.permissions,
          complexity: 'low',
        };
      } catch {
        analysis.fileAnalysis.json = {
          isValid: false,
          complexity: 'low',
        };
        analysis.potentialIssues.push('JSON文件格式无效');
      }
    }

    // 计算总体信息
    analysis.hasFiles = analysis.fileTypes.length > 0;
    analysis.totalSize = Object.values(analysis.fileSizes).reduce(
      (sum, size) => sum + size,
      0,
    );

    // 评估复杂度
    const complexities = Object.values(analysis.fileAnalysis).map(
      (fa: any) => fa.complexity,
    );
    if (complexities.includes('high') || analysis.totalSize > 2000) {
      analysis.estimatedComplexity = 'high';
    } else if (complexities.includes('medium') || analysis.totalSize > 500) {
      analysis.estimatedComplexity = 'medium';
    }

    // 检查潜在问题
    if (analysis.totalSize > 10000) {
      analysis.potentialIssues.push('文件内容过大，可能影响转换性能');
    }
    if (!analysis.hasFiles) {
      analysis.potentialIssues.push('没有有效的输入文件');
    }
    if (
      analysis.fileTypes.includes('TTML') &&
      !analysis.fileTypes.includes('TTSS')
    ) {
      analysis.potentialIssues.push('有TTML但缺少TTSS，样式可能丢失');
    }

    return analysis;
  }

  /**
   * 验证转换结果
   */
  private validateTransformResult(
    result: TransformResult,
    files: InputFiles,
    results: any,
  ): void {
    const validation = {
      isValid: false,
      completeness: 0,
      quality: 'unknown' as 'low' | 'medium' | 'high' | 'unknown',
      errors: [] as string[],
      warnings: [] as string[],
      recommendations: [] as string[],
    };

    // 基本有效性检查
    validation.isValid =
      result.success &&
      !!result.html &&
      result.html.length > 0 &&
      !!result.metadata;

    // 完整性检查
    let completenessScore = 0;
    const maxScore = 4;

    if (result.html?.includes('<!DOCTYPE html>')) {
      completenessScore++;
    }
    if (
      result.html?.includes('React.createElement') ||
      result.html?.includes('React.Fragment')
    ) {
      completenessScore++;
    }
    if ((result.metadata as any)?.elementCount > 0) {
      completenessScore++;
    }
    if ((result.metadata as any)?.cssRules > 0 || !files.ttss) {
      completenessScore++;
    }

    validation.completeness = (completenessScore / maxScore) * 100;

    // 质量评估
    if (validation.completeness >= 90) {
      validation.quality = 'high';
    } else if (validation.completeness >= 70) {
      validation.quality = 'medium';
    } else if (validation.completeness >= 50) {
      validation.quality = 'low';
    }

    // 错误检查
    if (!validation.isValid) {
      validation.errors.push('转换结果无效');
    }
    if (result.html?.length < 100) {
      validation.errors.push('HTML内容过短');
    }
    // 🔧 P0修复：模板引擎输出HTML而不是React元素，调整验证逻辑
    if (
      files.ttml &&
      !result.html?.includes('React.createElement') &&
      !result.html?.includes('<div')
    ) {
      validation.errors.push('TTML转换失败，缺少有效内容');
    }
    if (
      files.ttss &&
      (!(result.metadata as any)?.cssRules ||
        (result.metadata as any).cssRules === 0)
    ) {
      validation.warnings.push('TTSS转换可能失败，CSS规则为空');
    }

    // 检查空转换内容
    if (
      files.ttml &&
      (!results.ttml?.jsx || results.ttml.jsx.trim().length === 0)
    ) {
      validation.errors.push('没有可转换的内容 - TTML解析失败或内容为空');
    }

    // 检查JSX语法错误
    if (results.ttml?.jsx?.includes('": function')) {
      validation.errors.push('JSX语法错误 - 函数属性格式不正确');
    }

    // 检查TTSS处理结果
    if (
      files.ttss &&
      (!results.ttss?.css || results.ttss.css.trim().length === 0)
    ) {
      validation.warnings.push('TTSS处理失败，样式可能不会被应用');
    }

    // 性能警告
    if ((result.metadata as any)?.transformTime > 5000) {
      validation.warnings.push(
        `转换耗时过长: ${(result.metadata as any).transformTime}ms`,
      );
    }

    // 推荐
    if (validation.quality === 'low') {
      validation.recommendations.push('建议检查输入文件格式和语法');
    }
    if ((result.metadata as any)?.elementCount === 0 && files.ttml) {
      validation.recommendations.push('TTML文件可能包含无效的元素结构');
    }
    if ((result.metadata as any)?.cssRules === 0 && files.ttss) {
      validation.recommendations.push('TTSS文件可能包含无效的CSS规则');
    }

    console.log('🔍 [Parse5TransformEngine] 转换结果验证:', validation);

    if (validation.errors.length > 0) {
      console.error(
        '❌ [Parse5TransformEngine] 转换验证发现错误:',
        validation.errors,
      );
    }
    if (validation.warnings.length > 0) {
      console.warn(
        '⚠️ [Parse5TransformEngine] 转换验证发现警告:',
        validation.warnings,
      );
    }
    if (validation.recommendations.length > 0) {
      console.info(
        '💡 [Parse5TransformEngine] 转换建议:',
        validation.recommendations,
      );
    }
    if (validation.errors.length === 0 && validation.warnings.length === 0) {
      console.log(
        `✅ [Parse5TransformEngine] 转换结果验证通过 (质量: ${validation.quality}, 完整性: ${validation.completeness.toFixed(1)}%)`,
      );
    }
  }

  /**
   * 🆕 紧急CSS处理 - 当正常流程失败时的降级策略
   */
  private emergencyProcessCSS(ttss: string, componentId: string): string {
    console.log('🚨 [Parse5TransformEngine] 执行紧急CSS处理');

    try {
      // 最基础的RPX转换
      let emergencyCSS = ttss;

      // 简单RPX转换 (假设750px设计稿)
      emergencyCSS = emergencyCSS.replace(
        /(\d+(?:\.\d+)?)rpx/g,
        (match, value) => {
          const rpx = parseFloat(value);
          const vw = ((rpx / 750) * 100).toFixed(6);
          return `${vw}vw`;
        },
      );

      // 添加基础错误恢复样式
      const errorRecoveryCSS = `
/* 紧急处理的CSS - ${componentId} */
/* 原始转换内容 */
${emergencyCSS}

/* 错误恢复样式 */
.component-wrapper {
  position: relative;
  box-sizing: border-box;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  min-height: 50px;
  padding: 10px;
}

/* 基础Lynx组件支持 */
.lynx-view { display: block; }
.lynx-text { display: inline; }
.lynx-image { display: block; max-width: 100%; }
.lynx-button { 
  display: inline-block; 
  padding: 8px 16px; 
  border: 1px solid #ddd; 
  border-radius: 4px; 
  background: #fff; 
  cursor: pointer; 
}

/* 错误指示器 */
.ttml-error {
  padding: 15px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  color: #856404;
  font-family: monospace;
}
`;

      console.log(
        '✅ [Parse5TransformEngine] 紧急CSS处理完成，长度:',
        errorRecoveryCSS.length,
      );
      return errorRecoveryCSS;
    } catch (error) {
      console.error('❌ [Parse5TransformEngine] 紧急CSS处理也失败:', error);

      // 最终降级：返回最基础的CSS
      return `
/* 最终降级CSS - ${componentId} */
body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; }
.component-wrapper { 
  padding: 20px; 
  border: 1px solid #ddd; 
  border-radius: 4px; 
  background: #f9f9f9; 
}
.ttml-error { 
  color: #d63031; 
  background: #ffe0e0; 
  padding: 15px; 
  border-radius: 4px; 
}
`;
    }
  }
}

// 导出主要接口
export { Parse5TTMLAdapter } from './adapters/parse5-ttml-adapter';
export { TTSSProcessor } from './processors/ttss-processor';
export { HTMLGenerator } from './generators/html-generator';
export * from './mappings';

// 默认导出
export default Parse5TransformEngine;
