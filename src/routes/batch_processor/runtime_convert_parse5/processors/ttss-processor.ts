/**
 * @package runtime-convert-parse5
 * @description TTSS (TikTok Style Sheets) 处理器
 * 基于文档中提取的TTSS转换规则，实现RPX转换、作用域化等功能
 */

import type { Parse5TransformConfig } from '../index';
import { TTSS_CONVERSION_CONFIG, RpxMode } from '../mappings';
import {
  ComponentScopeManager,
  RPXConfig,
  DEFAULT_RPX_CONFIG,
  ENHANCED_EVENT_MAPPING,
} from '../mappings/comprehensive-lynx-mapping';

/**
 * CSS规则接口
 */
interface CSSRule {
  type: 'rule' | 'media' | 'keyframes' | 'import';
  selector?: string;
  declarations?: CSSDeclaration[];
  rules?: CSSRule[];
  media?: string;
  keyframeName?: string;
  importUrl?: string;
}

/**
 * CSS声明接口
 */
interface CSSDeclaration {
  property: string;
  value: string;
  important?: boolean;
}

/**
 * TTSS处理结果
 */
export interface TTSSProcessResult {
  css: string; // 转换后的CSS
  scopedCss: string; // 作用域化的CSS
  cssInJs: any; // CSS-in-JS对象
  classes: string[]; // 类名列表
  ruleCount: number; // 规则数量
  rpxConverted: boolean; // 是否进行了RPX转换
  errors: string[]; // 错误信息
  warnings: string[]; // 警告信息
}

/**
 * 禁止使用的TTSS属性列表
 * 基于 docs/TTSS_ATTRIBUTE_RESTRICTIONS.md
 */
const FORBIDDEN_TTSS_PROPERTIES = new Set([
  'backdrop-filter',
  'text-transform',
  'filter',
  'clip-path',
  'mask',
  'object-fit',
  'object-position',
  'scroll-behavior',
  'user-select',
  'pointer-events',
  'cursor',
  'resize',
  'outline',
  'appearance',
  '-webkit-appearance',
  '-moz-appearance',
  'tab-size',
  'white-space',
  'word-break',
  'word-wrap',
  'hyphens',
  'columns',
  'column-gap',
  'break-inside',
  'orphans',
  'widows',
  'page-break-before',
  'page-break-after',
  'mix-blend-mode',
  'isolation',
  'will-change',
  'contain',
  'overscroll-behavior',
  'scroll-snap-type',
  'touch-action',
]);

/**
 * 验证CSS属性是否被禁止使用
 */
function validateTTSSProperty(property: string, value: string): string | null {
  const normalizedProperty = property.toLowerCase().trim();
  
  // 检查禁止的属性
  if (FORBIDDEN_TTSS_PROPERTIES.has(normalizedProperty)) {
    return `禁止使用的TTSS属性: ${property} (值: ${value}) - 此属性在Lynx框架中不受支持`;
  }
  
  // 检查vendor前缀
  if (normalizedProperty.startsWith('-webkit-') || 
      normalizedProperty.startsWith('-moz-') || 
      normalizedProperty.startsWith('-ms-') || 
      normalizedProperty.startsWith('-o-')) {
    const baseProperty = normalizedProperty.replace(/^-\w+-/, '');
    if (FORBIDDEN_TTSS_PROPERTIES.has(baseProperty)) {
      return `禁止使用的带前缀TTSS属性: ${property} - 此属性在Lynx框架中不受支持`;
    }
  }
  
  return null;
}

/**
 * RPX转换模式配置
 */
export enum RpxConversionMode {
  VW = 'vw', // 推荐：视口宽度单位
  REM = 'rem', // 根字体大小单位
  PX = 'px', // 固定像素
  CALC = 'calc', // CSS calc() 函数
}

/**
 * RPX转换配置
 */
export interface RpxConfig {
  designWidth: number;
  defaultMode: RpxConversionMode;
  converters: Record<RpxConversionMode, (rpx: number) => string>;
}

/**
 * 增强的TTSS处理器
 * 基于web-speedy-plugin的完整转换功能，负责将TTSS转换为标准CSS
 * 包括RPX转换、作用域化、CSS-in-JS生成等
 */
export class TTSSProcessor {
  private config: Parse5TransformConfig & { debugMode?: boolean };
  private conversionConfig: typeof TTSS_CONVERSION_CONFIG;
  private rpxConfig: RpxConfig;
  private scopeManager: ComponentScopeManager;
  private enhancedRpxConfig: RPXConfig;
  private protectionMap: Map<string, string> = new Map();

  constructor(config: Parse5TransformConfig & { debugMode?: boolean }) {
    this.config = { debugMode: false, ...config };
    this.conversionConfig = TTSS_CONVERSION_CONFIG;
    this.scopeManager = ComponentScopeManager.getInstance();

    // 使用增强的RPX配置 - 模拟web-speedy-plugin
    this.enhancedRpxConfig = {
      designWidth:
        (config as any).rpx?.designWidth || DEFAULT_RPX_CONFIG.designWidth,
      rpxMode: (config as any).rpx?.rpxMode || DEFAULT_RPX_CONFIG.rpxMode,
    };

    // 保持向后兼容的RPX转换配置
    this.rpxConfig = {
      designWidth: this.enhancedRpxConfig.designWidth,
      defaultMode: RpxConversionMode.VW,
      converters: {
        [RpxConversionMode.VW]: (rpx: number) => this.convertRpxToVw(rpx),
        [RpxConversionMode.REM]: (rpx: number) => this.convertRpxToRem(rpx),
        [RpxConversionMode.PX]: (rpx: number) => `${rpx}px`,
        [RpxConversionMode.CALC]: (rpx: number) =>
          `calc(${rpx} * 100vw / ${this.enhancedRpxConfig.designWidth})`,
      },
    };
  }

  /**
   * 增强的RPX转换 - 基于web-speedy-plugin的转换逻辑
   */
  private convertRpxToVw(rpx: number): string {
    const vwValue = ((rpx / this.enhancedRpxConfig.designWidth) * 100).toFixed(
      6,
    );
    return `${vwValue}vw`;
  }

  private convertRpxToRem(rpx: number): string {
    // 假设1rem = designWidth/20 px，这是常见的移动端配置
    const remValue = (rpx / (this.enhancedRpxConfig.designWidth / 20)).toFixed(
      6,
    );
    return `${remValue}rem`;
  }

  /**
   * 增强的TTSS处理 - 基于web-speedy-plugin的完整转换流程
   */
  async process(
    ttss: string,
    componentId: string,
    options: {
      isGlobalModule?: boolean;
      tagV?: string;
      bumpSelectorSpecificity?: boolean;
    } = {},
  ): Promise<TTSSProcessResult> {
    const errors: string[] = [];
    const processStartTime = performance.now();

    try {
      console.log('🎨 [Enhanced TTSSProcessor] 开始增强TTSS处理');
      console.log('📋 [Enhanced TTSSProcessor] 配置:', {
        componentId,
        length: ttss.length,
        hasRpx: /\d+(?:\.\d+)?rpx/.test(ttss),
        rpxMode: this.enhancedRpxConfig.rpxMode,
        designWidth: this.enhancedRpxConfig.designWidth,
        isGlobalModule: options.isGlobalModule,
        enableScope: this.config.enableScope && !options.isGlobalModule,
      });

      if (!ttss?.trim()) {
        return this.createEmptyResult();
      }

      // 1. 生成或使用提供的tagV作用域标识
      const tagV =
        options.tagV || this.scopeManager.getComponentScope(componentId, ttss);
      console.log(`🔑 [Enhanced TTSSProcessor] 组件作用域: ${tagV}`);

      // 2. Parse CSS
      const parseStartTime = performance.now();
      const cssRules = this.parseCSS(ttss);
      const parseTime = performance.now() - parseStartTime;

      console.log('🔍 [Enhanced TTSSProcessor] CSS解析完成:', {
        rules: cssRules.length,
        parseTime: `${parseTime.toFixed(2)}ms`,
      });

      // 3. 增强的RPX单位转换 - 基于web-speedy-plugin
      const rpxStartTime = performance.now();
      const rpxConverted = this.enhancedConvertRpx(cssRules);
      const rpxTime = performance.now() - rpxStartTime;

      console.log('🔄 [Enhanced TTSSProcessor] RPX转换完成:', {
        mode: this.enhancedRpxConfig.rpxMode,
        designWidth: this.enhancedRpxConfig.designWidth,
        convertTime: `${rpxTime.toFixed(2)}ms`,
      });

      // 4. 增强的作用域处理 - 模拟web-speedy-plugin的labelSelectorsByTagV
      const scopeStartTime = performance.now();
      const scopedRules =
        this.config.enableScope && !options.isGlobalModule
          ? this.enhancedAddSelectorScope(rpxConverted, tagV)
          : rpxConverted;
      const scopeTime = performance.now() - scopeStartTime;

      console.log('🔒 [Enhanced TTSSProcessor] 作用域处理完成:', {
        enableScope: this.config.enableScope && !options.isGlobalModule,
        tagV,
        scopeTime: `${scopeTime.toFixed(2)}ms`,
      });

      // 5. 选择器权重提升 - 模拟webBumpAllSelectorSpecificity
      const bumpedRules = options.bumpSelectorSpecificity
        ? this.bumpSelectorSpecificity(scopedRules)
        : scopedRules;

      if (options.bumpSelectorSpecificity) {
        console.log('⬆️ [Enhanced TTSSProcessor] 选择器权重提升完成');
      }

      // 6. Generate CSS strings - 🔧 P0 修复：分离 iframe 和组件 CSS
      const generateStartTime = performance.now();

      // 🎯 核心修复：为不同使用场景生成不同版本的 CSS
      // css: 用于 iframe 独立渲染，无作用域限制
      const css = this.generateCSS(rpxConverted);

      // scopedCss: 用于组件集成，包含完整作用域保护
      const scopedCss = this.generateCSS(bumpedRules);

      // 🔍 调试：验证 CSS 生成差异
      console.log('🔧 [TTSSProcessor] CSS 生成对比验证:', {
        原始CSS长度: css.length,
        作用域CSS长度: scopedCss.length,
        原始CSS包含作用域标识: css.includes('[data-v-'),
        作用域CSS包含作用域标识: scopedCss.includes('[data-v-'),
        差异比例: `${(((scopedCss.length - css.length) / css.length) * 100).toFixed(1)}%`,
      });

      const generateTime = performance.now() - generateStartTime;

      // 5. Generate CSS-in-JS - 🔧 修复：使用非作用域化规则
      const cssInJsStartTime = performance.now();
      // 使用 rpxConverted 而非 scopedRules，确保 CSS-in-JS 映射正确
      const cssInJs = this.generateCssInJs(rpxConverted, componentId);
      const cssInJsTime = performance.now() - cssInJsStartTime;

      // 6. Extract class names - 🔧 修复：使用原始规则提取类名
      const classes = this.extractClassNames(rpxConverted);

      const totalProcessTime = performance.now() - processStartTime;

      const result = {
        css, // ✅ 纯净的、适用于 iframe 的 CSS
        scopedCss, // ✅ 作用域化的、适用于组件集成的 CSS
        cssInJs: cssInJs.cssObject, // ✅ 基于原始规则的正确映射
        classes,
        ruleCount: cssRules.length,
        rpxConverted: this.hasRpxValues(ttss),
        errors,
        // 🆕 新增调试信息
        debug: {
          originalRulesCount: rpxConverted.length,
          scopedRulesCount: bumpedRules.length,
          cssVariant: 'iframe-clean',
          scopedCssVariant: 'component-scoped',
          generationTime: generateTime,
          cssInJsVariant: 'original-rules-based',
        },
      };

      // 🔍 增强调试：详细记录 CSS 生成过程
      console.log('🔍 [TTSSProcessor] CSS 生成详细分析:', {
        // 基础信息
        componentId,
        处理阶段: '最终生成',

        // CSS 版本对比
        CSS版本对比: {
          iframe版本: {
            长度: css?.length || 0,
            预览: css?.substring(0, 100) || 'EMPTY',
            包含作用域: css?.includes('[data-v-') || false,
            包含RPX: css?.includes('rpx') || false,
          },
          组件版本: {
            长度: scopedCss?.length || 0,
            预览: scopedCss?.substring(0, 100) || 'EMPTY',
            包含作用域: scopedCss?.includes('[data-v-') || false,
            包含RPX: scopedCss?.includes('rpx') || false,
          },
        },

        // CSS-in-JS 信息
        'CSS-in-JS': {
          对象长度: Object.keys(cssInJs.cssObject || {}).length,
          类名数量: classes.length,
          样本类名: classes.slice(0, 3),
        },

        // 处理统计
        处理统计: {
          原始规则数: cssRules.length,
          RPX转换规则数: rpxConverted.length,
          作用域规则数: bumpedRules.length,
          最终类名数: classes.length,
        },
      });

      // 验证 CSS 有效性
      if (!css && !scopedCss) {
        console.error('🚨 [TTSSProcessor] 严重错误：CSS 和 scopedCss 都为空！');
      } else if (!css) {
        console.warn(
          '⚠️ [TTSSProcessor] 警告：iframe CSS 为空，可能影响独立渲染',
        );
      } else if (!scopedCss) {
        console.warn(
          '⚠️ [TTSSProcessor] 警告：组件 CSS 为空，可能影响作用域隔离',
        );
      } else {
        console.log('✅ [TTSSProcessor] CSS 生成验证通过');
      }

      // Simplified completion logging
      if (this.config.debugMode) {
        console.log('✅ [TTSSProcessor] TTSS processing completed', {
          totalTime: `${totalProcessTime.toFixed(2)}ms`,
          cssLength: scopedCss.length,
          ruleCount: cssRules.length,
          classCount: classes.length,
          hasErrors: errors.length > 0,
        });
      }

      return result;
    } catch (error) {
      const totalTime = performance.now() - processStartTime;
      const errorMsg = `TTSS处理失败: ${error instanceof Error ? error.message : String(error)}`;

      if (this.config.debugMode) {
        console.error('❌ [TTSSProcessor] TTSS processing failed', {
          error: error instanceof Error ? error.message : String(error),
          totalTime: `${totalTime.toFixed(2)}ms`,
          componentId,
          ttssLength: ttss?.length || 0,
        });
      }

      errors.push(errorMsg);

      return {
        css: '',
        scopedCss: '',
        cssInJs: {},
        classes: [],
        ruleCount: 0,
        rpxConverted: false,
        errors,
      };
    }
  }

  /**
   * 保护复杂CSS值
   */
  private protectComplexCSSValues(css: string): string {
    this.protectionMap.clear();
    let protectedCSS = css;
    let counter = 0;

    // 🔥 修复：使用更精确的正则表达式处理嵌套括号
    // 保护 linear-gradient (支持嵌套括号)
    protectedCSS = protectedCSS.replace(
      /linear-gradient\((?:[^()]*|\([^()]*\))*\)/g,
      match => {
        const placeholder = `__PROTECTED_GRADIENT_${counter++}__`;
        this.protectionMap.set(placeholder, match);
        return placeholder;
      },
    );

    // 保护 radial-gradient (支持嵌套括号)
    protectedCSS = protectedCSS.replace(
      /radial-gradient\((?:[^()]*|\([^()]*\))*\)/g,
      match => {
        const placeholder = `__PROTECTED_GRADIENT_${counter++}__`;
        this.protectionMap.set(placeholder, match);
        return placeholder;
      },
    );

    // 保护 calc() (支持嵌套括号)
    protectedCSS = protectedCSS.replace(
      /calc\((?:[^()]*|\([^()]*\))*\)/g,
      match => {
        const placeholder = `__PROTECTED_CALC_${counter++}__`;
        this.protectionMap.set(placeholder, match);
        return placeholder;
      },
    );

    // 保护 rgba()、rgb()、hsl()、hsla() (支持嵌套括号)
    protectedCSS = protectedCSS.replace(
      /(rgba?|hsla?)\((?:[^()]*|\([^()]*\))*\)/g,
      match => {
        const placeholder = `__PROTECTED_COLOR_${counter++}__`;
        this.protectionMap.set(placeholder, match);
        return placeholder;
      },
    );

    // 🔥 修复：更精确地保护 filter 和 backdrop-filter 属性值
    protectedCSS = protectedCSS.replace(
      /(image.pngbackdrop-filter|filter)\s*:\s*([^;{}]+)/g,
      (match, property, value) => {
        const placeholder = `__PROTECTED_FILTER_${counter++}__`;
        this.protectionMap.set(placeholder, match);
        return placeholder;
      },
    );

    // 🔥 修复：更精确地保护 box-shadow 属性值
    protectedCSS = protectedCSS.replace(
      /box-shadow\s*:\s*([^;{}]+)/g,
      (match, value) => {
        const placeholder = `__PROTECTED_SHADOW_${counter++}__`;
        this.protectionMap.set(placeholder, match);
        return placeholder;
      },
    );

    // 🔥 新增：保护 transform 属性值
    protectedCSS = protectedCSS.replace(
      /transform\s*:\s*([^;{}]+)/g,
      (match, value) => {
        const placeholder = `__PROTECTED_TRANSFORM_${counter++}__`;
        this.protectionMap.set(placeholder, match);
        return placeholder;
      },
    );

    return protectedCSS;
  }

  /**
   * 恢复被保护的CSS值
   */
  private restoreProtectedValues(value: string): string {
    let restoredValue = value;

    this.protectionMap.forEach((original, placeholder) => {
      restoredValue = restoredValue.replace(
        new RegExp(placeholder, 'g'),
        original,
      );
    });

    return restoredValue;
  }

  /**
   * 智能分割CSS声明
   */
  private smartSplitDeclarations(text: string): string[] {
    const parts: string[] = [];
    let current = '';
    let parenDepth = 0;

    for (let i = 0; i < text.length; i++) {
      const char = text[i];

      if (char === '(') {
        parenDepth++;
      } else if (char === ')') {
        parenDepth--;
      } else if (char === ';' && parenDepth === 0) {
        if (current.trim()) {
          parts.push(current.trim());
        }
        current = '';
        continue;
      }

      current += char;
    }

    if (current.trim()) {
      parts.push(current.trim());
    }

    return parts;
  }

  /**
   * 解析CSS
   */
  private parseCSS(css: string): CSSRule[] {
    const rules: CSSRule[] = [];

    try {
      // 1. 预处理：保护复杂的CSS值
      const protectedCSS = this.protectComplexCSSValues(css);

      // 2. 移除注释
      let cleanCss = protectedCSS.replace(/\/\*[\s\S]*?\*\//g, '');

      // 3. 处理@规则
      cleanCss = this.extractAtRules(cleanCss, rules);

      // 4. 改进的规则解析 - 支持嵌套和复杂值
      const enhancedRules = this.parseEnhancedRules(cleanCss);
      rules.push(...enhancedRules);
    } catch (error) {
      console.warn('CSS解析警告:', error);
      // 降级到简单解析
      return this.parseSimpleCSS(css);
    }

    return rules;
  }

  /**
   * 增强的规则解析
   */
  private parseEnhancedRules(css: string): CSSRule[] {
    const rules: CSSRule[] = [];

    // 改进的正则表达式，更好地处理嵌套结构
    const ruleRegex = /([^{}]+?)\s*\{([^{}]*(?:\{[^{}]*\}[^{}]*)*)\}/g;
    let match;

    while ((match = ruleRegex.exec(css)) !== null) {
      const [, selector, declarations] = match;

      const rule: CSSRule = {
        type: 'rule',
        selector: selector.trim(),
        declarations: this.parseDeclarations(declarations),
      };

      rules.push(rule);
    }

    return rules;
  }

  /**
   * 简单CSS解析（降级方案）
   */
  private parseSimpleCSS(css: string): CSSRule[] {
    const rules: CSSRule[] = [];

    try {
      let cleanCss = css.replace(/\/\*[\s\S]*?\*\//g, '');
      cleanCss = this.extractAtRules(cleanCss, rules);

      const ruleRegex = /([^{}]+)\s*\{([^{}]*)\}/g;
      let match;

      while ((match = ruleRegex.exec(cleanCss)) !== null) {
        const [, selector, declarations] = match;

        const rule: CSSRule = {
          type: 'rule',
          selector: selector.trim(),
          declarations: this.parseDeclarations(declarations),
        };

        rules.push(rule);
      }
    } catch (error) {
      console.warn('简单CSS解析也失败:', error);
    }

    return rules;
  }

  /**
   * 提取@规则
   */
  private extractAtRules(css: string, rules: CSSRule[]): string {
    let cleanCss = css;

    // 处理@media规则
    const mediaRegex = /@media\s+([^{]+)\s*\{([\s\S]*?)\}/g;
    cleanCss = cleanCss.replace(mediaRegex, (match, media, content) => {
      const mediaRules = this.parseCSS(content);
      rules.push({
        type: 'media',
        media: media.trim(),
        rules: mediaRules,
      });
      return '';
    });

    // 处理@keyframes规则
    const keyframesRegex = /@keyframes\s+([^{]+)\s*\{([\s\S]*?)\}/g;
    cleanCss = cleanCss.replace(keyframesRegex, (match, name, content) => {
      rules.push({
        type: 'keyframes',
        keyframeName: name.trim(),
        declarations: this.parseDeclarations(content),
      });
      return '';
    });

    // 处理@import规则
    const importRegex = /@import\s+['"]([^'"]+)['"];?/g;
    cleanCss = cleanCss.replace(importRegex, (match, url) => {
      rules.push({
        type: 'import',
        importUrl: url,
      });
      return '';
    });

    return cleanCss;
  }

  /**
   * 解析CSS声明
   */
  private parseDeclarations(declarations: string): CSSDeclaration[] {
    const result: CSSDeclaration[] = [];

    // 使用智能分割来处理复杂的CSS值
    const parts = this.smartSplitDeclarations(declarations);

    parts.forEach(part => {
      const colonIndex = part.indexOf(':');
      if (colonIndex > 0) {
        const property = part.substring(0, colonIndex).trim();
        let value = part.substring(colonIndex + 1).trim();

        // 移除末尾的分号
        if (value.endsWith(';')) {
          value = value.slice(0, -1).trim();
        }

        // 恢复被保护的复杂值
        value = this.restoreProtectedValues(value);

        if (property && value) {
          const important = value.includes('!important');
          const cleanValue = value.replace(/\s*!important\s*$/, '');

          result.push({
            property,
            value: cleanValue,
            important,
          });
        }
      }
    });

    return result;
  }

  /**
   * 解析CSS声明（旧版本，作为备用）
   */
  private parseDeclarationsLegacy(declarations: string): CSSDeclaration[] {
    const result: CSSDeclaration[] = [];

    const declRegex = /([^:;]+):([^;]+)(?:;|$)/g;
    let match;

    while ((match = declRegex.exec(declarations)) !== null) {
      const [, property, value] = match;

      const trimmedProperty = property.trim();
      const trimmedValue = value.trim();

      if (trimmedProperty && trimmedValue) {
        const important = trimmedValue.endsWith('!important');
        const cleanValue = important
          ? trimmedValue.replace(/\s*!important$/, '').trim()
          : trimmedValue;

        result.push({
          property: trimmedProperty,
          value: cleanValue,
          important,
        });
      }
    }

    return result;
  }

  /**
   * RPX单位转换
   */
  private convertRpxUnits(rules: CSSRule[]): CSSRule[] {
    return rules.map(rule => this.convertRuleRpx(rule));
  }

  /**
   * 转换规则中的RPX - 增强版，保持复杂CSS功能
   */
  private convertRuleRpx(rule: CSSRule, mode?: RpxConversionMode): CSSRule {
    const newRule = { ...rule };

    if (rule.declarations) {
      newRule.declarations = rule.declarations.map(decl => {
        // 🔥 增强：对于复杂CSS值，谨慎处理RPX转换
        const isComplexValue = this.isComplexCSSValue(decl.value);

        if (isComplexValue) {
          console.log(
            `    🎆 [TTSSProcessor] 复杂CSS值，谨慎转换RPX: ${decl.property}: ${decl.value.substring(0, 30)}...`,
          );
          return {
            ...decl,
            value: this.smartRpxConversion(decl.value, mode),
          };
        }

        return {
          ...decl,
          value: this.convertValueRpx(decl.value, mode),
        };
      });
    }

    if (rule.rules) {
      newRule.rules = rule.rules.map(subRule =>
        this.convertRuleRpx(subRule, mode),
      );
    }

    return newRule;
  }

  /**
   * 转换值中的RPX单位 - 企业级增强版
   */
  private convertValueRpx(value: string, mode?: RpxConversionMode): string {
    const conversionMode = mode || this.rpxConfig.defaultMode;
    const rpxRegex = /(\d+(?:\.\d+)?)rpx/g;

    return value.replace(rpxRegex, (match, rpxValue) => {
      const numValue = parseFloat(rpxValue);
      const converter = this.rpxConfig.converters[conversionMode];
      const convertedValue = converter(numValue);

      console.log(
        `🔢 [TTSSProcessor] RPX转换: ${rpxValue}rpx = ${convertedValue} (${conversionMode}模式)`,
      );

      return convertedValue;
    });
  }

  /**
   * 🔥 新增：检测是否为复杂CSS值
   */
  private isComplexCSSValue(value: string): boolean {
    const complexPatterns = [
      /\b(linear|radial|conic)-gradient\s*\(/,
      /\brepeating-(linear|radial)-gradient\s*\(/,
      /\b(calc|clamp|min|max|var)\s*\(/,
      /\b(url|attr|counter)\s*\(/,
      /\b(rgb|rgba|hsl|hsla)\s*\(/,
      /\bcubic-bezier\s*\(/,
      /\bsteps\s*\(/,
      /filter\s*:/,
      /transform\s*:/,
      /mask\s*:/,
      /clip-path\s*:/,
      /backdrop-filter\s*:/,
    ];

    return complexPatterns.some(pattern => pattern.test(value));
  }

  /**
   * 🔥 新增：智能RPX转换，保持复杂值的完整性
   */
  private smartRpxConversion(value: string, mode?: RpxConversionMode): string {
    // 对于复杂CSS值，只转换明确的RPX单位，保持其他部分不变
    const conversionMode = mode || this.rpxConfig.defaultMode;

    // 更精确的RPX匹配，避免匹配到复杂函数内部
    const safeRpxRegex = /\b(\d+(?:\.\d+)?)rpx\b/g;

    return value.replace(safeRpxRegex, (match, rpxValue) => {
      const numValue = parseFloat(rpxValue);
      const converter = this.rpxConfig.converters[conversionMode];
      const convertedValue = converter(numValue);

      console.log(
        `    🔢 [TTSSProcessor] 智能RPX转换: ${rpxValue}rpx = ${convertedValue}`,
      );

      return convertedValue;
    });
  }

  /**
   * 批量RPX转换 - 支持多种模式
   */
  private convertRpxUnits(css: string, mode?: RpxConversionMode): string {
    const conversionMode = mode || this.rpxConfig.defaultMode;
    const converter = this.rpxConfig.converters[conversionMode];

    console.log(`🔄 [TTSSProcessor] 开始RPX批量转换，模式: ${conversionMode}`);

    const rpxRegex = /(\d+(?:\.\d+)?)rpx/g;
    let convertedCount = 0;

    const result = css.replace(rpxRegex, (match, value) => {
      const rpxValue = parseFloat(value);
      const converted = converter(rpxValue);
      convertedCount++;

      console.log(`  🎯 [TTSSProcessor] ${match} = ${converted}`);

      return converted;
    });

    console.log(
      `✅ [TTSSProcessor] RPX转换完成，共转换 ${convertedCount} 个单位`,
    );

    return result;
  }

  /**
   * 转换CSS规则数组中的RPX单位
   */
  private convertRulesRpx(
    rules: CSSRule[],
    mode?: RpxConversionMode,
  ): CSSRule[] {
    console.log(
      `🔄 [TTSSProcessor] 开始转换CSS规则中的RPX单位，规则数量: ${rules.length}`,
    );

    const convertedRules = rules.map(rule => this.convertRuleRpx(rule, mode));

    console.log('✅ [TTSSProcessor] CSS规则RPX转换完成');

    return convertedRules;
  }

  /**
   * 添加选择器作用域 - 智能版，避免过度作用域化
   */
  private addSelectorScope(rules: CSSRule[], componentId: string): CSSRule[] {
    const scopeAttribute = `[data-v-${componentId}]`;

    console.log(
      `🎯 [TTSSProcessor] 开始智能作用域处理，规则数量: ${rules.length}`,
    );

    const scopedRules = rules.map(rule => {
      // 🔥 对于某些类型的规则，完全跳过作用域化
      if (this.shouldSkipScoping(rule)) {
        console.log(
          `    ↳ 跳过作用域化: ${rule.type} - ${rule.selector || rule.keyframeName || rule.media}`,
        );
        return rule;
      }

      return this.addRuleScope(rule, scopeAttribute);
    });

    console.log('✅ [TTSSProcessor] 智能作用域处理完成');
    return scopedRules;
  }

  /**
   * 🔥 新增：判断是否应该跳过作用域化
   */
  private shouldSkipScoping(rule: CSSRule): boolean {
    // @keyframes 动画不应该被作用域化
    if (rule.type === 'keyframes') {
      return true;
    }

    // @import 规则不应该被作用域化
    if (rule.type === 'import') {
      return true;
    }

    // 某些全局性质的 @media 查询
    if (rule.type === 'media' && rule.media) {
      const globalMediaPatterns = [
        /print/i,
        /screen\s+and\s+\(prefers-/i,
        /screen\s+and\s+\(color-/i,
        /@media\s+not\s+all/i,
      ];

      if (globalMediaPatterns.some(pattern => pattern.test(rule.media))) {
        return true;
      }
    }

    // 包含复杂CSS功能的规则
    if (rule.type === 'rule' && rule.selector) {
      if (
        this.isGlobalSelector(rule.selector) ||
        this.isComplexCSSFeature(rule.selector)
      ) {
        return true;
      }
    }

    return false;
  }

  /**
   * 为规则添加作用域
   */
  private addRuleScope(rule: CSSRule, scopeAttribute: string): CSSRule {
    const newRule = { ...rule };

    if (rule.type === 'rule' && rule.selector) {
      newRule.selector = this.addSelectorScopeToSelectors(
        rule.selector,
        scopeAttribute,
      );
    }

    if (rule.rules) {
      newRule.rules = rule.rules.map(subRule =>
        this.addRuleScope(subRule, scopeAttribute),
      );
    }

    return newRule;
  }

  /**
   * 为选择器字符串添加作用域 - 企业级增强版，智能防止过度作用域化
   */
  private addSelectorScopeToSelectors(
    selectors: string,
    scopeAttribute: string,
  ): string {
    console.log(`🎯 [TTSSProcessor] 处理选择器作用域: ${selectors}`);

    const processedSelectors = selectors
      .split(',')
      .map(selector => {
        const trimmed = selector.trim();

        console.log(`  🔍 [TTSSProcessor] 分析选择器: "${trimmed}"`);

        // 🔥 增强：保持全局选择器不变
        if (this.isGlobalSelector(trimmed)) {
          console.log('    ↳ 全局选择器，保持不变');
          return trimmed;
        }

        // 🔥 增强：检测复杂CSS功能，谨慎添加作用域
        if (this.isComplexCSSFeature(trimmed)) {
          console.log(`    ↳ 复杂CSS功能，保持不变: ${trimmed}`);
          return trimmed;
        }

        // 深度选择器处理
        if (trimmed.includes('>>>') || trimmed.includes('/deep/')) {
          const result = this.handleDeepSelector(trimmed, scopeAttribute);
          console.log(`    ↳ 深度选择器: ${trimmed} = ${result}`);
          return result;
        }

        // 伪类选择器特殊处理
        if (trimmed.includes(':')) {
          const result = this.handlePseudoSelector(trimmed, scopeAttribute);
          console.log(`    ↳ 伪类选择器: ${trimmed} = ${result}`);
          return result;
        }

        // 🔥 增强：智能选择器作用域添加
        const result = this.intelligentScopeAddition(trimmed, scopeAttribute);
        console.log(`    ↳ 智能作用域: ${trimmed} = ${result}`);
        return result;
      })
      .join(', ');

    console.log(
      `✅ [TTSSProcessor] 选择器作用域处理完成: ${processedSelectors}`,
    );

    return processedSelectors;
  }

  /**
   * 🔥 新增：智能作用域添加
   */
  private intelligentScopeAddition(
    selector: string,
    scopeAttribute: string,
  ): string {
    // 如果选择器已经包含了data-v-属性，避免重复添加
    if (selector.includes('[data-v-') || selector.includes('data-v-')) {
      return selector;
    }

    // 如果是单一类选择器或ID选择器，采用更精确的作用域
    if (/^\.[a-zA-Z][\w-]*$/.test(selector)) {
      // 单一类选择器：.container = [data-v-xxx].container
      return `${scopeAttribute}${selector}`;
    }

    if (/^#[a-zA-Z][\w-]*$/.test(selector)) {
      // 单一ID选择器：#main = [data-v-xxx]#main
      return `${scopeAttribute}${selector}`;
    }

    // 复合选择器，添加父级作用域
    return `${scopeAttribute} ${selector}`;
  }

  /**
   * 处理伪类选择器的作用域 - 企业级增强版
   */
  private handlePseudoSelector(
    selector: string,
    scopeAttribute: string,
  ): string {
    console.log(`🎯 [TTSSProcessor] 处理伪类选择器作用域: ${selector}`);

    const colonIndex = selector.indexOf(':');
    const beforeColon = selector.substring(0, colonIndex);
    const afterColon = selector.substring(colonIndex);

    console.log(
      `  🔍 [TTSSProcessor] 分割结果: "${beforeColon}" + "${afterColon}"`,
    );

    // 特殊伪类处理（全局伪类不添加作用域）
    const globalPseudoClasses = [':root', ':host', ':host-context', ':global'];

    if (globalPseudoClasses.some(pseudo => afterColon.startsWith(pseudo))) {
      console.log('    ↳ 全局伪类，保持不变');
      return selector;
    }

    // 处理复合伪类选择器 (如 :not(), :where(), :is())
    const complexPseudoMatch = afterColon.match(
      /^:(not|where|is|has)\((.+)\)(.*)$/,
    );
    if (complexPseudoMatch) {
      const [, pseudoFunction, innerSelectors, remaining] = complexPseudoMatch;
      const scopedInnerSelectors = innerSelectors
        .split(',')
        .map(inner => {
          const trimmedInner = inner.trim();
          if (this.isGlobalSelector(trimmedInner)) {
            return trimmedInner;
          }
          return `${scopeAttribute} ${trimmedInner}`;
        })
        .join(', ');

      const result = beforeColon.trim()
        ? `${scopeAttribute} ${beforeColon.trim()}:${pseudoFunction}(${scopedInnerSelectors})${remaining}`
        : `${scopeAttribute}:${pseudoFunction}(${scopedInnerSelectors})${remaining}`;

      console.log(`    ↳ 复合伪类: ${selector} = ${result}`);
      return result;
    }

    // 如果伪类前没有选择器，直接添加作用域
    if (!beforeColon.trim()) {
      const result = `${scopeAttribute}${afterColon}`;
      console.log(`    ↳ 无前缀伪类: ${selector} = ${result}`);
      return result;
    }

    // 检查是否为多重伪类 (如 .class:hover:focus)
    const pseudoChain = afterColon.match(
      /^((?::[a-zA-Z-]+(?:\([^)]*\))?)+)(.*)$/,
    );
    if (pseudoChain) {
      const [, pseudoPart, remaining] = pseudoChain;
      const result = `${scopeAttribute} ${beforeColon.trim()}${pseudoPart}${remaining}`;
      console.log(`    ↳ 多重伪类: ${selector} = ${result}`);
      return result;
    }

    // 为基础选择器添加作用域，保持伪类
    const result = `${scopeAttribute} ${beforeColon.trim()}${afterColon}`;
    console.log(`    ↳ 普通伪类: ${selector} = ${result}`);
    return result;
  }

  /**
   * 检查是否为全局选择器 - 企业级增强版，支持更多复杂场景
   */
  private isGlobalSelector(selector: string): boolean {
    // 增强的全局选择器模式，支持更多复杂场景
    const globalPatterns = [
      /^@/, // @media, @keyframes, @import等
      /^:root/, // CSS变量根选择器
      /^html\b/, // html标签
      /^body\b/, // body标签
      /^\*/, // 通配符选择器
      /^\.global-/, // 自定义全局类前缀
      /^:global\(/, // :global()语法
      /^#/, // ID选择器（通常为全局）
      /^\.page-/, // 页面级全局类
      /^\.app-/, // 应用级全局类
      /^\.layout-/, // 布局级全局类
      /^\.theme-/, // 主题相关全局类
      /^\.utility-/, // 工具类前缀
      /^\.u-/, // 简写工具类前缀
      /^:host/, // Shadow DOM Host选择器
      /^:where\(/, // CSS :where() 伪类
      /^:is\(/, // CSS :is() 伪类
      /^::before/, // 伪元素选择器
      /^::after/, // 伪元素选择器
      /^::placeholder/, // 占位符伪元素
      /^::selection/, // 选中文本伪元素
      /^::backdrop/, // 背景伪元素
      /^\[data-theme/, // 主题data属性选择器
      /^\[aria-/, // ARIA属性选择器
      /^\[role=/, // Role属性选择器

      // 🔥 新增：复杂全局样式模式
      /^\.(flex|grid|block|inline)/, // 常见布局类
      /^\.(m-|p-|mt-|mb-|ml-|mr-|pt-|pb-|pl-|pr-)/, // 间距工具类
      /^\.(w-|h-|min-|max-)/, // 尺寸工具类
      /^\.(text-|font-|color-)/, // 文本工具类
      /^\.(bg-|border-|shadow-)/, // 背景/边框/阴影工具类
      /^\.(cursor-|pointer-|select-)/, // 交互工具类
      /^\.(transition-|transform-|animation-)/, // 动画工具类
      /^\.(overflow-|z-|opacity-)/, // 层级/透明度工具类
      /^\.(position-|top-|bottom-|left-|right-)/, // 定位工具类

      // 🔧 修复：添加常见的容器和布局类，避免过度作用域化
      /^\.container$/, // 容器类
      /^\.header$/, // 头部类
      /^\.footer$/, // 底部类
      /^\.main$/, // 主要内容类
      /^\.sidebar$/, // 侧边栏类
      /^\.content$/, // 内容类
      /^\.wrapper$/, // 包装器类
      /^\.section$/, // 区块类
      /^\.row$/, // 行类
      /^\.col$/, // 列类
      /^\.grid-container$/, // 网格容器类
      /^\.grid-item$/, // 网格项类
      /^\.title$/, // 标题类
      /^\.subtitle$/, // 副标题类
      /^\.button$/, // 按钮类
      /^\.btn$/, // 按钮简写类
      /^\.card$/, // 卡片类
      /^\.modal$/, // 模态框类
      /^\.overlay$/, // 遮罩层类
    ];

    // 🔥 增强：基于内容的全局检测
    const containsGlobalFeatures = [
      'linear-gradient',
      'radial-gradient',
      'conic-gradient',
      'repeating-linear-gradient',
      'repeating-radial-gradient',
      'var(',
      'calc(',
      'clamp(',
      'min(',
      'max(',
      '@supports',
      '@layer',
      '@scope',
      '@container',
      'backdrop-filter',
      'mask-image',
      'clip-path',
    ];

    const isGlobalByPattern = globalPatterns.some(pattern =>
      pattern.test(selector),
    );
    const isGlobalByContent = containsGlobalFeatures.some(feature =>
      selector.toLowerCase().includes(feature.toLowerCase()),
    );

    const isGlobal = isGlobalByPattern || isGlobalByContent;

    if (isGlobal) {
      console.log(
        `    🌍 [TTSSProcessor] 识别为全局选择器: ${selector} (${isGlobalByPattern ? 'pattern' : 'content'})`,
      );
    }

    return isGlobal;
  }

  /**
   * 🔥 新增：检测复杂CSS功能
   */
  private isComplexCSSFeature(selector: string): boolean {
    const complexFeatures = [
      // CSS函数
      /\b(linear|radial|conic)-gradient\s*\(/,
      /\brepeating-(linear|radial)-gradient\s*\(/,
      /\b(var|calc|clamp|min|max)\s*\(/,
      /\b(url|attr|counter)\s*\(/,

      // CSS at-rules
      /^@(media|supports|layer|scope|container|page|font-face)\b/,

      // 复杂选择器模式
      /\[(style|class)\*=/, // 包含选择器
      /\w+\|\w+/, // 命名空间选择器
      /\$=/, // 结尾匹配选择器
      /\^=/, // 开头匹配选择器
      /~=/, // 词匹配选择器

      // CSS网格和Flexbox复杂语法
      /grid-template-areas/,
      /grid-template-columns/,
      /grid-template-rows/,
      /grid-auto-/,

      // 动画和变换
      /@keyframes\b/,
      /animation(-name|-duration|-timing)?\s*:/,
      /transform(-origin|-style)?\s*:/,

      // 现代CSS功能
      /aspect-ratio\s*:/,
      /container(-type|-name)?\s*:/,
      /scroll-timeline/,
      /view-timeline/,
      /backdrop-filter/,
      /mask-image/,
      /clip-path/,
    ];

    return complexFeatures.some(pattern => pattern.test(selector));
  }

  /**
   * 处理深度选择器
   */
  private handleDeepSelector(selector: string, scopeAttribute: string): string {
    return selector
      .replace(/\s*>>>\s*/, ` ${scopeAttribute} `)
      .replace(/\s*\/deep\/\s*/, ` ${scopeAttribute} `);
  }

  /**
   * 生成CSS字符串
   */
  private generateCSS(rules: CSSRule[]): string {
    return rules.map(rule => this.generateCSSRule(rule)).join('\n');
  }

  /**
   * 生成CSS规则字符串
   */
  private generateCSSRule(rule: CSSRule): string {
    switch (rule.type) {
      case 'rule':
        return this.generateStyleRule(rule);
      case 'media':
        return this.generateMediaRule(rule);
      case 'keyframes':
        return this.generateKeyframesRule(rule);
      case 'import':
        return this.generateImportRule(rule);
      default:
        return '';
    }
  }

  /**
   * 生成样式规则
   */
  private generateStyleRule(rule: CSSRule): string {
    if (!rule.selector || !rule.declarations) {
      return '';
    }

    const declarations = rule.declarations
      .map(
        decl =>
          `  ${decl.property}: ${decl.value}${decl.important ? ' !important' : ''};`,
      )
      .join('\n');

    return `${rule.selector} {\n${declarations}\n}`;
  }

  /**
   * 生成媒体查询规则
   */
  private generateMediaRule(rule: CSSRule): string {
    if (!rule.media || !rule.rules) {
      return '';
    }

    const nestedRules = rule.rules
      .map(subRule => this.generateCSSRule(subRule))
      .filter(Boolean)
      .join('\n');

    return `@media ${rule.media} {\n${nestedRules}\n}`;
  }

  /**
   * 生成关键帧规则
   */
  private generateKeyframesRule(rule: CSSRule): string {
    if (!rule.keyframeName || !rule.declarations) {
      return '';
    }

    const declarations = rule.declarations
      .map(
        decl =>
          `  ${decl.property}: ${decl.value}${decl.important ? ' !important' : ''};`,
      )
      .join('\n');

    return `@keyframes ${rule.keyframeName} {\n${declarations}\n}`;
  }

  /**
   * 生成导入规则
   */
  private generateImportRule(rule: CSSRule): string {
    if (!rule.importUrl) {
      return '';
    }
    return `@import "${rule.importUrl}";`;
  }

  /**
   * 生成CSS-in-JS对象
   */
  private generateCssInJs(
    rules: CSSRule[],
    componentId: string,
  ): {
    cssObject: any;
    cssString: string;
    classNames: string[];
  } {
    const cssObject: any = {};
    const classNames: string[] = [];

    rules.forEach(rule => {
      if (rule.type === 'rule' && rule.selector && rule.declarations) {
        const className = this.generateClassName(rule.selector, componentId);
        if (className) {
          cssObject[className] = {};
          classNames.push(className);

          rule.declarations.forEach(decl => {
            const property = this.camelCaseProperty(decl.property);
            const value = this.normalizePropertyValue(decl.value);
            cssObject[className][property] = value;
          });
        }
      }
    });

    return {
      cssObject,
      cssString: this.generateCssString(cssObject),
      classNames,
    };
  }

  /**
   * 生成类名
   */
  private generateClassName(
    selector: string,
    componentId: string,
  ): string | null {
    // 提取类选择器
    const classMatch = selector.match(/\.([a-zA-Z][\w-]*)/);
    if (classMatch) {
      return `${classMatch[1]}-${componentId}`;
    }

    // 处理标签选择器
    const tagMatch = selector.match(/^([a-zA-Z][\w-]*)/);
    if (tagMatch) {
      return `${tagMatch[1]}-${componentId}`;
    }

    return null;
  }

  /**
   * CSS属性名驼峰化
   */
  private camelCaseProperty(property: string): string {
    return property.replace(/-([a-z])/g, (match, letter) =>
      letter.toUpperCase(),
    );
  }

  /**
   * 属性值标准化
   */
  private normalizePropertyValue(value: string): any {
    // 处理数值
    if (/^\d+$/.test(value)) {
      return parseInt(value);
    }

    // 处理像素值
    if (/^\d+px$/.test(value)) {
      return parseInt(value);
    }

    // 处理百分比
    if (/^\d+%$/.test(value)) {
      return value;
    }

    // 保持字符串值
    return value;
  }

  /**
   * 生成CSS字符串从对象
   */
  private generateCssString(cssObject: any): string {
    return Object.entries(cssObject)
      .map(([className, styles]) => {
        const declarations = Object.entries(styles as any)
          .map(([property, value]) => {
            const kebabProperty = property.replace(
              /[A-Z]/g,
              letter => `-${letter.toLowerCase()}`,
            );
            return `  ${kebabProperty}: ${value};`;
          })
          .join('\n');

        return `.${className} {\n${declarations}\n}`;
      })
      .join('\n');
  }

  /**
   * 提取类名
   */
  private extractClassNames(rules: CSSRule[]): string[] {
    const classNames: string[] = [];

    const extractFromRule = (rule: CSSRule) => {
      if (rule.type === 'rule' && rule.selector) {
        const matches = rule.selector.match(/\.([a-zA-Z][\w-]*)/g);
        if (matches) {
          matches.forEach(match => {
            const className = match.slice(1); // 移除前导点
            if (!classNames.includes(className)) {
              classNames.push(className);
            }
          });
        }
      }

      if (rule.rules) {
        rule.rules.forEach(extractFromRule);
      }
    };

    rules.forEach(extractFromRule);
    return classNames;
  }

  /**
   * 检查是否包含RPX值
   */
  private hasRpxValues(css: string): boolean {
    return /\d+(?:\.\d+)?rpx/.test(css);
  }

  /**
   * 创建空结果
   */
  private createEmptyResult(): TTSSProcessResult {
    return {
      css: '',
      scopedCss: '',
      cssInJs: {},
      classes: [],
      ruleCount: 0,
      rpxConverted: false,
      errors: [],
    };
  }

  /**
   * 设置转换配置
   */
  public setConversionConfig(
    config: Partial<typeof TTSS_CONVERSION_CONFIG>,
  ): void {
    this.conversionConfig = { ...this.conversionConfig, ...config };
  }

  /**
   * 获取处理统计信息
   */
  public getStats(): any {
    return {
      conversionConfig: this.conversionConfig,
      rpxMode: this.conversionConfig.defaultMode,
      designWidth: this.conversionConfig.designWidth,
    };
  }

  // ===============================
  // 调试辅助方法
  // ===============================

  /**
   * 分析TTSS结构
   */
  private analyzeTTSSStructure(ttss: string): any {
    const analysis = {
      totalRules: (ttss.match(/[^{}]+\s*\{[^}]*\}/g) || []).length,
      selectors: [] as string[],
      properties: {} as Record<string, number>,
      units: {} as Record<string, number>,
      functions: {} as Record<string, number>,
      atRules: {} as Record<string, number>,
      comments: (ttss.match(/\/\*[\s\S]*?\*\//g) || []).length,
      rpxUsage: 0,
      potentialIssues: [] as string[],
    };

    // 提取选择器
    const selectorMatches = ttss.match(/([^{}]+)\s*\{/g) || [];
    analysis.selectors = selectorMatches.map(match =>
      match.replace(/\s*\{$/, '').trim(),
    );

    // 统计属性
    const propertyMatches = ttss.match(/([a-zA-Z-]+)\s*:/g) || [];
    propertyMatches.forEach(match => {
      const property = match.replace(/\s*:$/, '').trim();
      analysis.properties[property] = (analysis.properties[property] || 0) + 1;
    });

    // 统计单位
    const unitMatches =
      ttss.match(/\d+(?:\.\d+)?(px|rpx|rem|em|vw|vh|%)/g) || [];
    unitMatches.forEach(match => {
      const unit = match.replace(/[\d.]+/, '');
      analysis.units[unit] = (analysis.units[unit] || 0) + 1;
    });

    // 统计RPX使用
    analysis.rpxUsage = (ttss.match(/\d+(?:\.\d+)?rpx/g) || []).length;

    // 统计CSS函数
    const functionMatches = ttss.match(/([a-zA-Z-]+)\(/g) || [];
    functionMatches.forEach(match => {
      const func = match.replace(/\($/, '');
      analysis.functions[func] = (analysis.functions[func] || 0) + 1;
    });

    // 统计@规则
    const atRuleMatches = ttss.match(/@[a-zA-Z-]+/g) || [];
    atRuleMatches.forEach(atRule => {
      analysis.atRules[atRule] = (analysis.atRules[atRule] || 0) + 1;
    });

    // 检查潜在问题
    if (ttss.includes('{') && !ttss.includes('}')) {
      analysis.potentialIssues.push('不完整的CSS规则');
    }
    if (ttss.includes('rpx') && ttss.includes('px')) {
      analysis.potentialIssues.push('混合使用rpx和px单位');
    }
    if (analysis.selectors.some(s => s.length > 100)) {
      analysis.potentialIssues.push('选择器过长');
    }

    return analysis;
  }

  /**
   * 分类CSS规则
   */
  private classifyRules(rules: CSSRule[]): Record<string, number> {
    const classification = {
      normalRules: 0,
      mediaRules: 0,
      keyframeRules: 0,
      importRules: 0,
      unknownRules: 0,
    };

    rules.forEach(rule => {
      switch (rule.type) {
        case 'rule':
          classification.normalRules++;
          break;
        case 'media':
          classification.mediaRules++;
          break;
        case 'keyframes':
          classification.keyframeRules++;
          break;
        case 'import':
          classification.importRules++;
          break;
        default:
          classification.unknownRules++;
      }
    });

    return classification;
  }

  /**
   * 记录CSS规则详情
   */
  private logCSSRules(rules: CSSRule[]): void {
    console.log('📋 [TTSSProcessor] CSS规则详细信息:');

    rules.slice(0, 10).forEach((rule, index) => {
      const ruleInfo = {
        index,
        type: rule.type,
        selector: rule.selector || `@${rule.type}`,
        declarationCount: rule.declarations?.length || 0,
        nestedRules: rule.rules?.length || 0,
      };

      console.log(`  [${index}]`, ruleInfo);

      if (rule.declarations && rule.declarations.length > 0) {
        const sampleDecls = rule.declarations
          .slice(0, 3)
          .map(
            decl =>
              `${decl.property}: ${decl.value}${decl.important ? ' !important' : ''}`,
          );
        console.log('      属性样例:', sampleDecls.join('; '));

        if (rule.declarations.length > 3) {
          console.log(`      ... 还有${rule.declarations.length - 3}个属性`);
        }
      }
    });

    if (rules.length > 10) {
      console.log(`  ... 还有${rules.length - 10}个CSS规则`);
    }
  }

  /**
   * 获取RPX转换统计
   */
  private getRpxConversionStats(
    originalRules: CSSRule[],
    convertedRules: CSSRule[],
  ): any {
    const stats = {
      originalRpxCount: 0,
      convertedRpxCount: 0,
      conversionRate: 0,
      conversions: [] as Array<{
        property: string;
        original: string;
        converted: string;
      }>,
      unconvertedRpx: [] as string[],
    };

    // 统计原始RPX使用
    const countRpxInRules = (rules: CSSRule[]): number => {
      let count = 0;
      const countInRule = (rule: CSSRule) => {
        if (rule.declarations) {
          rule.declarations.forEach(decl => {
            const rpxMatches = decl.value.match(/\d+(?:\.\d+)?rpx/g);
            if (rpxMatches) {
              count += rpxMatches.length;
            }
          });
        }
        if (rule.rules) {
          rule.rules.forEach(countInRule);
        }
      };
      rules.forEach(countInRule);
      return count;
    };

    stats.originalRpxCount = countRpxInRules(originalRules);
    stats.convertedRpxCount = countRpxInRules(convertedRules);
    stats.conversionRate =
      stats.originalRpxCount > 0
        ? ((stats.originalRpxCount - stats.convertedRpxCount) /
            stats.originalRpxCount) *
          100
        : 0;

    // 收集转换样例
    const collectConversions = (origRule: CSSRule, convRule: CSSRule) => {
      if (origRule.declarations && convRule.declarations) {
        origRule.declarations.forEach((origDecl, index) => {
          const convDecl = convRule.declarations![index];
          if (origDecl && convDecl && origDecl.value !== convDecl.value) {
            stats.conversions.push({
              property: origDecl.property,
              original: origDecl.value,
              converted: convDecl.value,
            });
          }
        });
      }
    };

    originalRules.forEach((origRule, index) => {
      const convRule = convertedRules[index];
      if (convRule) {
        collectConversions(origRule, convRule);
      }
    });

    // 限制样例数量
    stats.conversions = stats.conversions.slice(0, 5);

    return stats;
  }

  /**
   * 统计修改的选择器
   */
  private countModifiedSelectors(
    originalRules: CSSRule[],
    scopedRules: CSSRule[],
  ): number {
    let modifiedCount = 0;

    originalRules.forEach((origRule, index) => {
      const scopedRule = scopedRules[index];
      if (
        origRule.selector &&
        scopedRule.selector &&
        origRule.selector !== scopedRule.selector
      ) {
        modifiedCount++;
      }
    });

    return modifiedCount;
  }

  /**
   * 验证TTSS到CSS的映射
   */
  private validateTTSSToCSSMapping(
    ttss: string,
    css: string,
    classes: string[],
  ): void {
    const validation = {
      sourceRules: (ttss.match(/[^{}]+\s*\{[^}]*\}/g) || []).length,
      targetRules: (css.match(/[^{}]+\s*\{[^}]*\}/g) || []).length,
      sourceRpx: (ttss.match(/\d+(?:\.\d+)?rpx/g) || []).length,
      targetRpx: (css.match(/\d+(?:\.\d+)?rpx/g) || []).length,
      sourceClasses: (ttss.match(/\.[a-zA-Z][a-zA-Z0-9_-]*/g) || []).length,
      extractedClasses: classes.length,
      hasContent: css.length > 0,
      preservedStructure: css.includes('{') && css.includes('}'),
      rpxConversionRate: 0,
      errors: [] as string[],
    };

    // 计算RPX转换率
    if (validation.sourceRpx > 0) {
      validation.rpxConversionRate =
        ((validation.sourceRpx - validation.targetRpx) / validation.sourceRpx) *
        100;
    }

    // 检查映射有效性
    if (validation.sourceRules > 0 && validation.targetRules === 0) {
      validation.errors.push('源TTSS有规则但CSS输出没有规则');
    }

    if (ttss.length > 10 && css.length < 10) {
      validation.errors.push('源TTSS有内容但CSS输出几乎为空');
    }

    if (!validation.preservedStructure && ttss.trim().length > 0) {
      validation.errors.push('CSS输出缺少有效的CSS结构');
    }

    if (validation.sourceRpx > 0 && validation.rpxConversionRate < 90) {
      validation.errors.push(
        `RPX转换率较低: ${validation.rpxConversionRate.toFixed(1)}%`,
      );
    }

    console.log('🔍 [TTSSProcessor] TTSS->CSS映射验证结果:', validation);

    if (validation.errors.length > 0) {
      console.warn('⚠️ [TTSSProcessor] 映射验证发现问题:', validation.errors);
    } else {
      console.log('✅ [TTSSProcessor] 映射验证通过');
    }
  }

  // ===============================
  // 增强的方法 - 基于web-speedy-plugin
  // ===============================

  /**
   * 增强的RPX转换 - 基于web-speedy-plugin的转换逻辑
   */
  private enhancedConvertRpx(rules: CSSRule[]): CSSRule[] {
    return rules.map(rule => {
      const convertedRule = { ...rule };

      if (rule.declarations) {
        convertedRule.declarations = rule.declarations.map(decl => ({
          ...decl,
          value: this.convertRpxInValue(decl.value),
        }));
      }

      if (rule.rules) {
        convertedRule.rules = this.enhancedConvertRpx(rule.rules);
      }

      return convertedRule;
    });
  }

  /**
   * 在值中转换RPX单位 - 支持多种转换模式
   */
  private convertRpxInValue(value: string): string {
    return value.replace(/(\d+(?:\.\d+)?)rpx/g, (match, rpxValue) => {
      const rpx = parseFloat(rpxValue);

      switch (this.enhancedRpxConfig.rpxMode) {
        case 'vw':
          return this.convertRpxToVw(rpx);
        case 'rem':
          return this.convertRpxToRem(rpx);
        case 'px':
          return `${rpx}px`;
        default:
          return match;
      }
    });
  }

  /**
   * 增强的作用域处理 - 智能判断是否需要作用域化
   * 🔥 修复：深拷贝规则避免作用域污染
   */
  private enhancedAddSelectorScope(rules: CSSRule[], tagV: string): CSSRule[] {
    return rules.map(rule => {
      // 深拷贝规则避免修改原始对象
      const newRule = { ...rule };

      if (newRule.type === 'rule' && newRule.selector) {
        // 智能判断是否需要作用域化
        if (this.shouldScopeSelector(newRule.selector)) {
          newRule.selector = this.applySelectorScope(newRule.selector, tagV);
        }
      } else if (newRule.type === 'media' && newRule.rules) {
        // 递归处理媒体查询中的规则
        newRule.rules = this.enhancedAddSelectorScope(newRule.rules, tagV);
      }

      // 深拷贝声明数组
      if (newRule.declarations) {
        newRule.declarations = newRule.declarations.map(decl => ({ ...decl }));
      }

      return newRule;
    });
  }

  /**
   * 判断选择器是否需要作用域化
   */
  private shouldScopeSelector(selector: string): boolean {
    const globalPatterns = [
      /^body\b/, // body 选择器
      /^html\b/, // html 选择器
      /^\*/, // 通配符选择器
      /:root/, // :root 选择器
      /^@/, // @规则
      /:global\(/, // 明确标记的全局选择器
      /^\.global-/, // 全局类名前缀
      /::before/, // 伪元素
      /::after/, // 伪元素
      /::placeholder/, // 伪元素
      /^from\b|^to\b/, // keyframes 关键字
      /^\d+%$/, // keyframes 百分比
    ];

    return !globalPatterns.some(pattern => pattern.test(selector.trim()));
  }

  /**
   * 应用选择器作用域
   */
  private applySelectorScope(selector: string, tagV: string): string {
    const scopeAttr = `[data-v-${tagV}]`;

    return selector
      .split(',')
      .map(s => {
        const trimmed = s.trim();

        // 跳过已经有作用域的选择器
        if (trimmed.includes(scopeAttr)) {
          return trimmed;
        }

        // 🔥 修复：更精确地处理伪类和伪元素选择器
        if (trimmed.includes(':')) {
          // 处理伪元素（::before, ::after 等）
          if (trimmed.includes('::')) {
            const pseudoElementIndex = trimmed.indexOf('::');
            const baseSelector = trimmed.substring(0, pseudoElementIndex);
            const pseudoElement = trimmed.substring(pseudoElementIndex);
            return `${baseSelector}${scopeAttr}${pseudoElement}`;
          }

          // 处理伪类（:hover, :focus 等）
          const pseudoClassMatch = trimmed.match(/^([^:]+)(:.+)$/);
          if (pseudoClassMatch) {
            const [, baseSelector, pseudoClasses] = pseudoClassMatch;
            return `${baseSelector}${scopeAttr}${pseudoClasses}`;
          }
        }

        // 普通选择器
        return `${trimmed}${scopeAttr}`;
      })
      .join(', ');
  }

  /**
   * 为选择器添加作用域 - 模拟官方逻辑
   */
  private addScopeToSelector(selector: string, tagV: string): string {
    return selector
      .split(',')
      .map(sel => {
        const trimmed = sel.trim();

        // 跳过特殊选择器
        if (
          trimmed.startsWith('@') ||
          trimmed.includes('::') ||
          trimmed.includes(':root') ||
          trimmed.includes('html') ||
          trimmed.includes('body') ||
          trimmed.includes('*')
        ) {
          return trimmed;
        }

        // 已经有作用域的选择器
        if (trimmed.includes(`[data-v-${tagV}]`)) {
          return trimmed;
        }

        // 为选择器添加作用域
        return `[data-v-${tagV}] ${trimmed}`;
      })
      .join(', ');
  }

  /**
   * 选择器权重提升 - 模拟webBumpAllSelectorSpecificity
   * 🔥 修复：确保深拷贝保护
   */
  private bumpSelectorSpecificity(rules: CSSRule[]): CSSRule[] {
    return rules.map(rule => {
      const bumpedRule = { ...rule };

      if (rule.type === 'rule' && rule.selector) {
        bumpedRule.selector = this.bumpSelector(rule.selector);
      }

      if (rule.rules) {
        bumpedRule.rules = this.bumpSelectorSpecificity(rule.rules);
      }

      // 🔥 修复：深拷贝声明数组
      if (bumpedRule.declarations) {
        bumpedRule.declarations = bumpedRule.declarations.map(decl => ({
          ...decl,
        }));
      }

      return bumpedRule;
    });
  }

  /**
   * 提升单个选择器的权重
   */
  private bumpSelector(selector: string): string {
    return selector
      .split(',')
      .map(sel => {
        const trimmed = sel.trim();

        // 跳过特殊选择器
        if (trimmed.startsWith('@') || trimmed.includes('::')) {
          return trimmed;
        }

        // 添加权重提升 - 使用:not(#\9)技巧
        return `${trimmed}:not(#\\9)`;
      })
      .join(', ');
  }
}
