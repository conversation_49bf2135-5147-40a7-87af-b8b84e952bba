/**
 * @package runtime-convert-parse5
 * @description 完整的Lynx到Web UI映射系统
 * 包含所有Lynx元素、属性、事件和样式的完整映射规则
 */

export interface ComprehensiveLynxMapping {
  tag: string;
  props?: Record<string, any>;
  selfClosing?: boolean;
  attributeMapping?: Record<string, string | ((value: string) => any)>;
  styleMapping?: Record<string, string | ((value: string) => string)>;
  eventMapping?: Record<string, string>;
  children?: string[];
  cssClasses?: string[];
  webStandard?: string; // 对应的Web标准
  // 新增：web-speedy-plugin 兼容属性
  attributeProcessor?: (
    attrs: Record<string, string>,
    tagV?: string,
  ) => Record<string, any>;
  webSpeedyCompatible?: boolean;
}

// ===============================
// 完整的Lynx元素映射表
// ===============================

export const COMPREHENSIVE_LYNX_MAPPING: Record<
  string,
  ComprehensiveLynxMapping
> = {
  // ===== 视图容器类 =====
  view: {
    tag: 'div',
    props: { className: 'lynx-view' },
    webStandard: 'HTML div element',
    webSpeedyCompatible: true,
    attributeMapping: {
      class: 'className',
      style: 'style',
      id: 'id',
      hidden: 'hidden',
      'hover-class': 'data-hover-class',
      'hover-start-time': 'data-hover-start-time',
      'hover-stay-time': 'data-hover-stay-time',
      animation: 'data-animation',
      'catch-move': 'data-catch-move',
      'disable-scroll': 'data-disable-scroll',
    },
    styleMapping: {
      'background-color': 'backgroundColor',
      'border-radius': 'borderRadius',
      'box-shadow': 'boxShadow',
      opacity: 'opacity',
    },
    cssClasses: ['lynx-view', 'view-container'],
    // web-speedy-plugin 兼容的属性处理器
    attributeProcessor: (attrs: Record<string, string>, tagV?: string) => {
      const processed = { ...attrs };

      // class 映射到 className
      if (attrs.class) {
        processed.className = processed.className
          ? `${processed.className} ${attrs.class}`
          : attrs.class;
        delete processed.class;
      }

      // hover-class 处理 - 模拟官方行为
      if (attrs['hover-class']) {
        const hoverClass = attrs['hover-class'];
        processed['data-hover-class'] = hoverClass;
        processed.onMouseEnter = `(e) => e.target.classList.add('${hoverClass}')`;
        processed.onMouseLeave = `(e) => e.target.classList.remove('${hoverClass}')`;
        delete processed['hover-class'];
      }

      // 添加作用域标识 - 模拟 tagV 系统
      if (tagV) {
        processed[`data-v-${tagV}`] = '';
      }

      return processed;
    },
  },

  'scroll-view': {
    tag: 'div',
    props: {
      className: 'lynx-scroll-view',
      style: { overflow: 'auto', WebkitOverflowScrolling: 'touch' },
    },
    webStandard: 'HTML div with scroll capabilities',
    attributeMapping: {
      'scroll-x': (value: string) => ({
        'data-scroll-x': value,
        overflowX: value === 'true' ? 'auto' : 'hidden',
      }),
      'scroll-y': (value: string) => ({
        'data-scroll-y': value,
        overflowY: value === 'true' ? 'auto' : 'hidden',
      }),
      'upper-threshold': 'data-upper-threshold',
      'lower-threshold': 'data-lower-threshold',
      'scroll-top': 'scrollTop',
      'scroll-left': 'scrollLeft',
      'scroll-into-view': 'data-scroll-into-view',
      'scroll-with-animation': 'data-scroll-with-animation',
      'enable-back-to-top': 'data-enable-back-to-top',
      'enable-flex': 'data-enable-flex',
      'scroll-anchoring': 'data-scroll-anchoring',
      'refresher-enabled': 'data-refresher-enabled',
      'refresher-threshold': 'data-refresher-threshold',
      'refresher-default-style': 'data-refresher-default-style',
      'refresher-background': 'data-refresher-background',
      'refresher-triggered': 'data-refresher-triggered',
    },
    eventMapping: {
      scroll: 'onScroll',
      scrolltoupper: 'onScrollToUpper',
      scrolltolower: 'onScrollToLower',
      refresherpulling: 'onRefresherPulling',
      refresherrefresh: 'onRefresherRefresh',
      refresherrestore: 'onRefresherRestore',
      refresherabort: 'onRefresherAbort',
    },
    cssClasses: ['lynx-scroll-view', 'scroll-container'],
  },

  swiper: {
    tag: 'div',
    props: { className: 'lynx-swiper' },
    webStandard: 'Custom swiper component',
    attributeMapping: {
      'indicator-dots': 'data-indicator-dots',
      'indicator-color': 'data-indicator-color',
      'indicator-active-color': 'data-indicator-active-color',
      autoplay: 'data-autoplay',
      current: 'data-current',
      'current-item-id': 'data-current-item-id',
      interval: 'data-interval',
      duration: 'data-duration',
      circular: 'data-circular',
      vertical: 'data-vertical',
      'previous-margin': 'data-previous-margin',
      'next-margin': 'data-next-margin',
      'display-multiple-items': 'data-display-multiple-items',
      'skip-hidden-item-layout': 'data-skip-hidden-item-layout',
      'disable-touch': 'data-disable-touch',
      touchable: 'data-touchable',
    },
    eventMapping: {
      change: 'onChange',
      transition: 'onTransition',
      animationfinish: 'onAnimationFinish',
    },
    cssClasses: ['lynx-swiper', 'swiper-container'],
  },

  'swiper-item': {
    tag: 'div',
    props: { className: 'lynx-swiper-item' },
    webStandard: 'Swiper slide item',
    attributeMapping: {
      'item-id': 'data-item-id',
    },
    cssClasses: ['lynx-swiper-item', 'swiper-slide'],
  },

  'movable-area': {
    tag: 'div',
    props: { className: 'lynx-movable-area' },
    webStandard: 'Draggable container area',
    attributeMapping: {
      'scale-area': 'data-scale-area',
    },
    cssClasses: ['lynx-movable-area', 'draggable-area'],
  },

  'movable-view': {
    tag: 'div',
    props: { className: 'lynx-movable-view' },
    webStandard: 'Draggable element',
    attributeMapping: {
      direction: 'data-direction',
      inertia: 'data-inertia',
      'out-of-bounds': 'data-out-of-bounds',
      x: 'data-x',
      y: 'data-y',
      damping: 'data-damping',
      friction: 'data-friction',
      disabled: 'data-disabled',
      scale: 'data-scale',
      'scale-min': 'data-scale-min',
      'scale-max': 'data-scale-max',
      'scale-value': 'data-scale-value',
      animation: 'data-animation',
    },
    eventMapping: {
      change: 'onChange',
      scale: 'onScale',
      htouchmove: 'onHTouchMove',
      vtouchmove: 'onVTouchMove',
    },
    cssClasses: ['lynx-movable-view', 'draggable-element'],
  },

  'cover-view': {
    tag: 'div',
    props: { className: 'lynx-cover-view' },
    webStandard: 'Overlay view component',
    attributeMapping: {
      'scroll-top': 'scrollTop',
    },
    cssClasses: ['lynx-cover-view', 'overlay-view'],
  },

  'cover-image': {
    tag: 'img',
    props: { className: 'lynx-cover-image' },
    selfClosing: true,
    webStandard: 'HTML img element for overlay',
    attributeMapping: {
      src: 'src',
      alt: 'alt',
      mode: 'data-mode',
    },
    cssClasses: ['lynx-cover-image', 'overlay-image'],
  },

  // ===== 文本类元素 =====
  text: {
    tag: 'span',
    props: { className: 'lynx-text' },
    webStandard: 'HTML span element',
    webSpeedyCompatible: true,
    attributeMapping: {
      selectable: (value: string) => ({
        'data-selectable': value,
        userSelect: value === 'true' ? 'text' : 'none',
      }),
      space: 'data-space',
      decode: 'data-decode',
    },
    styleMapping: {
      color: 'color',
      'font-size': 'fontSize',
      'font-weight': 'fontWeight',
      'line-height': 'lineHeight',
      'text-align': 'textAlign',
      'text-decoration': 'textDecoration',
    },
    cssClasses: ['lynx-text', 'text-content'],
    // web-speedy-plugin 兼容的属性处理器
    attributeProcessor: (attrs: Record<string, string>, tagV?: string) => {
      const processed = { ...attrs };
      const style = processed.style ? JSON.parse(processed.style) : {};

      // class 映射
      if (attrs.class) {
        processed.className = processed.className
          ? `${processed.className} ${attrs.class}`
          : attrs.class;
        delete processed.class;
      }

      // selectable 属性处理 - 模拟官方行为
      if (attrs.selectable === 'true') {
        style.userSelect = 'text';
        style.WebkitUserSelect = 'text';
        processed['data-selectable'] = 'true';
      } else if (attrs.selectable === 'false') {
        style.userSelect = 'none';
        style.WebkitUserSelect = 'none';
        processed['data-selectable'] = 'false';
      }
      delete processed.selectable;

      // space 属性处理
      if (attrs.space) {
        processed['data-space'] = attrs.space;
        if (attrs.space === 'nbsp') {
          style.whiteSpace = 'pre';
        } else if (attrs.space === 'ensp') {
          style.whiteSpace = 'pre-wrap';
        }
        delete processed.space;
      }

      // decode 属性
      if (attrs.decode) {
        processed['data-decode'] = attrs.decode;
        delete processed.decode;
      }

      // 添加作用域标识
      if (tagV) {
        processed[`data-v-${tagV}`] = '';
      }

      // 应用样式
      if (Object.keys(style).length > 0) {
        processed.style = style;
      }

      return processed;
    },
  },

  'rich-text': {
    tag: 'div',
    props: { className: 'lynx-rich-text' },
    webStandard: 'HTML div for rich content',
    attributeMapping: {
      nodes: 'data-nodes',
      space: 'data-space',
    },
    cssClasses: ['lynx-rich-text', 'rich-content'],
  },

  // ===== 媒体元素 =====
  image: {
    tag: 'img',
    props: { className: 'lynx-image' },
    selfClosing: true,
    webStandard: 'HTML img element',
    attributeMapping: {
      src: 'src',
      alt: 'alt',
      mode: (value: string) => {
        const modes = {
          scaleToFill: 'fill',
          aspectFit: 'contain',
          aspectFill: 'cover',
          widthFix: 'scale-down',
          heightFix: 'scale-down',
          top: 'top',
          bottom: 'bottom',
          center: 'center',
          left: 'left',
          right: 'right',
          'top left': 'top left',
          'top right': 'top right',
          'bottom left': 'bottom left',
          'bottom right': 'bottom right',
        };
        return {
          'data-mode': value,
          objectFit: modes[value as keyof typeof modes] || 'fill',
        };
      },
      webp: 'data-webp',
      'lazy-load': (value: string) => ({
        'data-lazy-load': value,
        loading: value === 'true' ? 'lazy' : 'eager',
      }),
      'fade-in': 'data-fade-in',
      binderror: 'onError',
      bindload: 'onLoad',
      'show-menu-by-longpress': 'data-show-menu',
    },
    eventMapping: {
      error: 'onError',
      load: 'onLoad',
    },
    cssClasses: ['lynx-image', 'responsive-image'],
  },

  video: {
    tag: 'video',
    props: {
      className: 'lynx-video',
      controls: true,
      preload: 'metadata',
    },
    webStandard: 'HTML video element',
    attributeMapping: {
      src: 'src',
      duration: 'data-duration',
      controls: 'controls',
      'danmu-list': 'data-danmu-list',
      'danmu-btn': 'data-danmu-btn',
      'enable-danmu': 'data-enable-danmu',
      autoplay: 'autoPlay',
      loop: 'loop',
      muted: 'muted',
      'initial-time': 'data-initial-time',
      'page-gesture': 'data-page-gesture',
      direction: 'data-direction',
      'show-progress': 'data-show-progress',
      'show-fullscreen-btn': 'data-show-fullscreen-btn',
      'show-play-btn': 'data-show-play-btn',
      'show-center-play-btn': 'data-show-center-play-btn',
      'enable-progress-gesture': 'data-enable-progress-gesture',
      'object-fit': 'data-object-fit',
      poster: 'poster',
      'show-mute-btn': 'data-show-mute-btn',
    },
    eventMapping: {
      play: 'onPlay',
      pause: 'onPause',
      ended: 'onEnded',
      timeupdate: 'onTimeUpdate',
      fullscreenchange: 'onFullscreenChange',
      waiting: 'onWaiting',
      error: 'onError',
      progress: 'onProgress',
    },
    cssClasses: ['lynx-video', 'video-player'],
  },

  audio: {
    tag: 'audio',
    props: {
      className: 'lynx-audio',
      controls: true,
    },
    webStandard: 'HTML audio element',
    attributeMapping: {
      id: 'id',
      src: 'src',
      loop: 'loop',
      controls: 'controls',
      poster: 'data-poster',
      name: 'data-name',
      author: 'data-author',
    },
    eventMapping: {
      error: 'onError',
      play: 'onPlay',
      pause: 'onPause',
      timeupdate: 'onTimeUpdate',
      ended: 'onEnded',
    },
    cssClasses: ['lynx-audio', 'audio-player'],
  },

  camera: {
    tag: 'div',
    props: { className: 'lynx-camera' },
    webStandard: 'Custom camera component',
    attributeMapping: {
      mode: 'data-mode',
      resolution: 'data-resolution',
      'device-position': 'data-device-position',
      flash: 'data-flash',
      'frame-size': 'data-frame-size',
    },
    eventMapping: {
      stop: 'onStop',
      error: 'onError',
      initdone: 'onInitDone',
      scancode: 'onScanCode',
    },
    cssClasses: ['lynx-camera', 'camera-view'],
  },

  // ===== 表单元素 =====
  input: {
    tag: 'input',
    props: { className: 'lynx-input' },
    selfClosing: true,
    webStandard: 'HTML input element',
    attributeMapping: {
      value: 'value',
      type: (value: string) => {
        const typeMap = {
          text: 'text',
          number: 'number',
          idcard: 'text',
          digit: 'tel',
          password: 'password',
        };
        return { type: typeMap[value as keyof typeof typeMap] || 'text' };
      },
      password: (value: string) => ({ type: 'password' }),
      placeholder: 'placeholder',
      'placeholder-style': 'data-placeholder-style',
      'placeholder-class': 'data-placeholder-class',
      disabled: 'disabled',
      maxlength: 'maxLength',
      'cursor-spacing': 'data-cursor-spacing',
      'auto-focus': 'autoFocus',
      focus: 'data-focus',
      'confirm-type': 'data-confirm-type',
      'confirm-hold': 'data-confirm-hold',
      cursor: 'data-cursor',
      'selection-start': 'data-selection-start',
      'selection-end': 'data-selection-end',
      'adjust-position': 'data-adjust-position',
      'hold-keyboard': 'data-hold-keyboard',
    },
    eventMapping: {
      input: 'onInput',
      focus: 'onFocus',
      blur: 'onBlur',
      confirm: 'onKeyDown',
      keyboardheightchange: 'onKeyboardHeightChange',
    },
    cssClasses: ['lynx-input', 'form-input'],
  },

  textarea: {
    tag: 'textarea',
    props: { className: 'lynx-textarea' },
    webStandard: 'HTML textarea element',
    attributeMapping: {
      value: 'value',
      placeholder: 'placeholder',
      'placeholder-style': 'data-placeholder-style',
      'placeholder-class': 'data-placeholder-class',
      disabled: 'disabled',
      maxlength: 'maxLength',
      'auto-focus': 'autoFocus',
      focus: 'data-focus',
      'auto-height': 'data-auto-height',
      fixed: 'data-fixed',
      'cursor-spacing': 'data-cursor-spacing',
      cursor: 'data-cursor',
      'show-confirm-bar': 'data-show-confirm-bar',
      'selection-start': 'data-selection-start',
      'selection-end': 'data-selection-end',
      'adjust-position': 'data-adjust-position',
      'hold-keyboard': 'data-hold-keyboard',
    },
    eventMapping: {
      focus: 'onFocus',
      blur: 'onBlur',
      linechange: 'onLineChange',
      input: 'onInput',
      confirm: 'onConfirm',
      keyboardheightchange: 'onKeyboardHeightChange',
    },
    cssClasses: ['lynx-textarea', 'form-textarea'],
  },

  button: {
    tag: 'button',
    props: { className: 'lynx-button' },
    webStandard: 'HTML button element',
    attributeMapping: {
      size: 'data-size',
      type: 'type',
      plain: 'data-plain',
      disabled: 'disabled',
      loading: 'data-loading',
      'form-type': (value: string) => ({
        type: value === 'submit' ? 'submit' : 'button',
      }),
      'open-type': 'data-open-type',
      'hover-class': 'data-hover-class',
      'hover-start-time': 'data-hover-start-time',
      'hover-stay-time': 'data-hover-stay-time',
      lang: 'data-lang',
      'session-from': 'data-session-from',
      'send-message-title': 'data-send-message-title',
      'send-message-path': 'data-send-message-path',
      'send-message-img': 'data-send-message-img',
      'app-parameter': 'data-app-parameter',
      'show-message-card': 'data-show-message-card',
    },
    eventMapping: {
      getuserinfo: 'onGetUserInfo',
      contact: 'onContact',
      getphonenumber: 'onGetPhoneNumber',
      error: 'onError',
      opensetting: 'onOpenSetting',
      launchapp: 'onLaunchApp',
    },
    cssClasses: ['lynx-button', 'interactive-button'],
  },

  switch: {
    tag: 'input',
    props: {
      type: 'checkbox',
      className: 'lynx-switch',
    },
    selfClosing: true,
    webStandard: 'HTML checkbox input styled as switch',
    attributeMapping: {
      checked: 'checked',
      disabled: 'disabled',
      type: 'data-switch-type',
      color: 'data-color',
    },
    eventMapping: {
      change: 'onChange',
    },
    cssClasses: ['lynx-switch', 'toggle-switch'],
  },

  slider: {
    tag: 'input',
    props: {
      type: 'range',
      className: 'lynx-slider',
    },
    selfClosing: true,
    webStandard: 'HTML range input',
    attributeMapping: {
      min: 'min',
      max: 'max',
      step: 'step',
      disabled: 'disabled',
      value: 'value',
      color: 'data-color',
      'selected-color': 'data-selected-color',
      activeColor: 'data-active-color',
      backgroundColor: 'data-background-color',
      'block-size': 'data-block-size',
      'block-color': 'data-block-color',
      'show-value': 'data-show-value',
    },
    eventMapping: {
      change: 'onChange',
      changing: 'onChanging',
    },
    cssClasses: ['lynx-slider', 'range-slider'],
  },

  picker: {
    tag: 'select',
    props: { className: 'lynx-picker' },
    webStandard: 'HTML select element or custom picker',
    attributeMapping: {
      mode: 'data-mode',
      disabled: 'disabled',
      range: 'data-range',
      value: 'value',
      start: 'data-start',
      end: 'data-end',
      fields: 'data-fields',
      'custom-item': 'data-custom-item',
    },
    eventMapping: {
      change: 'onChange',
      columnchange: 'onColumnChange',
      cancel: 'onCancel',
    },
    cssClasses: ['lynx-picker', 'select-picker'],
  },

  'picker-view': {
    tag: 'div',
    props: { className: 'lynx-picker-view' },
    webStandard: 'Custom picker view component',
    attributeMapping: {
      value: 'data-value',
      'indicator-style': 'data-indicator-style',
      'indicator-class': 'data-indicator-class',
      'mask-style': 'data-mask-style',
      'mask-class': 'data-mask-class',
    },
    eventMapping: {
      change: 'onChange',
      pickstart: 'onPickStart',
      pickend: 'onPickEnd',
    },
    cssClasses: ['lynx-picker-view', 'picker-container'],
  },

  'picker-view-column': {
    tag: 'div',
    props: { className: 'lynx-picker-view-column' },
    webStandard: 'Picker column component',
    cssClasses: ['lynx-picker-view-column', 'picker-column'],
  },

  checkbox: {
    tag: 'input',
    props: {
      type: 'checkbox',
      className: 'lynx-checkbox',
    },
    selfClosing: true,
    webStandard: 'HTML checkbox input',
    attributeMapping: {
      value: 'value',
      disabled: 'disabled',
      checked: 'checked',
      color: 'data-color',
    },
    eventMapping: {},
    cssClasses: ['lynx-checkbox', 'form-checkbox'],
  },

  'checkbox-group': {
    tag: 'div',
    props: { className: 'lynx-checkbox-group' },
    webStandard: 'Checkbox group container',
    eventMapping: {
      change: 'onChange',
    },
    cssClasses: ['lynx-checkbox-group', 'checkbox-container'],
  },

  radio: {
    tag: 'input',
    props: {
      type: 'radio',
      className: 'lynx-radio',
    },
    selfClosing: true,
    webStandard: 'HTML radio input',
    attributeMapping: {
      value: 'value',
      checked: 'checked',
      disabled: 'disabled',
      color: 'data-color',
    },
    cssClasses: ['lynx-radio', 'form-radio'],
  },

  'radio-group': {
    tag: 'div',
    props: { className: 'lynx-radio-group' },
    webStandard: 'Radio group container',
    eventMapping: {
      change: 'onChange',
    },
    cssClasses: ['lynx-radio-group', 'radio-container'],
  },

  form: {
    tag: 'form',
    props: { className: 'lynx-form' },
    webStandard: 'HTML form element',
    attributeMapping: {
      'report-submit': 'data-report-submit',
      'report-submit-timeout': 'data-report-submit-timeout',
    },
    eventMapping: {
      submit: 'onSubmit',
      reset: 'onReset',
    },
    cssClasses: ['lynx-form', 'form-container'],
  },

  label: {
    tag: 'label',
    props: { className: 'lynx-label' },
    webStandard: 'HTML label element',
    attributeMapping: {
      for: 'htmlFor',
    },
    cssClasses: ['lynx-label', 'form-label'],
  },

  // ===== 导航元素 =====
  navigator: {
    tag: 'a',
    props: { className: 'lynx-navigator' },
    webStandard: 'HTML anchor element for navigation',
    attributeMapping: {
      target: 'data-target',
      url: 'href',
      'open-type': (value: string) => {
        const targetMap = {
          navigate: '_self',
          redirect: '_self',
          switchTab: '_self',
          reLaunch: '_self',
          navigateBack: '_self',
        };
        return {
          'data-open-type': value,
          target: targetMap[value as keyof typeof targetMap] || '_self',
        };
      },
      delta: 'data-delta',
      'app-id': 'data-app-id',
      path: 'data-path',
      'extra-data': 'data-extra-data',
      version: 'data-version',
      'hover-class': 'data-hover-class',
      'hover-start-time': 'data-hover-start-time',
      'hover-stay-time': 'data-hover-stay-time',
    },
    eventMapping: {
      success: 'onSuccess',
      fail: 'onFail',
      complete: 'onComplete',
    },
    cssClasses: ['lynx-navigator', 'nav-link'],
  },

  'functional-page-navigator': {
    tag: 'a',
    props: { className: 'lynx-functional-page-navigator' },
    webStandard: 'Functional page navigation',
    attributeMapping: {
      version: 'data-version',
      name: 'data-name',
      args: 'data-args',
    },
    eventMapping: {
      success: 'onSuccess',
      fail: 'onFail',
      cancel: 'onCancel',
    },
    cssClasses: ['lynx-functional-page-navigator', 'functional-nav'],
  },

  // ===== 高级组件 =====
  progress: {
    tag: 'progress',
    props: { className: 'lynx-progress' },
    webStandard: 'HTML progress element',
    attributeMapping: {
      percent: 'value',
      'show-info': 'data-show-info',
      'border-radius': 'data-border-radius',
      'font-size': 'data-font-size',
      'stroke-width': 'data-stroke-width',
      color: 'data-color',
      activeColor: 'data-active-color',
      backgroundColor: 'data-background-color',
      active: 'data-active',
      'active-mode': 'data-active-mode',
      duration: 'data-duration',
    },
    cssClasses: ['lynx-progress', 'progress-bar'],
  },

  icon: {
    tag: 'i',
    props: { className: 'lynx-icon' },
    webStandard: 'Icon element',
    attributeMapping: {
      type: 'data-type',
      size: 'data-size',
      color: 'data-color',
    },
    cssClasses: ['lynx-icon', 'icon-element'],
  },

  canvas: {
    tag: 'canvas',
    props: { className: 'lynx-canvas' },
    webStandard: 'HTML canvas element',
    attributeMapping: {
      'canvas-id': 'id',
      'disable-scroll': 'data-disable-scroll',
      type: 'data-type',
    },
    eventMapping: {
      touchstart: 'onTouchStart',
      touchmove: 'onTouchMove',
      touchend: 'onTouchEnd',
      touchcancel: 'onTouchCancel',
      longtap: 'onLongTap',
      error: 'onError',
    },
    cssClasses: ['lynx-canvas', 'canvas-element'],
  },

  'web-view': {
    tag: 'iframe',
    props: { className: 'lynx-web-view' },
    selfClosing: true,
    webStandard: 'HTML iframe element',
    attributeMapping: {
      src: 'src',
      type: 'data-type',
    },
    eventMapping: {
      message: 'onMessage',
      load: 'onLoad',
      error: 'onError',
    },
    cssClasses: ['lynx-web-view', 'embedded-web'],
  },

  ad: {
    tag: 'div',
    props: { className: 'lynx-ad' },
    webStandard: 'Advertisement container',
    attributeMapping: {
      'unit-id': 'data-unit-id',
      'ad-intervals': 'data-ad-intervals',
      'ad-type': 'data-ad-type',
      'ad-theme': 'data-ad-theme',
    },
    eventMapping: {
      load: 'onLoad',
      error: 'onError',
      close: 'onClose',
    },
    cssClasses: ['lynx-ad', 'advertisement'],
  },

  // ===== 地图和定位 =====
  map: {
    tag: 'div',
    props: { className: 'lynx-map' },
    webStandard: 'Map container component',
    attributeMapping: {
      longitude: 'data-longitude',
      latitude: 'data-latitude',
      scale: 'data-scale',
      markers: 'data-markers',
      covers: 'data-covers',
      polyline: 'data-polyline',
      circles: 'data-circles',
      controls: 'data-controls',
      'include-points': 'data-include-points',
      'show-location': 'data-show-location',
      'layer-style': 'data-layer-style',
      skew: 'data-skew',
      rotate: 'data-rotate',
      'show-compass': 'data-show-compass',
      'show-scale': 'data-show-scale',
      'enable-overlooking': 'data-enable-overlooking',
      'enable-zoom': 'data-enable-zoom',
      'enable-scroll': 'data-enable-scroll',
      'enable-rotate': 'data-enable-rotate',
      'enable-satellite': 'data-enable-satellite',
      'enable-traffic': 'data-enable-traffic',
    },
    eventMapping: {
      tap: 'onTap',
      markertap: 'onMarkerTap',
      controltap: 'onControlTap',
      callouttap: 'onCalloutTap',
      updated: 'onUpdated',
      regionchange: 'onRegionChange',
      poitap: 'onPoiTap',
    },
    cssClasses: ['lynx-map', 'map-container'],
  },

  // ===== 媒体和直播 =====
  'live-player': {
    tag: 'video',
    props: { className: 'lynx-live-player' },
    webStandard: 'Live video player',
    attributeMapping: {
      src: 'src',
      mode: 'data-mode',
      autoplay: 'autoPlay',
      muted: 'muted',
      orientation: 'data-orientation',
      'object-fit': 'data-object-fit',
      'background-mute': 'data-background-mute',
      'min-cache': 'data-min-cache',
      'max-cache': 'data-max-cache',
    },
    eventMapping: {
      statechange: 'onStateChange',
      fullscreenchange: 'onFullscreenChange',
      netstatus: 'onNetStatus',
      audiovolumenotify: 'onAudioVolumeNotify',
    },
    cssClasses: ['lynx-live-player', 'live-video'],
  },

  'live-pusher': {
    tag: 'video',
    props: { className: 'lynx-live-pusher' },
    webStandard: 'Live video pusher/broadcaster',
    attributeMapping: {
      url: 'data-url',
      mode: 'data-mode',
      autopush: 'data-autopush',
      muted: 'muted',
      'enable-camera': 'data-enable-camera',
      'auto-focus': 'data-auto-focus',
      orientation: 'data-orientation',
      beauty: 'data-beauty',
      whiteness: 'data-whiteness',
      aspect: 'data-aspect',
      'min-bitrate': 'data-min-bitrate',
      'max-bitrate': 'data-max-bitrate',
      'audio-quality': 'data-audio-quality',
      'waiting-image': 'data-waiting-image',
      'waiting-image-hash': 'data-waiting-image-hash',
      zoom: 'data-zoom',
      'device-position': 'data-device-position',
      'background-mute': 'data-background-mute',
      mirror: 'data-mirror',
      'remote-mirror': 'data-remote-mirror',
      'local-mirror': 'data-local-mirror',
    },
    eventMapping: {
      statechange: 'onStateChange',
      netstatus: 'onNetStatus',
      error: 'onError',
      bgmstart: 'onBgmStart',
      bgmprogress: 'onBgmProgress',
      bgmcomplete: 'onBgmComplete',
    },
    cssClasses: ['lynx-live-pusher', 'live-broadcaster'],
  },

  // ===== 列表和内容 =====
  list: {
    tag: 'div',
    props: { className: 'lynx-list' },
    webStandard: 'List container',
    attributeMapping: {
      'scroll-direction': 'data-scroll-direction',
      'item-count': 'data-item-count',
      'buffer-size': 'data-buffer-size',
    },
    cssClasses: ['lynx-list', 'list-container'],
  },

  'list-item': {
    tag: 'div',
    props: { className: 'lynx-list-item' },
    webStandard: 'List item element',
    attributeMapping: {
      type: 'data-type',
    },
    cssClasses: ['lynx-list-item', 'list-element'],
  },

  cell: {
    tag: 'div',
    props: { className: 'lynx-cell' },
    webStandard: 'Cell/row element',
    attributeMapping: {
      'hover-class': 'data-hover-class',
      'hover-start-time': 'data-hover-start-time',
      'hover-stay-time': 'data-hover-stay-time',
    },
    cssClasses: ['lynx-cell', 'cell-element'],
  },

  header: {
    tag: 'header',
    props: { className: 'lynx-header' },
    webStandard: 'HTML header element',
    cssClasses: ['lynx-header', 'page-header'],
  },

  footer: {
    tag: 'footer',
    props: { className: 'lynx-footer' },
    webStandard: 'HTML footer element',
    cssClasses: ['lynx-footer', 'page-footer'],
  },

  // ===== 搜索和输入增强 =====
  'search-input': {
    tag: 'input',
    props: {
      type: 'search',
      className: 'lynx-search-input',
    },
    selfClosing: true,
    webStandard: 'HTML search input',
    attributeMapping: {
      value: 'value',
      placeholder: 'placeholder',
      disabled: 'disabled',
      maxlength: 'maxLength',
      focus: 'data-focus',
      'confirm-type': 'data-confirm-type',
    },
    eventMapping: {
      input: 'onInput',
      focus: 'onFocus',
      blur: 'onBlur',
      confirm: 'onConfirm',
    },
    cssClasses: ['lynx-search-input', 'search-field'],
  },

  // ===== 特殊和系统组件 =====
  'page-meta': {
    tag: 'div',
    props: { className: 'lynx-page-meta' },
    webStandard: 'Page metadata container',
    attributeMapping: {
      'background-text-style': 'data-background-text-style',
      'background-color': 'data-background-color',
      'background-color-top': 'data-background-color-top',
      'background-color-bottom': 'data-background-color-bottom',
      'scroll-top': 'data-scroll-top',
      'scroll-duration': 'data-scroll-duration',
      'page-style': 'data-page-style',
    },
    eventMapping: {
      resize: 'onResize',
      scroll: 'onScroll',
      scrolldone: 'onScrollDone',
    },
    cssClasses: ['lynx-page-meta', 'page-metadata'],
  },

  'navigation-bar': {
    tag: 'nav',
    props: { className: 'lynx-navigation-bar' },
    webStandard: 'Navigation bar component',
    attributeMapping: {
      title: 'data-title',
      loading: 'data-loading',
      'front-color': 'data-front-color',
      'background-color': 'data-background-color',
      'color-animation-duration': 'data-color-animation-duration',
      'color-animation-timing-func': 'data-color-animation-timing-func',
    },
    cssClasses: ['lynx-navigation-bar', 'nav-bar'],
  },

  // ===== 通用容器增强 =====
  block: {
    tag: 'div',
    props: { className: 'lynx-block' },
    webStandard: 'Block-level container',
    cssClasses: ['lynx-block', 'block-container'],
  },

  inline: {
    tag: 'span',
    props: { className: 'lynx-inline' },
    webStandard: 'Inline container',
    cssClasses: ['lynx-inline', 'inline-container'],
  },

  template: {
    tag: 'div',
    props: { className: 'lynx-template' },
    webStandard: 'Template container',
    attributeMapping: {
      name: 'data-template-name',
      is: 'data-template-is',
    },
    cssClasses: ['lynx-template', 'template-container'],
  },

  import: {
    tag: 'div',
    props: { className: 'lynx-import' },
    webStandard: 'Import statement container',
    attributeMapping: {
      src: 'data-import-src',
    },
    cssClasses: ['lynx-import', 'import-container'],
  },

  include: {
    tag: 'div',
    props: { className: 'lynx-include' },
    webStandard: 'Include statement container',
    attributeMapping: {
      src: 'data-include-src',
    },
    cssClasses: ['lynx-include', 'include-container'],
  },

  // ===== 默认和未知元素 =====
  '*': {
    tag: 'div',
    props: { className: 'lynx-unknown' },
    webStandard: 'Fallback container for unknown elements',
    attributeMapping: {
      class: 'className',
      style: 'style',
      id: 'id',
      title: 'title',
    },
    cssClasses: ['lynx-unknown', 'fallback-container'],
  },
};

// ===============================
// 通用属性映射增强
// ===============================

export const UNIVERSAL_ATTRIBUTE_MAPPING: Record<
  string,
  string | ((value: string, element?: string) => any)
> = {
  // 标准HTML属性
  id: 'id',
  class: 'className',
  style: 'style',
  title: 'title',
  lang: 'lang',
  dir: 'dir',
  hidden: 'hidden',
  tabindex: 'tabIndex',
  accesskey: 'accessKey',
  contenteditable: 'contentEditable',
  draggable: 'draggable',
  spellcheck: 'spellCheck',
  translate: 'translate',

  // ARIA属性
  role: 'role',
  'aria-label': 'aria-label',
  'aria-labelledby': 'aria-labelledby',
  'aria-describedby': 'aria-describedby',
  'aria-hidden': 'aria-hidden',
  'aria-expanded': 'aria-expanded',
  'aria-selected': 'aria-selected',
  'aria-checked': 'aria-checked',
  'aria-disabled': 'aria-disabled',
  'aria-required': 'aria-required',
  'aria-invalid': 'aria-invalid',
  'aria-live': 'aria-live',
  'aria-atomic': 'aria-atomic',
  'aria-busy': 'aria-busy',
  'aria-controls': 'aria-controls',
  'aria-owns': 'aria-owns',
  'aria-flowto': 'aria-flowto',
  'aria-haspopup': 'aria-haspopup',
  'aria-level': 'aria-level',
  'aria-multiline': 'aria-multiline',
  'aria-multiselectable': 'aria-multiselectable',
  'aria-orientation': 'aria-orientation',
  'aria-readonly': 'aria-readonly',
  'aria-sort': 'aria-sort',
  'aria-valuemax': 'aria-valuemax',
  'aria-valuemin': 'aria-valuemin',
  'aria-valuenow': 'aria-valuenow',
  'aria-valuetext': 'aria-valuetext',

  // Data属性 (通用模式)
  'data-*': (value: string, attrName: string) => ({ [attrName]: value }),

  // Lynx特有的通用属性
  'hover-class': 'data-hover-class',
  'hover-start-time': 'data-hover-start-time',
  'hover-stay-time': 'data-hover-stay-time',
  animation: 'data-animation',
  bindtap: 'onClick',
  bindtouchstart: 'onTouchStart',
  bindtouchmove: 'onTouchMove',
  bindtouchcancel: 'onTouchCancel',
  bindtouchend: 'onTouchEnd',
  bindlongpress: 'onContextMenu',
  bindlongtap: 'onContextMenu',
  bindtransitionend: 'onTransitionEnd',
  bindanimationstart: 'onAnimationStart',
  bindanimationiteration: 'onAnimationIteration',
  bindanimationend: 'onAnimationEnd',
  bindtouchforcechange: 'onTouchStart',

  // 表单相关通用属性
  bindinput: 'onInput',
  bindchange: 'onChange',
  bindfocus: 'onFocus',
  bindblur: 'onBlur',
  bindsubmit: 'onSubmit',
  bindreset: 'onReset',

  // 媒体相关通用属性
  bindload: 'onLoad',
  binderror: 'onError',
  bindplay: 'onPlay',
  bindpause: 'onPause',
  bindended: 'onEnded',
  bindtimeupdate: 'onTimeUpdate',

  // 滚动相关通用属性
  bindscroll: 'onScroll',
  bindscrolltoupper: (value: string) => ({
    'data-scrolltoupper': value,
    onScroll: '(e) => { /* Handle scroll to upper */ }',
  }),
  bindscrolltolower: (value: string) => ({
    'data-scrolltolower': value,
    onScroll: '(e) => { /* Handle scroll to lower */ }',
  }),

  // 可见性相关属性
  bindappear: (value: string) => ({
    'data-appear': value,
    onIntersect: '(e) => { /* Handle appear */ }',
  }),
  binddisappear: (value: string) => ({
    'data-disappear': value,
    onIntersect: '(e) => { /* Handle disappear */ }',
  }),

  // 手势相关属性
  'catch-move': 'data-catch-move',
  'disable-scroll': 'data-disable-scroll',

  // 尺寸和位置属性
  width: (value: string) => ({ style: { width: value } }),
  height: (value: string) => ({ style: { height: value } }),
  'min-width': (value: string) => ({ style: { minWidth: value } }),
  'min-height': (value: string) => ({ style: { minHeight: value } }),
  'max-width': (value: string) => ({ style: { maxWidth: value } }),
  'max-height': (value: string) => ({ style: { maxHeight: value } }),
};

// ===============================
// TTSS样式映射增强
// ===============================

export const COMPREHENSIVE_STYLE_MAPPING: Record<
  string,
  string | ((value: string) => any)
> = {
  // 布局相关
  display: 'display',
  position: 'position',
  top: 'top',
  right: 'right',
  bottom: 'bottom',
  left: 'left',
  'z-index': 'zIndex',
  float: 'float',
  clear: 'clear',
  overflow: 'overflow',
  'overflow-x': 'overflowX',
  'overflow-y': 'overflowY',
  visibility: 'visibility',
  opacity: 'opacity',

  // Flexbox
  flex: 'flex',
  'flex-direction': 'flexDirection',
  'flex-wrap': 'flexWrap',
  'flex-flow': 'flexFlow',
  'justify-content': 'justifyContent',
  'align-items': 'alignItems',
  'align-content': 'alignContent',
  'align-self': 'alignSelf',
  'flex-grow': 'flexGrow',
  'flex-shrink': 'flexShrink',
  'flex-basis': 'flexBasis',
  order: 'order',

  // Grid
  grid: 'grid',
  'grid-area': 'gridArea',
  'grid-auto-columns': 'gridAutoColumns',
  'grid-auto-flow': 'gridAutoFlow',
  'grid-auto-rows': 'gridAutoRows',
  'grid-column': 'gridColumn',
  'grid-column-end': 'gridColumnEnd',
  'grid-column-gap': 'gridColumnGap',
  'grid-column-start': 'gridColumnStart',
  'grid-gap': 'gridGap',
  'grid-row': 'gridRow',
  'grid-row-end': 'gridRowEnd',
  'grid-row-gap': 'gridRowGap',
  'grid-row-start': 'gridRowStart',
  'grid-template': 'gridTemplate',
  'grid-template-areas': 'gridTemplateAreas',
  'grid-template-columns': 'gridTemplateColumns',
  'grid-template-rows': 'gridTemplateRows',

  // 尺寸
  width: 'width',
  height: 'height',
  'min-width': 'minWidth',
  'min-height': 'minHeight',
  'max-width': 'maxWidth',
  'max-height': 'maxHeight',

  // 内外边距
  margin: 'margin',
  'margin-top': 'marginTop',
  'margin-right': 'marginRight',
  'margin-bottom': 'marginBottom',
  'margin-left': 'marginLeft',
  padding: 'padding',
  'padding-top': 'paddingTop',
  'padding-right': 'paddingRight',
  'padding-bottom': 'paddingBottom',
  'padding-left': 'paddingLeft',

  // 边框
  border: 'border',
  'border-width': 'borderWidth',
  'border-style': 'borderStyle',
  'border-color': 'borderColor',
  'border-top': 'borderTop',
  'border-top-width': 'borderTopWidth',
  'border-top-style': 'borderTopStyle',
  'border-top-color': 'borderTopColor',
  'border-right': 'borderRight',
  'border-right-width': 'borderRightWidth',
  'border-right-style': 'borderRightStyle',
  'border-right-color': 'borderRightColor',
  'border-bottom': 'borderBottom',
  'border-bottom-width': 'borderBottomWidth',
  'border-bottom-style': 'borderBottomStyle',
  'border-bottom-color': 'borderBottomColor',
  'border-left': 'borderLeft',
  'border-left-width': 'borderLeftWidth',
  'border-left-style': 'borderLeftStyle',
  'border-left-color': 'borderLeftColor',
  'border-radius': 'borderRadius',
  'border-top-left-radius': 'borderTopLeftRadius',
  'border-top-right-radius': 'borderTopRightRadius',
  'border-bottom-right-radius': 'borderBottomRightRadius',
  'border-bottom-left-radius': 'borderBottomLeftRadius',

  // 背景
  background: 'background',
  'background-color': 'backgroundColor',
  'background-image': 'backgroundImage',
  'background-repeat': 'backgroundRepeat',
  'background-position': 'backgroundPosition',
  'background-size': 'backgroundSize',
  'background-attachment': 'backgroundAttachment',
  'background-origin': 'backgroundOrigin',
  'background-clip': 'backgroundClip',

  // 文本
  color: 'color',
  font: 'font',
  'font-family': 'fontFamily',
  'font-size': 'fontSize',
  'font-weight': 'fontWeight',
  'font-style': 'fontStyle',
  'font-variant': 'fontVariant',
  'line-height': 'lineHeight',
  'letter-spacing': 'letterSpacing',
  'word-spacing': 'wordSpacing',
  'text-align': 'textAlign',
  'text-decoration': 'textDecoration',
  'text-indent': 'textIndent',
  'text-transform': 'textTransform',
  'text-shadow': 'textShadow',
  'white-space': 'whiteSpace',
  'word-wrap': 'wordWrap',
  'word-break': 'wordBreak',
  'overflow-wrap': 'overflowWrap',
  'text-overflow': 'textOverflow',
  'vertical-align': 'verticalAlign',

  // 阴影和效果
  'box-shadow': 'boxShadow',
  filter: 'filter',
  'backdrop-filter': 'backdropFilter',

  // 变换
  transform: 'transform',
  'transform-origin': 'transformOrigin',
  'transform-style': 'transformStyle',
  perspective: 'perspective',
  'perspective-origin': 'perspectiveOrigin',
  'backface-visibility': 'backfaceVisibility',

  // 过渡和动画
  transition: 'transition',
  'transition-property': 'transitionProperty',
  'transition-duration': 'transitionDuration',
  'transition-timing-function': 'transitionTimingFunction',
  'transition-delay': 'transitionDelay',
  animation: 'animation',
  'animation-name': 'animationName',
  'animation-duration': 'animationDuration',
  'animation-timing-function': 'animationTimingFunction',
  'animation-delay': 'animationDelay',
  'animation-iteration-count': 'animationIterationCount',
  'animation-direction': 'animationDirection',
  'animation-fill-mode': 'animationFillMode',
  'animation-play-state': 'animationPlayState',

  // 交互
  cursor: 'cursor',
  'pointer-events': 'pointerEvents',
  'user-select': 'userSelect',
  'touch-action': 'touchAction',

  // 表格
  'table-layout': 'tableLayout',
  'border-collapse': 'borderCollapse',
  'border-spacing': 'borderSpacing',
  'caption-side': 'captionSide',
  'empty-cells': 'emptyCells',

  // 列表
  'list-style': 'listStyle',
  'list-style-type': 'listStyleType',
  'list-style-position': 'listStylePosition',
  'list-style-image': 'listStyleImage',

  // 轮廓
  outline: 'outline',
  'outline-color': 'outlineColor',
  'outline-style': 'outlineStyle',
  'outline-width': 'outlineWidth',
  'outline-offset': 'outlineOffset',

  // 滚动
  'scroll-behavior': 'scrollBehavior',
  'scroll-snap-type': 'scrollSnapType',
  'scroll-snap-align': 'scrollSnapAlign',
  'scrollbar-width': 'scrollbarWidth',
  'scrollbar-color': 'scrollbarColor',

  // CSS自定义属性
  '--*': (value: string, propName: string) => ({ [propName]: value }),

  // Lynx特有样式处理
  rpx: (value: string) => {
    // RPX单位转换为vw
    const rpxMatch = value.match(/(\d+(?:\.\d+)?)rpx/g);
    if (rpxMatch) {
      return rpxMatch.reduce((acc, match) => {
        const rpxValue = parseFloat(match);
        const vwValue = ((rpxValue / 750) * 100).toFixed(6);
        return acc.replace(match, `${vwValue}vw`);
      }, value);
    }
    return value;
  },
};

// ===============================
// 导出所有映射配置
// ===============================

// ===============================
// 增强的事件映射 - 基于 web-speedy-plugin
// ===============================

export const ENHANCED_EVENT_MAPPING: Record<
  string,
  {
    reactEvent: string;
    handler: (eventExpr: string) => string;
    stopPropagation?: boolean;
    preventDefault?: boolean;
  }
> = {
  // Touch 事件
  bindtouchstart: {
    reactEvent: 'onTouchStart',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindtouchmove: {
    reactEvent: 'onTouchMove',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindtouchend: {
    reactEvent: 'onTouchEnd',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindtouchcancel: {
    reactEvent: 'onTouchCancel',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },

  // 滚动事件 - 特殊处理，模拟官方行为
  bindscroll: {
    reactEvent: 'onScroll',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindscrolltoupper: {
    reactEvent: 'onScroll',
    handler: (eventExpr: string) => `(e) => { 
      const { scrollTop, scrollLeft } = e.target;
      const threshold = 50;
      if (scrollTop <= threshold || scrollLeft <= threshold) {
        const handler = ${eventExpr};
        if (handler) handler(e);
      }
    }`,
  },
  bindscrolltolower: {
    reactEvent: 'onScroll',
    handler: (eventExpr: string) => `(e) => {
      const { scrollTop, scrollLeft, scrollHeight, scrollWidth, clientHeight, clientWidth } = e.target;
      const threshold = 50;
      if (scrollTop + clientHeight >= scrollHeight - threshold || 
          scrollLeft + clientWidth >= scrollWidth - threshold) {
        const handler = ${eventExpr};
        if (handler) handler(e);
      }
    }`,
  },

  // 表单事件 - 模拟官方的事件格式
  bindinput: {
    reactEvent: 'onInput',
    handler: (eventExpr: string) => `(e) => { 
      const handler = ${eventExpr}; 
      if (handler) handler({ detail: { value: e.target.value }, target: e.target, currentTarget: e.currentTarget }); 
    }`,
  },
  bindchange: {
    reactEvent: 'onChange',
    handler: (eventExpr: string) => `(e) => { 
      const handler = ${eventExpr}; 
      if (handler) handler({ detail: { value: e.target.value }, target: e.target, currentTarget: e.currentTarget }); 
    }`,
  },
  bindfocus: {
    reactEvent: 'onFocus',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindblur: {
    reactEvent: 'onBlur',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },

  // 点击事件
  bindtap: {
    reactEvent: 'onClick',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindlongpress: {
    reactEvent: 'onContextMenu',
    preventDefault: true,
    handler: (eventExpr: string) => `(e) => { 
      e.preventDefault(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },
  bindlongtap: {
    reactEvent: 'onContextMenu',
    preventDefault: true,
    handler: (eventExpr: string) => `(e) => { 
      e.preventDefault(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },

  // 媒体事件
  bindload: {
    reactEvent: 'onLoad',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  binderror: {
    reactEvent: 'onError',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindplay: {
    reactEvent: 'onPlay',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindpause: {
    reactEvent: 'onPause',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindended: {
    reactEvent: 'onEnded',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },
  bindtimeupdate: {
    reactEvent: 'onTimeUpdate',
    handler: (eventExpr: string) =>
      `(e) => { const handler = ${eventExpr}; if (handler) handler(e); }`,
  },

  // Catch 事件 (阻止冒泡) - 模拟官方的 catch 行为
  catchtouchstart: {
    reactEvent: 'onTouchStart',
    stopPropagation: true,
    handler: (eventExpr: string) => `(e) => { 
      e.stopPropagation(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },
  catchtouchmove: {
    reactEvent: 'onTouchMove',
    stopPropagation: true,
    handler: (eventExpr: string) => `(e) => { 
      e.stopPropagation(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },
  catchtouchend: {
    reactEvent: 'onTouchEnd',
    stopPropagation: true,
    handler: (eventExpr: string) => `(e) => { 
      e.stopPropagation(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },
  catchtap: {
    reactEvent: 'onClick',
    stopPropagation: true,
    handler: (eventExpr: string) => `(e) => { 
      e.stopPropagation(); 
      const handler = ${eventExpr}; 
      if (handler) handler(e); 
    }`,
  },
};

// ===============================
// RPX 转换配置 - 模拟官方配置
// ===============================

export interface RPXConfig {
  designWidth: number;
  rpxMode: 'vw' | 'rem' | 'px';
}

export const DEFAULT_RPX_CONFIG: RPXConfig = {
  designWidth: 750,
  rpxMode: 'vw',
};

// ===============================
// 作用域管理器 - 模拟 tagV 系统
// ===============================

export class ComponentScopeManager {
  private static instance: ComponentScopeManager;
  private componentScopes = new Map<string, string>();

  public static getInstance(): ComponentScopeManager {
    if (!ComponentScopeManager.instance) {
      ComponentScopeManager.instance = new ComponentScopeManager();
    }
    return ComponentScopeManager.instance;
  }

  public getComponentScope(componentId: string, content: string): string {
    const key = `${componentId}:${this.generateContentHash(content)}`;

    if (!this.componentScopes.has(key)) {
      const hash = this.generateHash(key);
      this.componentScopes.set(key, hash.slice(0, 5));
    }

    return this.componentScopes.get(key)!;
  }

  private generateContentHash(content: string): string {
    return content.length.toString(36) + content.slice(-10);
  }

  private generateHash(input: string): string {
    let hash = 0;
    for (let i = 0; i < input.length; i++) {
      const char = input.charCodeAt(i);
      hash = (hash << 5) - hash + char;
      hash = hash & hash;
    }
    return Math.abs(hash).toString(36);
  }
}

export {
  COMPREHENSIVE_LYNX_MAPPING as LYNX_ELEMENT_MAPPING,
  UNIVERSAL_ATTRIBUTE_MAPPING as ATTRIBUTE_MAPPING,
  COMPREHENSIVE_STYLE_MAPPING as STYLE_MAPPING,
};

export default COMPREHENSIVE_LYNX_MAPPING;
