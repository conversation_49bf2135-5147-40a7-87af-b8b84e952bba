/**
 * @package runtime-convert-parse5
 * @description 特殊事件处理器 - 基于pePromptLoader.ts规则实现完整的Lynx事件转换
 * 实现滚动、触摸、捕获等复杂事件的完整转换逻辑
 */

import type { EventMapping } from './index';

/**
 * 处理后的事件接口
 */
export interface ProcessedEvent {
  name: string;
  handler: string;
  implementation: string;
  stopPropagation?: boolean;
  special?: string;
}

/**
 * 处理后的事件集合
 */
export interface ProcessedEvents {
  [eventName: string]: string;
}

/**
 * 滚动事件处理器
 * 处理Lynx中的复杂滚动事件，包括滚动到顶部、底部等特殊逻辑
 */
export class ScrollEventProcessor {
  /**
   * 处理滚动相关的复杂事件绑定
   * 将Lynx的bindscrolltoupper/bindscrolltolower转换为React的onScroll
   */
  processScrollEvents(
    element: any,
    events: Record<string, string>,
  ): ProcessedEvents {
    const scrollEvents: ProcessedEvent[] = [];

    // 基础滚动事件
    if (events['bindscroll']) {
      scrollEvents.push({
        name: 'onScroll',
        handler: events['bindscroll'],
        implementation: `onScroll={${events['bindscroll']}}`,
      });
    }

    // 滚动到顶部事件
    if (events['bindscrolltoupper']) {
      const threshold = element.attributes?.['upper-threshold'] || '50';
      scrollEvents.push({
        name: 'onScroll',
        handler: events['bindscrolltoupper'],
        implementation: `onScroll={(e) => {
          const { scrollTop } = e.target;
          if (scrollTop <= ${threshold}) {
            (${events['bindscrolltoupper']})(e);
          }
        }}`,
      });
    }

    // 滚动到底部事件
    if (events['bindscrolltolower']) {
      const threshold = element.attributes?.['lower-threshold'] || '50';
      scrollEvents.push({
        name: 'onScroll',
        handler: events['bindscrolltolower'],
        implementation: `onScroll={(e) => {
          const { scrollTop, scrollHeight, clientHeight } = e.target;
          if (scrollTop + clientHeight >= scrollHeight - ${threshold}) {
            (${events['bindscrolltolower']})(e);
          }
        }}`,
      });
    }

    // 滚动开始事件
    if (events['bindscrollstart']) {
      scrollEvents.push({
        name: 'onScrollStart',
        handler: events['bindscrollstart'],
        implementation: `onScroll={(e) => {
          // 滚动开始检测逻辑
          if (!e.target.scrolling) {
            e.target.scrolling = true;
            (${events['bindscrollstart']})(e);
          }
        }}`,
      });
    }

    // 滚动结束事件
    if (events['bindscrollend']) {
      scrollEvents.push({
        name: 'onScrollEnd',
        handler: events['bindscrollend'],
        implementation: `onScroll={(e) => {
          // 滚动结束检测逻辑（需要debounce）
          clearTimeout(e.target.scrollEndTimer);
          e.target.scrollEndTimer = setTimeout(() => {
            e.target.scrolling = false;
            (${events['bindscrollend']})(e);
          }, 150);
        }}`,
      });
    }

    // 合并多个滚动事件到单一处理器
    return this.mergeScrollEventHandlers(scrollEvents);
  }

  /**
   * 合并多个滚动事件处理器为单一的onScroll事件
   */
  private mergeScrollEventHandlers(events: ProcessedEvent[]): ProcessedEvents {
    if (events.length === 0) {
      return {};
    }

    if (events.length === 1) {
      return { [events[0].name]: events[0].implementation };
    }

    // 合并多个滚动事件处理逻辑
    const handlerLogic = events
      .map(event => {
        // 提取处理逻辑，去掉外层的onScroll包装
        const logic = event.implementation
          .replace(/onScroll=\{|\}$/g, '')
          .replace(/^\(e\) => \{|\}$/g, '');
        return logic.trim();
      })
      .join('\n      ');

    const combinedHandler = `onScroll={(e) => {
      ${handlerLogic}
    }}`;

    return { onScroll: combinedHandler };
  }
}

/**
 * 触摸事件处理器
 * 处理长按、捕获等复杂触摸事件
 */
export class TouchEventProcessor {
  /**
   * 实现长按事件模拟
   * 将Lynx的bindlongpress转换为React的触摸事件组合
   */
  processLongPressEvent(handler: string, threshold: number = 350): string {
    return `
      onTouchStart={(e) => {
        const timer = setTimeout(() => {
          (${handler})(e);
        }, ${threshold}); // ${threshold}ms长按阈值
        
        const clearTimer = () => {
          clearTimeout(timer);
          e.target.removeEventListener('touchend', clearTimer);
          e.target.removeEventListener('touchcancel', clearTimer);
        };
        
        e.target.addEventListener('touchend', clearTimer, { once: true });
        e.target.addEventListener('touchcancel', clearTimer, { once: true });
      }}
    `;
  }

  /**
   * 处理capture事件绑定
   * 将capture-bind/capture-catch转换为React的捕获事件
   */
  processCaptureEvent(
    eventType: string,
    handler: string,
    shouldStop: boolean,
  ): string {
    const reactEvent = this.mapToReactCaptureEvent(eventType);
    const stopPropagation = shouldStop ? 'e.stopPropagation(); ' : '';

    return `${reactEvent}={(e) => { ${stopPropagation}(${handler})(e); }}`;
  }

  /**
   * 映射Lynx事件到React捕获事件
   */
  private mapToReactCaptureEvent(lynxEvent: string): string {
    const mapping: Record<string, string> = {
      tap: 'onClickCapture',
      touchstart: 'onTouchStartCapture',
      touchmove: 'onTouchMoveCapture',
      touchend: 'onTouchEndCapture',
      touchcancel: 'onTouchCancelCapture',
      longpress: 'onContextMenuCapture',
    };
    return mapping[lynxEvent] || 'onClickCapture';
  }

  /**
   * 处理触摸力度变化事件
   * 将bindtouchforcechange转换为React触摸事件
   */
  processTouchForceChangeEvent(handler: string): string {
    return `
      onTouchStart={(e) => {
        // 模拟力度变化检测
        if (e.touches[0].force !== undefined) {
          const lastForce = e.target.lastForce || 0;
          if (Math.abs(e.touches[0].force - lastForce) > 0.1) {
            e.target.lastForce = e.touches[0].force;
            (${handler})(e);
          }
        }
      }}
    `;
  }
}

/**
 * 表单事件处理器
 * 处理表单相关的特殊事件，如确认键、键盘高度变化等
 */
export class FormEventProcessor {
  /**
   * 处理确认键事件
   * 将bindconfirm转换为React的onKeyDown事件
   */
  processConfirmEvent(handler: string): string {
    return `
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.keyCode === 13) {
          (${handler})(e);
        }
      }}
    `;
  }

  /**
   * 处理键盘高度变化事件
   * 在Web环境中模拟键盘高度变化
   */
  processKeyboardHeightChangeEvent(handler: string): string {
    return `
      onFocus={(e) => {
        // 模拟键盘弹起
        const keyboardHeight = window.innerHeight * 0.4; // 假设键盘占40%高度
        const event = { ...e, keyboardHeight, show: true };
        (${handler})(event);
      }}
      onBlur={(e) => {
        // 模拟键盘收起
        const event = { ...e, keyboardHeight: 0, show: false };
        (${handler})(event);
      }}
    `;
  }

  /**
   * 处理实时输入事件
   * 将bindinput转换为React的onChange事件
   */
  processInputEvent(handler: string): string {
    return `
      onChange={(e) => {
        // 实时输入事件，兼容Lynx的事件对象格式
        const lynxEvent = {
          ...e,
          detail: {
            value: e.target.value,
            cursor: e.target.selectionStart
          }
        };
        (${handler})(lynxEvent);
      }}
    `;
  }
}

/**
 * 媒体事件处理器
 * 处理音视频相关的事件
 */
export class MediaEventProcessor {
  /**
   * 处理媒体加载事件
   * 增强bindload事件，提供更多媒体信息
   */
  processLoadEvent(handler: string): string {
    return `
      onLoad={(e) => {
        // 增强媒体加载事件信息
        const mediaEvent = {
          ...e,
          detail: {
            width: e.target.videoWidth || e.target.naturalWidth,
            height: e.target.videoHeight || e.target.naturalHeight,
            duration: e.target.duration
          }
        };
        (${handler})(mediaEvent);
      }}
    `;
  }

  /**
   * 处理媒体错误事件
   * 增强binderror事件，提供详细错误信息
   */
  processErrorEvent(handler: string): string {
    return `
      onError={(e) => {
        // 增强媒体错误事件信息
        const errorEvent = {
          ...e,
          detail: {
            errMsg: e.target.error?.message || 'Media load error',
            errCode: e.target.error?.code || -1
          }
        };
        (${handler})(errorEvent);
      }}
    `;
  }

  /**
   * 处理时间更新事件
   * 将bindtimeupdate转换为React的onTimeUpdate事件
   */
  processTimeUpdateEvent(handler: string): string {
    return `
      onTimeUpdate={(e) => {
        // 兼容Lynx的时间更新事件格式
        const timeEvent = {
          ...e,
          detail: {
            currentTime: e.target.currentTime,
            duration: e.target.duration
          }
        };
        (${handler})(timeEvent);
      }}
    `;
  }
}

/**
 * 曝光事件处理器
 * 处理元素可见性相关的事件
 */
export class VisibilityEventProcessor {
  /**
   * 处理元素出现事件
   * 使用IntersectionObserver实现bindappear
   */
  processAppearEvent(handler: string): string {
    return `
      ref={(element) => {
        if (element) {
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                const appearEvent = {
                  target: entry.target,
                  detail: {
                    boundingClientRect: entry.boundingClientRect,
                    intersectionRatio: entry.intersectionRatio
                  }
                };
                (${handler})(appearEvent);
              }
            });
          }, { threshold: 0.1 });
          observer.observe(element);
        }
      }}
    `;
  }

  /**
   * 处理元素消失事件
   * 使用IntersectionObserver实现binddisappear
   */
  processDisappearEvent(handler: string): string {
    return `
      ref={(element) => {
        if (element) {
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (!entry.isIntersecting) {
                const disappearEvent = {
                  target: entry.target,
                  detail: {
                    boundingClientRect: entry.boundingClientRect,
                    intersectionRatio: entry.intersectionRatio
                  }
                };
                (${handler})(disappearEvent);
              }
            });
          }, { threshold: 0.1 });
          observer.observe(element);
        }
      }}
    `;
  }
}

/**
 * 统一事件处理器管理器
 * 协调所有特殊事件处理器的工作
 */
export class EventProcessorManager {
  private scrollProcessor = new ScrollEventProcessor();
  private touchProcessor = new TouchEventProcessor();
  private formProcessor = new FormEventProcessor();
  private mediaProcessor = new MediaEventProcessor();
  private visibilityProcessor = new VisibilityEventProcessor();

  /**
   * 处理元素的所有事件绑定
   */
  processElementEvents(
    element: any,
    events: Record<string, string>,
  ): ProcessedEvents {
    const processedEvents: ProcessedEvents = {};

    // 处理滚动事件
    const scrollEvents = this.scrollProcessor.processScrollEvents(
      element,
      events,
    );
    Object.assign(processedEvents, scrollEvents);

    // 处理其他特殊事件
    Object.entries(events).forEach(([eventName, handler]) => {
      switch (eventName) {
        case 'bindlongpress':
        case 'catchlongpress':
          processedEvents.onTouchStart =
            this.touchProcessor.processLongPressEvent(handler);
          break;

        case 'bindconfirm':
        case 'catchconfirm':
          processedEvents.onKeyDown =
            this.formProcessor.processConfirmEvent(handler);
          break;

        case 'bindkeyboardheightchange':
        case 'catchkeyboardheightchange':
          const keyboardEvents =
            this.formProcessor.processKeyboardHeightChangeEvent(handler);
          Object.assign(
            processedEvents,
            this.parseMultipleEvents(keyboardEvents),
          );
          break;

        case 'bindinput':
        case 'catchinput':
          processedEvents.onChange =
            this.formProcessor.processInputEvent(handler);
          break;

        case 'bindload':
        case 'catchload':
          processedEvents.onLoad =
            this.mediaProcessor.processLoadEvent(handler);
          break;

        case 'binderror':
        case 'catcherror':
          processedEvents.onError =
            this.mediaProcessor.processErrorEvent(handler);
          break;

        case 'bindtimeupdate':
        case 'catchtimeupdate':
          processedEvents.onTimeUpdate =
            this.mediaProcessor.processTimeUpdateEvent(handler);
          break;

        case 'bindappear':
        case 'catchappear':
          processedEvents.ref =
            this.visibilityProcessor.processAppearEvent(handler);
          break;

        case 'binddisappear':
        case 'catchdisappear':
          processedEvents.ref =
            this.visibilityProcessor.processDisappearEvent(handler);
          break;

        case 'bindtouchforcechange':
        case 'catchtouchforcechange':
          processedEvents.onTouchStart =
            this.touchProcessor.processTouchForceChangeEvent(handler);
          break;
      }

      // 处理capture事件
      if (
        eventName.startsWith('capture-bind') ||
        eventName.startsWith('capture-catch:')
      ) {
        const isCapture = eventName.startsWith('capture-catch:');
        const baseEvent = eventName.split(':')[1];
        const captureEvent = this.touchProcessor.processCaptureEvent(
          baseEvent,
          handler,
          isCapture,
        );
        Object.assign(processedEvents, this.parseMultipleEvents(captureEvent));
      }
    });

    return processedEvents;
  }

  /**
   * 解析包含多个事件的字符串
   */
  private parseMultipleEvents(eventString: string): ProcessedEvents {
    const events: ProcessedEvents = {};

    // 简单的正则解析多个事件定义
    const eventMatches = eventString.match(/(on\w+)=\{[^}]+\}/g);
    if (eventMatches) {
      eventMatches.forEach(match => {
        const [eventName, eventHandler] = match.split('=');
        events[eventName] = eventHandler;
      });
    }

    return events;
  }
}

/**
 * 全局事件处理器实例
 */
export const eventProcessorManager = new EventProcessorManager();
