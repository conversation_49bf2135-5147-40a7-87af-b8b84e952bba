/**
 * @package runtime-convert-parse5
 * @description TTML/TTSS映射规则定义
 * 基于文档中提取的@byted-lynx/web-speedy-plugin映射规则
 */

/**
 * 元素映射配置
 */
export interface ElementMapping {
  tag: string; // 目标HTML标签
  props?: Record<string, any>; // 默认属性
  selfClosing?: boolean; // 是否自闭合
  attributeMapping?: Record<string, string>; // 属性映射
}

/**
 * 指令映射配置
 */
export interface DirectiveMapping {
  type: 'conditional' | 'iteration' | 'optimization' | 'event';
  transform: (value: string, node: any, context: any) => any;
}

/**
 * 事件映射配置
 */
export interface EventMapping {
  reactEvent: string; // React事件名
  stopPropagation: boolean; // 是否阻止事件传播
  special?: string; // 特殊事件类型 (upper/lower/appear/disappear)
}

// ===============================
// TTML元素映射表 - 增强版映射规则
// ===============================

export const TTML_ELEMENT_MAPPING: Record<string, ElementMapping> = {
  // 布局容器 - 增强映射
  view: {
    tag: 'div',
    props: { className: 'lynx-view' },
    attributeMapping: {
      class: 'className',
      style: 'style',
      'hover-class': 'data-hover-class',
      'hover-start-time': 'data-hover-start-time',
      'hover-stay-time': 'data-hover-stay-time',
    },
  },
  'scroll-view': {
    tag: 'div',
    props: {
      className: 'lynx-scroll-view',
      style: { overflow: 'auto' },
    },
    attributeMapping: {
      'scroll-x': 'data-scroll-x',
      'scroll-y': 'data-scroll-y',
      'upper-threshold': 'data-upper-threshold',
      'lower-threshold': 'data-lower-threshold',
      'scroll-into-view': 'data-scroll-into-view',
      'scroll-with-animation': 'data-scroll-with-animation',
      'enable-back-to-top': 'data-enable-back-to-top',
    },
  },

  // 文本相关 - 增强映射
  text: {
    tag: 'span',
    props: { className: 'lynx-text' },
    attributeMapping: {
      selectable: 'data-selectable',
      'user-select': 'style',
      decode: 'data-decode',
    },
  },
  'rich-text': {
    tag: 'div',
    props: { className: 'lynx-rich-text' },
  },

  // 媒体元素
  image: {
    tag: 'img',
    props: { className: 'lynx-image' },
    selfClosing: true,
    attributeMapping: {
      src: 'src',
      mode: 'objectFit',
      'lazy-load': 'loading',
      'fade-in': 'data-fade-in',
      webp: 'data-webp',
      'show-menu-by-longpress': 'data-show-menu',
    },
  },
  video: {
    tag: 'video',
    props: {
      className: 'lynx-video',
      controls: true,
    },
    attributeMapping: {
      src: 'src',
      poster: 'poster',
      autoplay: 'autoPlay',
      loop: 'loop',
      muted: 'muted',
    },
  },
  audio: {
    tag: 'audio',
    props: {
      className: 'lynx-audio',
      controls: true,
    },
    attributeMapping: {
      src: 'src',
      autoplay: 'autoPlay',
      loop: 'loop',
    },
  },

  // 表单元素 - 增强映射
  input: {
    tag: 'input',
    props: { className: 'lynx-input' },
    selfClosing: true,
    attributeMapping: {
      type: 'type',
      placeholder: 'placeholder',
      value: 'value',
      defaultValue: 'defaultValue',
      maxlength: 'maxLength',
      disabled: 'disabled',
      password: 'type',
      'placeholder-style': 'data-placeholder-style',
      'placeholder-class': 'data-placeholder-class',
      'cursor-spacing': 'data-cursor-spacing',
      'auto-focus': 'autoFocus',
      focus: 'data-focus',
      'confirm-type': 'data-confirm-type',
      'confirm-hold': 'data-confirm-hold',
      cursor: 'data-cursor',
      'selection-start': 'data-selection-start',
      'selection-end': 'data-selection-end',
      'adjust-position': 'data-adjust-position',
      'hold-keyboard': 'data-hold-keyboard',
    },
  },
  textarea: {
    tag: 'textarea',
    props: { className: 'lynx-textarea' },
    attributeMapping: {
      placeholder: 'placeholder',
      maxlength: 'maxLength',
      disabled: 'disabled',
      'auto-height': 'data-auto-height',
    },
  },
  button: {
    tag: 'button',
    props: { className: 'lynx-button' },
    attributeMapping: {
      type: 'type',
      disabled: 'disabled',
      'form-type': 'type',
      'open-type': 'data-open-type',
    },
  },
  switch: {
    tag: 'input',
    props: {
      type: 'checkbox',
      className: 'lynx-switch',
    },
    selfClosing: true,
    attributeMapping: {
      checked: 'checked',
      disabled: 'disabled',
      color: 'data-color',
    },
  },
  slider: {
    tag: 'input',
    props: {
      type: 'range',
      className: 'lynx-slider',
    },
    selfClosing: true,
    attributeMapping: {
      min: 'min',
      max: 'max',
      step: 'step',
      value: 'value',
      disabled: 'disabled',
    },
  },
  picker: {
    tag: 'select',
    props: { className: 'lynx-picker' },
    attributeMapping: {
      value: 'value',
      disabled: 'disabled',
      range: 'data-range',
    },
  },
  checkbox: {
    tag: 'input',
    props: {
      type: 'checkbox',
      className: 'lynx-checkbox',
    },
    selfClosing: true,
    attributeMapping: {
      value: 'value',
      checked: 'checked',
      disabled: 'disabled',
      color: 'data-color',
    },
  },
  'checkbox-group': {
    tag: 'div',
    props: { className: 'lynx-checkbox-group' },
  },
  radio: {
    tag: 'input',
    props: {
      type: 'radio',
      className: 'lynx-radio',
    },
    selfClosing: true,
    attributeMapping: {
      value: 'value',
      checked: 'checked',
      disabled: 'disabled',
      color: 'data-color',
    },
  },
  'radio-group': {
    tag: 'div',
    props: { className: 'lynx-radio-group' },
  },

  // 导航相关
  navigator: {
    tag: 'a',
    props: { className: 'lynx-navigator' },
    attributeMapping: {
      url: 'href',
      'open-type': 'target',
      'hover-class': 'data-hover-class',
    },
  },
  link: {
    tag: 'a',
    props: { className: 'lynx-link' },
    attributeMapping: {
      href: 'href',
      target: 'target',
    },
  },

  // 高级组件
  swiper: {
    tag: 'div',
    props: { className: 'lynx-swiper' },
    attributeMapping: {
      'indicator-dots': 'data-indicator-dots',
      'indicator-color': 'data-indicator-color',
      'indicator-active-color': 'data-indicator-active-color',
      autoplay: 'data-autoplay',
      interval: 'data-interval',
      duration: 'data-duration',
      circular: 'data-circular',
      vertical: 'data-vertical',
      'previous-margin': 'data-previous-margin',
      'next-margin': 'data-next-margin',
    },
  },
  'swiper-item': {
    tag: 'div',
    props: { className: 'lynx-swiper-item' },
  },
  progress: {
    tag: 'progress',
    props: { className: 'lynx-progress' },
    attributeMapping: {
      percent: 'value',
      'show-info': 'data-show-info',
      'border-radius': 'data-border-radius',
      'font-size': 'data-font-size',
    },
  },
  'web-view': {
    tag: 'iframe',
    props: { className: 'lynx-web-view' },
    selfClosing: true,
    attributeMapping: {
      src: 'src',
    },
  },

  // 列表相关
  list: {
    tag: 'div',
    props: { className: 'lynx-list' },
  },
  'list-item': {
    tag: 'div',
    props: { className: 'lynx-list-item' },
  },
  cell: {
    tag: 'div',
    props: { className: 'lynx-cell' },
  },
  header: {
    tag: 'header',
    props: { className: 'lynx-header' },
  },
  footer: {
    tag: 'footer',
    props: { className: 'lynx-footer' },
  },

  // 画布和媒体
  canvas: {
    tag: 'canvas',
    props: { className: 'lynx-canvas' },
    attributeMapping: {
      'canvas-id': 'id',
      'disable-scroll': 'data-disable-scroll',
    },
  },
  'cover-image': {
    tag: 'img',
    props: { className: 'lynx-cover-image' },
    selfClosing: true,
    attributeMapping: {
      src: 'src',
    },
  },
  'cover-view': {
    tag: 'div',
    props: { className: 'lynx-cover-view' },
  },

  // 可移动视图组件
  'movable-area': {
    tag: 'div',
    props: { className: 'lynx-movable-area' },
    attributeMapping: {
      'scale-area': 'data-scale-area',
    },
  },
  'movable-view': {
    tag: 'div',
    props: { className: 'lynx-movable-view' },
    attributeMapping: {
      direction: 'data-direction',
      inertia: 'data-inertia',
      'out-of-bounds': 'data-out-of-bounds',
      x: 'data-x',
      y: 'data-y',
      damping: 'data-damping',
      friction: 'data-friction',
      disabled: 'data-disabled',
      scale: 'data-scale',
      'scale-min': 'data-scale-min',
      'scale-max': 'data-scale-max',
      'scale-value': 'data-scale-value',
    },
  },

  // === 新增元素映射 - 增强Parse5解析能力 ===

  // Enhanced fallback mappings for better coverage
  '*': {
    tag: 'div',
    props: { className: 'lynx-unknown' },
    attributeMapping: {
      class: 'className',
      style: 'style',
      id: 'id',
      title: 'title',
    },
  },

  // Common Lynx elements that might be missing
  block: {
    tag: 'div',
    props: { className: 'lynx-block' },
  },
  inline: {
    tag: 'span',
    props: { className: 'lynx-inline' },
  },
  template: {
    tag: 'div',
    props: { className: 'lynx-template' },
  },

  // 常见HTML元素的直接映射
  div: { tag: 'div', props: { className: 'lynx-div' } },
  span: { tag: 'span', props: { className: 'lynx-span' } },
  p: { tag: 'p', props: { className: 'lynx-p' } },
  h1: { tag: 'h1', props: { className: 'lynx-h1' } },
  h2: { tag: 'h2', props: { className: 'lynx-h2' } },
  h3: { tag: 'h3', props: { className: 'lynx-h3' } },
  ul: { tag: 'ul', props: { className: 'lynx-ul' } },
  ol: { tag: 'ol', props: { className: 'lynx-ol' } },
  li: { tag: 'li', props: { className: 'lynx-li' } },
  a: {
    tag: 'a',
    props: { className: 'lynx-a' },
    attributeMapping: {
      href: 'href',
      target: 'target',
    },
  },
  img: {
    tag: 'img',
    props: { className: 'lynx-img' },
    selfClosing: true,
    attributeMapping: {
      src: 'src',
      alt: 'alt',
      width: 'width',
      height: 'height',
    },
  },

  // 更多Lynx专用组件（避免重复）
  // form和label已在后面定义
  icon: {
    tag: 'i',
    props: { className: 'lynx-icon' },
    attributeMapping: {
      type: 'data-type',
      size: 'data-size',
      color: 'data-color',
    },
  },
  camera: {
    tag: 'div',
    props: { className: 'lynx-camera' },
    attributeMapping: {
      mode: 'data-mode',
      resolution: 'data-resolution',
      flash: 'data-flash',
    },
  },
  'live-player': {
    tag: 'video',
    props: { className: 'lynx-live-player' },
    attributeMapping: {
      src: 'src',
      mode: 'data-mode',
      autoplay: 'autoPlay',
      muted: 'muted',
      orientation: 'data-orientation',
      'object-fit': 'data-object-fit',
    },
  },
  'live-pusher': {
    tag: 'video',
    props: { className: 'lynx-live-pusher' },
    attributeMapping: {
      url: 'data-url',
      mode: 'data-mode',
      autopush: 'data-autopush',
      muted: 'muted',
      'enable-camera': 'data-enable-camera',
      'auto-focus': 'data-auto-focus',
      orientation: 'data-orientation',
      beauty: 'data-beauty',
      whiteness: 'data-whiteness',
    },
  },
  map: {
    tag: 'div',
    props: { className: 'lynx-map' },
    attributeMapping: {
      longitude: 'data-longitude',
      latitude: 'data-latitude',
      scale: 'data-scale',
      markers: 'data-markers',
      polyline: 'data-polyline',
      circles: 'data-circles',
      controls: 'data-controls',
      'include-points': 'data-include-points',
      'show-location': 'data-show-location',
    },
  },

  // ===============================
  // 基于pePromptLoader.ts缺失的关键组件映射补充
  // ===============================

  // 文本相关组件补充
  label: {
    tag: 'label',
    props: { className: 'lynx-label' },
    attributeMapping: {
      for: 'htmlFor',
    },
  },

  // 表单相关组件补充
  form: {
    tag: 'form',
    props: { className: 'lynx-form' },
    attributeMapping: {
      'report-submit': 'data-report-submit',
      'report-submit-timeout': 'data-report-submit-timeout',
    },
  },

  // 多媒体组件补充
  ad: {
    tag: 'div',
    props: { className: 'lynx-ad' },
    attributeMapping: {
      'unit-id': 'data-unit-id',
      'ad-intervals': 'data-ad-intervals',
      'ad-type': 'data-ad-type',
      'ad-theme': 'data-ad-theme',
    },
  },

  'official-account': {
    tag: 'div',
    props: { className: 'lynx-official-account' },
  },

  'open-data': {
    tag: 'div',
    props: { className: 'lynx-open-data' },
    attributeMapping: {
      type: 'data-type',
      'open-gid': 'data-open-gid',
      lang: 'lang',
    },
  },

  // 功能组件补充
  'web-view': {
    tag: 'iframe',
    props: { className: 'lynx-web-view' },
    selfClosing: true,
    attributeMapping: {
      src: 'src',
      'webview-styles': 'data-webview-styles',
    },
  },

  'functional-page-navigator': {
    tag: 'a',
    props: { className: 'lynx-functional-page-navigator' },
    attributeMapping: {
      version: 'data-version',
      name: 'data-name',
      args: 'data-args',
    },
  },

  // 输入组件补充
  'keyboard-accessory': {
    tag: 'div',
    props: { className: 'lynx-keyboard-accessory' },
  },

  // 导航组件补充
  'navigation-bar': {
    tag: 'nav',
    props: { className: 'lynx-navigation-bar' },
    attributeMapping: {
      title: 'data-title',
      loading: 'data-loading',
      'front-color': 'data-front-color',
      'background-color': 'data-background-color',
      'color-animation-duration': 'data-color-animation-duration',
      'color-animation-timing-func': 'data-color-animation-timing-func',
    },
  },

  'page-meta': {
    tag: 'div',
    props: { className: 'lynx-page-meta' },
    attributeMapping: {
      'background-text-style': 'data-background-text-style',
      'background-color': 'data-background-color',
      'background-color-top': 'data-background-color-top',
      'background-color-bottom': 'data-background-color-bottom',
      'scroll-top': 'data-scroll-top',
      'scroll-duration': 'data-scroll-duration',
      'page-style': 'data-page-style',
      'root-font-size': 'data-root-font-size',
    },
  },

  // 复杂组件补充
  'match-media': {
    tag: 'div',
    props: { className: 'lynx-match-media' },
    attributeMapping: {
      'min-width': 'data-min-width',
      'max-width': 'data-max-width',
      width: 'data-width',
      'min-height': 'data-min-height',
      'max-height': 'data-max-height',
      height: 'data-height',
      orientation: 'data-orientation',
    },
  },

  'share-element': {
    tag: 'div',
    props: { className: 'lynx-share-element' },
    attributeMapping: {
      'map-from': 'data-map-from',
      'map-to': 'data-map-to',
      duration: 'data-duration',
      'easing-function': 'data-easing-function',
    },
  },

  // 列表组件补充
  'recycle-view': {
    tag: 'div',
    props: { className: 'lynx-recycle-view' },
    attributeMapping: {
      height: 'data-height',
      'placeholder-image': 'data-placeholder-image',
    },
  },

  'recycle-item': {
    tag: 'div',
    props: { className: 'lynx-recycle-item' },
    attributeMapping: {
      item: 'data-item',
    },
  },

  // 视觉效果组件补充
  'page-container': {
    tag: 'div',
    props: { className: 'lynx-page-container' },
    attributeMapping: {
      show: 'data-show',
      duration: 'data-duration',
      'z-index': 'data-z-index',
      overlay: 'data-overlay',
      position: 'data-position',
      round: 'data-round',
      'close-on-slidedown': 'data-close-on-slidedown',
    },
  },

  // 最后加入fallback映射，确保所有未知组件都有合适的降级
  '*': {
    tag: 'div',
    props: { className: 'lynx-unknown' },
    attributeMapping: {
      class: 'className',
      style: 'style',
      id: 'id',
      title: 'title',
    },
  },
};

// ===============================
// 自闭合标签集合 - 增强版
// ===============================

export const SELF_CLOSING_TAGS = new Set([
  // Lynx组件
  'image',
  'input',
  'switch',
  'slider',
  'progress',
  'web-view',
  'cover-image',
  'checkbox',
  'radio',
  'canvas',
  'icon',
  // HTML元素
  'img',
  'br',
  'hr',
  'meta',
  'link',
  'area',
  'base',
  'col',
  'embed',
  'source',
  'track',
  'wbr',
]);

// ===============================
// 降级映射规则
// ===============================

export const FALLBACK_ELEMENT_MAPPING: Record<string, ElementMapping> = {
  // 默认降级映射
  default: {
    tag: 'div',
    props: { className: 'lynx-fallback' },
    attributeMapping: {
      class: 'className',
      style: 'style',
      id: 'id',
    },
  },

  // 文本类元素降级
  'text-fallback': {
    tag: 'span',
    props: { className: 'lynx-text-fallback' },
  },

  // 输入类元素降级
  'input-fallback': {
    tag: 'input',
    props: {
      className: 'lynx-input-fallback',
      type: 'text',
    },
    selfClosing: true,
  },

  // 媒体类元素降级
  'media-fallback': {
    tag: 'div',
    props: {
      className: 'lynx-media-fallback',
      style: {
        width: '100px',
        height: '100px',
        background: '#f0f0f0',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
    },
  },
};

// ===============================
// 通用属性映射 - 完整增强版（基于pePromptLoader.ts规则）
// ===============================

export const COMMON_ATTRIBUTE_MAPPING: Record<string, string> = {
  // React标准属性
  class: 'className',
  for: 'htmlFor',
  tabindex: 'tabIndex',
  readonly: 'readOnly',
  maxlength: 'maxLength',
  cellpadding: 'cellPadding',
  cellspacing: 'cellSpacing',
  rowspan: 'rowSpan',
  colspan: 'colSpan',
  usemap: 'useMap',
  frameborder: 'frameBorder',

  // 通用属性
  style: 'style',
  id: 'id',
  title: 'title',
  hidden: 'hidden',
  lang: 'lang',
  dir: 'dir',

  // 事件属性（保持不变，由事件映射处理）
  onclick: 'onClick',
  onchange: 'onChange',
  oninput: 'onInput',
  onfocus: 'onFocus',
  onblur: 'onBlur',

  // 保持原样的属性
  'data-*': 'data-*', // data属性
  'aria-*': 'aria-*', // aria属性
  role: 'role', // role属性

  // Lynx特有属性到data属性的映射
  'hover-class': 'data-hover-class',
  'hover-start-time': 'data-hover-start-time',
  'hover-stay-time': 'data-hover-stay-time',
  animation: 'data-animation',
  bindtap: 'onClick',
  bindchange: 'onChange',
  bindinput: 'onInput',
  bindblur: 'onBlur',
  bindfocus: 'onFocus',

  // ===============================
  // 基于pePromptLoader.ts缺失的关键属性映射补充
  // ===============================

  // 更多驼峰命名转换（完整映射）
  'auto-focus': 'autoFocus',
  autofocus: 'autoFocus',
  autoplay: 'autoPlay',
  cellpadding: 'cellPadding',
  cellspacing: 'cellSpacing',
  colspan: 'colSpan',
  contenteditable: 'contentEditable',
  crossorigin: 'crossOrigin',
  datetime: 'dateTime',
  enctype: 'encType',
  formaction: 'formAction',
  formenctype: 'formEncType',
  formmethod: 'formMethod',
  formnovalidate: 'formNoValidate',
  formtarget: 'formTarget',
  frameborder: 'frameBorder',
  hreflang: 'hrefLang',
  inputmode: 'inputMode',
  marginheight: 'marginHeight',
  marginwidth: 'marginWidth',
  maxlength: 'maxLength',
  mediagroup: 'mediaGroup',
  minlength: 'minLength',
  novalidate: 'noValidate',
  radiogroup: 'radioGroup',
  readonly: 'readOnly',
  rowspan: 'rowSpan',
  spellcheck: 'spellCheck',
  srcdoc: 'srcDoc',
  srclang: 'srcLang',
  srcset: 'srcSet',
  tabindex: 'tabIndex',
  usemap: 'useMap',

  // 图片组件属性映射（完整映射）
  mode: 'data-mode', // 显示模式：scaleToFill, aspectFit, aspectFill, widthFix, heightFix
  'lazy-load': 'data-lazy-load', // 懒加载
  'fade-in': 'data-fade-in', // 淡入效果
  webp: 'data-webp', // WebP支持
  'show-menu-by-longpress': 'data-show-menu', // 长按显示菜单

  // 表单控件属性映射（完整映射）
  'placeholder-style': 'data-placeholder-style',
  'placeholder-class': 'data-placeholder-class',
  'cursor-spacing': 'data-cursor-spacing',
  'confirm-type': 'data-confirm-type',
  'confirm-hold': 'data-confirm-hold',
  cursor: 'data-cursor',
  'selection-start': 'data-selection-start',
  'selection-end': 'data-selection-end',
  'adjust-position': 'data-adjust-position',
  'hold-keyboard': 'data-hold-keyboard',
  'auto-height': 'data-auto-height',
  fixed: 'data-fixed',
  'auto-blur': 'data-auto-blur',

  // 导航组件属性映射（完整映射）
  url: 'href',
  'open-type': 'data-open-type', // navigate, redirect, switchTab, reLaunch, navigateBack
  delta: 'data-delta',
  'app-id': 'data-app-id',
  path: 'data-path',
  'extra-data': 'data-extra-data',
  version: 'data-version',

  // 轮播组件属性映射（完整映射）
  'indicator-dots': 'data-indicator-dots',
  'indicator-color': 'data-indicator-color',
  'indicator-active-color': 'data-indicator-active-color',
  'active-class': 'data-active-class',
  'changing-class': 'data-changing-class',
  autoplay: 'data-autoplay',
  interval: 'data-interval',
  duration: 'data-duration',
  circular: 'data-circular',
  vertical: 'data-vertical',
  'previous-margin': 'data-previous-margin',
  'next-margin': 'data-next-margin',
  'snap-to-edge': 'data-snap-to-edge',
  'display-multiple-items': 'data-display-multiple-items',
  'skip-hidden-item-layout': 'data-skip-hidden-item-layout',
  'disable-touch': 'data-disable-touch',
  touchable: 'data-touchable',
  'easing-function': 'data-easing-function',

  // 滚动容器属性映射（完整映射）
  'scroll-x': 'data-scroll-x',
  'scroll-y': 'data-scroll-y',
  'upper-threshold': 'data-upper-threshold',
  'lower-threshold': 'data-lower-threshold',
  'scroll-top': 'data-scroll-top',
  'scroll-left': 'data-scroll-left',
  'scroll-into-view': 'data-scroll-into-view',
  'scroll-with-animation': 'data-scroll-with-animation',
  'enable-back-to-top': 'data-enable-back-to-top',
  'enable-flex': 'data-enable-flex',
  'scroll-anchoring': 'data-scroll-anchoring',
  'refresher-enabled': 'data-refresher-enabled',
  'refresher-threshold': 'data-refresher-threshold',
  'refresher-default-style': 'data-refresher-default-style',
  'refresher-background': 'data-refresher-background',
  'refresher-triggered': 'data-refresher-triggered',
  'show-scrollbar': 'data-show-scrollbar',
  'paging-enabled': 'data-paging-enabled',
  'fast-deceleration': 'data-fast-deceleration',

  // 可移动视图组件属性映射（完整映射）
  direction: 'data-direction',
  inertia: 'data-inertia',
  'out-of-bounds': 'data-out-of-bounds',
  x: 'data-x',
  y: 'data-y',
  damping: 'data-damping',
  friction: 'data-friction',
  disabled: 'data-disabled',
  scale: 'data-scale',
  'scale-min': 'data-scale-min',
  'scale-max': 'data-scale-max',
  'scale-value': 'data-scale-value',
  'scale-area': 'data-scale-area',

  // 媒体组件属性映射（完整映射）
  poster: 'poster',
  'object-fit': 'data-object-fit',
  'poster-for-crawler': 'data-poster-for-crawler',
  'show-progress': 'data-show-progress',
  'show-fullscreen-btn': 'data-show-fullscreen-btn',
  'show-play-btn': 'data-show-play-btn',
  'show-center-play-btn': 'data-show-center-play-btn',
  'enable-progress-gesture': 'data-enable-progress-gesture',
  'enable-play-gesture': 'data-enable-play-gesture',
  'vslide-gesture': 'data-vslide-gesture',
  'vslide-gesture-in-fullscreen': 'data-vslide-gesture-in-fullscreen',
  'ad-unit-id': 'data-ad-unit-id',
  'poster-size': 'data-poster-size',
  'show-mute-btn': 'data-show-mute-btn',
  title: 'data-title',
  'enable-danmu': 'data-enable-danmu',
  'danmu-list': 'data-danmu-list',
  'danmu-btn': 'data-danmu-btn',
  'enable-play-gesture': 'data-enable-play-gesture',
  'page-gesture': 'data-page-gesture',

  // 地图组件属性映射（完整映射）
  longitude: 'data-longitude',
  latitude: 'data-latitude',
  scale: 'data-scale',
  markers: 'data-markers',
  covers: 'data-covers',
  polyline: 'data-polyline',
  circles: 'data-circles',
  controls: 'data-controls',
  'include-points': 'data-include-points',
  'show-location': 'data-show-location',
  'layer-style': 'data-layer-style',
  'enable-3D': 'data-enable-3D',
  'show-compass': 'data-show-compass',
  'show-scale': 'data-show-scale',
  'enable-overlooking': 'data-enable-overlooking',
  'enable-zoom': 'data-enable-zoom',
  'enable-scroll': 'data-enable-scroll',
  'enable-rotate': 'data-enable-rotate',
  'enable-satellite': 'data-enable-satellite',
  'enable-traffic': 'data-enable-traffic',

  // Canvas组件属性映射（完整映射）
  'canvas-id': 'id',
  'disable-scroll': 'data-disable-scroll',
  '2d': 'data-2d',
  type: 'data-type',

  // 直播组件属性映射（完整映射）
  src: 'src',
  mode: 'data-mode',
  autoplay: 'autoPlay',
  muted: 'muted',
  orientation: 'data-orientation',
  autopush: 'data-autopush',
  'enable-camera': 'data-enable-camera',
  'auto-focus': 'data-auto-focus',
  beauty: 'data-beauty',
  whiteness: 'data-whiteness',
  aspect: 'data-aspect',
  'min-bitrate': 'data-min-bitrate',
  'max-bitrate': 'data-max-bitrate',
  'audio-quality': 'data-audio-quality',
  'waiting-image': 'data-waiting-image',
  'waiting-image-hash': 'data-waiting-image-hash',
  zoom: 'data-zoom',
  'device-position': 'data-device-position',
  'background-mute': 'data-background-mute',
  mirror: 'data-mirror',
  'remote-mirror': 'data-remote-mirror',
  'local-mirror': 'data-local-mirror',
  'audio-reverb-type': 'data-audio-reverb-type',
  'enable-mic': 'data-enable-mic',
  'enable-agc': 'data-enable-agc',
  'enable-ans': 'data-enable-ans',
  'audio-volume-type': 'data-audio-volume-type',
  'video-width': 'data-video-width',
  'video-height': 'data-video-height',

  // 进度条组件属性映射（完整映射）
  percent: 'value',
  'show-info': 'data-show-info',
  'border-radius': 'data-border-radius',
  'font-size': 'data-font-size',
  'stroke-width': 'data-stroke-width',
  color: 'data-color',
  activeColor: 'data-activeColor',
  backgroundColor: 'data-backgroundColor',
  active: 'data-active',

  // 选择器组件属性映射（完整映射）
  value: 'value',
  range: 'data-range',
  'range-key': 'data-range-key',
  start: 'data-start',
  end: 'data-end',
  fields: 'data-fields',
  'custom-item': 'data-custom-item',

  // Switch组件属性映射（完整映射）
  checked: 'checked',
  disabled: 'disabled',
  type: 'data-type',
  color: 'data-color',

  // Slider组件属性映射（完整映射）
  min: 'min',
  max: 'max',
  step: 'step',
  value: 'value',
  'show-value': 'data-show-value',
  activeColor: 'data-activeColor',
  backgroundColor: 'data-backgroundColor',
  'block-size': 'data-block-size',
  'block-color': 'data-block-color',

  // 复选框和单选框组件属性映射（完整映射）
  value: 'value',
  disabled: 'disabled',
  checked: 'checked',
  color: 'data-color',

  // 按钮组件属性映射（完整映射）
  size: 'data-size',
  type: 'type',
  plain: 'data-plain',
  disabled: 'disabled',
  loading: 'data-loading',
  'form-type': 'type',
  'open-type': 'data-open-type',
  'hover-class': 'data-hover-class',
  'hover-stop-propagation': 'data-hover-stop-propagation',
  'hover-start-time': 'data-hover-start-time',
  'hover-stay-time': 'data-hover-stay-time',
  lang: 'lang',
  'business-id': 'data-business-id',
  'session-from': 'data-session-from',
  'send-message-title': 'data-send-message-title',
  'send-message-path': 'data-send-message-path',
  'send-message-img': 'data-send-message-img',
  'app-parameter': 'data-app-parameter',
  'show-message-card': 'data-show-message-card',
};

// ===============================
// TTML指令映射表
// ===============================

export const TTML_DIRECTIVE_MAPPING: Record<string, DirectiveMapping> = {
  // 条件渲染指令
  'lx:if': {
    type: 'conditional',
    transform: (value: string, node: any, context: any) => ({
      type: 'ConditionalExpression',
      test: parseExpression(value, context),
      consequent: transformNode(node, context),
      alternate: null,
    }),
  },

  'lx:elif': {
    type: 'conditional',
    transform: (value: string, node: any, context: any) => ({
      type: 'ConditionalExpression',
      test: parseExpression(value, context),
      consequent: transformNode(node, context),
      alternate: null, // 由chain处理器处理
    }),
  },

  'lx:else': {
    type: 'conditional',
    transform: (value: string, node: any, context: any) =>
      transformNode(node, context),
  },

  // 列表渲染指令
  'lx:for': {
    type: 'iteration',
    transform: (value: string, node: any, context: any) => {
      const { itemName, indexName, listExpression } = parseForExpression(value);

      return {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'CallExpression',
          callee: {
            type: 'MemberExpression',
            object: parseExpression(listExpression, context),
            property: { type: 'Identifier', name: 'map' },
          },
          arguments: [
            {
              type: 'ArrowFunctionExpression',
              params: [
                { type: 'Identifier', name: itemName },
                { type: 'Identifier', name: indexName },
              ],
              body: transformNode(node, {
                ...context,
                scope: {
                  ...context.scope,
                  [itemName]: true,
                  [indexName]: true,
                },
              }),
            },
          ],
        },
      };
    },
  },

  'lx:key': {
    type: 'optimization',
    transform: (value: string, node: any, context: any) =>
      // React key属性
      ({
        type: 'JSXAttribute',
        name: { type: 'JSXIdentifier', name: 'key' },
        value: {
          type: 'JSXExpressionContainer',
          expression: parseExpression(value, context),
        },
      }),
  },

  // TikTok模板语法支持
  'tt:if': {
    type: 'conditional',
    transform: (value: string, node: any, context: any) => ({
      type: 'ConditionalExpression',
      test: parseExpression(value, context),
      consequent: transformNode(node, context),
      alternate: null,
    }),
  },

  'tt:for': {
    type: 'iteration',
    transform: (value: string, node: any, context: any) => {
      const { itemName, indexName, listExpression } = parseForExpression(value);

      return {
        type: 'JSXExpressionContainer',
        expression: {
          type: 'CallExpression',
          callee: {
            type: 'MemberExpression',
            object: parseExpression(listExpression, context),
            property: { type: 'Identifier', name: 'map' },
          },
          arguments: [
            {
              type: 'ArrowFunctionExpression',
              params: [
                { type: 'Identifier', name: itemName },
                { type: 'Identifier', name: indexName },
              ],
              body: transformNode(node, {
                ...context,
                scope: {
                  ...context.scope,
                  [itemName]: true,
                  [indexName]: true,
                },
              }),
            },
          ],
        },
      };
    },
  },

  'tt:key': {
    type: 'optimization',
    transform: (value: string, node: any, context: any) => ({
      type: 'JSXAttribute',
      name: { type: 'JSXIdentifier', name: 'key' },
      value: {
        type: 'JSXExpressionContainer',
        expression: parseExpression(value, context),
      },
    }),
  },
};

// ===============================
// 事件指令映射表 - 完整增强版（基于pePromptLoader.ts规则）
// ===============================

export const EVENT_DIRECTIVE_MAPPING: Record<string, EventMapping> = {
  // 冒泡事件 (bind*)
  bindtap: { reactEvent: 'onClick', stopPropagation: false },
  bindtouchstart: { reactEvent: 'onTouchStart', stopPropagation: false },
  bindtouchmove: { reactEvent: 'onTouchMove', stopPropagation: false },
  bindtouchcancel: { reactEvent: 'onTouchCancel', stopPropagation: false },
  bindtouchend: { reactEvent: 'onTouchEnd', stopPropagation: false },
  bindlongpress: { reactEvent: 'onContextMenu', stopPropagation: false },
  bindlongtap: { reactEvent: 'onContextMenu', stopPropagation: false },
  bindtransitionend: {
    reactEvent: 'onTransitionEnd',
    stopPropagation: false,
  },
  bindanimationstart: {
    reactEvent: 'onAnimationStart',
    stopPropagation: false,
  },
  bindanimationiteration: {
    reactEvent: 'onAnimationIteration',
    stopPropagation: false,
  },
  bindanimationend: { reactEvent: 'onAnimationEnd', stopPropagation: false },
  bindtouchforcechange: {
    reactEvent: 'onTouchStart',
    stopPropagation: false,
  },

  // 表单事件
  bindinput: { reactEvent: 'onInput', stopPropagation: false },
  bindfocus: { reactEvent: 'onFocus', stopPropagation: false },
  bindblur: { reactEvent: 'onBlur', stopPropagation: false },
  bindconfirm: { reactEvent: 'onKeyDown', stopPropagation: false },
  bindkeyboardheightchange: {
    reactEvent: 'onFocus',
    stopPropagation: false,
  },
  bindchange: { reactEvent: 'onChange', stopPropagation: false },
  bindcolumnchange: { reactEvent: 'onChange', stopPropagation: false },
  bindscroll: { reactEvent: 'onScroll', stopPropagation: false },
  bindscrolltoupper: {
    reactEvent: 'onScroll',
    stopPropagation: false,
    special: 'upper',
  },
  bindscrolltolower: {
    reactEvent: 'onScroll',
    stopPropagation: false,
    special: 'lower',
  },

  // 媒体事件
  binderror: { reactEvent: 'onError', stopPropagation: false },
  bindload: { reactEvent: 'onLoad', stopPropagation: false },
  bindtimeupdate: { reactEvent: 'onTimeUpdate', stopPropagation: false },
  bindended: { reactEvent: 'onEnded', stopPropagation: false },
  bindplay: { reactEvent: 'onPlay', stopPropagation: false },
  bindpause: { reactEvent: 'onPause', stopPropagation: false },

  // 可见性事件
  bindappear: {
    reactEvent: 'onIntersect',
    stopPropagation: false,
    special: 'appear',
  },
  binddisappear: {
    reactEvent: 'onIntersect',
    stopPropagation: false,
    special: 'disappear',
  },

  // 捕获事件 (catch:*)
  'catch:tap': { reactEvent: 'onClick', stopPropagation: true },
  'catch:input': { reactEvent: 'onInput', stopPropagation: true },
  'catch:change': { reactEvent: 'onChange', stopPropagation: true },
  'catch:scroll': { reactEvent: 'onScroll', stopPropagation: true },
  'catch:touchstart': { reactEvent: 'onTouchStart', stopPropagation: true },
  'catch:touchmove': { reactEvent: 'onTouchMove', stopPropagation: true },
  'catch:touchend': { reactEvent: 'onTouchEnd', stopPropagation: true },
  'catch:longpress': { reactEvent: 'onContextMenu', stopPropagation: true },
  'catch:longtap': { reactEvent: 'onContextMenu', stopPropagation: true },

  // 新增更多事件映射
  bindsubmit: { reactEvent: 'onSubmit', stopPropagation: false },
  bindreset: { reactEvent: 'onReset', stopPropagation: false },
  bindresize: { reactEvent: 'onResize', stopPropagation: false },
  bindselect: { reactEvent: 'onSelect', stopPropagation: false },
  bindwheel: { reactEvent: 'onWheel', stopPropagation: false },
  bindcontextmenu: { reactEvent: 'onContextMenu', stopPropagation: false },
  binddoubleclick: { reactEvent: 'onDoubleClick', stopPropagation: false },
  binddragstart: { reactEvent: 'onDragStart', stopPropagation: false },
  binddragend: { reactEvent: 'onDragEnd', stopPropagation: false },
  binddrop: { reactEvent: 'onDrop', stopPropagation: false },
  binddragover: { reactEvent: 'onDragOver', stopPropagation: false },

  // 对应的catch版本
  'catch:submit': { reactEvent: 'onSubmit', stopPropagation: true },
  'catch:reset': { reactEvent: 'onReset', stopPropagation: true },
  'catch:doubleclick': { reactEvent: 'onDoubleClick', stopPropagation: true },

  // ===============================
  // 基于pePromptLoader.ts缺失的关键事件映射补充
  // ===============================

  // 简化版本事件绑定（无前缀）- 兼容pePromptLoader.ts语法
  bindtap: { reactEvent: 'onClick', stopPropagation: false },
  catchtap: { reactEvent: 'onClick', stopPropagation: true },
  bindlongpress: { reactEvent: 'onContextMenu', stopPropagation: false },
  catchlongpress: { reactEvent: 'onContextMenu', stopPropagation: true },
  bindlongtap: { reactEvent: 'onContextMenu', stopPropagation: false },
  catchlongtap: { reactEvent: 'onContextMenu', stopPropagation: true },

  // 触摸事件系列（完整映射）
  bindtouchstart: { reactEvent: 'onTouchStart', stopPropagation: false },
  catchtouchstart: { reactEvent: 'onTouchStart', stopPropagation: true },
  bindtouchmove: { reactEvent: 'onTouchMove', stopPropagation: false },
  catchtouchmove: { reactEvent: 'onTouchMove', stopPropagation: true },
  bindtouchend: { reactEvent: 'onTouchEnd', stopPropagation: false },
  catchtouchend: { reactEvent: 'onTouchEnd', stopPropagation: true },
  bindtouchcancel: { reactEvent: 'onTouchCancel', stopPropagation: false },
  catchtouchcancel: { reactEvent: 'onTouchCancel', stopPropagation: true },
  bindtouchforcechange: { reactEvent: 'onTouchStart', stopPropagation: false },
  catchtouchforcechange: { reactEvent: 'onTouchStart', stopPropagation: true },

  // 表单事件系列（完整映射）
  bindinput: { reactEvent: 'onInput', stopPropagation: false },
  catchinput: { reactEvent: 'onInput', stopPropagation: true },
  bindchange: { reactEvent: 'onChange', stopPropagation: false },
  catchchange: { reactEvent: 'onChange', stopPropagation: true },
  bindblur: { reactEvent: 'onBlur', stopPropagation: false },
  catchblur: { reactEvent: 'onBlur', stopPropagation: true },
  bindfocus: { reactEvent: 'onFocus', stopPropagation: false },
  catchfocus: { reactEvent: 'onFocus', stopPropagation: true },
  bindconfirm: { reactEvent: 'onKeyDown', stopPropagation: false },
  catchconfirm: { reactEvent: 'onKeyDown', stopPropagation: true },
  bindkeyboardheightchange: { reactEvent: 'onFocus', stopPropagation: false },
  catchkeyboardheightchange: { reactEvent: 'onFocus', stopPropagation: true },

  // 滚动事件系列（完整映射）
  bindscroll: { reactEvent: 'onScroll', stopPropagation: false },
  catchscroll: { reactEvent: 'onScroll', stopPropagation: true },
  bindscrolltoupper: {
    reactEvent: 'onScroll',
    stopPropagation: false,
    special: 'upper',
  },
  catchscrolltoupper: {
    reactEvent: 'onScroll',
    stopPropagation: true,
    special: 'upper',
  },
  bindscrolltolower: {
    reactEvent: 'onScroll',
    stopPropagation: false,
    special: 'lower',
  },
  catchscrolltolower: {
    reactEvent: 'onScroll',
    stopPropagation: true,
    special: 'lower',
  },
  bindscrollstart: { reactEvent: 'onScroll', stopPropagation: false },
  catchscrollstart: { reactEvent: 'onScroll', stopPropagation: true },
  bindscrollend: { reactEvent: 'onScroll', stopPropagation: false },
  catchscrollend: { reactEvent: 'onScroll', stopPropagation: true },

  // 媒体事件系列（完整映射）
  bindload: { reactEvent: 'onLoad', stopPropagation: false },
  catchload: { reactEvent: 'onLoad', stopPropagation: true },
  binderror: { reactEvent: 'onError', stopPropagation: false },
  catcherror: { reactEvent: 'onError', stopPropagation: true },
  bindplay: { reactEvent: 'onPlay', stopPropagation: false },
  catchplay: { reactEvent: 'onPlay', stopPropagation: true },
  bindpause: { reactEvent: 'onPause', stopPropagation: false },
  catchpause: { reactEvent: 'onPause', stopPropagation: true },
  bindended: { reactEvent: 'onEnded', stopPropagation: false },
  catchended: { reactEvent: 'onEnded', stopPropagation: true },
  bindtimeupdate: { reactEvent: 'onTimeUpdate', stopPropagation: false },
  catchtimeupdate: { reactEvent: 'onTimeUpdate', stopPropagation: true },
  bindwaiting: { reactEvent: 'onWaiting', stopPropagation: false },
  catchwaiting: { reactEvent: 'onWaiting', stopPropagation: true },
  bindprogress: { reactEvent: 'onProgress', stopPropagation: false },
  catchprogress: { reactEvent: 'onProgress', stopPropagation: true },
  bindcanplay: { reactEvent: 'onCanPlay', stopPropagation: false },
  catchcanplay: { reactEvent: 'onCanPlay', stopPropagation: true },

  // 曝光事件系列（完整映射）
  bindappear: {
    reactEvent: 'onIntersect',
    stopPropagation: false,
    special: 'appear',
  },
  catchappear: {
    reactEvent: 'onIntersect',
    stopPropagation: true,
    special: 'appear',
  },
  binddisappear: {
    reactEvent: 'onIntersect',
    stopPropagation: false,
    special: 'disappear',
  },
  catchdisappear: {
    reactEvent: 'onIntersect',
    stopPropagation: true,
    special: 'disappear',
  },

  // 动画事件系列（完整映射）
  bindtransitionend: { reactEvent: 'onTransitionEnd', stopPropagation: false },
  catchtransitionend: { reactEvent: 'onTransitionEnd', stopPropagation: true },
  bindanimationstart: {
    reactEvent: 'onAnimationStart',
    stopPropagation: false,
  },
  catchanimationstart: {
    reactEvent: 'onAnimationStart',
    stopPropagation: true,
  },
  bindanimationiteration: {
    reactEvent: 'onAnimationIteration',
    stopPropagation: false,
  },
  catchanimationiteration: {
    reactEvent: 'onAnimationIteration',
    stopPropagation: true,
  },
  bindanimationend: { reactEvent: 'onAnimationEnd', stopPropagation: false },
  catchanimationend: { reactEvent: 'onAnimationEnd', stopPropagation: true },

  // 高级事件绑定（capture-bind/capture-catch）
  'capture-bindtap': { reactEvent: 'onClickCapture', stopPropagation: false },
  'capture-catch:tap': { reactEvent: 'onClickCapture', stopPropagation: true },
  'capture-bindtouchstart': {
    reactEvent: 'onTouchStartCapture',
    stopPropagation: false,
  },
  'capture-catch:touchstart': {
    reactEvent: 'onTouchStartCapture',
    stopPropagation: true,
  },
  'capture-bindtouchmove': {
    reactEvent: 'onTouchMoveCapture',
    stopPropagation: false,
  },
  'capture-catch:touchmove': {
    reactEvent: 'onTouchMoveCapture',
    stopPropagation: true,
  },
  'capture-bindtouchend': {
    reactEvent: 'onTouchEndCapture',
    stopPropagation: false,
  },
  'capture-catch:touchend': {
    reactEvent: 'onTouchEndCapture',
    stopPropagation: true,
  },
  'capture-bindlongpress': {
    reactEvent: 'onContextMenuCapture',
    stopPropagation: false,
  },
  'capture-catch:longpress': {
    reactEvent: 'onContextMenuCapture',
    stopPropagation: true,
  },

  // 选择器事件系列（完整映射）
  bindcolumnchange: { reactEvent: 'onChange', stopPropagation: false },
  catchcolumnchange: { reactEvent: 'onChange', stopPropagation: true },
  bindcancel: { reactEvent: 'onCancel', stopPropagation: false },
  catchcancel: { reactEvent: 'onCancel', stopPropagation: true },

  // 轮播事件系列（完整映射）
  bindcurrent: { reactEvent: 'onChange', stopPropagation: false },
  catchcurrent: { reactEvent: 'onChange', stopPropagation: true },
  bindtransition: { reactEvent: 'onTransition', stopPropagation: false },
  catchtransition: { reactEvent: 'onTransition', stopPropagation: true },
  bindanimationfinish: { reactEvent: 'onAnimationEnd', stopPropagation: false },
  catchanimationfinish: { reactEvent: 'onAnimationEnd', stopPropagation: true },

  // 地图事件系列（完整映射）
  bindregionchange: { reactEvent: 'onChange', stopPropagation: false },
  catchregionchange: { reactEvent: 'onChange', stopPropagation: true },
  bindmarkertap: { reactEvent: 'onClick', stopPropagation: false },
  catchmarkertap: { reactEvent: 'onClick', stopPropagation: true },
  bindcallouttap: { reactEvent: 'onClick', stopPropagation: false },
  catchcallouttap: { reactEvent: 'onClick', stopPropagation: true },
  bindcontroltap: { reactEvent: 'onClick', stopPropagation: false },
  catchcontroltap: { reactEvent: 'onClick', stopPropagation: true },

  // 直播事件系列（完整映射）
  bindstatechange: { reactEvent: 'onChange', stopPropagation: false },
  catchstatechange: { reactEvent: 'onChange', stopPropagation: true },
  bindfullscreenchange: {
    reactEvent: 'onFullscreenChange',
    stopPropagation: false,
  },
  catchfullscreenchange: {
    reactEvent: 'onFullscreenChange',
    stopPropagation: true,
  },
  bindnetstatus: { reactEvent: 'onNetStatus', stopPropagation: false },
  catchnetstatus: { reactEvent: 'onNetStatus', stopPropagation: true },
};

// ===============================
// 事件映射工具函数
// ===============================

/**
 * 获取事件映射（支持模糊匹配）
 */
export function getEventMapping(eventName: string): EventMapping | null {
  // 1. 直接匹配
  if (EVENT_DIRECTIVE_MAPPING[eventName]) {
    return EVENT_DIRECTIVE_MAPPING[eventName];
  }

  // 2. 去掉前缀匹配
  const baseEvent = eventName.replace(/^(bind|catch):/, '');
  const prefix = eventName.startsWith('catch:') ? 'catch:' : 'bind';

  const fallbackMapping = {
    click: 'onClick',
    tap: 'onClick',
    touch: 'onTouchStart',
    mouse: 'onMouseDown',
    key: 'onKeyDown',
  };

  for (const [pattern, reactEvent] of Object.entries(fallbackMapping)) {
    if (baseEvent.includes(pattern)) {
      return {
        reactEvent,
        stopPropagation: prefix === 'catch:',
      };
    }
  }

  // 3. 默认返回onClick
  return {
    reactEvent: 'onClick',
    stopPropagation: prefix === 'catch:',
  };
}

// ===============================
// TTSS转换配置
// ===============================

export enum RpxMode {
  VW = 'vw', // viewport width
  REM = 'rem', // root em
  PX = 'px', // fixed pixel
  CALC = 'calc', // CSS calc()
}

export interface TTSSConversionConfig {
  designWidth: number; // 设计稿宽度
  defaultMode: RpxMode; // 默认转换模式

  // 不同转换模式的计算公式
  converters: Record<
    RpxMode,
    (rpxValue: number, designWidth: number) => string
  >;

  // CSS单位转换配置
  unitConversion: {
    convertRpx: Record<string, (rpxValue: number) => string>;
    convertRpxDefault: (rpxValue: number) => string;
    preserveUnit: (value: string) => string;
  };
}

export const TTSS_CONVERSION_CONFIG: TTSSConversionConfig = {
  designWidth: 750, // 设计稿宽度 (与TikTok标准一致)
  defaultMode: RpxMode.VW, // 默认转换模式 (推荐VW以获得最佳响应式效果)

  // 不同转换模式的计算公式
  converters: {
    [RpxMode.VW]: (rpxValue: number, designWidth: number) =>
      `${((rpxValue / designWidth) * 100).toFixed(6)}vw`,

    [RpxMode.REM]: (rpxValue: number, designWidth: number) =>
      `${(rpxValue / 37.5).toFixed(6)}rem`,

    [RpxMode.PX]: (rpxValue: number, designWidth: number) =>
      `${rpxValue * (750 / designWidth)}px`, // 假设基准750px

    [RpxMode.CALC]: (rpxValue: number, designWidth: number) =>
      `calc(${rpxValue} * 100vw / ${designWidth})`,
  },

  // CSS单位转换配置
  unitConversion: {
    // 具体转换方法
    convertRpx: {
      toVw: (rpxValue: number): string =>
        `${((rpxValue / 750) * 100).toFixed(6)}vw`,
      toRem: (rpxValue: number): string => `${(rpxValue / 37.5).toFixed(6)}rem`,
      toPx: (rpxValue: number): string => `${rpxValue}px`,
      toCalc: (rpxValue: number): string => `calc(${rpxValue} * 100vw / 750)`,
    },

    // 默认转换 (使用VW模式，与web-speedy-plugin默认行为一致)
    convertRpxDefault: (rpxValue: number): string =>
      `${((rpxValue / 750) * 100).toFixed(6)}vw`,

    // 保持所有其他单位不变，支持AI接口任意单位
    preserveUnit: (value: string): string => value,
  },
};

// ===============================
// 工具函数
// ===============================

/**
 * 解析表达式（简化版）
 */
function parseExpression(expression: string, context: any): any {
  // 简化的表达式解析器
  const trimmed = expression.trim();

  // 简单变量
  if (/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(trimmed)) {
    return { type: 'Identifier', name: trimmed };
  }

  // 属性访问
  if (trimmed.includes('.')) {
    const parts = trimmed.split('.');
    let expr = { type: 'Identifier', name: parts[0] };

    for (let i = 1; i < parts.length; i++) {
      expr = {
        type: 'MemberExpression',
        object: expr,
        property: { type: 'Identifier', name: parts[i] },
      };
    }

    return expr;
  }

  // 字面量
  if (/^['"].*['"]$/.test(trimmed)) {
    return { type: 'Literal', value: trimmed.slice(1, -1) };
  }

  if (/^\d+$/.test(trimmed)) {
    return { type: 'Literal', value: parseInt(trimmed) };
  }

  if (/^\d+\.\d+$/.test(trimmed)) {
    return { type: 'Literal', value: parseFloat(trimmed) };
  }

  if (trimmed === 'true' || trimmed === 'false') {
    return { type: 'Literal', value: trimmed === 'true' };
  }

  // 默认返回标识符
  return { type: 'Identifier', name: trimmed };
}

/**
 * 解析for表达式
 */
function parseForExpression(forValue: string): {
  itemName: string;
  indexName: string;
  listExpression: string;
} {
  // 解析 "item in list" 或 "item, index in list"
  const parts = forValue.split(' in ');
  const itemPart = parts[0].trim();
  const listExpression = parts[1].trim();

  let itemName: string, indexName: string;
  if (itemPart.includes(',')) {
    [itemName, indexName] = itemPart.split(',').map(s => s.trim());
  } else {
    itemName = itemPart;
    indexName = 'index';
  }

  return { itemName, indexName, listExpression };
}

/**
 * 转换节点（占位符）
 */
function transformNode(node: any, context: any): any {
  // 实际实现将在Parse5TTMLAdapter中
  return node;
}

// ===============================
// 映射规则工具函数
// ===============================

/**
 * 获取元素映射规则（增强版）
 */
export function getElementMapping(tagName: string): ElementMapping {
  // 1. 尝试直接映射
  if (TTML_ELEMENT_MAPPING[tagName]) {
    return TTML_ELEMENT_MAPPING[tagName];
  }

  // 2. 尝试按类型降级
  const textElements = ['text', 'rich-text', 'label'];
  const inputElements = ['input', 'textarea', 'switch', 'slider', 'picker'];
  const mediaElements = ['image', 'video', 'audio', 'canvas', 'camera'];

  if (textElements.some(el => tagName.includes(el))) {
    return FALLBACK_ELEMENT_MAPPING['text-fallback'];
  }

  if (inputElements.some(el => tagName.includes(el))) {
    return FALLBACK_ELEMENT_MAPPING['input-fallback'];
  }

  if (mediaElements.some(el => tagName.includes(el))) {
    return FALLBACK_ELEMENT_MAPPING['media-fallback'];
  }

  // 3. 默认降级到div
  return FALLBACK_ELEMENT_MAPPING.default;
}

/**
 * 判断是否为自闭合标签
 */
export function isSelfClosingTag(tagName: string): boolean {
  return SELF_CLOSING_TAGS.has(tagName);
}

/**
 * 验证映射规则完整性
 */
export function validateMappingRules(): {
  valid: boolean;
  issues: string[];
  stats: {
    totalMappings: number;
    selfClosingTags: number;
    fallbackRules: number;
  };
} {
  const issues: string[] = [];
  const stats = {
    totalMappings: Object.keys(TTML_ELEMENT_MAPPING).length,
    selfClosingTags: SELF_CLOSING_TAGS.size,
    fallbackRules: Object.keys(FALLBACK_ELEMENT_MAPPING).length,
  };

  // 检查必要的映射是否存在
  const requiredMappings = ['view', 'text', 'image', 'button', 'input'];
  requiredMappings.forEach(tag => {
    if (!TTML_ELEMENT_MAPPING[tag]) {
      issues.push(`缺少必要的映射规则: ${tag}`);
    }
  });

  // 检查自闭合标签配置是否一致
  Object.entries(TTML_ELEMENT_MAPPING).forEach(([tag, mapping]) => {
    if (mapping.selfClosing && !SELF_CLOSING_TAGS.has(tag)) {
      issues.push(`自闭合标签配置不一致: ${tag}`);
    }
  });

  return {
    valid: issues.length === 0,
    issues,
    stats,
  };
}
